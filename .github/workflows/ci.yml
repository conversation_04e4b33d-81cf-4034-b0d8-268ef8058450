name: CI Pipeline with Test Sharding

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop

jobs:
  test:
    name: Test Shard ${{ matrix.shard_index }} / ${{ strategy.job-total }}
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        shard_total: [4]
        shard_index: [1, 2, 3, 4]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      # --- Node.js Setup for Client Tests ---
      - name: Set up Node.js for client
        uses: actions/setup-node@v4
        with:
          node-version: '24'
          cache: 'pnpm'

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install client dependencies
        working-directory: ./client
        run: pnpm install --frozen-lockfile

      - name: Run client tests with sharding and JUnit report
        working-directory: ./client
        run: pnpm test --shard-index ${{ matrix.shard_index }} --shard-total ${{ matrix.shard_total }} --outputFile=junit-client-results-${{ matrix.shard_index }}.xml --reporters=default --reporters=junit
        # The --outputFile will generate a report for each shard
        # --reporters=default --reporters=junit ensures both console output and JUnit XML

      # --- Python Setup for Server Tests ---
      - name: Set up Python for server
        uses: actions/setup-python@v5
        with:
          python-version: '3.13' # Ensure this matches your server's Python version
          cache: 'pip'

      - name: Install uv for server
        run: pip install uv

      - name: Install server dependencies with uv
        working-directory: ./server
        run: uv sync

      - name: Run server tests and generate JUnit report
        working-directory: ./server
        # Install pytest-junitxml if not already in requirements.txt
        run: |
          pip install pytest-junitxml || true # Only install if not present
          uv run pytest --junitxml=junit-server-results-${{ matrix.shard_index }}.xml
        # IMPORTANT: All server tests run in each shard job,
        # but a unique report file will be generated for each job.

      # --- Report Publishing Steps ---
      - name: Upload Client Test Results Artifacts
        uses: actions/upload-artifact@v4
        if: always() # Uploads even if tests fail
        with:
          name: client-test-results-shard-${{ matrix.shard_index }}
          path: client/junit-client-results-${{ matrix.shard_index }}.xml

      - name: Upload Server Test Results Artifacts
        uses: actions/upload-artifact@v4
        if: always() # Uploads even if tests fail
        with:
          name: server-test-results-shard-${{ matrix.shard_index }}
          path: server/junit-server-results-${{ matrix.shard_index }}.xml

      - name: Publish Test Results to GitHub Actions Summary (Client)
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: Client Tests (Shard ${{ matrix.shard_index }})
          path: client/junit-client-results-${{ matrix.shard_index }}.xml
          reporter: junit
          fail-on-error: false # Allows overall workflow to continue if reporter fails

      - name: Publish Test Results to GitHub Actions Summary (Server)
        uses: dorny/test-reporter@v1
        if: always()
        with:
          name: Server Tests (Shard ${{ matrix.shard_index }})
          path: server/junit-server-results-${{ matrix.shard_index }}.xml
          reporter: junit
          fail-on-error: false # Allows overall workflow to continue if reporter fails