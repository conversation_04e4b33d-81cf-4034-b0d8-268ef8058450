This directory contains the C# application responsible for integrating with CAD software, specifically AutoCAD, via its native APIs (.NET API or ObjectARX). This service performs operations that require direct interaction with AutoCAD, such as:

Generating or modifying CAD drawings based on design data.

Extracting detailed information from DWG files.

Performing complex geometric validations or transformations within the CAD environment.

Automating AutoCAD commands and workflows.

Technologies
Language: C#

Framework: .NET (e.g., ASP.NET Core for API, or a console application for local plugins)

AutoCAD APIs: Autodesk ObjectARX (C++) or AutoCAD .NET API (C#)

Communication: Typically exposes a gRPC or REST API for the Python backend, or uses message queues for asynchronous tasks.

Integration with Python Backend
The Python API server (located in ../server) communicates with this service via defined API endpoints or message queues. The Python backend sends requests for CAD-specific operations, and this C# service executes them, returning results or status updates.

Deployment
This service is designed for independent deployment. It can run as a separate microservice (potentially containerized with Docker) or as a local agent/plugin that interacts with a desktop AutoCAD installation, depending on the specific integration strategy (cloud-based Design Automation API vs. local AutoCAD plugin).