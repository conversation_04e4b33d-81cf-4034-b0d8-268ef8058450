# PostgreSQL Silent Installer Plan

## 1. Objective

To create a scripted, silent installer for PostgreSQL on Windows. This installer will automate the entire setup process, including installation, configuration, and user creation, requiring minimal user interaction. This is a critical component for deploying the shared local database server as defined in the project's architecture.

## 2. Chosen Approach: Command-Line Installation Script

The chosen method is to use a PowerShell or Batch script that executes the official PostgreSQL installer from EnterpriseDB with a set of command-line arguments to achieve a silent, unattended installation.

**Rationale:**
-   **No External Dependencies:** This approach does not require the end-user to install additional package managers like Chocolatey.
-   **Official Installer:** It leverages the official, trusted installer, ensuring a standard and reliable setup.
-   **Customizable:** Command-line arguments provide sufficient control over the installation process, including the installation directory, data directory, password, and port.

## 3. Installer Components

The final installation package will consist of:
1.  **The official PostgreSQL Windows installer (`.exe`)**: This will be bundled with our application's deployment assets.
2.  **`install_postgres.ps1` (PowerShell Script)**: The main script that orchestrates the installation.
3.  **`setup.cfg`**: A configuration file containing parameters like the desired installation path, data directory, password, etc. This allows for easy customization without modifying the script itself.
4.  **`init_db.sql`**: An SQL script to be run after installation to create the application-specific user and database.

## 4. Installation Steps (to be performed by the script)

### Step 1: Pre-flight Checks
-   The script will first check for existing PostgreSQL installations to avoid conflicts.
-   It will verify that it is being run with administrator privileges.
-   It will read the installation parameters from `setup.cfg`.

### Step 2: Silent Installation
-   The script will execute the PostgreSQL installer executable with the necessary command-line arguments. An example command would be:
    ```powershell
    .\\postgresql-16.3-1-windows-x64.exe --mode unattended --unattendedmodeui none --datadir "C:\\PostgreSQL\\data" --superpassword "your_secure_password" --serverport 5432
    ```
-   The script will wait for the installation process to complete and check the exit code for success.

### Step 3: Post-Installation Configuration
-   **Update `pg_hba.conf`**: The script will programmatically add a line to `pg_hba.conf` to allow connections from other machines on the local network. For example, adding `host all all ***********/24 md5`.
-   **Update `postgresql.conf`**: The script will modify `postgresql.conf` to set `listen_addresses = '*'`, allowing it to accept connections on all network interfaces.

### Step 4: Database and User Initialization
-   The script will use `psql.exe` (now in the system's PATH) to execute the `init_db.sql` script.
-   This SQL script will:
    -   Create a new user role for the application (e.g., `ued_app_user`).
    -   Create the application database (e.g., `ultimate_electrical_designer_local`).
    -   Grant the necessary privileges on the database to the new user.

### Step 5: Firewall Configuration
-   The script will add a new inbound rule to the Windows Firewall to allow traffic on the PostgreSQL port (e.g., 5432).

### Step 6: Service Management
-   The script will ensure the PostgreSQL service is running and set to start automatically on system boot.

## 5. Configuration File (`setup.cfg`)

This file will provide the parameters for the installation script.

```ini
[PostgreSQL]
InstallerPath=./postgresql-16.3-1-windows-x64.exe
InstallDir=C:\Program Files\PostgreSQL\16
DataDir=C:\PostgreSQL\data
Password=a_very_secure_default_password
Port=5432

[Database]
AppName=ultimate_electrical_designer
DBName=ued_local
DBUser=ued_app_user
DBPassword=another_secure_password

[Network]
AllowedSubnet=***********/24
```

## 6. Security Considerations

-   **Default Passwords**: The script will use strong, randomly generated default passwords and strongly recommend the user to change them immediately after installation. The script will output the generated passwords to a secure file on the desktop.
-   **Firewall Rules**: The firewall rule will be as specific as possible, only allowing connections from the specified local subnet.

This plan provides a clear path to creating a robust, automated installer for the shared local PostgreSQL database, which is a foundational piece of the new architecture.
