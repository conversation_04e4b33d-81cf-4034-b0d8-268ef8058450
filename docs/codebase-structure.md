ued/
├── .env
├── README.md
├── cad-integrator-service/
│   ├── Dockerfile
│   ├── README.md
│   └── src/
│       ├── ultimate_electrical_designer.CadIntegrator.csproj
│       ├── Controllers/
│       │   └── CadController.cs
│       └── Services/
│           └── AutoCADService.cs
├── client/
│   ├── playwright.config.ts
│   ├── tailwind.config.ts
│   ├── vitest.config.ts
│   ├── vitest.setup.ts
│   ├── docs/
│   │   ├── api-client-guide.md
│   │   └── migration-guide.md
│   ├── public/
│   ├── src/
│   │   ├── app/
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx
│   │   │   ├── providers.tsx
│   │   │   ├── admin/
│   │   │   │   └── roles/
│   │   │   │       └── page.tsx
│   │   │   ├── login/
│   │   │   │   └── page.tsx
│   │   │   └── register/
│   │   │       └── page.tsx
│   │   ├── assets/
│   │   ├── components/
│   │   │   ├── atoms/
│   │   │   │   ├── badge-alt.tsx
│   │   │   │   ├── badge.tsx
│   │   │   │   ├── button.tsx
│   │   │   │   ├── checkbox.tsx
│   │   │   │   ├── form.tsx
│   │   │   │   ├── icon.tsx
│   │   │   │   ├── input.tsx
│   │   │   │   ├── label.tsx
│   │   │   │   ├── progress.tsx
│   │   │   │   ├── radio-group.tsx
│   │   │   │   ├── separator.tsx
│   │   │   │   ├── skeleton.tsx
│   │   │   │   ├── slider.tsx
│   │   │   │   ├── sonner.tsx
│   │   │   │   ├── state.tsx
│   │   │   │   ├── switch.tsx
│   │   │   │   ├── textarea.tsx
│   │   │   │   ├── timeline.tsx
│   │   │   │   ├── toggle.tsx
│   │   │   │   └── __tests__/
│   │   │   │       ├── button.auth.test.tsx
│   │   │   │       ├── icon.auth.test.tsx
│   │   │   │       ├── input.auth.test.tsx
│   │   │   │       └── label.auth.test.tsx
│   │   │   ├── modules/
│   │   │   │   ├── auth/
│   │   │   │   │   ├── RouteGuard.tsx
│   │   │   │   │   ├── types.ts
│   │   │   │   │   ├── RoleManagement/
│   │   │   │   │   │   └── index.tsx
│   │   │   │   │   └── __tests__/
│   │   │   │   │       └── types.test.ts
│   │   │   │   └── navigation/
│   │   │   │       ├── app-sidebar.tsx
│   │   │   │       ├── nav-items.ts
│   │   │   │       ├── nav-list-item.tsx
│   │   │   │       ├── nav-user.tsx
│   │   │   │       ├── types.ts
│   │   │   │       └── __tests__/
│   │   │   │           ├── app-sidebar.test.tsx
│   │   │   │           └── types.test.ts
│   │   │   ├── molecules/
│   │   │   │   ├── accordion.tsx
│   │   │   │   ├── alert.tsx
│   │   │   │   ├── avatar.tsx
│   │   │   │   ├── breadcrumb.tsx
│   │   │   │   ├── card.tsx
│   │   │   │   ├── collapsible.tsx
│   │   │   │   ├── form.tsx
│   │   │   │   ├── hover-card.tsx
│   │   │   │   ├── input-field.tsx
│   │   │   │   ├── password-input.tsx
│   │   │   │   ├── popover.tsx
│   │   │   │   ├── select-native.tsx
│   │   │   │   ├── select.tsx
│   │   │   │   ├── table.tsx
│   │   │   │   ├── toast.tsx
│   │   │   │   ├── toaster.tsx
│   │   │   │   ├── toggle-group.tsx
│   │   │   │   ├── tooltip.tsx
│   │   │   │   └── __tests__/
│   │   │   │       ├── input-field.test.tsx
│   │   │   │       └── password-input.test.tsx
│   │   │   ├── organisms/
│   │   │   │   ├── alert-dialog.tsx
│   │   │   │   ├── calendar-rac.tsx
│   │   │   │   ├── calendar.tsx
│   │   │   │   ├── command.tsx
│   │   │   │   ├── datefield-rac.tsx
│   │   │   │   ├── dialog.tsx
│   │   │   │   ├── dropdown-menu.tsx
│   │   │   │   ├── index.ts
│   │   │   │   ├── login-form.tsx
│   │   │   │   ├── multiselect.tsx
│   │   │   │   ├── navigation-menu.tsx
│   │   │   │   ├── pagination.tsx
│   │   │   │   ├── registration-form.tsx
│   │   │   │   ├── sheet.tsx
│   │   │   │   ├── sidebar.tsx
│   │   │   │   ├── stepper.tsx
│   │   │   │   ├── tree.tsx
│   │   │   │   └── __tests__/
│   │   │   │       ├── login-form.test.tsx
│   │   │   │       └── registration-form.test.tsx
│   │   │   └── templates/
│   │   │       ├── auth-layout.tsx
│   │   │       ├── checkbox-tree.tsx
│   │   │       ├── cropper.tsx
│   │   │       ├── index.ts
│   │   │       ├── login-page.tsx
│   │   │       ├── register-page.tsx
│   │   │       ├── resizable.tsx
│   │   │       ├── scroll-area.tsx
│   │   │       ├── tabs.tsx
│   │   │       └── __tests__/
│   │   │           ├── auth-layout.test.tsx
│   │   │           ├── login-page.test.tsx
│   │   │           └── register-page.test.tsx
│   │   ├── core/
│   │   │   └── caching/
│   │   │       ├── cache_provider.tsx
│   │   │       ├── index.ts
│   │   │       ├── indexed_db_persister.ts
│   │   │       ├── sync_manager.ts
│   │   │       └── __tests__/
│   │   │           ├── cache_provider.integration.test.tsx
│   │   │           ├── cache_provider.test.tsx
│   │   │           ├── cache_provider.validation.test.tsx
│   │   │           ├── indexed_db_persister.integration.test.ts
│   │   │           ├── indexed_db_persister.test.ts
│   │   │           ├── indexed_db_persister.validation.test.ts
│   │   │           ├── sync_manager.test.ts
│   │   │           └── sync_manager.validation.test.tsx
│   │   ├── hooks/
│   │   │   ├── useAuth.ts
│   │   │   ├── useCharacterLimit.ts
│   │   │   ├── useCopyToClipboard.ts
│   │   │   ├── useFileUpload.ts
│   │   │   ├── useHasPermission.ts
│   │   │   ├── useLayout.tsx
│   │   │   ├── useOfflineMutation.ts
│   │   │   ├── usePagination.ts
│   │   │   ├── useSliderWithInput.ts
│   │   │   ├── useToast.ts
│   │   │   ├── __tests__/
│   │   │   │   ├── useHasPermission.test.tsx
│   │   │   │   └── useOfflineMutation.test.tsx
│   │   │   └── api/
│   │   │       ├── useAuth.ts
│   │   │       ├── useAuthorization.ts
│   │   │       ├── useComponentCategories.ts
│   │   │       ├── useComponentTypes.ts
│   │   │       ├── useComponents.ts
│   │   │       ├── useProjects.ts
│   │   │       ├── useTasks.ts
│   │   │       ├── useUsers.ts
│   │   │       └── __tests__/
│   │   │           ├── useAuth.test.tsx
│   │   │           ├── useAuthorization.test.tsx
│   │   │           └── useRegister.test.tsx
│   │   ├── lib/
│   │   │   ├── config.ts
│   │   │   ├── fonts.ts
│   │   │   ├── utils.ts
│   │   │   ├── api/
│   │   │   │   ├── audit.ts
│   │   │   │   ├── client.ts
│   │   │   │   ├── index.ts
│   │   │   │   ├── keys.ts
│   │   │   │   ├── rbac.ts
│   │   │   │   ├── types.ts
│   │   │   │   ├── __tests__/
│   │   │   │   │   └── client.zod.test.ts
│   │   │   │   ├── endpoints/
│   │   │   │   │   ├── auth.ts
│   │   │   │   │   ├── authorization.ts
│   │   │   │   │   ├── component-categories.ts
│   │   │   │   │   ├── component-types.ts
│   │   │   │   │   ├── components.ts
│   │   │   │   │   ├── health.ts
│   │   │   │   │   ├── index.ts
│   │   │   │   │   ├── projects.ts
│   │   │   │   │   ├── tasks.ts
│   │   │   │   │   ├── users.ts
│   │   │   │   │   └── __tests__/
│   │   │   │   │       ├── endpoints.test.ts
│   │   │   │   │       └── endpoints.zod.test.ts
│   │   │   │   └── types/
│   │   │   │       ├── auth.ts
│   │   │   │       ├── authorization.ts
│   │   │   │       ├── common.ts
│   │   │   │       ├── components.ts
│   │   │   │       ├── index.ts
│   │   │   │       ├── projects.ts
│   │   │   │       ├── tasks.ts
│   │   │   │       ├── users.ts
│   │   │   │       └── __tests__/
│   │   │   │           └── types.test.ts
│   │   │   ├── auth/
│   │   │   │   └── tokenManager.ts
│   │   │   ├── store/
│   │   │   │   └── index.ts
│   │   │   ├── validation/
│   │   │   │   ├── auth.ts
│   │   │   │   ├── base.ts
│   │   │   │   ├── forms.ts
│   │   │   │   ├── index.ts
│   │   │   │   ├── __tests__/
│   │   │   │   │   └── auth.test.ts
│   │   │   │   └── api/
│   │   │   │       └── auth.ts
│   │   │   └── websocket/
│   │   ├── providers/
│   │   │   ├── query-provider.tsx
│   │   │   └── theme-provider.tsx
│   │   ├── stores/
│   │   │   ├── authStore.ts
│   │   │   ├── networkStatusStore.ts
│   │   │   └── __tests__/
│   │   │       └── networkStatusStore.test.ts
│   │   ├── styles/
│   │   ├── test/
│   │   │   ├── setup.ts
│   │   │   ├── utils.tsx
│   │   │   ├── factories/
│   │   │   ├── integration/
│   │   │   │   ├── auth-integration.test.tsx
│   │   │   │   ├── offline-mode-integration.test.tsx
│   │   │   │   └── rbac-workflow.test.tsx
│   │   │   └── reporters/
│   │   │       └── domain-reporter.ts
│   │   └── utils/
│   │       ├── active-theme.tsx
│   │       ├── tailwind-indicator.tsx
│   │       └── textUtils.ts
│   ├── tests/
│   │   ├── e2e/
│   │   │   ├── auth-login.spec.ts
│   │   │   ├── auth-register.spec.ts
│   │   │   ├── cache-persistence.spec.ts
│   │   │   ├── component-management.spec.ts
│   │   │   ├── global.setup.ts
│   │   │   ├── offline-mode.spec.ts
│   │   │   └── rbac-audit-workflow.spec.ts
│   │   └── mocks/
│   │       ├── browser.ts
│   │       ├── server.ts
│   │       ├── fixtures/
│   │       │   ├── auth.ts
│   │       │   ├── componentCategories.ts
│   │       │   ├── componentTypes.ts
│   │       │   └── components.ts
│   │       └── handlers/
│   │           ├── auth.ts
│   │           ├── componentCategories.ts
│   │           ├── componentTypes.ts
│   │           ├── components.ts
│   │           ├── health.ts
│   │           ├── projects.ts
│   │           └── users.ts
│   └── ~/
├── computation-engine-service/
│   ├── Dockerfile
│   ├── README.md
│   └── src/
│       ├── ultimate_electrical_designer.ComputationEngine.csproj
│       ├── Controllers/
│       │   └── ComputationController.cs
│       └── Services/
│           └── PowerFlowSolver.cs
├── docs/
│   ├── README.md
│   ├── TESTING.md
│   ├── codebase-structure.md
│   ├── design.md
│   ├── product.md
│   ├── requirements.md
│   ├── rules.md
│   ├── tasks.md
│   ├── tech.md
│   ├── workflows.md
│   ├── deployment/
│   │   └── POSTGRESQL_INSTALLER_PLAN.md
│   ├── developer-guides/
│   │   ├── client/
│   │   │   ├── atomid-design-guide.md
│   │   │   └── cache-design-guide.md
│   │   └── server/
│   │       └── synchronization-developer-guide.md
│   ├── personal/
│   │   ├── agents/
│   │   │   ├── code.md
│   │   │   ├── framework.md
│   │   │   ├── orchestrator.md
│   │   │   ├── quality.md
│   │   │   ├── task-planner.md
│   │   │   ├── technical-design.md
│   │   │   └── template.md
│   │   └── templates/
│   │       └── fix-type-errors.md
│   └── tasks/
│       ├── add-zod-package/
│       │   ├── IMPLEMENTATION.md
│       │   ├── TASK_PLAN.md
│       │   └── TECH_DESIGN.md
│       ├── api-client/
│       │   ├── 1-IMPLEMENTATION_SUMMARY.md
│       │   ├── 2-VERIFICATION_REPORT.md
│       │   └── 3-REMEDIATION_SUMMARY.md
│       ├── authentication-system/
│       │   └── AUTHENTICATION_HANDOVER.md
│       └── authorization-system-(FR-1.3)/
│           ├── AUTHORIZATION_HANDOVER.md
│           ├── IMPLEMENTATION.md
│           ├── TASK_PLAN.md
│           └── TECH_DESIGN.md
├── scripts/
├── server/
│   ├── .env
│   ├── Dockerfile
│   ├── README.md
│   ├── conftest.py
│   ├── pyproject.toml
│   ├── data/
│   │   └── seed_general.py
│   ├── docs/
│   │   ├── 2025-07-19_test-database-integrity/
│   │   │   ├── 01-test_database_integrity_report.md
│   │   │   ├── 02-enhanced_verification_report.md
│   │   │   ├── 03-test_coverage_immediate_actions.md
│   │   │   ├── 04-performance_testing_enhancement_summary.md
│   │   │   ├── 05-database_operations_testing_comprehensive_summary.md
│   │   │   └── 06-advanced_validation_compatibility.md
│   │   ├── 2025-07-20_dual-database-setup-DEPRECATED/
│   │   │   ├── discovery-analysis.md
│   │   │   ├── dual-database-guide.md
│   │   │   ├── implementation-report.md
│   │   │   └── plan.md
│   │   ├── 2025-07-21_offline-mode/
│   │   │   ├── discovery_analysis.md
│   │   │   └── plan.md
│   │   ├── 2025-07-22_unified_local_database/
│   │   │   ├── phase1_discovery_analysis.md
│   │   │   ├── phase2_implementation_plan.md
│   │   │   └── phase2_implementation_report.md
│   │   └── 2025-07-29_comprehensive-test-verification/
│   │       ├── phase4_comprehensive_verification_report.md
│   │       └── systematic_issues_resolution_summary.md
│   ├── src/
│   │   ├── app.py
│   │   ├── main.py
│   │   ├── alembic/
│   │   ├── api/
│   │   │   ├── main_router.py
│   │   │   └── v1/
│   │   │       ├── auth_routes.py
│   │   │       ├── authorization_routes.py
│   │   │       ├── component_category_routes.py
│   │   │       ├── component_routes.py
│   │   │       ├── component_type_routes.py
│   │   │       ├── cross_validation_routes.py
│   │   │       ├── health_routes.py
│   │   │       ├── parallel_validation_routes.py
│   │   │       ├── project_phase_routes.py
│   │   │       ├── project_routes.py
│   │   │       ├── router.py
│   │   │       ├── system_configuration_routes.py
│   │   │       ├── task_routes.py
│   │   │       ├── user_preferences_routes.py
│   │   │       ├── user_routes.py
│   │   │       └── validation_routes.py
│   │   ├── config/
│   │   │   ├── logging_config.py
│   │   │   └── settings.py
│   │   ├── core/
│   │   │   ├── auth/
│   │   │   │   └── dependencies.py
│   │   │   ├── calculations/
│   │   │   ├── database/
│   │   │   │   ├── connection_manager.py
│   │   │   │   ├── dependencies.py
│   │   │   │   ├── engine.py
│   │   │   │   ├── initialization.py
│   │   │   │   └── session.py
│   │   │   ├── enums/
│   │   │   │   ├── calculation_enums.py
│   │   │   │   ├── common_enums.py
│   │   │   │   ├── data_io_enums.py
│   │   │   │   ├── electrical_enums.py
│   │   │   │   ├── heat_tracing_enums.py
│   │   │   │   ├── mechanical_enums.py
│   │   │   │   ├── project_management_enums.py
│   │   │   │   ├── standards_enums.py
│   │   │   │   └── system_enums.py
│   │   │   ├── errors/
│   │   │   │   ├── exceptions.py
│   │   │   │   └── unified_error_handler.py
│   │   │   ├── integrations/
│   │   │   │   └── README.md
│   │   │   ├── models/
│   │   │   │   ├── base.py
│   │   │   │   └── general/
│   │   │   │       ├── activity_log.py
│   │   │   │       ├── component.py
│   │   │   │       ├── component_category.py
│   │   │   │       ├── component_type.py
│   │   │   │       ├── permission.py
│   │   │   │       ├── project.py
│   │   │   │       ├── project_phase.py
│   │   │   │       ├── synchronization_log.py
│   │   │   │       ├── system_configuration.py
│   │   │   │       ├── task.py
│   │   │   │       ├── user.py
│   │   │   │       └── user_role.py
│   │   │   ├── monitoring/
│   │   │   │   ├── performance_monitor.py
│   │   │   │   └── unified_performance_monitor.py
│   │   │   ├── repositories/
│   │   │   │   ├── base_repository.py
│   │   │   │   ├── repository_dependencies.py
│   │   │   │   └── general/
│   │   │   │       ├── component_category_repository.py
│   │   │   │       ├── component_repository.py
│   │   │   │       ├── component_type_repository.py
│   │   │   │       ├── permission_repository.py
│   │   │   │       ├── project_member_repository.py
│   │   │   │       ├── project_repository.py
│   │   │   │       ├── role_repository.py
│   │   │   │       ├── task_repository.py
│   │   │   │       ├── user_preference_repository.py
│   │   │   │       └── user_repository.py
│   │   │   ├── schemas/
│   │   │   │   ├── base_schemas.py
│   │   │   │   ├── error.py
│   │   │   │   ├── health.py
│   │   │   │   └── general/
│   │   │   │       ├── audit_trail_schemas.py
│   │   │   │       ├── component_category_schemas.py
│   │   │   │       ├── component_schemas.py
│   │   │   │       ├── component_type_schemas.py
│   │   │   │       ├── permission_schemas.py
│   │   │   │       ├── project_member_schemas.py
│   │   │   │       ├── project_phase_schemas.py
│   │   │   │       ├── project_schemas.py
│   │   │   │       ├── system_configuration_schemas.py
│   │   │   │       ├── task_schemas.py
│   │   │   │       ├── user_role_schemas.py
│   │   │   │       └── user_schemas.py
│   │   │   ├── security/
│   │   │   │   ├── enhanced_dependencies.py
│   │   │   │   ├── input_validators.py
│   │   │   │   ├── password_handler.py
│   │   │   │   └── unified_security_validator.py
│   │   │   ├── services/
│   │   │   │   ├── dependencies.py
│   │   │   │   └── general/
│   │   │   │       ├── audit_trail_service.py
│   │   │   │       ├── authorization_service.py
│   │   │   │       ├── component_category_service.py
│   │   │   │       ├── component_service.py
│   │   │   │       ├── component_type_service.py
│   │   │   │       ├── health_service.py
│   │   │   │       ├── project_member_service.py
│   │   │   │       ├── project_phase_service.py
│   │   │   │       ├── project_service.py
│   │   │   │       ├── synchronization_service.py
│   │   │   │       ├── task_manager_service.py
│   │   │   │       └── user_service.py
│   │   │   ├── standards/
│   │   │   ├── utils/
│   │   │   │   ├── advanced_cache_manager.py
│   │   │   │   ├── crud_endpoint_factory.py
│   │   │   │   ├── datetime_utils.py
│   │   │   │   ├── file_io_utils.py
│   │   │   │   ├── json_validation.py
│   │   │   │   ├── logger.py
│   │   │   │   ├── memory_manager.py
│   │   │   │   ├── pagination_utils.py
│   │   │   │   ├── performance_optimizer.py
│   │   │   │   ├── performance_utils.py
│   │   │   │   ├── query_optimizer.py
│   │   │   │   ├── query_utils.py
│   │   │   │   ├── search_query_builder.py
│   │   │   │   ├── security.py
│   │   │   │   ├── string_utils.py
│   │   │   │   └── uuid_utils.py
│   │   │   └── validation/
│   │   │       ├── advanced_validators.py
│   │   │       ├── compatibility_matrix.py
│   │   │       ├── constraint_validator.py
│   │   │       ├── cross_entity_validator.py
│   │   │       ├── data_format_validator.py
│   │   │       ├── intelligent_caching.py
│   │   │       ├── json_schema_validator.py
│   │   │       ├── legacy_migration_validator.py
│   │   │       ├── parallel_processor.py
│   │   │       └── standards_validator.py
│   │   ├── middleware/
│   │   │   ├── caching_middleware.py
│   │   │   ├── context_middleware.py
│   │   │   ├── logging_middleware.py
│   │   │   ├── rate_limiting_middleware.py
│   │   │   └── security_middleware.py
│   │   └── ~/
│   ├── tests/
│   │   ├── conftest.py
│   │   ├── test_cleanup_utilities.py
│   │   ├── test_transaction_isolation.py
│   │   ├── api/
│   │   │   ├── conftest.py
│   │   │   └── v1/
│   │   │       ├── test_auth_routes.py
│   │   │       ├── test_authorization_routes.py
│   │   │       ├── test_component_category_routes.py
│   │   │       ├── test_component_routes.py
│   │   │       ├── test_component_type_routes.py
│   │   │       ├── test_health_routes.py
│   │   │       ├── test_project_routes.py
│   │   │       ├── test_task_routes.py
│   │   │       └── test_user_routes.py
│   │   ├── core/
│   │   │   ├── calculations/
│   │   │   │   └── conftest.py
│   │   │   ├── config/
│   │   │   │   └── test_settings.py
│   │   │   ├── database/
│   │   │   │   ├── test_alembic_migration_automation.py
│   │   │   │   ├── test_connection_manager.py
│   │   │   │   ├── test_connection_manager_integration.py
│   │   │   │   ├── test_migration_rollback_scenarios.py
│   │   │   │   ├── test_project_database_routing.py
│   │   │   │   └── test_task_migration.py
│   │   │   ├── errors/
│   │   │   │   ├── test_error_context.py
│   │   │   │   ├── test_error_handler_decorators.py
│   │   │   │   ├── test_error_handling_result.py
│   │   │   │   ├── test_middleware_safe_error_handlers.py
│   │   │   │   └── test_unified_error_handler.py
│   │   │   ├── models/
│   │   │   │   ├── test_component.py
│   │   │   │   ├── test_component_category.py
│   │   │   │   ├── test_component_relational.py
│   │   │   │   ├── test_component_type.py
│   │   │   │   ├── test_project_model_database_url.py
│   │   │   │   ├── test_synchronization_log_model.py
│   │   │   │   └── test_task.py
│   │   │   ├── repositories/
│   │   │   │   ├── conftest.py
│   │   │   │   ├── test_component_category_repository.py
│   │   │   │   ├── test_component_repository.py
│   │   │   │   ├── test_component_type_repository.py
│   │   │   │   ├── test_task_repository.py
│   │   │   │   └── general/
│   │   │   │       └── test_role_repository.py
│   │   │   ├── security/
│   │   │   │   ├── test_input_validators.py
│   │   │   │   └── test_password_handler.py
│   │   │   ├── services/
│   │   │   │   ├── conftest.py
│   │   │   │   ├── test_component_category_service.py
│   │   │   │   ├── test_component_service.py
│   │   │   │   ├── test_component_type_service.py
│   │   │   │   ├── test_project_member_service.py
│   │   │   │   ├── test_project_service.py
│   │   │   │   ├── test_project_service_database_url.py
│   │   │   │   ├── test_synchronization_service_conflict_resolution.py
│   │   │   │   ├── test_synchronization_service_main_orchestration.py
│   │   │   │   ├── test_synchronization_service_utilities.py
│   │   │   │   ├── test_task_manager_service.py
│   │   │   │   ├── test_user_service.py
│   │   │   │   └── general/
│   │   │   │       └── test_authorization_service.py
│   │   │   └── utils/
│   │   │       └── test_advanced_search.py
│   │   ├── fixtures/
│   │   │   ├── assertion_helpers.py
│   │   │   ├── integration_factories.py
│   │   │   ├── mock_factories.py
│   │   │   └── test_mock_factories.py
│   │   ├── integration/
│   │   │   ├── test_component_management_workflow.py
│   │   │   ├── test_comprehensive_data_integrity.py
│   │   │   ├── test_constraint_violations.py
│   │   │   ├── test_data_integrity.py
│   │   │   ├── test_middleware_integration.py
│   │   │   ├── test_middleware_safe_error_handling.py
│   │   │   ├── test_synchronization_service_cdc.py
│   │   │   ├── test_synchronization_service_conflict_integration.py
│   │   │   ├── test_synchronization_service_log_integration.py
│   │   │   ├── test_synchronization_service_transaction_management.py
│   │   │   └── test_validation_integration.py
│   │   ├── middleware/
│   │   │   ├── conftest.py
│   │   │   ├── test_caching_middleware.py
│   │   │   ├── test_context_middleware.py
│   │   │   ├── test_logging_middleware.py
│   │   │   ├── test_rate_limiting_middleware.py
│   │   │   └── test_security_middleware.py
│   │   ├── performance/
│   │   │   ├── conftest.py
│   │   │   ├── locust_validation_load_tests.py
│   │   │   ├── sync_repository_adapter.py
│   │   │   ├── test_component_performance.py
│   │   │   ├── test_concurrent_validation_stress.py
│   │   │   ├── test_database_performance.py
│   │   │   ├── test_email_lookup_benchmarks.py
│   │   │   ├── test_email_lookup_scale_performance.py
│   │   │   ├── test_memory_usage_concurrency.py
│   │   │   ├── test_performance_optimization.py
│   │   │   └── test_validation_pipeline_performance.py
│   │   └── validation/
│   │       ├── test_advanced_validators.py
│   │       ├── test_compatibility_matrix.py
│   │       ├── test_data_format_validator.py
│   │       ├── test_json_schema_validator.py
│   │       ├── test_legacy_migration_validator.py
│   │       ├── test_parallel_processor.py
│   │       └── test_standards_validator.py
│   └── ~/
└── ~/