# Agentic Framework

This document outlines the agentic framework for the Ultimate Electrical Designer project. The framework consists of
five specialized agents, each with a distinct role and responsibility. The agents work together in a predefined pipeline
to ensure that the project's standards and methodologies are consistently applied throughout the feature development
lifecycle.

## Agents

1. **Orchestrator Agent**: Oversees the entire feature development process, ensuring adherence to the 5-Phase
   Implementation Methodology and coordinating between specialized agents.
2. **Technical Design Agent**: Defines the technical architecture and design for new features, ensuring alignment with
   project standards and the 5-layer architecture.
3. **Task Planner Agent**: Breaks down the design into actionable implementation tasks, creating a structured plan for
   the development team.
4. **Backend/Frontend Agent**: Implements the feature by executing the task plan, adhering to the project's
   architectural patterns and development standards.
5. **Code Quality Agent**: Enforces all quality standards and rules, ensuring that the implemented code meets the
   project's engineering-grade quality requirements.

## Pipeline

The agentic pipeline follows a sequential workflow, with each agent's output serving as the input for the next agent in
the pipeline. The pipeline consists of the following stages:

1. **Discovery & Analysis**: The Orchestrator Agent delegates the initial request to the Technical Design Agent for
   defining the feature's purpose, scope, and technical requirements.
2. **Task Planning**: The Technical Design Agent's output is passed to the Task Planner Agent, which breaks down the
   design into actionable implementation tasks.
3. **Implementation**: The Task Planner Agent's output is passed to the Backend/Frontend Agent, which implements the
   feature by executing the task plan.
4. **Verification**: The Backend/Frontend Agent's output is passed to the Code Quality Agent, which enforces all quality
   standards and rules.
5. **Documentation & Handover**: The Code Quality Agent's output is passed back to the Orchestrator Agent, which
   oversees the final documentation and handover process.

## Documentation

All agents MUST reference the project's documentation for guidance on technical standards, architectural patterns, and
quality requirements. The documentation is stored in the `docs/` directory and includes the following files:

- `README.md`: Project overview and high-level guidelines.
- `requirements.md`: Detailed requirements and acceptance criteria.
- `design.md`: Technical design and architecture documentation.
- `tech.md`: Technology stack and tooling documentation.
- `rules.md`: Development standards and quality gates.
- `workflows.md`: Common development workflows and methodologies.
- `TESTING.md`: Authoritative testing documentation and strategy.
