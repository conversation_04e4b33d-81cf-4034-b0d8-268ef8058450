{"Type": "Folder", "Name": "ued", "Path": ".", "Children": [{"Type": "File", "Name": ".env", "Path": "./.env"}, {"Type": "Folder", "Name": "cad-integrator-service", "Path": "./cad-integrator-service", "Children": [{"Type": "File", "Name": "Dockerfile", "Path": "./cad-integrator-service/Dockerfile"}, {"Type": "File", "Name": "README.md", "Path": "./cad-integrator-service/README.md"}, {"Type": "Folder", "Name": "src", "Path": "./cad-integrator-service/src", "Children": [{"Type": "Folder", "Name": "Controllers", "Path": "./cad-integrator-service/src/Controllers", "Children": [{"Type": "File", "Name": "CadController.cs", "Path": "./cad-integrator-service/src/Controllers/CadController.cs"}]}, {"Type": "Folder", "Name": "Services", "Path": "./cad-integrator-service/src/Services", "Children": [{"Type": "File", "Name": "AutoCADService.cs", "Path": "./cad-integrator-service/src/Services/AutoCADService.cs"}]}, {"Type": "File", "Name": "ultimate_electrical_designer.CadIntegrator.csproj", "Path": "./cad-integrator-service/src/ultimate_electrical_designer.CadIntegrator.csproj"}]}]}, {"Type": "Folder", "Name": "client", "Path": "./client", "Children": [{"Type": "Folder", "Name": "docs", "Path": "./client/docs", "Children": [{"Type": "File", "Name": "api-client-guide.md", "Path": "./client/docs/api-client-guide.md"}, {"Type": "File", "Name": "migration-guide.md", "Path": "./client/docs/migration-guide.md"}]}, {"Type": "File", "Name": "playwright.config.ts", "Path": "./client/playwright.config.ts", "comments": "@see https://playwright.dev/docs/test-configuration"}, {"Type": "Folder", "Name": "public", "Path": "./client/public", "Children": []}, {"Type": "Folder", "Name": "src", "Path": "./client/src", "Children": [{"Type": "Folder", "Name": "app", "Path": "./client/src/app", "Children": [{"Type": "Folder", "Name": "admin", "Path": "./client/src/app/admin", "Children": [{"Type": "Folder", "Name": "roles", "Path": "./client/src/app/admin/roles", "Children": [{"Type": "File", "Name": "page.tsx", "Path": "./client/src/app/admin/roles/page.tsx", "comments": "Role Management Page"}]}]}, {"Type": "File", "Name": "layout.tsx", "Path": "./client/src/app/layout.tsx"}, {"Type": "Folder", "Name": "login", "Path": "./client/src/app/login", "Children": [{"Type": "File", "Name": "page.tsx", "Path": "./client/src/app/login/page.tsx", "comments": "Login Page Route"}]}, {"Type": "File", "Name": "page.tsx", "Path": "./client/src/app/page.tsx"}, {"Type": "File", "Name": "providers.tsx", "Path": "./client/src/app/providers.tsx"}, {"Type": "Folder", "Name": "register", "Path": "./client/src/app/register", "Children": [{"Type": "File", "Name": "page.tsx", "Path": "./client/src/app/register/page.tsx", "comments": "Registration Page Route"}]}]}, {"Type": "Folder", "Name": "assets", "Path": "./client/src/assets", "Children": []}, {"Type": "Folder", "Name": "components", "Path": "./client/src/components", "Children": [{"Type": "Folder", "Name": "atoms", "Path": "./client/src/components/atoms", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/atoms/__tests__", "Children": [{"Type": "File", "Name": "button.auth.test.tsx", "Path": "./client/src/components/atoms/__tests__/button.auth.test.tsx", "comments": "@file Auth-specific Button component tests"}, {"Type": "File", "Name": "icon.auth.test.tsx", "Path": "./client/src/components/atoms/__tests__/icon.auth.test.tsx", "comments": "@file Auth-specific Icon component tests"}, {"Type": "File", "Name": "input.auth.test.tsx", "Path": "./client/src/components/atoms/__tests__/input.auth.test.tsx", "comments": "@file Auth-specific Input component tests"}, {"Type": "File", "Name": "label.auth.test.tsx", "Path": "./client/src/components/atoms/__tests__/label.auth.test.tsx", "comments": "@file Auth-specific Label component tests"}]}, {"Type": "File", "Name": "badge-alt.tsx", "Path": "./client/src/components/atoms/badge-alt.tsx"}, {"Type": "File", "Name": "badge.tsx", "Path": "./client/src/components/atoms/badge.tsx", "comments": "Unified Badge System - UI Primitives Layer"}, {"Type": "File", "Name": "button.tsx", "Path": "./client/src/components/atoms/button.tsx"}, {"Type": "File", "Name": "checkbox.tsx", "Path": "./client/src/components/atoms/checkbox.tsx"}, {"Type": "File", "Name": "form.tsx", "Path": "./client/src/components/atoms/form.tsx", "comments": "Unified Form System - UI Primitives Layer"}, {"Type": "File", "Name": "icon.tsx", "Path": "./client/src/components/atoms/icon.tsx", "comments": "Unified Icon System - UI Primitives Layer"}, {"Type": "File", "Name": "input.tsx", "Path": "./client/src/components/atoms/input.tsx"}, {"Type": "File", "Name": "label.tsx", "Path": "./client/src/components/atoms/label.tsx"}, {"Type": "File", "Name": "progress.tsx", "Path": "./client/src/components/atoms/progress.tsx"}, {"Type": "File", "Name": "radio-group.tsx", "Path": "./client/src/components/atoms/radio-group.tsx"}, {"Type": "File", "Name": "separator.tsx", "Path": "./client/src/components/atoms/separator.tsx"}, {"Type": "File", "Name": "skeleton.tsx", "Path": "./client/src/components/atoms/skeleton.tsx"}, {"Type": "File", "Name": "slider.tsx", "Path": "./client/src/components/atoms/slider.tsx"}, {"Type": "File", "Name": "sonner.tsx", "Path": "./client/src/components/atoms/sonner.tsx"}, {"Type": "File", "Name": "state.tsx", "Path": "./client/src/components/atoms/state.tsx", "comments": "Unified State Components - UI Primitives Layer"}, {"Type": "File", "Name": "switch.tsx", "Path": "./client/src/components/atoms/switch.tsx"}, {"Type": "File", "Name": "textarea.tsx", "Path": "./client/src/components/atoms/textarea.tsx"}, {"Type": "File", "Name": "timeline.tsx", "Path": "./client/src/components/atoms/timeline.tsx"}, {"Type": "File", "Name": "toggle.tsx", "Path": "./client/src/components/atoms/toggle.tsx"}]}, {"Type": "Folder", "Name": "modules", "Path": "./client/src/components/modules", "Children": [{"Type": "Folder", "Name": "auth", "Path": "./client/src/components/modules/auth", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/modules/auth/__tests__", "Children": [{"Type": "File", "Name": "types.test.ts", "Path": "./client/src/components/modules/auth/__tests__/types.test.ts", "comments": "@file Auth types test"}]}, {"Type": "Folder", "Name": "RoleManagement", "Path": "./client/src/components/modules/auth/RoleManagement", "Children": [{"Type": "File", "Name": "index.tsx", "Path": "./client/src/components/modules/auth/RoleManagement/index.tsx", "comments": "Role Management Component"}]}, {"Type": "File", "Name": "RouteGuard.tsx", "Path": "./client/src/components/modules/auth/RouteGuard.tsx"}, {"Type": "File", "Name": "types.ts", "Path": "./client/src/components/modules/auth/types.ts", "comments": "Authentication and user management types"}]}, {"Type": "Folder", "Name": "navigation", "Path": "./client/src/components/modules/navigation", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/modules/navigation/__tests__", "Children": [{"Type": "File", "Name": "app-sidebar.test.tsx", "Path": "./client/src/components/modules/navigation/__tests__/app-sidebar.test.tsx"}, {"Type": "File", "Name": "types.test.ts", "Path": "./client/src/components/modules/navigation/__tests__/types.test.ts", "comments": "@file Navigation types test"}]}, {"Type": "File", "Name": "app-sidebar.tsx", "Path": "./client/src/components/modules/navigation/app-sidebar.tsx"}, {"Type": "File", "Name": "nav-items.ts", "Path": "./client/src/components/modules/navigation/nav-items.ts"}, {"Type": "File", "Name": "nav-list-item.tsx", "Path": "./client/src/components/modules/navigation/nav-list-item.tsx"}, {"Type": "File", "Name": "nav-user.tsx", "Path": "./client/src/components/modules/navigation/nav-user.tsx"}, {"Type": "File", "Name": "types.ts", "Path": "./client/src/components/modules/navigation/types.ts"}]}]}, {"Type": "Folder", "Name": "molecules", "Path": "./client/src/components/molecules", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/molecules/__tests__", "Children": [{"Type": "File", "Name": "input-field.test.tsx", "Path": "./client/src/components/molecules/__tests__/input-field.test.tsx", "comments": "@file InputField molecule tests"}, {"Type": "File", "Name": "password-input.test.tsx", "Path": "./client/src/components/molecules/__tests__/password-input.test.tsx", "comments": "@file PasswordInput molecule tests"}]}, {"Type": "File", "Name": "accordion.tsx", "Path": "./client/src/components/molecules/accordion.tsx"}, {"Type": "File", "Name": "alert.tsx", "Path": "./client/src/components/molecules/alert.tsx"}, {"Type": "File", "Name": "avatar.tsx", "Path": "./client/src/components/molecules/avatar.tsx"}, {"Type": "File", "Name": "breadcrumb.tsx", "Path": "./client/src/components/molecules/breadcrumb.tsx"}, {"Type": "File", "Name": "card.tsx", "Path": "./client/src/components/molecules/card.tsx"}, {"Type": "File", "Name": "collapsible.tsx", "Path": "./client/src/components/molecules/collapsible.tsx"}, {"Type": "File", "Name": "form.tsx", "Path": "./client/src/components/molecules/form.tsx", "comments": "Form UI Components for react-hook-form integration"}, {"Type": "File", "Name": "hover-card.tsx", "Path": "./client/src/components/molecules/hover-card.tsx"}, {"Type": "File", "Name": "input-field.tsx", "Path": "./client/src/components/molecules/input-field.tsx", "comments": "@file InputField molecule component"}, {"Type": "File", "Name": "password-input.tsx", "Path": "./client/src/components/molecules/password-input.tsx", "comments": "@file PasswordInput molecule component"}, {"Type": "File", "Name": "popover.tsx", "Path": "./client/src/components/molecules/popover.tsx"}, {"Type": "File", "Name": "select-native.tsx", "Path": "./client/src/components/molecules/select-native.tsx"}, {"Type": "File", "Name": "select.tsx", "Path": "./client/src/components/molecules/select.tsx"}, {"Type": "File", "Name": "table.tsx", "Path": "./client/src/components/molecules/table.tsx"}, {"Type": "File", "Name": "toast.tsx", "Path": "./client/src/components/molecules/toast.tsx"}, {"Type": "File", "Name": "toaster.tsx", "Path": "./client/src/components/molecules/toaster.tsx"}, {"Type": "File", "Name": "toggle-group.tsx", "Path": "./client/src/components/molecules/toggle-group.tsx"}, {"Type": "File", "Name": "tooltip.tsx", "Path": "./client/src/components/molecules/tooltip.tsx"}]}, {"Type": "Folder", "Name": "organisms", "Path": "./client/src/components/organisms", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/organisms/__tests__", "Children": [{"Type": "File", "Name": "login-form.test.tsx", "Path": "./client/src/components/organisms/__tests__/login-form.test.tsx", "comments": "@file LoginForm organism tests"}, {"Type": "File", "Name": "registration-form.test.tsx", "Path": "./client/src/components/organisms/__tests__/registration-form.test.tsx", "comments": "@file RegistrationForm organism tests"}]}, {"Type": "File", "Name": "alert-dialog.tsx", "Path": "./client/src/components/organisms/alert-dialog.tsx"}, {"Type": "File", "Name": "calendar-rac.tsx", "Path": "./client/src/components/organisms/calendar-rac.tsx"}, {"Type": "File", "Name": "calendar.tsx", "Path": "./client/src/components/organisms/calendar.tsx"}, {"Type": "File", "Name": "command.tsx", "Path": "./client/src/components/organisms/command.tsx"}, {"Type": "File", "Name": "datefield-rac.tsx", "Path": "./client/src/components/organisms/datefield-rac.tsx"}, {"Type": "File", "Name": "dialog.tsx", "Path": "./client/src/components/organisms/dialog.tsx"}, {"Type": "File", "Name": "dropdown-menu.tsx", "Path": "./client/src/components/organisms/dropdown-menu.tsx"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/components/organisms/index.ts", "comments": "@file Organism components index"}, {"Type": "File", "Name": "login-form.tsx", "Path": "./client/src/components/organisms/login-form.tsx", "comments": "@file LoginForm organism component"}, {"Type": "File", "Name": "multiselect.tsx", "Path": "./client/src/components/organisms/multiselect.tsx", "comments": "fixed option that can't be removed."}, {"Type": "File", "Name": "navigation-menu.tsx", "Path": "./client/src/components/organisms/navigation-menu.tsx"}, {"Type": "File", "Name": "pagination.tsx", "Path": "./client/src/components/organisms/pagination.tsx"}, {"Type": "File", "Name": "registration-form.tsx", "Path": "./client/src/components/organisms/registration-form.tsx", "comments": "@file RegistrationForm organism component"}, {"Type": "File", "Name": "sheet.tsx", "Path": "./client/src/components/organisms/sheet.tsx"}, {"Type": "File", "Name": "sidebar.tsx", "Path": "./client/src/components/organisms/sidebar.tsx"}, {"Type": "File", "Name": "stepper.tsx", "Path": "./client/src/components/organisms/stepper.tsx"}, {"Type": "File", "Name": "tree.tsx", "Path": "./client/src/components/organisms/tree.tsx"}]}, {"Type": "Folder", "Name": "templates", "Path": "./client/src/components/templates", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/components/templates/__tests__", "Children": [{"Type": "File", "Name": "auth-layout.test.tsx", "Path": "./client/src/components/templates/__tests__/auth-layout.test.tsx", "comments": "@file AuthLayout template tests"}, {"Type": "File", "Name": "login-page.test.tsx", "Path": "./client/src/components/templates/__tests__/login-page.test.tsx", "comments": "@file LoginPage template tests"}, {"Type": "File", "Name": "register-page.test.tsx", "Path": "./client/src/components/templates/__tests__/register-page.test.tsx", "comments": "@file RegisterPage template tests"}]}, {"Type": "File", "Name": "auth-layout.tsx", "Path": "./client/src/components/templates/auth-layout.tsx", "comments": "@file AuthLayout template component"}, {"Type": "File", "Name": "checkbox-tree.tsx", "Path": "./client/src/components/templates/checkbox-tree.tsx", "comments": "IMPORTANT: This component was built for demo purposes only and has not been tested in production."}, {"Type": "File", "Name": "cropper.tsx", "Path": "./client/src/components/templates/cropper.tsx"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/components/templates/index.ts", "comments": "@file Template components index"}, {"Type": "File", "Name": "login-page.tsx", "Path": "./client/src/components/templates/login-page.tsx", "comments": "@file LoginPage template component"}, {"Type": "File", "Name": "register-page.tsx", "Path": "./client/src/components/templates/register-page.tsx", "comments": "@file RegisterPage template component"}, {"Type": "File", "Name": "resizable.tsx", "Path": "./client/src/components/templates/resizable.tsx"}, {"Type": "File", "Name": "scroll-area.tsx", "Path": "./client/src/components/templates/scroll-area.tsx"}, {"Type": "File", "Name": "tabs.tsx", "Path": "./client/src/components/templates/tabs.tsx"}]}]}, {"Type": "Folder", "Name": "core", "Path": "./client/src/core", "Children": [{"Type": "Folder", "Name": "caching", "Path": "./client/src/core/caching", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/core/caching/__tests__", "Children": [{"Type": "File", "Name": "cache_provider.integration.test.tsx", "Path": "./client/src/core/caching/__tests__/cache_provider.integration.test.tsx", "comments": "Integration tests for CacheProvider with real browser persistence"}, {"Type": "File", "Name": "cache_provider.test.tsx", "Path": "./client/src/core/caching/__tests__/cache_provider.test.tsx", "comments": "Integration tests for CacheProvider with IndexedDBPersister"}, {"Type": "File", "Name": "cache_provider.validation.test.tsx", "Path": "./client/src/core/caching/__tests__/cache_provider.validation.test.tsx", "comments": "Validation tests for Work Batch 7.4 and 7.5 completion"}, {"Type": "File", "Name": "indexed_db_persister.integration.test.ts", "Path": "./client/src/core/caching/__tests__/indexed_db_persister.integration.test.ts", "comments": "Integration tests for IndexedDBPersister with mocked idb library"}, {"Type": "File", "Name": "indexed_db_persister.test.ts", "Path": "./client/src/core/caching/__tests__/indexed_db_persister.test.ts", "comments": "Unit tests for IndexedDBPersister"}, {"Type": "File", "Name": "indexed_db_persister.validation.test.ts", "Path": "./client/src/core/caching/__tests__/indexed_db_persister.validation.test.ts", "comments": "Validation tests for IndexedDBPersister Work Batch 7.1 completion"}, {"Type": "File", "Name": "sync_manager.test.ts", "Path": "./client/src/core/caching/__tests__/sync_manager.test.ts", "comments": "Unit tests for SyncManager"}, {"Type": "File", "Name": "sync_manager.validation.test.tsx", "Path": "./client/src/core/caching/__tests__/sync_manager.validation.test.tsx", "comments": "Validation tests for Work Batch 7.6 and 7.7 completion"}]}, {"Type": "File", "Name": "cache_provider.tsx", "Path": "./client/src/core/caching/cache_provider.tsx", "comments": "CacheProvider - React Query integration with IndexedDBPersister"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/core/caching/index.ts", "comments": "Core caching functionality for client-side persistence"}, {"Type": "File", "Name": "indexed_db_persister.ts", "Path": "./client/src/core/caching/indexed_db_persister.ts", "comments": "IndexedDBPersister - Custom persister for React Query using IndexedDB"}, {"Type": "File", "Name": "sync_manager.ts", "Path": "./client/src/core/caching/sync_manager.ts", "comments": "SyncManager - Offline mutation synchronization service"}]}]}, {"Type": "Folder", "Name": "hooks", "Path": "./client/src/hooks", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/hooks/__tests__", "Children": [{"Type": "File", "Name": "useHasPermission.test.tsx", "Path": "./client/src/hooks/__tests__/useHasPermission.test.tsx", "comments": "Comprehensive test suite for useHasPermission hook"}, {"Type": "File", "Name": "useOfflineMutation.test.tsx", "Path": "./client/src/hooks/__tests__/useOfflineMutation.test.tsx", "comments": "Unit tests for useOfflineMutation hook"}]}, {"Type": "Folder", "Name": "api", "Path": "./client/src/hooks/api", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/hooks/api/__tests__", "Children": [{"Type": "File", "Name": "useAuth.test.tsx", "Path": "./client/src/hooks/api/__tests__/useAuth.test.tsx"}, {"Type": "File", "Name": "useAuthorization.test.tsx", "Path": "./client/src/hooks/api/__tests__/useAuthorization.test.tsx", "comments": "Comprehensive test suite for Authorization hooks"}, {"Type": "File", "Name": "useRegister.test.tsx", "Path": "./client/src/hooks/api/__tests__/useRegister.test.tsx", "comments": "@file useRegister hook tests"}]}, {"Type": "File", "Name": "useAuth.ts", "Path": "./client/src/hooks/api/useAuth.ts", "comments": "React Query hooks for authentication"}, {"Type": "File", "Name": "useAuthorization.ts", "Path": "./client/src/hooks/api/useAuthorization.ts", "comments": "React Query hooks for authorization (RBAC)"}, {"Type": "File", "Name": "useComponentCategories.ts", "Path": "./client/src/hooks/api/useComponentCategories.ts", "comments": "Component Category Management React Query Hooks"}, {"Type": "File", "Name": "useComponents.ts", "Path": "./client/src/hooks/api/useComponents.ts", "comments": "Component Management React Query Hooks"}, {"Type": "File", "Name": "useComponentTypes.ts", "Path": "./client/src/hooks/api/useComponentTypes.ts", "comments": "Component Type Management React Query Hooks"}, {"Type": "File", "Name": "useProjects.ts", "Path": "./client/src/hooks/api/useProjects.ts", "comments": "Project Management React Query Hooks"}, {"Type": "File", "Name": "useTasks.ts", "Path": "./client/src/hooks/api/useTasks.ts", "comments": "Task Management React Query Hooks"}, {"Type": "File", "Name": "useUsers.ts", "Path": "./client/src/hooks/api/useUsers.ts", "comments": "User Management React Query Hooks"}]}, {"Type": "File", "Name": "useAuth.ts", "Path": "./client/src/hooks/useAuth.ts", "comments": "Compatibility wrapper hook for tests that still import '@/hooks/useAuth'"}, {"Type": "File", "Name": "useCharacterLimit.ts", "Path": "./client/src/hooks/useCharacterLimit.ts"}, {"Type": "File", "Name": "useCopyToClipboard.ts", "Path": "./client/src/hooks/useCopyToClipboard.ts"}, {"Type": "File", "Name": "useFileUpload.ts", "Path": "./client/src/hooks/useFileUpload.ts"}, {"Type": "File", "Name": "useHasPermission.ts", "Path": "./client/src/hooks/useHasPermission.ts", "comments": "Custom hook for permission-based access control"}, {"Type": "File", "Name": "useLayout.tsx", "Path": "./client/src/hooks/useLayout.tsx"}, {"Type": "File", "Name": "useOfflineMutation.ts", "Path": "./client/src/hooks/useOfflineMutation.ts", "comments": "useOfflineMutation Hook - Offline-first mutation handling"}, {"Type": "File", "Name": "usePagination.ts", "Path": "./client/src/hooks/usePagination.ts"}, {"Type": "File", "Name": "useSliderWithInput.ts", "Path": "./client/src/hooks/useSliderWithInput.ts"}, {"Type": "File", "Name": "useToast.ts", "Path": "./client/src/hooks/useToast.ts"}]}, {"Type": "Folder", "Name": "lib", "Path": "./client/src/lib", "Children": [{"Type": "Folder", "Name": "api", "Path": "./client/src/lib/api", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/lib/api/__tests__", "Children": [{"Type": "File", "Name": "client.zod.test.ts", "Path": "./client/src/lib/api/__tests__/client.zod.test.ts", "comments": "API Client Zod Validation - TDD"}]}, {"Type": "File", "Name": "audit.ts", "Path": "./client/src/lib/api/audit.ts", "comments": "Audit API client"}, {"Type": "File", "Name": "client.ts", "Path": "./client/src/lib/api/client.ts", "comments": "API Client for Ultimate Electrical Designer"}, {"Type": "Folder", "Name": "endpoints", "Path": "./client/src/lib/api/endpoints", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/lib/api/endpoints/__tests__", "Children": [{"Type": "File", "Name": "endpoints.test.ts", "Path": "./client/src/lib/api/endpoints/__tests__/endpoints.test.ts", "comments": "API Endpoints Tests"}, {"Type": "File", "Name": "endpoints.zod.test.ts", "Path": "./client/src/lib/api/endpoints/__tests__/endpoints.zod.test.ts", "comments": "API Endpoints - Zod validation tests (TDD)"}]}, {"Type": "File", "Name": "auth.ts", "Path": "./client/src/lib/api/endpoints/auth.ts", "comments": "Authentication API Endpoints"}, {"Type": "File", "Name": "authorization.ts", "Path": "./client/src/lib/api/endpoints/authorization.ts", "comments": "Authorization API endpoints"}, {"Type": "File", "Name": "component-categories.ts", "Path": "./client/src/lib/api/endpoints/component-categories.ts", "comments": "Component Category Management API Endpoints"}, {"Type": "File", "Name": "component-types.ts", "Path": "./client/src/lib/api/endpoints/component-types.ts", "comments": "Component Type Management API Endpoints"}, {"Type": "File", "Name": "components.ts", "Path": "./client/src/lib/api/endpoints/components.ts", "comments": "Component Management API Endpoints"}, {"Type": "File", "Name": "health.ts", "Path": "./client/src/lib/api/endpoints/health.ts", "comments": "Health Check API Endpoints"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/lib/api/endpoints/index.ts", "comments": "API Endpoints Barrel Export"}, {"Type": "File", "Name": "projects.ts", "Path": "./client/src/lib/api/endpoints/projects.ts", "comments": "Project Management API Endpoints"}, {"Type": "File", "Name": "tasks.ts", "Path": "./client/src/lib/api/endpoints/tasks.ts", "comments": "Task Management API Endpoints"}, {"Type": "File", "Name": "users.ts", "Path": "./client/src/lib/api/endpoints/users.ts", "comments": "User Management API Endpoints"}]}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/lib/api/index.ts", "comments": "API module exports"}, {"Type": "File", "Name": "keys.ts", "Path": "./client/src/lib/api/keys.ts", "comments": "React Query keys for consistent caching and invalidation"}, {"Type": "File", "Name": "rbac.ts", "Path": "./client/src/lib/api/rbac.ts", "comments": "RBAC API client"}, {"Type": "Folder", "Name": "types", "Path": "./client/src/lib/api/types", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/lib/api/types/__tests__", "Children": [{"Type": "File", "Name": "types.test.ts", "Path": "./client/src/lib/api/types/__tests__/types.test.ts", "comments": "Type Interface Tests"}]}, {"Type": "File", "Name": "auth.ts", "Path": "./client/src/lib/api/types/auth.ts", "comments": "Authentication and user management types"}, {"Type": "File", "Name": "authorization.ts", "Path": "./client/src/lib/api/types/authorization.ts", "comments": "Authorization and RBAC types"}, {"Type": "File", "Name": "common.ts", "Path": "./client/src/lib/api/types/common.ts", "comments": "Common API types and interfaces"}, {"Type": "File", "Name": "components.ts", "Path": "./client/src/lib/api/types/components.ts", "comments": "Component management types"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/lib/api/types/index.ts", "comments": "API Types Barrel Export"}, {"Type": "File", "Name": "projects.ts", "Path": "./client/src/lib/api/types/projects.ts", "comments": "Project management types"}, {"Type": "File", "Name": "tasks.ts", "Path": "./client/src/lib/api/types/tasks.ts", "comments": "Task management types"}, {"Type": "File", "Name": "users.ts", "Path": "./client/src/lib/api/types/users.ts", "comments": "User management types"}]}, {"Type": "File", "Name": "types.ts", "Path": "./client/src/lib/api/types.ts"}]}, {"Type": "Folder", "Name": "auth", "Path": "./client/src/lib/auth", "Children": [{"Type": "File", "Name": "tokenManager.ts", "Path": "./client/src/lib/auth/tokenManager.ts", "comments": "Lightweight TokenManager used by tests and API client integration points"}]}, {"Type": "File", "Name": "config.ts", "Path": "./client/src/lib/config.ts"}, {"Type": "File", "Name": "fonts.ts", "Path": "./client/src/lib/fonts.ts"}, {"Type": "Folder", "Name": "store", "Path": "./client/src/lib/store", "Children": [{"Type": "File", "Name": "index.ts", "Path": "./client/src/lib/store/index.ts"}]}, {"Type": "File", "Name": "utils.ts", "Path": "./client/src/lib/utils.ts"}, {"Type": "Folder", "Name": "validation", "Path": "./client/src/lib/validation", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/lib/validation/__tests__", "Children": [{"Type": "File", "Name": "auth.test.ts", "Path": "./client/src/lib/validation/__tests__/auth.test.ts", "comments": "@file Auth validation test"}]}, {"Type": "Folder", "Name": "api", "Path": "./client/src/lib/validation/api", "Children": [{"Type": "File", "Name": "auth.ts", "Path": "./client/src/lib/validation/api/auth.ts", "comments": "Zod schemas for Auth API request/response types"}]}, {"Type": "File", "Name": "auth.ts", "Path": "./client/src/lib/validation/auth.ts", "comments": "Authentication form validation utilities"}, {"Type": "File", "Name": "base.ts", "Path": "./client/src/lib/validation/base.ts", "comments": "Zod base schemas and helpers"}, {"Type": "File", "Name": "forms.ts", "Path": "./client/src/lib/validation/forms.ts"}, {"Type": "File", "Name": "index.ts", "Path": "./client/src/lib/validation/index.ts"}]}, {"Type": "Folder", "Name": "websocket", "Path": "./client/src/lib/websocket", "Children": []}]}, {"Type": "Folder", "Name": "providers", "Path": "./client/src/providers", "Children": [{"Type": "File", "Name": "query-provider.tsx", "Path": "./client/src/providers/query-provider.tsx"}, {"Type": "File", "Name": "theme-provider.tsx", "Path": "./client/src/providers/theme-provider.tsx"}]}, {"Type": "Folder", "Name": "stores", "Path": "./client/src/stores", "Children": [{"Type": "Folder", "Name": "__tests__", "Path": "./client/src/stores/__tests__", "Children": [{"Type": "File", "Name": "networkStatusStore.test.ts", "Path": "./client/src/stores/__tests__/networkStatusStore.test.ts", "comments": "Unit tests for Network Status Store"}]}, {"Type": "File", "Name": "authStore.ts", "Path": "./client/src/stores/authStore.ts", "comments": "Zustand store for authentication state management"}, {"Type": "File", "Name": "networkStatusStore.ts", "Path": "./client/src/stores/networkStatusStore.ts", "comments": "Network Status Store - Global online/offline state management"}]}, {"Type": "Folder", "Name": "styles", "Path": "./client/src/styles", "Children": []}, {"Type": "Folder", "Name": "test", "Path": "./client/src/test", "Children": [{"Type": "Folder", "Name": "factories", "Path": "./client/src/test/factories", "Children": []}, {"Type": "Folder", "Name": "integration", "Path": "./client/src/test/integration", "Children": [{"Type": "File", "Name": "auth-integration.test.tsx", "Path": "./client/src/test/integration/auth-integration.test.tsx", "comments": "Authentication Integration Tests"}, {"Type": "File", "Name": "offline-mode-integration.test.tsx", "Path": "./client/src/test/integration/offline-mode-integration.test.tsx", "comments": "Offline Mode Integration Tests"}, {"Type": "File", "Name": "rbac-workflow.test.tsx", "Path": "./client/src/test/integration/rbac-workflow.test.tsx", "comments": "Integration tests for RBAC workflow"}]}, {"Type": "Folder", "Name": "reporters", "Path": "./client/src/test/reporters", "Children": [{"Type": "File", "Name": "domain-reporter.ts", "Path": "./client/src/test/reporters/domain-reporter.ts"}]}, {"Type": "File", "Name": "setup.ts", "Path": "./client/src/test/setup.ts"}, {"Type": "File", "Name": "utils.tsx", "Path": "./client/src/test/utils.tsx"}]}, {"Type": "Folder", "Name": "utils", "Path": "./client/src/utils", "Children": [{"Type": "File", "Name": "active-theme.tsx", "Path": "./client/src/utils/active-theme.tsx"}, {"Type": "File", "Name": "tailwind-indicator.tsx", "Path": "./client/src/utils/tailwind-indicator.tsx"}, {"Type": "File", "Name": "textUtils.ts", "Path": "./client/src/utils/textUtils.ts", "comments": "@param val Text"}]}]}, {"Type": "File", "Name": "tailwind.config.ts", "Path": "./client/tailwind.config.ts", "comments": "@type {import('tailwindcss').Config}"}, {"Type": "Folder", "Name": "tests", "Path": "./client/tests", "Children": [{"Type": "Folder", "Name": "e2e", "Path": "./client/tests/e2e", "Children": [{"Type": "File", "Name": "auth-login.spec.ts", "Path": "./client/tests/e2e/auth-login.spec.ts", "comments": "Playwright E2E Tests for Login Flow"}, {"Type": "File", "Name": "auth-register.spec.ts", "Path": "./client/tests/e2e/auth-register.spec.ts", "comments": "Playwright E2E Tests for Registration Flow"}, {"Type": "File", "Name": "cache-persistence.spec.ts", "Path": "./client/tests/e2e/cache-persistence.spec.ts", "comments": "End-to-end tests for cache persistence across page reloads"}, {"Type": "File", "Name": "component-management.spec.ts", "Path": "./client/tests/e2e/component-management.spec.ts", "comments": "Component Management E2E Tests"}, {"Type": "File", "Name": "global.setup.ts", "Path": "./client/tests/e2e/global.setup.ts", "comments": "Playwright Global Setup"}, {"Type": "File", "Name": "offline-mode.spec.ts", "Path": "./client/tests/e2e/offline-mode.spec.ts", "comments": "E2E tests for Offline Mode workflow"}, {"Type": "File", "Name": "rbac-audit-workflow.spec.ts", "Path": "./client/tests/e2e/rbac-audit-workflow.spec.ts", "comments": "E2E tests for RBAC and Audit Trail workflows"}]}, {"Type": "Folder", "Name": "mocks", "Path": "./client/tests/mocks", "Children": [{"Type": "File", "Name": "browser.ts", "Path": "./client/tests/mocks/browser.ts", "comments": "MSW Browser Worker Setup for Playwright E2E Tests"}, {"Type": "Folder", "Name": "fixtures", "Path": "./client/tests/mocks/fixtures", "Children": [{"Type": "File", "Name": "auth.ts", "Path": "./client/tests/mocks/fixtures/auth.ts", "comments": "Authentication Fixtures for MSW Testing"}, {"Type": "File", "Name": "componentCategories.ts", "Path": "./client/tests/mocks/fixtures/componentCategories.ts", "comments": "Component Category Fixtures for MSW"}, {"Type": "File", "Name": "components.ts", "Path": "./client/tests/mocks/fixtures/components.ts", "comments": "Test fixtures for component management"}, {"Type": "File", "Name": "componentTypes.ts", "Path": "./client/tests/mocks/fixtures/componentTypes.ts", "comments": "Component Type Fixtures for MSW Testing"}]}, {"Type": "Folder", "Name": "handlers", "Path": "./client/tests/mocks/handlers", "Children": [{"Type": "File", "Name": "auth.ts", "Path": "./client/tests/mocks/handlers/auth.ts", "comments": "Authentication API Handlers for MSW"}, {"Type": "File", "Name": "componentCategories.ts", "Path": "./client/tests/mocks/handlers/componentCategories.ts", "comments": "Component Category API Handlers for MSW"}, {"Type": "File", "Name": "components.ts", "Path": "./client/tests/mocks/handlers/components.ts", "comments": "MSW handlers for component API endpoints"}, {"Type": "File", "Name": "componentTypes.ts", "Path": "./client/tests/mocks/handlers/componentTypes.ts", "comments": "Component Type API Handlers for MSW"}, {"Type": "File", "Name": "health.ts", "Path": "./client/tests/mocks/handlers/health.ts", "comments": "Health Check API Handlers for MSW"}, {"Type": "File", "Name": "projects.ts", "Path": "./client/tests/mocks/handlers/projects.ts", "comments": "MSW handlers for project API endpoints"}, {"Type": "File", "Name": "users.ts", "Path": "./client/tests/mocks/handlers/users.ts", "comments": "User Management API Handlers for MSW"}]}, {"Type": "File", "Name": "server.ts", "Path": "./client/tests/mocks/server.ts", "comments": "Mock Service Worker (MSW) Server Setup for Playwright E2E Tests"}]}]}, {"Type": "File", "Name": "vitest.config.ts", "Path": "./client/vitest.config.ts"}, {"Type": "File", "Name": "vitest.setup.ts", "Path": "./client/vitest.setup.ts"}, {"Type": "Folder", "Name": "~", "Path": "./client/~", "Children": []}]}, {"Type": "Folder", "Name": "computation-engine-service", "Path": "./computation-engine-service", "Children": [{"Type": "File", "Name": "Dockerfile", "Path": "./computation-engine-service/Dockerfile"}, {"Type": "File", "Name": "README.md", "Path": "./computation-engine-service/README.md"}, {"Type": "Folder", "Name": "src", "Path": "./computation-engine-service/src", "Children": [{"Type": "Folder", "Name": "Controllers", "Path": "./computation-engine-service/src/Controllers", "Children": [{"Type": "File", "Name": "ComputationController.cs", "Path": "./computation-engine-service/src/Controllers/ComputationController.cs"}]}, {"Type": "Folder", "Name": "Services", "Path": "./computation-engine-service/src/Services", "Children": [{"Type": "File", "Name": "PowerFlowSolver.cs", "Path": "./computation-engine-service/src/Services/PowerFlowSolver.cs"}]}, {"Type": "File", "Name": "ultimate_electrical_designer.ComputationEngine.csproj", "Path": "./computation-engine-service/src/ultimate_electrical_designer.ComputationEngine.csproj"}]}]}, {"Type": "Folder", "Name": "docs", "Path": "./docs", "Children": [{"Type": "File", "Name": "codebase-structure.md", "Path": "./docs/codebase-structure.md"}, {"Type": "Folder", "Name": "deployment", "Path": "./docs/deployment", "Children": [{"Type": "File", "Name": "POSTGRESQL_INSTALLER_PLAN.md", "Path": "./docs/deployment/POSTGRESQL_INSTALLER_PLAN.md"}]}, {"Type": "File", "Name": "design.md", "Path": "./docs/design.md"}, {"Type": "Folder", "Name": "developer-guides", "Path": "./docs/developer-guides", "Children": [{"Type": "Folder", "Name": "client", "Path": "./docs/developer-guides/client", "Children": [{"Type": "File", "Name": "atomid-design-guide.md", "Path": "./docs/developer-guides/client/atomid-design-guide.md"}, {"Type": "File", "Name": "cache-design-guide.md", "Path": "./docs/developer-guides/client/cache-design-guide.md"}]}, {"Type": "Folder", "Name": "server", "Path": "./docs/developer-guides/server", "Children": [{"Type": "File", "Name": "synchronization-developer-guide.md", "Path": "./docs/developer-guides/server/synchronization-developer-guide.md"}]}]}, {"Type": "Folder", "Name": "personal", "Path": "./docs/personal", "Children": [{"Type": "Folder", "Name": "agents", "Path": "./docs/personal/agents", "Children": [{"Type": "File", "Name": "code.md", "Path": "./docs/personal/agents/code.md"}, {"Type": "File", "Name": "framework.md", "Path": "./docs/personal/agents/framework.md"}, {"Type": "File", "Name": "orchestrator.md", "Path": "./docs/personal/agents/orchestrator.md"}, {"Type": "File", "Name": "quality.md", "Path": "./docs/personal/agents/quality.md"}, {"Type": "File", "Name": "task-planner.md", "Path": "./docs/personal/agents/task-planner.md"}, {"Type": "File", "Name": "technical-design.md", "Path": "./docs/personal/agents/technical-design.md"}, {"Type": "File", "Name": "template.md", "Path": "./docs/personal/agents/template.md"}]}, {"Type": "Folder", "Name": "templates", "Path": "./docs/personal/templates", "Children": [{"Type": "File", "Name": "fix-type-errors.md", "Path": "./docs/personal/templates/fix-type-errors.md"}]}]}, {"Type": "File", "Name": "product.md", "Path": "./docs/product.md"}, {"Type": "File", "Name": "README.md", "Path": "./docs/README.md"}, {"Type": "File", "Name": "requirements.md", "Path": "./docs/requirements.md"}, {"Type": "File", "Name": "rules.md", "Path": "./docs/rules.md"}, {"Type": "Folder", "Name": "tasks", "Path": "./docs/tasks", "Children": [{"Type": "Folder", "Name": "add-zod-package", "Path": "./docs/tasks/add-zod-package", "Children": [{"Type": "File", "Name": "IMPLEMENTATION.md", "Path": "./docs/tasks/add-zod-package/IMPLEMENTATION.md"}, {"Type": "File", "Name": "TASK_PLAN.md", "Path": "./docs/tasks/add-zod-package/TASK_PLAN.md"}, {"Type": "File", "Name": "TECH_DESIGN.md", "Path": "./docs/tasks/add-zod-package/TECH_DESIGN.md"}]}, {"Type": "Folder", "Name": "api-client", "Path": "./docs/tasks/api-client", "Children": [{"Type": "File", "Name": "1-IMPLEMENTATION_SUMMARY.md", "Path": "./docs/tasks/api-client/1-IMPLEMENTATION_SUMMARY.md"}, {"Type": "File", "Name": "2-VERIFICATION_REPORT.md", "Path": "./docs/tasks/api-client/2-VERIFICATION_REPORT.md"}, {"Type": "File", "Name": "3-REMEDIATION_SUMMARY.md", "Path": "./docs/tasks/api-client/3-REMEDIATION_SUMMARY.md"}]}, {"Type": "Folder", "Name": "authentication-system", "Path": "./docs/tasks/authentication-system", "Children": [{"Type": "File", "Name": "AUTHENTICATION_HANDOVER.md", "Path": "./docs/tasks/authentication-system/AUTHENTICATION_HANDOVER.md"}]}, {"Type": "Folder", "Name": "authorization-system-(FR-1.3)", "Path": "./docs/tasks/authorization-system-(FR-1.3)", "Children": [{"Type": "File", "Name": "AUTHORIZATION_HANDOVER.md", "Path": "./docs/tasks/authorization-system-(FR-1.3)/AUTHORIZATION_HANDOVER.md"}, {"Type": "File", "Name": "IMPLEMENTATION.md", "Path": "./docs/tasks/authorization-system-(FR-1.3)/IMPLEMENTATION.md"}, {"Type": "File", "Name": "TASK_PLAN.md", "Path": "./docs/tasks/authorization-system-(FR-1.3)/TASK_PLAN.md"}, {"Type": "File", "Name": "TECH_DESIGN.md", "Path": "./docs/tasks/authorization-system-(FR-1.3)/TECH_DESIGN.md"}]}]}, {"Type": "File", "Name": "tasks.md", "Path": "./docs/tasks.md"}, {"Type": "File", "Name": "tech.md", "Path": "./docs/tech.md"}, {"Type": "File", "Name": "TESTING.md", "Path": "./docs/TESTING.md"}, {"Type": "File", "Name": "workflows.md", "Path": "./docs/workflows.md"}]}, {"Type": "File", "Name": "README.md", "Path": "./README.md"}, {"Type": "Folder", "Name": "scripts", "Path": "./scripts", "Children": []}, {"Type": "Folder", "Name": "server", "Path": "./server", "Children": [{"Type": "File", "Name": ".env", "Path": "./server/.env"}, {"Type": "File", "Name": "conftest.py", "Path": "./server/conftest.py", "comments": "Pytest configuration."}, {"Type": "Folder", "Name": "data", "Path": "./server/data", "Children": [{"Type": "File", "Name": "seed_general.py", "Path": "./server/data/seed_general.py", "comments": "This script, `seed_general.py`, provides a comprehensive and idempotent mechanism"}]}, {"Type": "File", "Name": "Dockerfile", "Path": "./server/Dockerfile"}, {"Type": "Folder", "Name": "docs", "Path": "./server/docs", "Children": [{"Type": "Folder", "Name": "2025-07-19_test-database-integrity", "Path": "./server/docs/2025-07-19_test-database-integrity", "Children": [{"Type": "File", "Name": "01-test_database_integrity_report.md", "Path": "./server/docs/2025-07-19_test-database-integrity/01-test_database_integrity_report.md"}, {"Type": "File", "Name": "02-enhanced_verification_report.md", "Path": "./server/docs/2025-07-19_test-database-integrity/02-enhanced_verification_report.md"}, {"Type": "File", "Name": "03-test_coverage_immediate_actions.md", "Path": "./server/docs/2025-07-19_test-database-integrity/03-test_coverage_immediate_actions.md"}, {"Type": "File", "Name": "04-performance_testing_enhancement_summary.md", "Path": "./server/docs/2025-07-19_test-database-integrity/04-performance_testing_enhancement_summary.md"}, {"Type": "File", "Name": "05-database_operations_testing_comprehensive_summary.md", "Path": "./server/docs/2025-07-19_test-database-integrity/05-database_operations_testing_comprehensive_summary.md"}, {"Type": "File", "Name": "06-advanced_validation_compatibility.md", "Path": "./server/docs/2025-07-19_test-database-integrity/06-advanced_validation_compatibility.md"}]}, {"Type": "Folder", "Name": "2025-07-20_dual-database-setup-DEPRECATED", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED", "Children": [{"Type": "File", "Name": "discovery-analysis.md", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED/discovery-analysis.md"}, {"Type": "File", "Name": "dual-database-guide.md", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED/dual-database-guide.md"}, {"Type": "File", "Name": "implementation-report.md", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED/implementation-report.md"}, {"Type": "File", "Name": "plan.md", "Path": "./server/docs/2025-07-20_dual-database-setup-DEPRECATED/plan.md"}]}, {"Type": "Folder", "Name": "2025-07-21_offline-mode", "Path": "./server/docs/2025-07-21_offline-mode", "Children": [{"Type": "File", "Name": "discovery_analysis.md", "Path": "./server/docs/2025-07-21_offline-mode/discovery_analysis.md"}, {"Type": "File", "Name": "plan.md", "Path": "./server/docs/2025-07-21_offline-mode/plan.md"}]}, {"Type": "Folder", "Name": "2025-07-22_unified_local_database", "Path": "./server/docs/2025-07-22_unified_local_database", "Children": [{"Type": "File", "Name": "phase1_discovery_analysis.md", "Path": "./server/docs/2025-07-22_unified_local_database/phase1_discovery_analysis.md"}, {"Type": "File", "Name": "phase2_implementation_plan.md", "Path": "./server/docs/2025-07-22_unified_local_database/phase2_implementation_plan.md"}, {"Type": "File", "Name": "phase2_implementation_report.md", "Path": "./server/docs/2025-07-22_unified_local_database/phase2_implementation_report.md"}]}, {"Type": "Folder", "Name": "2025-07-29_comprehensive-test-verification", "Path": "./server/docs/2025-07-29_comprehensive-test-verification", "Children": [{"Type": "File", "Name": "phase4_comprehensive_verification_report.md", "Path": "./server/docs/2025-07-29_comprehensive-test-verification/phase4_comprehensive_verification_report.md"}, {"Type": "File", "Name": "systematic_issues_resolution_summary.md", "Path": "./server/docs/2025-07-29_comprehensive-test-verification/systematic_issues_resolution_summary.md"}]}]}, {"Type": "File", "Name": "pyproject.toml", "Path": "./server/pyproject.toml"}, {"Type": "File", "Name": "README.md", "Path": "./server/README.md"}, {"Type": "Folder", "Name": "src", "Path": "./server/src", "Children": [{"Type": "Folder", "Name": "alembic", "Path": "./server/src/alembic", "Children": []}, {"Type": "Folder", "Name": "api", "Path": "./server/src/api", "Children": [{"Type": "File", "Name": "main_router.py", "Path": "./server/src/api/main_router.py", "comments": "Main API Router Configuration."}, {"Type": "Folder", "Name": "v1", "Path": "./server/src/api/v1", "Children": [{"Type": "File", "Name": "auth_routes.py", "Path": "./server/src/api/v1/auth_routes.py", "comments": "Authentication API Routes."}, {"Type": "File", "Name": "authorization_routes.py", "Path": "./server/src/api/v1/authorization_routes.py", "comments": "Authorization API Routes."}, {"Type": "File", "Name": "component_category_routes.py", "Path": "./server/src/api/v1/component_category_routes.py", "comments": "Component Category API Routes."}, {"Type": "File", "Name": "component_routes.py", "Path": "./server/src/api/v1/component_routes.py", "comments": "Component API Routes."}, {"Type": "File", "Name": "component_type_routes.py", "Path": "./server/src/api/v1/component_type_routes.py", "comments": "Component Type API Routes."}, {"Type": "File", "Name": "cross_validation_routes.py", "Path": "./server/src/api/v1/cross_validation_routes.py", "comments": "Cross-entity validation API routes for advanced dependency checking."}, {"Type": "File", "Name": "health_routes.py", "Path": "./server/src/api/v1/health_routes.py", "comments": "Health Check API Routes."}, {"Type": "File", "Name": "parallel_validation_routes.py", "Path": "./server/src/api/v1/parallel_validation_routes.py", "comments": "Parallel Validation API Endpoints."}, {"Type": "File", "Name": "project_phase_routes.py", "Path": "./server/src/api/v1/project_phase_routes.py", "comments": "Project Phase and Milestone API endpoints."}, {"Type": "File", "Name": "project_routes.py", "Path": "./server/src/api/v1/project_routes.py", "comments": "Project and Project Member API endpoints."}, {"Type": "File", "Name": "router.py", "Path": "./server/src/api/v1/router.py", "comments": "API Version 1 Router Configuration."}, {"Type": "File", "Name": "system_configuration_routes.py", "Path": "./server/src/api/v1/system_configuration_routes.py", "comments": "System Configuration API endpoints."}, {"Type": "File", "Name": "task_routes.py", "Path": "./server/src/api/v1/task_routes.py", "comments": "Task Management API Routes."}, {"Type": "File", "Name": "user_preferences_routes.py", "Path": "./server/src/api/v1/user_preferences_routes.py", "comments": "User Preferences API Routes."}, {"Type": "File", "Name": "user_routes.py", "Path": "./server/src/api/v1/user_routes.py", "comments": "User Management API Routes."}, {"Type": "File", "Name": "validation_routes.py", "Path": "./server/src/api/v1/validation_routes.py", "comments": "WebSocket validation routes for real-time electrical parameter validation."}]}]}, {"Type": "File", "Name": "app.py", "Path": "./server/src/app.py", "comments": "Application Module."}, {"Type": "Folder", "Name": "config", "Path": "./server/src/config", "Children": [{"Type": "File", "Name": "logging_config.py", "Path": "./server/src/config/logging_config.py", "comments": "Logging Configuration."}, {"Type": "File", "Name": "settings.py", "Path": "./server/src/config/settings.py", "comments": "Application Configuration Settings."}]}, {"Type": "Folder", "Name": "core", "Path": "./server/src/core", "Children": [{"Type": "Folder", "Name": "auth", "Path": "./server/src/core/auth", "Children": [{"Type": "File", "Name": "dependencies.py", "Path": "./server/src/core/auth/dependencies.py", "comments": "Authentication Dependencies."}]}, {"Type": "Folder", "Name": "calculations", "Path": "./server/src/core/calculations", "Children": []}, {"Type": "Folder", "Name": "database", "Path": "./server/src/core/database", "Children": [{"Type": "File", "Name": "connection_manager.py", "Path": "./server/src/core/database/connection_manager.py", "comments": "Dynamic Database Connection Management."}, {"Type": "File", "Name": "dependencies.py", "Path": "./server/src/core/database/dependencies.py", "comments": "Database Dependencies."}, {"Type": "File", "Name": "engine.py", "Path": "./server/src/core/database/engine.py", "comments": "Database Engine Management."}, {"Type": "File", "Name": "initialization.py", "Path": "./server/src/core/database/initialization.py", "comments": "Database Initialization."}, {"Type": "File", "Name": "session.py", "Path": "./server/src/core/database/session.py", "comments": "Database Session Management."}]}, {"Type": "Folder", "Name": "enums", "Path": "./server/src/core/enums", "Children": [{"Type": "File", "Name": "calculation_enums.py", "Path": "./server/src/core/enums/calculation_enums.py", "comments": "Enumeration types for various engineering calculations"}, {"Type": "File", "Name": "common_enums.py", "Path": "./server/src/core/enums/common_enums.py", "comments": "Common, non-domain-specific enumeration types"}, {"Type": "File", "Name": "data_io_enums.py", "Path": "./server/src/core/enums/data_io_enums.py", "comments": "Enumeration types pertaining to data input/output"}, {"Type": "File", "Name": "electrical_enums.py", "Path": "./server/src/core/enums/electrical_enums.py", "comments": "Comprehensive enumeration types specific to electrical"}, {"Type": "File", "Name": "heat_tracing_enums.py", "Path": "./server/src/core/enums/heat_tracing_enums.py", "comments": "Enumeration types specifically tailored for heat tracing"}, {"Type": "File", "Name": "mechanical_enums.py", "Path": "./server/src/core/enums/mechanical_enums.py", "comments": "Enumeration types related to mechanical elements and"}, {"Type": "File", "Name": "project_management_enums.py", "Path": "./server/src/core/enums/project_management_enums.py", "comments": "Enumeration types essential for managing project"}, {"Type": "File", "Name": "standards_enums.py", "Path": "./server/src/core/enums/standards_enums.py", "comments": "Enumeration types related to engineering standards,"}, {"Type": "File", "Name": "system_enums.py", "Path": "./server/src/core/enums/system_enums.py", "comments": "Enumeration types for internal system operations,"}]}, {"Type": "Folder", "Name": "errors", "Path": "./server/src/core/errors", "Children": [{"Type": "File", "Name": "exceptions.py", "Path": "./server/src/core/errors/exceptions.py", "comments": "Custom Exception Classes."}, {"Type": "File", "Name": "unified_error_handler.py", "Path": "./server/src/core/errors/unified_error_handler.py", "comments": "Unified Error Handling System."}]}, {"Type": "Folder", "Name": "integrations", "Path": "./server/src/core/integrations", "Children": [{"Type": "File", "Name": "README.md", "Path": "./server/src/core/integrations/README.md"}]}, {"Type": "Folder", "Name": "models", "Path": "./server/src/core/models", "Children": [{"Type": "File", "Name": "base.py", "Path": "./server/src/core/models/base.py", "comments": "Base Model Classes and Common Database Patterns."}, {"Type": "Folder", "Name": "general", "Path": "./server/src/core/models/general", "Children": [{"Type": "File", "Name": "activity_log.py", "Path": "./server/src/core/models/general/activity_log.py", "comments": "Activity Log and Audit Trail Database Models."}, {"Type": "File", "Name": "component.py", "Path": "./server/src/core/models/general/component.py", "comments": "Component Database Model."}, {"Type": "File", "Name": "component_category.py", "Path": "./server/src/core/models/general/component_category.py", "comments": "Component Category Database Model."}, {"Type": "File", "Name": "component_type.py", "Path": "./server/src/core/models/general/component_type.py", "comments": "Component Type Database Model."}, {"Type": "File", "Name": "permission.py", "Path": "./server/src/core/models/general/permission.py", "comments": "Permission and Role-Permission Database Models."}, {"Type": "File", "Name": "project.py", "Path": "./server/src/core/models/general/project.py", "comments": "Project Database Model."}, {"Type": "File", "Name": "project_phase.py", "Path": "./server/src/core/models/general/project_phase.py", "comments": "Project Phase and Milestone Database Models."}, {"Type": "File", "Name": "synchronization_log.py", "Path": "./server/src/core/models/general/synchronization_log.py", "comments": "Synchronization Log Database Models."}, {"Type": "File", "Name": "system_configuration.py", "Path": "./server/src/core/models/general/system_configuration.py", "comments": "System Configuration Database Models."}, {"Type": "File", "Name": "task.py", "Path": "./server/src/core/models/general/task.py", "comments": "Task and TaskAssignment Database Models."}, {"Type": "File", "Name": "user.py", "Path": "./server/src/core/models/general/user.py", "comments": "User and User Preference Database Models."}, {"Type": "File", "Name": "user_role.py", "Path": "./server/src/core/models/general/user_role.py", "comments": "User Role and Role Assignment Database Models."}]}]}, {"Type": "Folder", "Name": "monitoring", "Path": "./server/src/core/monitoring", "Children": [{"Type": "File", "Name": "performance_monitor.py", "Path": "./server/src/core/monitoring/performance_monitor.py", "comments": "Performance Monitor Mo<PERSON>le"}, {"Type": "File", "Name": "unified_performance_monitor.py", "Path": "./server/src/core/monitoring/unified_performance_monitor.py", "comments": "Unified Performance Monitoring System."}]}, {"Type": "Folder", "Name": "repositories", "Path": "./server/src/core/repositories", "Children": [{"Type": "File", "Name": "base_repository.py", "Path": "./server/src/core/repositories/base_repository.py", "comments": "Base Repository."}, {"Type": "Folder", "Name": "general", "Path": "./server/src/core/repositories/general", "Children": [{"Type": "File", "Name": "component_category_repository.py", "Path": "./server/src/core/repositories/general/component_category_repository.py", "comments": "Component Category Repository."}, {"Type": "File", "Name": "component_repository.py", "Path": "./server/src/core/repositories/general/component_repository.py", "comments": "Component Repository."}, {"Type": "File", "Name": "component_type_repository.py", "Path": "./server/src/core/repositories/general/component_type_repository.py", "comments": "Component Type Repository."}, {"Type": "File", "Name": "permission_repository.py", "Path": "./server/src/core/repositories/general/permission_repository.py", "comments": "Permission Repository."}, {"Type": "File", "Name": "project_member_repository.py", "Path": "./server/src/core/repositories/general/project_member_repository.py", "comments": "Project Member Repository."}, {"Type": "File", "Name": "project_repository.py", "Path": "./server/src/core/repositories/general/project_repository.py", "comments": "Project Repository."}, {"Type": "File", "Name": "role_repository.py", "Path": "./server/src/core/repositories/general/role_repository.py", "comments": "Role Repository."}, {"Type": "File", "Name": "task_repository.py", "Path": "./server/src/core/repositories/general/task_repository.py", "comments": "Task Repository for database operations."}, {"Type": "File", "Name": "user_preference_repository.py", "Path": "./server/src/core/repositories/general/user_preference_repository.py", "comments": "User Preference Repository."}, {"Type": "File", "Name": "user_repository.py", "Path": "./server/src/core/repositories/general/user_repository.py", "comments": "User Repository."}]}, {"Type": "File", "Name": "repository_dependencies.py", "Path": "./server/src/core/repositories/repository_dependencies.py", "comments": "Repository Dependencies."}]}, {"Type": "Folder", "Name": "schemas", "Path": "./server/src/core/schemas", "Children": [{"Type": "File", "Name": "base_schemas.py", "Path": "./server/src/core/schemas/base_schemas.py", "comments": "Base schemas."}, {"Type": "File", "Name": "error.py", "Path": "./server/src/core/schemas/error.py", "comments": "Error Response Schema Definitions."}, {"Type": "Folder", "Name": "general", "Path": "./server/src/core/schemas/general", "Children": [{"Type": "File", "Name": "audit_trail_schemas.py", "Path": "./server/src/core/schemas/general/audit_trail_schemas.py", "comments": "Pydantic schemas for Activity Log and Audit Trail models."}, {"Type": "File", "Name": "component_category_schemas.py", "Path": "./server/src/core/schemas/general/component_category_schemas.py", "comments": "Component Category Schemas."}, {"Type": "File", "Name": "component_schemas.py", "Path": "./server/src/core/schemas/general/component_schemas.py", "comments": "Component Schemas."}, {"Type": "File", "Name": "component_type_schemas.py", "Path": "./server/src/core/schemas/general/component_type_schemas.py", "comments": "Component Type Schemas."}, {"Type": "File", "Name": "permission_schemas.py", "Path": "./server/src/core/schemas/general/permission_schemas.py", "comments": "Pydantic schemas for Permission and RolePermission models."}, {"Type": "File", "Name": "project_member_schemas.py", "Path": "./server/src/core/schemas/general/project_member_schemas.py", "comments": "Project Member schemas."}, {"Type": "File", "Name": "project_phase_schemas.py", "Path": "./server/src/core/schemas/general/project_phase_schemas.py", "comments": "Project Phase and Milestone schemas."}, {"Type": "File", "Name": "project_schemas.py", "Path": "./server/src/core/schemas/general/project_schemas.py", "comments": "Project schemas."}, {"Type": "File", "Name": "system_configuration_schemas.py", "Path": "./server/src/core/schemas/general/system_configuration_schemas.py", "comments": "System Configuration schemas."}, {"Type": "File", "Name": "task_schemas.py", "Path": "./server/src/core/schemas/general/task_schemas.py", "comments": "Task Management Schemas."}, {"Type": "File", "Name": "user_role_schemas.py", "Path": "./server/src/core/schemas/general/user_role_schemas.py", "comments": "Pydantic schemas for User Role and Role Assignment models."}, {"Type": "File", "Name": "user_schemas.py", "Path": "./server/src/core/schemas/general/user_schemas.py", "comments": "User schemas."}]}, {"Type": "File", "Name": "health.py", "Path": "./server/src/core/schemas/health.py", "comments": "Health Check Schemas."}]}, {"Type": "Folder", "Name": "security", "Path": "./server/src/core/security", "Children": [{"Type": "File", "Name": "enhanced_dependencies.py", "Path": "./server/src/core/security/enhanced_dependencies.py", "comments": "Enhanced Security Dependencies."}, {"Type": "File", "Name": "input_validators.py", "Path": "./server/src/core/security/input_validators.py", "comments": "Input Validation Module."}, {"Type": "File", "Name": "password_handler.py", "Path": "./server/src/core/security/password_handler.py", "comments": "Password handling with professional security practices."}, {"Type": "File", "Name": "unified_security_validator.py", "Path": "./server/src/core/security/unified_security_validator.py", "comments": "Unified Security Validation System."}]}, {"Type": "Folder", "Name": "services", "Path": "./server/src/core/services", "Children": [{"Type": "File", "Name": "dependencies.py", "Path": "./server/src/core/services/dependencies.py", "comments": "Service Dependencies."}, {"Type": "Folder", "Name": "general", "Path": "./server/src/core/services/general", "Children": [{"Type": "File", "Name": "audit_trail_service.py", "Path": "./server/src/core/services/general/audit_trail_service.py", "comments": "Generic Audit Trail Service for System-Wide Activity Logging."}, {"Type": "File", "Name": "authorization_service.py", "Path": "./server/src/core/services/general/authorization_service.py", "comments": "Authorization Service."}, {"Type": "File", "Name": "component_category_service.py", "Path": "./server/src/core/services/general/component_category_service.py", "comments": "Component Category Service."}, {"Type": "File", "Name": "component_service.py", "Path": "./server/src/core/services/general/component_service.py", "comments": "Component Service."}, {"Type": "File", "Name": "component_type_service.py", "Path": "./server/src/core/services/general/component_type_service.py", "comments": "Component Type Service."}, {"Type": "File", "Name": "health_service.py", "Path": "./server/src/core/services/general/health_service.py", "comments": "Health Check Service."}, {"Type": "File", "Name": "project_member_service.py", "Path": "./server/src/core/services/general/project_member_service.py", "comments": "Project Member Service."}, {"Type": "File", "Name": "project_phase_service.py", "Path": "./server/src/core/services/general/project_phase_service.py", "comments": "Project Phase Service Layer."}, {"Type": "File", "Name": "project_service.py", "Path": "./server/src/core/services/general/project_service.py", "comments": "Project Service Layer."}, {"Type": "File", "Name": "synchronization_service.py", "Path": "./server/src/core/services/general/synchronization_service.py", "comments": "Synchronization Service for Unified Local Database Management."}, {"Type": "File", "Name": "task_manager_service.py", "Path": "./server/src/core/services/general/task_manager_service.py", "comments": "Task Manager Service for business logic operations."}, {"Type": "File", "Name": "user_service.py", "Path": "./server/src/core/services/general/user_service.py", "comments": "User Service."}]}]}, {"Type": "Folder", "Name": "standards", "Path": "./server/src/core/standards", "Children": []}, {"Type": "Folder", "Name": "utils", "Path": "./server/src/core/utils", "Children": [{"Type": "File", "Name": "advanced_cache_manager.py", "Path": "./server/src/core/utils/advanced_cache_manager.py", "comments": "Advanced Cache Manager for Component Management System."}, {"Type": "File", "Name": "crud_endpoint_factory.py", "Path": "./server/src/core/utils/crud_endpoint_factory.py", "comments": "CRUD Endpoint Factory."}, {"Type": "File", "Name": "datetime_utils.py", "Path": "./server/src/core/utils/datetime_utils.py", "comments": "DateTime Utilities."}, {"Type": "File", "Name": "file_io_utils.py", "Path": "./server/src/core/utils/file_io_utils.py", "comments": "File I/O Utilities."}, {"Type": "File", "Name": "json_validation.py", "Path": "./server/src/core/utils/json_validation.py", "comments": "JSON Validation Utilities."}, {"Type": "File", "Name": "logger.py", "Path": "./server/src/core/utils/logger.py", "comments": "Logger Utility Module"}, {"Type": "File", "Name": "memory_manager.py", "Path": "./server/src/core/utils/memory_manager.py", "comments": "Memory Management Utilities."}, {"Type": "File", "Name": "pagination_utils.py", "Path": "./server/src/core/utils/pagination_utils.py", "comments": "Pagination Utilities."}, {"Type": "File", "Name": "performance_optimizer.py", "Path": "./server/src/core/utils/performance_optimizer.py", "comments": "Performance Optimization Utilities."}, {"Type": "File", "Name": "performance_utils.py", "Path": "./server/src/core/utils/performance_utils.py", "comments": "Performance optimization utilities for large dataset operations."}, {"Type": "File", "Name": "query_optimizer.py", "Path": "./server/src/core/utils/query_optimizer.py", "comments": "Database Query Optimizer for Component Management System."}, {"Type": "File", "Name": "query_utils.py", "Path": "./server/src/core/utils/query_utils.py", "comments": "Query Utilities."}, {"Type": "File", "Name": "search_query_builder.py", "Path": "./server/src/core/utils/search_query_builder.py", "comments": "Advanced Search Query Builder for Component Management."}, {"Type": "File", "Name": "security.py", "Path": "./server/src/core/utils/security.py", "comments": "Security utilities."}, {"Type": "File", "Name": "string_utils.py", "Path": "./server/src/core/utils/string_utils.py", "comments": "String Utilities."}, {"Type": "File", "Name": "uuid_utils.py", "Path": "./server/src/core/utils/uuid_utils.py", "comments": "UUID Utilities."}]}, {"Type": "Folder", "Name": "validation", "Path": "./server/src/core/validation", "Children": [{"Type": "File", "Name": "advanced_validators.py", "Path": "./server/src/core/validation/advanced_validators.py", "comments": "Advanced Validation Module."}, {"Type": "File", "Name": "compatibility_matrix.py", "Path": "./server/src/core/validation/compatibility_matrix.py", "comments": "Project-Component Compatibility Matrix."}, {"Type": "File", "Name": "constraint_validator.py", "Path": "./server/src/core/validation/constraint_validator.py", "comments": "Complex Constraint Validation System."}, {"Type": "File", "Name": "cross_entity_validator.py", "Path": "./server/src/core/validation/cross_entity_validator.py", "comments": "Cross-Entity Dependency Validation Engine."}, {"Type": "File", "Name": "data_format_validator.py", "Path": "./server/src/core/validation/data_format_validator.py", "comments": "Multi-format Data Compatibility Validation."}, {"Type": "File", "Name": "intelligent_caching.py", "Path": "./server/src/core/validation/intelligent_caching.py", "comments": "Intelligent Validation Caching System with Invalidation."}, {"Type": "File", "Name": "json_schema_validator.py", "Path": "./server/src/core/validation/json_schema_validator.py", "comments": "Advanced JSON Schema Validation with JSON Path Queries."}, {"Type": "File", "Name": "legacy_migration_validator.py", "Path": "./server/src/core/validation/legacy_migration_validator.py", "comments": "Legacy Data Format Migration Validation System."}, {"Type": "File", "Name": "parallel_processor.py", "Path": "./server/src/core/validation/parallel_processor.py", "comments": "Parallel Validation Processing System."}, {"Type": "File", "Name": "standards_validator.py", "Path": "./server/src/core/validation/standards_validator.py", "comments": "Dynamic Standards Compliance Validation System."}]}]}, {"Type": "File", "Name": "main.py", "Path": "./server/src/main.py", "comments": "Main entrypoint for the application."}, {"Type": "Folder", "Name": "middleware", "Path": "./server/src/middleware", "Children": [{"Type": "File", "Name": "caching_middleware.py", "Path": "./server/src/middleware/caching_middleware.py", "comments": "Caching Middleware."}, {"Type": "File", "Name": "context_middleware.py", "Path": "./server/src/middleware/context_middleware.py", "comments": "Context Middleware."}, {"Type": "File", "Name": "logging_middleware.py", "Path": "./server/src/middleware/logging_middleware.py", "comments": "Logging Middleware."}, {"Type": "File", "Name": "rate_limiting_middleware.py", "Path": "./server/src/middleware/rate_limiting_middleware.py", "comments": "Rate Limiting Middleware."}, {"Type": "File", "Name": "security_middleware.py", "Path": "./server/src/middleware/security_middleware.py", "comments": "Security Middleware."}]}, {"Type": "Folder", "Name": "~", "Path": "./server/src/~", "Children": []}]}, {"Type": "Folder", "Name": "tests", "Path": "./server/tests", "Children": [{"Type": "Folder", "Name": "api", "Path": "./server/tests/api", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/api/conftest.py", "comments": "Test fixtures for API layer tests."}, {"Type": "Folder", "Name": "v1", "Path": "./server/tests/api/v1", "Children": [{"Type": "File", "Name": "test_auth_routes.py", "Path": "./server/tests/api/v1/test_auth_routes.py", "comments": "Tests for authentication API endpoints."}, {"Type": "File", "Name": "test_authorization_routes.py", "Path": "./server/tests/api/v1/test_authorization_routes.py", "comments": "Comprehensive test suite for Authorization API routes"}, {"Type": "File", "Name": "test_component_category_routes.py", "Path": "./server/tests/api/v1/test_component_category_routes.py", "comments": "Tests for Component Category functionality."}, {"Type": "File", "Name": "test_component_routes.py", "Path": "./server/tests/api/v1/test_component_routes.py", "comments": "Component API Routes Tests."}, {"Type": "File", "Name": "test_component_type_routes.py", "Path": "./server/tests/api/v1/test_component_type_routes.py", "comments": "Tests for Component Type functionality."}, {"Type": "File", "Name": "test_health_routes.py", "Path": "./server/tests/api/v1/test_health_routes.py", "comments": "Tests for health check API endpoints."}, {"Type": "File", "Name": "test_project_routes.py", "Path": "./server/tests/api/v1/test_project_routes.py", "comments": "Tests for project management API endpoints."}, {"Type": "File", "Name": "test_task_routes.py", "Path": "./server/tests/api/v1/test_task_routes.py", "comments": "Integration tests for Task API endpoints."}, {"Type": "File", "Name": "test_user_routes.py", "Path": "./server/tests/api/v1/test_user_routes.py", "comments": "Tests for user management API endpoints."}]}]}, {"Type": "File", "Name": "conftest.py", "Path": "./server/tests/conftest.py", "comments": "Global test configuration."}, {"Type": "Folder", "Name": "core", "Path": "./server/tests/core", "Children": [{"Type": "Folder", "Name": "calculations", "Path": "./server/tests/core/calculations", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/core/calculations/conftest.py", "comments": "Fixtures for calculations layer tests."}]}, {"Type": "Folder", "Name": "config", "Path": "./server/tests/core/config", "Children": [{"Type": "File", "Name": "test_settings.py", "Path": "./server/tests/core/config/test_settings.py", "comments": "Test suite for application settings."}]}, {"Type": "Folder", "Name": "database", "Path": "./server/tests/core/database", "Children": [{"Type": "File", "Name": "test_alembic_migration_automation.py", "Path": "./server/tests/core/database/test_alembic_migration_automation.py", "comments": "Test suite for Alembic migration automation with data integrity validation."}, {"Type": "File", "Name": "test_connection_manager.py", "Path": "./server/tests/core/database/test_connection_manager.py", "comments": "Unit tests for DynamicConnectionManager."}, {"Type": "File", "Name": "test_connection_manager_integration.py", "Path": "./server/tests/core/database/test_connection_manager_integration.py", "comments": "Integration tests for DynamicConnectionManager with database connectivity."}, {"Type": "File", "Name": "test_migration_rollback_scenarios.py", "Path": "./server/tests/core/database/test_migration_rollback_scenarios.py", "comments": "Test suite for Alembic migration rollback scenarios."}, {"Type": "File", "Name": "test_project_database_routing.py", "Path": "./server/tests/core/database/test_project_database_routing.py", "comments": "Integration tests for project-specific database routing."}, {"Type": "File", "Name": "test_task_migration.py", "Path": "./server/tests/core/database/test_task_migration.py", "comments": "Test suite for Task and TaskAssignment migration."}]}, {"Type": "Folder", "Name": "errors", "Path": "./server/tests/core/errors", "Children": [{"Type": "File", "Name": "test_error_context.py", "Path": "./server/tests/core/errors/test_error_context.py", "comments": "Test suite for unified error handling system."}, {"Type": "File", "Name": "test_error_handler_decorators.py", "Path": "./server/tests/core/errors/test_error_handler_decorators.py", "comments": "Test suite for unified error handling system."}, {"Type": "File", "Name": "test_error_handling_result.py", "Path": "./server/tests/core/errors/test_error_handling_result.py", "comments": "Test suite for unified error handling system."}, {"Type": "File", "Name": "test_middleware_safe_error_handlers.py", "Path": "./server/tests/core/errors/test_middleware_safe_error_handlers.py", "comments": "Test suite for middleware-safe error handling system."}, {"Type": "File", "Name": "test_unified_error_handler.py", "Path": "./server/tests/core/errors/test_unified_error_handler.py", "comments": "Test suite for unified error handling system."}]}, {"Type": "Folder", "Name": "models", "Path": "./server/tests/core/models", "Children": [{"Type": "File", "Name": "test_component.py", "Path": "./server/tests/core/models/test_component.py", "comments": "Unit tests for Component model."}, {"Type": "File", "Name": "test_component_category.py", "Path": "./server/tests/core/models/test_component_category.py", "comments": "Unit tests for Component Category functionality."}, {"Type": "File", "Name": "test_component_relational.py", "Path": "./server/tests/core/models/test_component_relational.py", "comments": "Unit tests for Component model with relational approach."}, {"Type": "File", "Name": "test_component_type.py", "Path": "./server/tests/core/models/test_component_type.py", "comments": "Unit tests for Component Type functionality."}, {"Type": "File", "Name": "test_project_model_database_url.py", "Path": "./server/tests/core/models/test_project_model_database_url.py", "comments": "Unit tests for Project model database_url field functionality."}, {"Type": "File", "Name": "test_synchronization_log_model.py", "Path": "./server/tests/core/models/test_synchronization_log_model.py", "comments": "Unit tests for SynchronizationLog and SynchronizationConflict models."}, {"Type": "File", "Name": "test_task.py", "Path": "./server/tests/core/models/test_task.py", "comments": "Unit tests for Task and TaskAssignment models."}]}, {"Type": "Folder", "Name": "repositories", "Path": "./server/tests/core/repositories", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/core/repositories/conftest.py", "comments": "Test fixtures for repository layer tests."}, {"Type": "Folder", "Name": "general", "Path": "./server/tests/core/repositories/general", "Children": [{"Type": "File", "Name": "test_role_repository.py", "Path": "./server/tests/core/repositories/general/test_role_repository.py", "comments": "Comprehensive test suite for Role Repository"}]}, {"Type": "File", "Name": "test_component_category_repository.py", "Path": "./server/tests/core/repositories/test_component_category_repository.py", "comments": "Unit tests for Component Category functionality."}, {"Type": "File", "Name": "test_component_repository.py", "Path": "./server/tests/core/repositories/test_component_repository.py", "comments": "Unit tests for Component Repository."}, {"Type": "File", "Name": "test_component_type_repository.py", "Path": "./server/tests/core/repositories/test_component_type_repository.py", "comments": "Unit tests for Component Type functionality."}, {"Type": "File", "Name": "test_task_repository.py", "Path": "./server/tests/core/repositories/test_task_repository.py", "comments": "Unit tests for TaskRepository."}]}, {"Type": "Folder", "Name": "security", "Path": "./server/tests/core/security", "Children": [{"Type": "File", "Name": "test_input_validators.py", "Path": "./server/tests/core/security/test_input_validators.py", "comments": "Unit tests for input validation modules."}, {"Type": "File", "Name": "test_password_handler.py", "Path": "./server/tests/core/security/test_password_handler.py", "comments": "Unit tests for the PasswordHandler class."}]}, {"Type": "Folder", "Name": "services", "Path": "./server/tests/core/services", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/core/services/conftest.py", "comments": "Test fixtures for service layer tests."}, {"Type": "Folder", "Name": "general", "Path": "./server/tests/core/services/general", "Children": [{"Type": "File", "Name": "test_authorization_service.py", "Path": "./server/tests/core/services/general/test_authorization_service.py", "comments": "Comprehensive test suite for Authorization Service"}]}, {"Type": "File", "Name": "test_component_category_service.py", "Path": "./server/tests/core/services/test_component_category_service.py", "comments": "Unit tests for Component Category functionality."}, {"Type": "File", "Name": "test_component_service.py", "Path": "./server/tests/core/services/test_component_service.py", "comments": "Unit tests for Component Service."}, {"Type": "File", "Name": "test_component_type_service.py", "Path": "./server/tests/core/services/test_component_type_service.py", "comments": "Unit tests for Component Type functionality."}, {"Type": "File", "Name": "test_project_member_service.py", "Path": "./server/tests/core/services/test_project_member_service.py"}, {"Type": "File", "Name": "test_project_service.py", "Path": "./server/tests/core/services/test_project_service.py"}, {"Type": "File", "Name": "test_project_service_database_url.py", "Path": "./server/tests/core/services/test_project_service_database_url.py", "comments": "Unit tests for ProjectService database URL integration."}, {"Type": "File", "Name": "test_synchronization_service_conflict_resolution.py", "Path": "./server/tests/core/services/test_synchronization_service_conflict_resolution.py", "comments": "Unit tests for SynchronizationService conflict resolution methods."}, {"Type": "File", "Name": "test_synchronization_service_main_orchestration.py", "Path": "./server/tests/core/services/test_synchronization_service_main_orchestration.py", "comments": "Unit tests for SynchronizationService main orchestration method."}, {"Type": "File", "Name": "test_synchronization_service_utilities.py", "Path": "./server/tests/core/services/test_synchronization_service_utilities.py", "comments": "Unit tests for SynchronizationService utility methods."}, {"Type": "File", "Name": "test_task_manager_service.py", "Path": "./server/tests/core/services/test_task_manager_service.py", "comments": "Unit and integration tests for TaskManagerService."}, {"Type": "File", "Name": "test_user_service.py", "Path": "./server/tests/core/services/test_user_service.py", "comments": "Unit tests for the UserService class."}]}, {"Type": "Folder", "Name": "utils", "Path": "./server/tests/core/utils", "Children": [{"Type": "File", "Name": "test_advanced_search.py", "Path": "./server/tests/core/utils/test_advanced_search.py", "comments": "Unit tests for Advanced Component Search functionality."}]}]}, {"Type": "Folder", "Name": "fixtures", "Path": "./server/tests/fixtures", "Children": [{"Type": "File", "Name": "assertion_helpers.py", "Path": "./server/tests/fixtures/assertion_helpers.py", "comments": "Assertion helpers for ID-agnostic testing."}, {"Type": "File", "Name": "integration_factories.py", "Path": "./server/tests/fixtures/integration_factories.py", "comments": "Integration data factories for API tests."}, {"Type": "File", "Name": "mock_factories.py", "Path": "./server/tests/fixtures/mock_factories.py", "comments": "Mock data factories for unit tests."}, {"Type": "File", "Name": "test_mock_factories.py", "Path": "./server/tests/fixtures/test_mock_factories.py", "comments": "Tests for mock factories to ensure they work correctly."}]}, {"Type": "Folder", "Name": "integration", "Path": "./server/tests/integration", "Children": [{"Type": "File", "Name": "test_component_management_workflow.py", "Path": "./server/tests/integration/test_component_management_workflow.py", "comments": "Comprehensive integration tests for Component Management API workflow."}, {"Type": "File", "Name": "test_comprehensive_data_integrity.py", "Path": "./server/tests/integration/test_comprehensive_data_integrity.py", "comments": "Comprehensive Data Integrity Testing Suite"}, {"Type": "File", "Name": "test_constraint_violations.py", "Path": "./server/tests/integration/test_constraint_violations.py", "comments": "Comprehensive Constraint Violation Testing"}, {"Type": "File", "Name": "test_data_integrity.py", "Path": "./server/tests/integration/test_data_integrity.py", "comments": "This module contains tests for ensuring data integrity across various layers"}, {"Type": "File", "Name": "test_middleware_integration.py", "Path": "./server/tests/integration/test_middleware_integration.py", "comments": "Integration tests for the complete middleware stack."}, {"Type": "File", "Name": "test_middleware_safe_error_handling.py", "Path": "./server/tests/integration/test_middleware_safe_error_handling.py", "comments": "Integration tests for middleware-safe error handling."}, {"Type": "File", "Name": "test_synchronization_service_cdc.py", "Path": "./server/tests/integration/test_synchronization_service_cdc.py", "comments": "Integration tests for SynchronizationService Change Data Capture (CDC) implementation."}, {"Type": "File", "Name": "test_synchronization_service_conflict_integration.py", "Path": "./server/tests/integration/test_synchronization_service_conflict_integration.py", "comments": "Integration tests for SynchronizationService conflict resolution integration."}, {"Type": "File", "Name": "test_synchronization_service_log_integration.py", "Path": "./server/tests/integration/test_synchronization_service_log_integration.py", "comments": "Integration tests for SynchronizationService with SynchronizationLog model."}, {"Type": "File", "Name": "test_synchronization_service_transaction_management.py", "Path": "./server/tests/integration/test_synchronization_service_transaction_management.py", "comments": "Integration tests for SynchronizationService transaction management and sync log integration."}, {"Type": "File", "Name": "test_validation_integration.py", "Path": "./server/tests/integration/test_validation_integration.py", "comments": "Dedicated Test Suite for Immediate Action Enhancements"}]}, {"Type": "Folder", "Name": "middleware", "Path": "./server/tests/middleware", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/middleware/conftest.py", "comments": "Test fixtures for middleware tests."}, {"Type": "File", "Name": "test_caching_middleware.py", "Path": "./server/tests/middleware/test_caching_middleware.py", "comments": "Unit tests for CachingMiddleware following robust test patterns."}, {"Type": "File", "Name": "test_context_middleware.py", "Path": "./server/tests/middleware/test_context_middleware.py", "comments": "Unit tests for ContextMiddleware following robust test patterns."}, {"Type": "File", "Name": "test_logging_middleware.py", "Path": "./server/tests/middleware/test_logging_middleware.py", "comments": "Unit tests for LoggingMiddleware following robust test patterns."}, {"Type": "File", "Name": "test_rate_limiting_middleware.py", "Path": "./server/tests/middleware/test_rate_limiting_middleware.py", "comments": "Unit tests for RateLimitingMiddleware following robust test patterns."}, {"Type": "File", "Name": "test_security_middleware.py", "Path": "./server/tests/middleware/test_security_middleware.py", "comments": "Unit tests for SecurityMiddleware."}]}, {"Type": "Folder", "Name": "performance", "Path": "./server/tests/performance", "Children": [{"Type": "File", "Name": "conftest.py", "Path": "./server/tests/performance/conftest.py", "comments": "Test fixtures for performance tests."}, {"Type": "File", "Name": "locust_validation_load_tests.py", "Path": "./server/tests/performance/locust_validation_load_tests.py", "comments": "Locust Load Tests for Validation Logic Under High Concurrency."}, {"Type": "File", "Name": "sync_repository_adapter.py", "Path": "./server/tests/performance/sync_repository_adapter.py", "comments": "Synchronous adapter for async repositories used in performance tests."}, {"Type": "File", "Name": "test_component_performance.py", "Path": "./server/tests/performance/test_component_performance.py", "comments": "Performance benchmarks for Component operations."}, {"Type": "File", "Name": "test_concurrent_validation_stress.py", "Path": "./server/tests/performance/test_concurrent_validation_stress.py", "comments": "Concurrent Validation Stress Tests."}, {"Type": "File", "Name": "test_database_performance.py", "Path": "./server/tests/performance/test_database_performance.py", "comments": "Database Performance and Stress Testing."}, {"Type": "File", "Name": "test_email_lookup_benchmarks.py", "Path": "./server/tests/performance/test_email_lookup_benchmarks.py", "comments": "Email Lookup Performance Benchmarks with Large Datasets."}, {"Type": "File", "Name": "test_email_lookup_scale_performance.py", "Path": "./server/tests/performance/test_email_lookup_scale_performance.py", "comments": "Scale Performance Tests for Case-Insensitive Email Lookups."}, {"Type": "File", "Name": "test_memory_usage_concurrency.py", "Path": "./server/tests/performance/test_memory_usage_concurrency.py", "comments": "Memory Usage Tests Under High Concurrency."}, {"Type": "File", "Name": "test_performance_optimization.py", "Path": "./server/tests/performance/test_performance_optimization.py", "comments": "Tests for Performance Optimization functionality."}, {"Type": "File", "Name": "test_validation_pipeline_performance.py", "Path": "./server/tests/performance/test_validation_pipeline_performance.py", "comments": "Validation Pipeline Performance Tests."}]}, {"Type": "File", "Name": "test_cleanup_utilities.py", "Path": "./server/tests/test_cleanup_utilities.py", "comments": "Test cleanup utilities and validation functionality."}, {"Type": "File", "Name": "test_transaction_isolation.py", "Path": "./server/tests/test_transaction_isolation.py", "comments": "Test transaction isolation and predictable ID functionality."}, {"Type": "Folder", "Name": "validation", "Path": "./server/tests/validation", "Children": [{"Type": "File", "Name": "test_advanced_validators.py", "Path": "./server/tests/validation/test_advanced_validators.py", "comments": "Unit tests for advanced validation system."}, {"Type": "File", "Name": "test_compatibility_matrix.py", "Path": "./server/tests/validation/test_compatibility_matrix.py", "comments": "Unit tests for compatibility matrix validation system."}, {"Type": "File", "Name": "test_data_format_validator.py", "Path": "./server/tests/validation/test_data_format_validator.py", "comments": "Unit tests for multi-format data compatibility validation."}, {"Type": "File", "Name": "test_json_schema_validator.py", "Path": "./server/tests/validation/test_json_schema_validator.py", "comments": "Unit tests for advanced JSON schema validation with JSON path queries."}, {"Type": "File", "Name": "test_legacy_migration_validator.py", "Path": "./server/tests/validation/test_legacy_migration_validator.py", "comments": "Unit tests for legacy data format migration validation system."}, {"Type": "File", "Name": "test_parallel_processor.py", "Path": "./server/tests/validation/test_parallel_processor.py", "comments": "Unit tests for parallel validation processing system."}, {"Type": "File", "Name": "test_standards_validator.py", "Path": "./server/tests/validation/test_standards_validator.py", "comments": "Unit tests for standards compliance validation system."}]}]}, {"Type": "Folder", "Name": "~", "Path": "./server/~", "Children": []}]}, {"Type": "Folder", "Name": "~", "Path": "./~", "Children": []}]}