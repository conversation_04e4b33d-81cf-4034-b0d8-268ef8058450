# Synchronization & Offline Development Guide
## Ultimate Electrical Designer

**Document Version:** 1.0  
**Last Updated:** July 2025  
**Target Audience:** Develo<PERSON>, Technical Leads, System Integrators  
**Prerequisites:** Understanding of React Query, IndexedDB, PostgreSQL, and FastAPI  

---

## Table of Contents

1. [Overview](#overview)
2. [Adding New Entities to Sync](#adding-new-entities-to-sync)
3. [Debugging Synchronization Issues](#debugging-synchronization-issues)
4. [Extending Conflict Resolution](#extending-conflict-resolution)
5. [Best Practices for Offline Mutations](#best-practices-for-offline-mutations)
6. [Testing Guidelines](#testing-guidelines)
7. [Performance Optimization](#performance-optimization)
8. [Troubleshooting Common Issues](#troubleshooting-common-issues)
9. [Production Deployment Considerations](#production-deployment-considerations)
10. [API Reference](#api-reference)

---

## Overview

The Ultimate Electrical Designer implements a comprehensive **Unified Local Database & Synchronization** system that provides seamless offline capabilities and intelligent data synchronization. This guide provides practical guidance for developers working with this system.

### System Components

```mermaid
graph LR
    subgraph Frontend
        RQ[React Query]
        OM[useOfflineMutation]
        SM[SyncManager]
        IDB[IndexedDB]
    end
    
    subgraph Backend
        SS[SynchronizationService]
        CDC[Change Data Capture]
        CR[Conflict Resolution]
    end
    
    subgraph Databases
        LDB[(Local PostgreSQL)]
        CDB[(Central PostgreSQL)]
    end
    
    OM --> IDB
    SM --> SS
    SS --> CDC
    SS --> CR
    SS --> LDB
    SS --> CDB
```

### Key Concepts

- **Outbox Pattern**: Client-side queuing of offline operations
- **Change Data Capture (CDC)**: Timestamp-based change detection
- **Last-Write Wins**: Primary conflict resolution strategy
- **Bi-directional Sync**: Data flows between local and central databases
- **Optimistic Updates**: Immediate UI updates with rollback capability

---

## Adding New Entities to Sync

### Step 1: Backend Model Enhancement

Add synchronization metadata to your entity model:

```python
# server/src/core/models/your_entity.py
from sqlalchemy import Column, DateTime, Integer, String, Boolean
from sqlalchemy.sql import func
from .base import Base

class YourEntity(Base):
    __tablename__ = "your_entity"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(String(500))
    
    # 🔥 REQUIRED: Synchronization metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_deleted = Column(Boolean, default=False)  # Soft delete for sync
    
    # Optional: Project association for scoped sync
    project_id = Column(Integer, ForeignKey('project.id'))
```

### Step 2: Repository Change Detection

Add CDC support to your repository:

```python
# server/src/core/repositories/your_entity_repository.py
from datetime import datetime
from typing import List, Optional
from .base import BaseRepository
from ..models.your_entity import YourEntity

class YourEntityRepository(BaseRepository[YourEntity]):
    
    async def get_changes_since(
        self, 
        project_id: int, 
        since_timestamp: datetime
    ) -> List[ChangeRecord]:
        """
        Get all changes for entities since the specified timestamp.
        
        Returns:
            List of ChangeRecord objects with operation type and data
        """
        
        # Query for all changes since timestamp
        query = select(YourEntity).where(
            and_(
                YourEntity.project_id == project_id,
                YourEntity.updated_at > since_timestamp
            )
        ).order_by(YourEntity.updated_at)
        
        result = await self.db_session.execute(query)
        entities = result.scalars().all()
        
        # Convert to ChangeRecord format
        changes = []
        for entity in entities:
            # Determine operation type
            if entity.created_at == entity.updated_at:
                operation = 'create'
            elif entity.is_deleted:
                operation = 'delete'
            else:
                operation = 'update'
                
            changes.append(ChangeRecord(
                entity_type='your_entity',
                entity_id=entity.id,
                operation=operation,
                data=entity.to_dict(),
                timestamp=entity.updated_at
            ))
        
        return changes
    
    async def apply_changes(
        self, 
        changes: List[ChangeRecord]
    ) -> List[ApplyResult]:
        """
        Apply a list of changes to the database.
        
        Handles:
        - Create operations (INSERT)
        - Update operations (UPDATE)
        - Delete operations (soft delete)
        - Conflict detection and resolution
        """
        
        results = []
        
        for change in changes:
            try:
                if change.operation == 'create':
                    # Create new entity
                    entity = YourEntity(**change.data)
                    self.db_session.add(entity)
                    results.append(ApplyResult(
                        success=True,
                        operation=change.operation,
                        entity_id=change.entity_id
                    ))
                    
                elif change.operation == 'update':
                    # Update existing entity
                    entity = await self.get_by_id(change.entity_id)
                    if entity:
                        # Check for conflicts
                        if entity.updated_at > change.timestamp:
                            # Conflict detected - newer version exists
                            results.append(ApplyResult(
                                success=False,
                                operation=change.operation,
                                entity_id=change.entity_id,
                                conflict=True,
                                conflict_data=entity.to_dict()
                            ))
                        else:
                            # Apply update
                            for key, value in change.data.items():
                                if hasattr(entity, key):
                                    setattr(entity, key, value)
                            entity.updated_at = func.now()
                            results.append(ApplyResult(
                                success=True,
                                operation=change.operation,
                                entity_id=change.entity_id
                            ))
                    
                elif change.operation == 'delete':
                    # Soft delete entity
                    entity = await self.get_by_id(change.entity_id)
                    if entity:
                        entity.is_deleted = True
                        entity.updated_at = func.now()
                        results.append(ApplyResult(
                            success=True,
                            operation=change.operation,
                            entity_id=change.entity_id
                        ))
                        
                await self.db_session.commit()
                
            except Exception as e:
                await self.db_session.rollback()
                results.append(ApplyResult(
                    success=False,
                    operation=change.operation,
                    entity_id=change.entity_id,
                    error=str(e)
                ))
        
        return results
```

### Step 3: Service Integration

Integrate with the SynchronizationService:

```python
# server/src/core/services/general/synchronization_service.py
# Add to the SynchronizationService class

async def _get_entity_changes(
    self, 
    project_id: int, 
    since_timestamp: datetime
) -> List[ChangeRecord]:
    """
    Get changes for all synchronized entities.
    """
    
    all_changes = []
    
    # Add your entity to the synchronized entities list
    repositories = [
        self.project_repo,
        self.component_repo,
        self.your_entity_repo,  # 🔥 Add your repository here
        # ... other repositories
    ]
    
    for repo in repositories:
        if hasattr(repo, 'get_changes_since'):
            changes = await repo.get_changes_since(project_id, since_timestamp)
            all_changes.extend(changes)
    
    return sorted(all_changes, key=lambda x: x.timestamp)
```

### Step 4: Frontend Hook Creation

Create a typed hook for your entity:

```typescript
// client/src/hooks/api/useYourEntity.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useOfflineMutation } from '../cache/useOfflineMutation'
import { yourEntityApi } from '../../api/yourEntity'
import type { YourEntity, CreateYourEntityRequest, UpdateYourEntityRequest } from '../../types/yourEntity'

// Query hooks
export function useYourEntities(projectId: number) {
  return useQuery({
    queryKey: ['your-entities', projectId],
    queryFn: () => yourEntityApi.getAll(projectId),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

export function useYourEntity(id: number) {
  return useQuery({
    queryKey: ['your-entities', id],
    queryFn: () => yourEntityApi.getById(id),
    enabled: !!id,
  })
}

// Mutation hooks with offline support
export function useCreateYourEntity() {
  const queryClient = useQueryClient()
  
  return useOfflineMutation<YourEntity, Error, CreateYourEntityRequest>({
    endpoint: '/api/v1/your-entities',
    method: 'POST',
    mutationFn: yourEntityApi.create,
    
    // 🔥 IMPORTANT: Optimistic response for offline usage
    optimisticResponse: (variables) => ({
      id: `temp_${Date.now()}`, // Temporary ID
      ...variables,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      _optimistic: true, // Flag for UI indication
    }),
    
    onSuccess: (data, variables) => {
      // Update queries
      queryClient.invalidateQueries({ queryKey: ['your-entities', variables.project_id] })
      queryClient.setQueryData(['your-entities', data.id], data)
    },
    
    onError: (error, variables, context) => {
      console.error('Failed to create your entity:', error)
    }
  })
}

export function useUpdateYourEntity() {
  const queryClient = useQueryClient()
  
  return useOfflineMutation<YourEntity, Error, UpdateYourEntityRequest>({
    endpoint: `/api/v1/your-entities/${variables => variables.id}`,
    method: 'PUT',
    mutationFn: yourEntityApi.update,
    
    onMutate: async (variables) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['your-entities', variables.id] })
      
      // Snapshot previous value
      const previousEntity = queryClient.getQueryData(['your-entities', variables.id])
      
      // Optimistic update
      queryClient.setQueryData(['your-entities', variables.id], (old: YourEntity) => ({
        ...old,
        ...variables,
        updated_at: new Date().toISOString(),
        _optimistic: true,
      }))
      
      return { previousEntity }
    },
    
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousEntity) {
        queryClient.setQueryData(['your-entities', variables.id], context.previousEntity)
      }
    },
    
    onSettled: () => {
      // Refresh data after mutation
      queryClient.invalidateQueries({ queryKey: ['your-entities'] })
    }
  })
}

export function useDeleteYourEntity() {
  const queryClient = useQueryClient()
  
  return useOfflineMutation<void, Error, { id: number; project_id: number }>({
    endpoint: `/api/v1/your-entities/${variables => variables.id}`,
    method: 'DELETE',
    mutationFn: yourEntityApi.delete,
    
    onMutate: async (variables) => {
      // Remove from list queries optimistically
      queryClient.setQueryData(['your-entities', variables.project_id], (old: YourEntity[]) => 
        old?.filter(entity => entity.id !== variables.id) || []
      )
      
      // Remove from individual query
      queryClient.removeQueries({ queryKey: ['your-entities', variables.id] })
    },
    
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['your-entities', variables.project_id] })
    }
  })
}
```

### Step 5: API Client Implementation

Create the API client:

```typescript
// client/src/api/yourEntity.ts
import { apiClient } from './client'
import type { YourEntity, CreateYourEntityRequest, UpdateYourEntityRequest } from '../types/yourEntity'

export const yourEntityApi = {
  async getAll(projectId: number): Promise<YourEntity[]> {
    const response = await apiClient.get(`/api/v1/projects/${projectId}/your-entities`)
    return response.data
  },
  
  async getById(id: number): Promise<YourEntity> {
    const response = await apiClient.get(`/api/v1/your-entities/${id}`)
    return response.data
  },
  
  async create(data: CreateYourEntityRequest): Promise<YourEntity> {
    const response = await apiClient.post('/api/v1/your-entities', data)
    return response.data
  },
  
  async update({ id, ...data }: UpdateYourEntityRequest): Promise<YourEntity> {
    const response = await apiClient.put(`/api/v1/your-entities/${id}`, data)
    return response.data
  },
  
  async delete(id: number): Promise<void> {
    await apiClient.delete(`/api/v1/your-entities/${id}`)
  }
}
```

---

## Debugging Synchronization Issues

### Backend Debugging

#### 1. Enable Detailed Logging

```python
# server/src/core/services/general/synchronization_service.py
import logging

logger = logging.getLogger(__name__)

class SynchronizationService:
    async def synchronize_project(self, project_id: int) -> SynchronizationResult:
        logger.info(f"Starting synchronization for project {project_id}")
        
        try:
            # Log timing information
            start_time = time.time()
            
            # Get changes with detailed logging
            local_changes = await self._get_local_changes(project_id, last_sync_timestamp)
            logger.info(f"Found {len(local_changes)} local changes")
            
            central_changes = await self._get_central_changes(project_id, last_sync_timestamp)
            logger.info(f"Found {len(central_changes)} central changes")
            
            # Log conflicts
            conflicts = await self._detect_conflicts(local_changes, central_changes)
            if conflicts:
                logger.warning(f"Detected {len(conflicts)} conflicts")
                for conflict in conflicts:
                    logger.warning(f"Conflict: {conflict.entity_type}:{conflict.entity_id} - {conflict.conflict_type}")
            
            # Log completion
            duration = time.time() - start_time
            logger.info(f"Synchronization completed in {duration:.2f}s")
            
        except Exception as e:
            logger.error(f"Synchronization failed for project {project_id}: {str(e)}", exc_info=True)
            raise
```

#### 2. Sync History Query

```sql
-- Query recent synchronization logs
SELECT 
    project_id,
    sync_direction,
    operation_type,
    conflicts_detected,
    conflicts_resolved,
    status,
    started_at,
    completed_at,
    duration_ms,
    error_message
FROM synchronization_log 
WHERE project_id = ? 
ORDER BY started_at DESC 
LIMIT 20;
```

#### 3. Check Change Detection

```python
# Debug CDC mechanism
async def debug_change_detection(project_id: int, since: datetime):
    """Debug helper to inspect change detection."""
    
    # Check what changes are detected
    changes = await sync_service._get_local_changes(project_id, since)
    
    print(f"Changes detected since {since}:")
    for change in changes:
        print(f"  {change.entity_type}:{change.entity_id} - {change.operation} at {change.timestamp}")
    
    # Check timestamps
    latest_timestamp = await sync_service._get_last_sync_timestamp(project_id)
    print(f"Last sync timestamp: {latest_timestamp}")
```

### Frontend Debugging

#### 1. Outbox Inspection

```typescript
// Debug utility for inspecting outbox contents
export async function debugOutbox(): Promise<void> {
  const { openDB } = await import('idb')
  
  try {
    const db = await openDB('query-cache', 1)
    const transaction = db.transaction('mutation-outbox', 'readonly')
    const store = transaction.objectStore('mutation-outbox')
    const mutations = await store.getAll()
    
    console.group('🔍 Outbox Inspection')
    console.log(`Total mutations: ${mutations.length}`)
    
    mutations.forEach((mutation, index) => {
      console.group(`Mutation ${index + 1}`)
      console.log(`ID: ${mutation.id}`)
      console.log(`Endpoint: ${mutation.endpoint}`)
      console.log(`Method: ${mutation.method}`)
      console.log(`Status: ${mutation.status}`)
      console.log(`Retry Count: ${mutation.retryCount}`)
      console.log(`Timestamp: ${mutation.timestamp}`)
      console.log(`Variables:`, mutation.variables)
      if (mutation.lastError) {
        console.error(`Last Error: ${mutation.lastError}`)
      }
      console.groupEnd()
    })
    
    console.groupEnd()
  } catch (error) {
    console.error('Failed to inspect outbox:', error)
  }
}

// Add to window for debugging
if (typeof window !== 'undefined') {
  (window as any).debugOutbox = debugOutbox
}
```

#### 2. Network Quality Monitoring

```typescript
// Debug network quality detection
export function debugNetworkQuality(): void {
  const networkMonitor = new NetworkMonitor()
  
  console.group('🌐 Network Quality Debug')
  
  // Current status
  console.log(`Online: ${navigator.onLine}`)
  console.log(`Connection:`, (navigator as any).connection)
  
  // Monitor changes
  networkMonitor.addListener((status, quality) => {
    console.log(`Network status changed: ${status} (${quality})`)
  })
  
  // Test quality detection
  networkMonitor.detectConnectionQuality().then(quality => {
    console.log(`Detected quality: ${quality}`)
    const strategy = networkMonitor.getAdaptiveSyncStrategy()
    console.log(`Sync strategy:`, strategy)
  })
  
  console.groupEnd()
}
```

#### 3. Sync Manager Status

```typescript
// Debug sync manager state
export function debugSyncManager(): void {
  const syncManager = SyncManager.getInstance()
  
  console.group('⚙️ Sync Manager Debug')
  
  // Current state
  console.log(`Is processing: ${syncManager.isCurrentlyProcessing()}`)
  console.log(`Current operation: ${syncManager.getCurrentOperation()}`)
  
  // Get statistics
  syncManager.getStats().then(stats => {
    console.log('Statistics:', stats)
  })
  
  // Monitor sync events
  syncManager.onSyncStart(() => {
    console.log('🟡 Sync started')
  })
  
  syncManager.onSyncComplete((result) => {
    console.log('🟢 Sync completed:', result)
  })
  
  syncManager.onSyncError((error) => {
    console.error('🔴 Sync error:', error)
  })
  
  console.groupEnd()
}
```

---

## Extending Conflict Resolution

The system currently supports **Last-Write Wins** strategy. Here's how to add new resolution strategies:

### 1. Add New Strategy Enum

```python
# server/src/core/models/enums.py
class ConflictResolutionStrategy(str, Enum):
    LAST_WRITE_WINS = "last_write_wins"
    MANUAL_RESOLUTION = "manual_resolution"
    MERGE_CHANGES = "merge_changes"  # 🔥 New strategy
    CLIENT_WINS = "client_wins"      # 🔥 New strategy
    SERVER_WINS = "server_wins"      # 🔥 New strategy
```

### 2. Implement Resolution Logic

```python
# server/src/core/services/general/synchronization_service.py
async def _resolve_conflict(
    self, 
    conflict: ConflictRecord,
    strategy: ConflictResolutionStrategy
) -> ConflictResolution:
    """Resolve conflict using specified strategy."""
    
    if strategy == ConflictResolutionStrategy.LAST_WRITE_WINS:
        return await self._apply_last_write_wins(conflict)
    
    elif strategy == ConflictResolutionStrategy.MERGE_CHANGES:
        return await self._apply_merge_changes(conflict)
    
    elif strategy == ConflictResolutionStrategy.CLIENT_WINS:
        return await self._apply_client_wins(conflict)
    
    elif strategy == ConflictResolutionStrategy.SERVER_WINS:
        return await self._apply_server_wins(conflict)
    
    elif strategy == ConflictResolutionStrategy.MANUAL_RESOLUTION:
        return await self._queue_manual_resolution(conflict)
    
    else:
        raise ValueError(f"Unknown conflict resolution strategy: {strategy}")

async def _apply_merge_changes(self, conflict: ConflictRecord) -> ConflictResolution:
    """
    Merge changes from both local and central versions.
    
    Strategy:
    - Combine non-conflicting fields
    - Use timestamps to resolve field-level conflicts
    - Preserve both versions for audit trail
    """
    
    local_data = conflict.local_data
    central_data = conflict.central_data
    merged_data = {}
    
    # Get all unique fields
    all_fields = set(local_data.keys()) | set(central_data.keys())
    
    for field in all_fields:
        local_value = local_data.get(field)
        central_value = central_data.get(field)
        
        if local_value == central_value:
            # No conflict for this field
            merged_data[field] = local_value
        else:
            # Field-level conflict - use Last-Write Wins
            local_timestamp = local_data.get('updated_at')
            central_timestamp = central_data.get('updated_at')
            
            if local_timestamp and central_timestamp:
                if local_timestamp > central_timestamp:
                    merged_data[field] = local_value
                else:
                    merged_data[field] = central_value
            else:
                # Fallback to central value
                merged_data[field] = central_value
    
    return ConflictResolution(
        conflict_id=conflict.id,
        resolution_strategy='merge_changes',
        winner='merged',
        resolved_data=merged_data,
        metadata={
            'merged_fields': len(all_fields),
            'conflicting_fields': len([f for f in all_fields if local_data.get(f) != central_data.get(f)])
        }
    )
```

### 3. Frontend Strategy Selection

```typescript
// client/src/lib/sync/conflict-resolver.ts
export interface ConflictResolutionOptions {
  strategy: 'last_write_wins' | 'merge_changes' | 'client_wins' | 'server_wins' | 'manual'
  entityType: string
  priority?: 'high' | 'medium' | 'low'
}

export class ConflictResolver {
  async resolveConflict(
    conflict: ConflictRecord,
    options: ConflictResolutionOptions
  ): Promise<ConflictResolution> {
    
    switch (options.strategy) {
      case 'merge_changes':
        return this.mergeChanges(conflict)
      
      case 'client_wins':
        return this.clientWins(conflict)
      
      case 'server_wins':
        return this.serverWins(conflict)
      
      case 'manual':
        return this.requestManualResolution(conflict)
      
      default:
        return this.lastWriteWins(conflict)
    }
  }
  
  private async mergeChanges(conflict: ConflictRecord): Promise<ConflictResolution> {
    // Implement field-level merging logic
    const localData = conflict.local_data
    const serverData = conflict.server_data
    const merged = { ...serverData }
    
    // Custom merging logic for specific fields
    if (conflict.entity_type === 'project') {
      // Example: Merge project-specific fields intelligently
      merged.name = this.resolveStringConflict(localData.name, serverData.name)
      merged.description = this.mergeDescriptions(localData.description, serverData.description)
      merged.settings = { ...serverData.settings, ...localData.settings }
    }
    
    return {
      success: true,
      strategy: 'merge_changes',
      resolvedData: merged,
      metadata: {
        mergedFields: Object.keys(merged).length,
        strategy: 'intelligent_merge'
      }
    }
  }
}
```

---

## Best Practices for Offline Mutations

### 1. Design for Offline-First

```typescript
// ✅ Good: Design mutations to work offline
const useCreateProject = () => {
  return useOfflineMutation({
    endpoint: '/api/v1/projects',
    method: 'POST',
    mutationFn: createProject,
    
    // Always provide optimistic response
    optimisticResponse: (variables) => ({
      id: `temp_${Date.now()}`,
      ...variables,
      status: 'DRAFT',
      created_at: new Date().toISOString(),
      _optimistic: true
    }),
    
    // Handle dependency chains
    metadata: {
      entityType: 'project',
      operation: 'create',
      priority: 'high'
    }
  })
}

// ❌ Bad: No offline consideration
const useCreateProject = () => {
  return useMutation({
    mutationFn: createProject, // Will fail offline
    onSuccess: () => {
      // No optimistic updates
    }
  })
}
```

### 2. Handle Temporary IDs

```typescript
// Utility for managing temporary IDs
export function generateTempId(prefix: string = 'temp'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2)}`
}

export function isTempId(id: string | number): boolean {
  return typeof id === 'string' && id.startsWith('temp_')
}

// Usage in components
const CreateProjectForm = () => {
  const createProject = useCreateProject()
  
  const handleSubmit = async (data: CreateProjectRequest) => {
    // Create with temporary ID for immediate UI feedback
    const tempProject = {
      id: generateTempId('project'),
      ...data,
      _optimistic: true
    }
    
    // Mutation will handle offline queueing
    await createProject.mutateAsync(data)
  }
}
```

### 3. Implement Dependency Management

```typescript
// Handle dependencies between mutations
interface MutationDependency {
  dependsOn: string[]  // IDs of other mutations
  entityType: string
  priority: number
}

const useCreateProjectWithComponents = () => {
  const createProject = useCreateProject()
  const createComponent = useCreateComponent()
  
  return async (projectData: ProjectData, components: ComponentData[]) => {
    // Create project first
    const project = await createProject.mutateAsync(projectData)
    
    // Create components with dependency on project
    const componentPromises = components.map(componentData =>
      createComponent.mutateAsync({
        ...componentData,
        project_id: project.id,
        // Metadata for dependency tracking
        _metadata: {
          dependsOn: [project.id],
          priority: 'medium'
        }
      })
    )
    
    await Promise.all(componentPromises)
  }
}
```

### 4. Provide User Feedback

```typescript
// Component with offline status indication
const ProjectCard = ({ project }: { project: Project }) => {
  const isOptimistic = project._optimistic
  const isOffline = !navigator.onLine
  
  return (
    <Card className={cn("project-card", {
      "opacity-75": isOptimistic,
      "border-orange-500": isOffline
    })}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{project.name}</CardTitle>
          {isOptimistic && (
            <Badge variant="outline" className="text-orange-600">
              <Clock className="w-3 h-3 mr-1" />
              Pending Sync
            </Badge>
          )}
          {isOffline && (
            <Badge variant="secondary">
              <WifiOff className="w-3 h-3 mr-1" />
              Offline
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <p>{project.description}</p>
      </CardContent>
    </Card>
  )
}
```

---

## Testing Guidelines

### 1. Unit Tests for Offline Hooks

```typescript
// tests/hooks/useOfflineMutation.test.ts
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useOfflineMutation } from '../../src/hooks/cache/useOfflineMutation'

// Mock network status
const mockNavigator = {
  onLine: true
}
Object.defineProperty(window, 'navigator', {
  value: mockNavigator,
  writable: true
})

describe('useOfflineMutation', () => {
  let queryClient: QueryClient
  
  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
      }
    })
  })
  
  const wrapper = ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
  
  test('executes mutation when online', async () => {
    const mutationFn = jest.fn().mockResolvedValue({ id: 1, name: 'Test' })
    
    const { result } = renderHook(() =>
      useOfflineMutation({
        endpoint: '/api/test',
        method: 'POST',
        mutationFn
      }),
      { wrapper }
    )
    
    await result.current.mutateAsync({ name: 'Test' })
    
    expect(mutationFn).toHaveBeenCalledWith({ name: 'Test' })
  })
  
  test('queues mutation when offline', async () => {
    // Set offline
    mockNavigator.onLine = false
    
    const mutationFn = jest.fn().mockResolvedValue({ id: 1, name: 'Test' })
    const addToOutbox = jest.fn()
    
    // Mock SyncManager
    jest.doMock('../../src/lib/sync/sync-manager', () => ({
      SyncManager: {
        getInstance: () => ({
          addToOutbox
        })
      }
    }))
    
    const { result } = renderHook(() =>
      useOfflineMutation({
        endpoint: '/api/test',
        method: 'POST',
        mutationFn,
        optimisticResponse: (variables) => ({ ...variables, id: 'temp_123' })
      }),
      { wrapper }
    )
    
    const result = await result.current.mutateAsync({ name: 'Test' })
    
    expect(mutationFn).not.toHaveBeenCalled()
    expect(addToOutbox).toHaveBeenCalledWith(expect.objectContaining({
      endpoint: '/api/test',
      method: 'POST',
      variables: { name: 'Test' }
    }))
    expect(result).toEqual({ name: 'Test', id: 'temp_123' })
  })
})
```

### 2. Integration Tests for Sync Flow

```typescript
// tests/integration/sync-flow.test.ts
import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { SyncManager } from '../../src/lib/sync/sync-manager'

const server = setupServer(
  rest.post('/api/v1/projects', (req, res, ctx) => {
    return res(ctx.json({ id: 1, name: 'Test Project' }))
  })
)

describe('Sync Flow Integration', () => {
  beforeAll(() => server.listen())
  afterEach(() => server.resetHandlers())
  afterAll(() => server.close())
  
  test('processes outbox when coming online', async () => {
    const syncManager = SyncManager.getInstance()
    
    // Add mutation to outbox
    await syncManager.addToOutbox({
      id: 'test-mutation',
      endpoint: '/api/v1/projects',
      method: 'POST',
      variables: { name: 'Test Project' },
      timestamp: new Date().toISOString(),
      retryCount: 0,
      status: 'pending'
    })
    
    // Process outbox
    const result = await syncManager.processOutbox()
    
    expect(result.status).toBe('completed')
    expect(result.completed).toBe(1)
    
    // Verify outbox is empty
    const outboxCount = await syncManager.getOutboxCount()
    expect(outboxCount).toBe(0)
  })
})
```

### 3. E2E Tests for Offline Scenarios

See the comprehensive E2E tests created in Work Batches 8.1-8.3 for complete offline scenario testing.

---

## Performance Optimization

### 1. Optimize IndexedDB Operations

```typescript
// Batch IndexedDB operations for better performance
export class OptimizedIndexedDBPersister implements Persister {
  private readonly batchSize = 100
  private pendingOperations: Array<() => Promise<void>> = []
  private flushTimeout: NodeJS.Timeout | null = null
  
  async persistClient(client: PersistedClient): Promise<void> {
    // Add to batch
    this.pendingOperations.push(async () => {
      await this.doPersistClient(client)
    })
    
    // Flush batch if needed
    if (this.pendingOperations.length >= this.batchSize) {
      await this.flushBatch()
    } else {
      this.scheduleFlush()
    }
  }
  
  private scheduleFlush(): void {
    if (this.flushTimeout) return
    
    this.flushTimeout = setTimeout(() => {
      this.flushBatch()
    }, 1000) // Flush after 1 second
  }
  
  private async flushBatch(): Promise<void> {
    if (this.flushTimeout) {
      clearTimeout(this.flushTimeout)
      this.flushTimeout = null
    }
    
    const operations = this.pendingOperations.splice(0, this.batchSize)
    
    if (operations.length === 0) return
    
    // Execute all operations in a single transaction
    const db = await this.getDatabase()
    const transaction = db.transaction(['query-cache'], 'readwrite')
    
    await Promise.all(operations.map(op => op()))
    
    await transaction.complete
  }
}
```

### 2. Intelligent Sync Scheduling

```typescript
// Smart sync scheduling based on network conditions
export class SmartSyncScheduler {
  private syncIntervals = new Map<string, NodeJS.Timeout>()
  
  scheduleSync(
    projectId: string, 
    networkQuality: ConnectionQuality,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): void {
    
    // Clear existing schedule
    const existingInterval = this.syncIntervals.get(projectId)
    if (existingInterval) {
      clearTimeout(existingInterval)
    }
    
    // Determine sync interval based on quality and priority
    const baseInterval = this.getBaseInterval(networkQuality)
    const priorityMultiplier = this.getPriorityMultiplier(priority)
    const interval = baseInterval * priorityMultiplier
    
    // Schedule sync
    const timeout = setTimeout(() => {
      this.triggerSync(projectId)
    }, interval)
    
    this.syncIntervals.set(projectId, timeout)
  }
  
  private getBaseInterval(quality: ConnectionQuality): number {
    switch (quality) {
      case 'fast': return 5000    // 5 seconds
      case 'slow': return 30000   // 30 seconds
      case 'unstable': return 60000 // 1 minute
      default: return 15000       // 15 seconds
    }
  }
  
  private getPriorityMultiplier(priority: string): number {
    switch (priority) {
      case 'high': return 0.5    // Sync more frequently
      case 'low': return 2.0     // Sync less frequently
      default: return 1.0        // Normal frequency
    }
  }
}
```

### 3. Memory Management

```typescript
// Implement memory-conscious caching
export class MemoryOptimizedCache {
  private readonly maxCacheSize = 50 * 1024 * 1024 // 50MB
  private cacheSize = 0
  private cache = new Map<string, CacheEntry>()
  
  set(key: string, value: any): void {
    const serialized = JSON.stringify(value)
    const size = new Blob([serialized]).size
    
    // Check if addition would exceed limit
    if (this.cacheSize + size > this.maxCacheSize) {
      this.evictOldEntries(size)
    }
    
    // Remove existing entry if updating
    if (this.cache.has(key)) {
      const existing = this.cache.get(key)!
      this.cacheSize -= existing.size
    }
    
    // Add new entry
    this.cache.set(key, {
      value: serialized,
      size,
      lastAccessed: Date.now()
    })
    
    this.cacheSize += size
  }
  
  private evictOldEntries(requiredSpace: number): void {
    // Sort by last accessed time
    const entries = Array.from(this.cache.entries())
      .sort(([,a], [,b]) => a.lastAccessed - b.lastAccessed)
    
    let freedSpace = 0
    
    for (const [key, entry] of entries) {
      this.cache.delete(key)
      this.cacheSize -= entry.size
      freedSpace += entry.size
      
      if (freedSpace >= requiredSpace) break
    }
  }
}
```

---

## Troubleshooting Common Issues

### Issue 1: Sync Not Triggering

**Symptoms:**
- Outbox has pending mutations
- Network is online
- No sync activity

**Diagnosis:**
```typescript
// Check sync manager status
const syncManager = SyncManager.getInstance()
console.log('Is processing:', syncManager.isCurrentlyProcessing())
console.log('Network status:', navigator.onLine)

// Check network monitor
const networkMonitor = new NetworkMonitor()
networkMonitor.detectConnectionQuality().then(quality => {
  console.log('Connection quality:', quality)
})

// Manually trigger sync
syncManager.processOutbox().then(result => {
  console.log('Manual sync result:', result)
})
```

**Solutions:**
1. Verify network event listeners are attached
2. Check for JavaScript errors blocking sync
3. Ensure sync manager is properly initialized
4. Verify backend endpoints are accessible

### Issue 2: Conflicts Not Resolving

**Symptoms:**
- Mutations appear to succeed but data doesn't match expected state
- Conflict errors in logs
- Data inconsistency between clients

**Diagnosis:**
```python
# Check conflict detection
conflicts = await sync_service._detect_conflicts(local_changes, central_changes)
for conflict in conflicts:
    print(f"Conflict: {conflict.entity_type}:{conflict.entity_id}")
    print(f"  Local: {conflict.local_data}")
    print(f"  Central: {conflict.central_data}")
    print(f"  Type: {conflict.conflict_type}")

# Check resolution strategy
strategy = ConflictResolutionStrategy.LAST_WRITE_WINS
resolution = await sync_service._resolve_conflict(conflict, strategy)
print(f"Resolution: {resolution.winner} - {resolution.resolved_data}")
```

**Solutions:**
1. Verify timestamp consistency across systems
2. Check conflict resolution strategy configuration
3. Ensure proper conflict logging
4. Validate resolution logic for specific entity types

### Issue 3: Performance Degradation

**Symptoms:**
- Slow sync operations
- High memory usage
- Browser freezing during sync

**Diagnosis:**
```typescript
// Performance monitoring
console.time('sync-operation')

const result = await syncManager.processOutbox()

console.timeEnd('sync-operation')
console.log('Processed:', result.processed)
console.log('Memory usage:', performance.memory)

// Check outbox size
const outboxCount = await syncManager.getOutboxCount()
console.log('Outbox size:', outboxCount)
```

**Solutions:**
1. Implement batched processing for large outboxes
2. Add memory management for large datasets
3. Optimize IndexedDB queries
4. Implement progressive sync for large projects

### Issue 4: Data Loss During Sync

**Symptoms:**
- Missing data after sync
- Inconsistent state between local and central databases
- User reports of lost changes

**Diagnosis:**
```python
# Audit data integrity
async def audit_sync_integrity(project_id: int):
    # Compare record counts
    local_count = await local_repo.count_by_project(project_id)
    central_count = await central_repo.count_by_project(project_id)
    
    print(f"Local records: {local_count}")
    print(f"Central records: {central_count}")
    
    # Check for missing records
    local_ids = await local_repo.get_all_ids(project_id)
    central_ids = await central_repo.get_all_ids(project_id)
    
    missing_in_central = local_ids - central_ids
    missing_in_local = central_ids - local_ids
    
    if missing_in_central:
        print(f"Missing in central: {missing_in_central}")
    if missing_in_local:
        print(f"Missing in local: {missing_in_local}")
```

**Solutions:**
1. Implement comprehensive sync validation
2. Add integrity checks before and after sync
3. Implement rollback mechanism for failed syncs
4. Add detailed audit logging

---

## Production Deployment Considerations

### 1. Environment Configuration

```bash
# Environment variables for production
SYNC_BATCH_SIZE=50                    # Batch size for sync operations
SYNC_RETRY_ATTEMPTS=5                 # Max retry attempts
SYNC_TIMEOUT_SECONDS=300              # Sync operation timeout
SYNC_LOG_RETENTION_DAYS=90            # How long to keep sync logs
CONFLICT_RESOLUTION_STRATEGY=last_write_wins  # Default strategy
ENABLE_SYNC_METRICS=true              # Enable monitoring
```

### 2. Monitoring and Alerting

```python
# Production monitoring setup
from prometheus_client import Counter, Histogram, Gauge
import logging

# Metrics
sync_operations_total = Counter('sync_operations_total', 'Total sync operations', ['status', 'project_id'])
sync_duration_seconds = Histogram('sync_duration_seconds', 'Sync operation duration')
sync_conflicts_total = Counter('sync_conflicts_total', 'Total conflicts detected', ['entity_type', 'resolution'])
sync_queue_size = Gauge('sync_queue_size', 'Current sync queue size')

class ProductionSynchronizationService(SynchronizationService):
    async def synchronize_project(self, project_id: int) -> SynchronizationResult:
        with sync_duration_seconds.time():
            try:
                result = await super().synchronize_project(project_id)
                
                # Record metrics
                sync_operations_total.labels(status='success', project_id=project_id).inc()
                sync_conflicts_total.labels(
                    entity_type='all',
                    resolution=result.conflict_resolution_strategy
                ).inc(result.conflicts_resolved)
                
                # Alert on high conflict rates
                if result.conflicts_detected > 10:
                    logger.warning(f"High conflict rate for project {project_id}: {result.conflicts_detected}")
                
                return result
                
            except Exception as e:
                sync_operations_total.labels(status='error', project_id=project_id).inc()
                raise
```

### 3. Database Optimization

```sql
-- Production database indexes for sync performance
CREATE INDEX CONCURRENTLY idx_project_updated_at ON project(updated_at) WHERE is_deleted = false;
CREATE INDEX CONCURRENTLY idx_sync_log_project_id_started_at ON synchronization_log(project_id, started_at DESC);
CREATE INDEX CONCURRENTLY idx_component_project_updated ON component(project_id, updated_at) WHERE is_deleted = false;

-- Partitioning for large sync logs
CREATE TABLE synchronization_log_2024 PARTITION OF synchronization_log 
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');
```

### 4. Security Considerations

```python
# Secure sync endpoint with comprehensive validation
@router.post("/projects/{project_id}/sync")
async def synchronize_project(
    project_id: int,
    request: SyncRequest,
    current_user: User = Depends(get_current_user),
    rate_limiter: RateLimiter = Depends(get_rate_limiter)
):
    # Rate limiting
    await rate_limiter.check_rate_limit(f"sync:{current_user.id}", limit=10, window=300)
    
    # Permission validation
    project = await project_repo.get_by_id(project_id)
    if not project:
        raise HTTPException(404, "Project not found")
    
    await check_project_access(current_user, project, 'sync')
    
    # Input validation
    if request.last_sync_timestamp and request.last_sync_timestamp > datetime.utcnow():
        raise HTTPException(400, "Invalid sync timestamp from future")
    
    # Audit logging
    await audit_service.log_sync_attempt(
        user_id=current_user.id,
        project_id=project_id,
        client_ip=request.client.host
    )
    
    try:
        result = await sync_service.synchronize_project(project_id)
        
        # Success audit
        await audit_service.log_sync_success(
            user_id=current_user.id,
            project_id=project_id,
            sync_result=result
        )
        
        return result
        
    except Exception as e:
        # Error audit
        await audit_service.log_sync_error(
            user_id=current_user.id,
            project_id=project_id,
            error=str(e)
        )
        raise
```

---

## API Reference

### Backend API Endpoints

#### POST /api/v1/projects/{project_id}/sync
Trigger synchronization for a specific project.

**Request:**
```json
{
  "last_sync_timestamp": "2024-07-22T10:30:00Z",
  "force_full_sync": false,
  "conflict_resolution_strategy": "last_write_wins"
}
```

**Response:**
```json
{
  "project_id": 123,
  "status": "completed",
  "local_to_central": {
    "created": 5,
    "updated": 3,
    "deleted": 1,
    "errors": 0
  },
  "central_to_local": {
    "created": 2,
    "updated": 8,
    "deleted": 0,
    "errors": 0
  },
  "conflicts_detected": 2,
  "conflicts_resolved": 2,
  "conflict_resolution_strategy": "last_write_wins",
  "sync_direction": "bidirectional",
  "started_at": "2024-07-22T10:31:00Z",
  "completed_at": "2024-07-22T10:31:15Z",
  "duration_ms": 15000,
  "message": "Synchronization completed successfully"
}
```

#### GET /api/v1/projects/{project_id}/sync/history
Get synchronization history for a project.

**Response:**
```json
{
  "items": [
    {
      "id": 456,
      "project_id": 123,
      "sync_direction": "bidirectional",
      "operation_type": "manual",
      "started_at": "2024-07-22T10:31:00Z",
      "completed_at": "2024-07-22T10:31:15Z",
      "duration_ms": 15000,
      "status": "completed",
      "conflicts_detected": 2,
      "conflicts_resolved": 2,
      "local_to_central": {"created": 5, "updated": 3, "deleted": 1, "errors": 0},
      "central_to_local": {"created": 2, "updated": 8, "deleted": 0, "errors": 0}
    }
  ],
  "total": 25,
  "page": 1,
  "size": 10,
  "pages": 3
}
```

### Frontend Hooks API

#### useOfflineMutation
Hook for offline-capable mutations.

```typescript
interface OfflineMutationOptions<TData, TError, TVariables> {
  endpoint: string | ((variables: TVariables) => string)
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  mutationFn: (variables: TVariables) => Promise<TData>
  optimisticResponse?: (variables: TVariables) => TData
  onSuccess?: (data: TData, variables: TVariables) => void
  onError?: (error: TError, variables: TVariables) => void
  metadata?: {
    entityType: string
    operation: 'create' | 'update' | 'delete'
    priority: 'high' | 'medium' | 'low'
  }
}
```

#### SyncManager API
Central sync management interface.

```typescript
interface SyncManager {
  // Core methods
  processOutbox(): Promise<SyncResult>
  addToOutbox(entry: OutboxEntry): Promise<void>
  getOutboxCount(): Promise<number>
  
  // Status methods
  isCurrentlyProcessing(): boolean
  getCurrentOperation(): string | null
  getStats(): Promise<SyncStats>
  
  // Event handlers
  onSyncStart(callback: () => void): void
  onSyncComplete(callback: (result: SyncResult) => void): void
  onSyncError(callback: (error: Error) => void): void
  
  // Management
  clearOutbox(): Promise<void>
  retryFailedOperations(): Promise<SyncResult>
}
```

---

This comprehensive developer guide provides all the practical information needed to work with the Ultimate Electrical Designer's synchronization and offline systems. For additional support, refer to the design documentation and consult the development team.