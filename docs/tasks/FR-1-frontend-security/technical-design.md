# FR-1 Frontend Security Features - Technical Design Document

**Document Version:** 1.0  
**Created:** August 11, 2025  
**Status:** Technical Design Phase  
**Architecture:** React + Next.js with TypeScript Integration

---

## Table of Contents

1. [Overview & Architecture](#overview--architecture)
2. [UI/UX Workflow Design](#uiux-workflow-design)
3. [Component Architecture](#component-architecture)
4. [State Management Strategy](#state-management-strategy)
5. [API Client Integration](#api-client-integration)
6. [Testing Strategy](#testing-strategy)
7. [Security Considerations](#security-considerations)
8. [Implementation Timeline](#implementation-timeline)

---

## Overview & Architecture

### Project Context

The Ultimate Electrical Designer requires frontend implementation of three critical FR-1 security features:

1. **Account Lockout Protection**: UI feedback for locked accounts with informative messaging
2. **Email Verification Workflow**: Complete signup-to-activation user experience
3. **Enhanced Password Reset**: Secure token-based password recovery interface

### Architecture Alignment

This implementation follows the established **5-layer frontend architecture**:

- **App Router Layer**: Next.js 15 routing with layout support
- **Module Layer**: Domain-specific feature modules (`auth/`)
- **Component Layer**: Reusable UI components with shadcn/ui
- **Hook Layer**: Custom React hooks for business logic
- **API Layer**: Type-safe client with React Query integration

### Technical Foundation

- **Framework**: Next.js 15.4+ with App Router
- **Language**: TypeScript 5.8+ with strict type checking
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS 4.1+ with design tokens
- **State Management**: Zustand 5.0+ for client state, React Query 5.83+ for server state
- **Form Handling**: React Hook Form with Zod 4.0+ validation
- **Testing**: Vitest 3.2+ with React Testing Library and Playwright

---

## UI/UX Workflow Design

### 1. Email Verification Workflow

**User Journey**: Registration → Email Sent → Email Click → Account Activation → Dashboard Access

```mermaid
graph TD
    A[Registration Form] --> B{Form Validation}
    B -->|Valid| C[Submit Registration]
    B -->|Invalid| A
    C --> D[Registration Success Page]
    D --> E[Check Your Email Message]
    E --> F[Email Verification Link]
    F --> G{Token Validation}
    G -->|Valid| H[Account Activated Page]
    G -->|Invalid/Expired| I[Verification Failed Page]
    H --> J[Redirect to Login]
    I --> K[Resend Verification Option]
    K --> E
```

**Key UX Considerations**:

- **Clear Messaging**: Inform users about inactive account status until email verification
- **Email Instructions**: Detailed guidance on checking email and spam folders
- **Resend Functionality**: Allow users to request new verification emails
- **Token Expiration**: Clear communication about 24-hour expiration window
- **Success Feedback**: Confirmation of successful account activation

### 2. Password Reset Workflow

**User Journey**: Forgot Password → Email Entry → Email Sent → Reset Link → New Password → Login

```mermaid
graph TD
    A[Login Page] --> B[Forgot Password Link]
    B --> C[Reset Request Form]
    C --> D{Email Validation}
    D -->|Valid| E[Submit Reset Request]
    D -->|Invalid| C
    E --> F[Reset Email Sent Page]
    F --> G[Email Reset Link]
    G --> H{Token Validation}
    H -->|Valid| I[New Password Form]
    H -->|Invalid/Expired| J[Reset Failed Page]
    I --> K[Password Updated Success]
    J --> L[Request New Reset Link]
    K --> M[Redirect to Login]
    L --> C
```

**Key UX Considerations**:

- **Email Field Focus**: Auto-focus on email input for quick access
- **Security Messaging**: Inform users about 1-hour token expiration
- **Password Strength**: Real-time password strength validation
- **Success Confirmation**: Clear feedback on successful password update
- **Error Handling**: Graceful handling of expired or invalid tokens

### 3. Account Lockout Protection

**User Experience**: Failed Login → Progressive Warnings → Lockout Message → Automatic Unlock

```mermaid
graph TD
    A[Login Attempt] --> B{Credentials Valid}
    B -->|Valid| C[Successful Login]
    B -->|Invalid| D[Failed Attempt Counter]
    D --> E{Attempts Count}
    E -->|< 3| F[Generic Error Message]
    E -->|3-4| G[Warning Message]
    E -->|≥ 5| H[Account Locked Message]
    F --> A
    G --> I[Remaining Attempts Info]
    I --> A
    H --> J[30-Minute Lockout Timer]
    J --> K[Auto-Unlock Notification]
    K --> A
```

**Key UX Considerations**:

- **Progressive Disclosure**: Gradually increase warning severity
- **Attempt Counter**: Show remaining attempts (starting at attempt 3)
- **Lockout Messaging**: Clear explanation of temporary lockout (30 minutes)
- **Timer Display**: Optional countdown timer for lockout expiration
- **Alternative Actions**: Provide password reset option during lockout

---

## Component Architecture

### Module Structure

```
client/src/components/modules/auth/
├── components/
│   ├── EmailVerification/
│   │   ├── EmailVerificationPage.tsx
│   │   ├── EmailSentNotification.tsx
│   │   ├── VerificationFailedPage.tsx
│   │   └── ResendVerificationForm.tsx
│   ├── PasswordReset/
│   │   ├── ForgotPasswordForm.tsx
│   │   ├── ResetEmailSentPage.tsx
│   │   ├── NewPasswordForm.tsx
│   │   └── ResetFailedPage.tsx
│   ├── AccountLockout/
│   │   ├── LockoutWarningBanner.tsx
│   │   ├── AccountLockedMessage.tsx
│   │   └── LockoutTimer.tsx
│   └── Enhanced/
│       ├── LoginFormEnhanced.tsx (updated existing)
│       └── RegistrationFormEnhanced.tsx (updated existing)
├── hooks/
│   ├── useEmailVerification.ts
│   ├── usePasswordReset.ts
│   ├── useAccountLockout.ts
│   └── useSecurityState.ts
├── services/
│   ├── emailVerificationService.ts
│   ├── passwordResetService.ts
│   └── securityService.ts
├── types/
│   ├── emailVerification.ts
│   ├── passwordReset.ts
│   └── security.ts
└── validation/
    ├── emailVerificationSchemas.ts
    ├── passwordResetSchemas.ts
    └── securitySchemas.ts
```

### Core Components

#### 1. Email Verification Components

**EmailVerificationPage.tsx**:
```typescript
interface EmailVerificationPageProps {
  token: string;
  onSuccess: () => void;
  onError: (error: string) => void;
}

export const EmailVerificationPage: React.FC<EmailVerificationPageProps> = ({
  token,
  onSuccess,
  onError
}) => {
  const { verifyEmail, isLoading, error } = useEmailVerification();
  
  // Auto-verify on mount with token
  // Handle success/error states
  // Provide resend verification option
};
```

**EmailSentNotification.tsx**:
```typescript
interface EmailSentNotificationProps {
  email: string;
  onResendClick: () => void;
  canResend: boolean;
  resendCountdown?: number;
}

export const EmailSentNotification: React.FC<EmailSentNotificationProps> = ({
  email,
  onResendClick,
  canResend,
  resendCountdown
}) => {
  // Display email instructions
  // Provide resend functionality with rate limiting
  // Show countdown timer for resend availability
};
```

#### 2. Password Reset Components

**ForgotPasswordForm.tsx**:
```typescript
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address')
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export const ForgotPasswordForm: React.FC = () => {
  const { requestPasswordReset, isLoading } = usePasswordReset();
  const form = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema)
  });
  
  // Handle form submission
  // Provide loading states
  // Navigate to success page
};
```

**NewPasswordForm.tsx**:
```typescript
const newPasswordSchema = z.object({
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
});

export const NewPasswordForm: React.FC<NewPasswordFormProps> = ({
  token,
  onSuccess,
  onError
}) => {
  const { resetPassword, isLoading } = usePasswordReset();
  
  // Handle password reset with token
  // Real-time password strength validation
  // Confirmation matching validation
};
```

#### 3. Account Lockout Components

**LockoutWarningBanner.tsx**:
```typescript
interface LockoutWarningBannerProps {
  attemptsRemaining: number;
  onPasswordResetClick: () => void;
}

export const LockoutWarningBanner: React.FC<LockoutWarningBannerProps> = ({
  attemptsRemaining,
  onPasswordResetClick
}) => {
  // Progressive warning messages
  // Remaining attempts display
  // Password reset suggestion
};
```

**AccountLockedMessage.tsx**:
```typescript
interface AccountLockedMessageProps {
  lockoutExpiresAt: Date;
  onPasswordResetClick: () => void;
}

export const AccountLockedMessage: React.FC<AccountLockedMessageProps> = ({
  lockoutExpiresAt,
  onPasswordResetClick
}) => {
  // Account locked explanation
  // Optional countdown timer
  // Alternative action buttons
};
```

### Enhanced Existing Components

**LoginFormEnhanced.tsx** (extends existing LoginForm):
```typescript
export const LoginFormEnhanced: React.FC = () => {
  const { login, isLoading, error } = useAuth();
  const { lockoutState } = useAccountLockout();
  
  // Existing login functionality
  // + Account lockout state integration
  // + Progressive warning display
  // + Lockout message handling
  // + Password reset navigation
};
```

**RegistrationFormEnhanced.tsx** (extends existing RegistrationForm):
```typescript
export const RegistrationFormEnhanced: React.FC = () => {
  const { register, isLoading } = useAuth();
  const { sendVerificationEmail } = useEmailVerification();
  
  // Existing registration functionality
  // + Email verification workflow integration
  // + Success page navigation
  // + Clear messaging about email verification requirement
};
```

---

## State Management Strategy

### Zustand Store Structure

#### Security State Store

```typescript
// stores/securityStore.ts
interface SecurityState {
  // Account Lockout State
  lockoutState: {
    isLocked: boolean;
    lockoutExpiresAt: Date | null;
    failedAttempts: number;
    attemptsRemaining: number;
  };
  
  // Email Verification State
  emailVerificationState: {
    isVerificationSent: boolean;
    sentToEmail: string | null;
    canResend: boolean;
    resendAvailableAt: Date | null;
  };
  
  // Password Reset State
  passwordResetState: {
    isResetSent: boolean;
    sentToEmail: string | null;
    resetToken: string | null;
    resetExpiresAt: Date | null;
  };
  
  // Actions
  setLockoutState: (state: Partial<SecurityState['lockoutState']>) => void;
  setEmailVerificationState: (state: Partial<SecurityState['emailVerificationState']>) => void;
  setPasswordResetState: (state: Partial<SecurityState['passwordResetState']>) => void;
  resetSecurityState: () => void;
}

export const useSecurityStore = create<SecurityState>((set) => ({
  lockoutState: {
    isLocked: false,
    lockoutExpiresAt: null,
    failedAttempts: 0,
    attemptsRemaining: 5
  },
  emailVerificationState: {
    isVerificationSent: false,
    sentToEmail: null,
    canResend: true,
    resendAvailableAt: null
  },
  passwordResetState: {
    isResetSent: false,
    sentToEmail: null,
    resetToken: null,
    resetExpiresAt: null
  },
  
  setLockoutState: (state) => set((current) => ({
    lockoutState: { ...current.lockoutState, ...state }
  })),
  
  setEmailVerificationState: (state) => set((current) => ({
    emailVerificationState: { ...current.emailVerificationState, ...state }
  })),
  
  setPasswordResetState: (state) => set((current) => ({
    passwordResetState: { ...current.passwordResetState, ...state }
  })),
  
  resetSecurityState: () => set({
    lockoutState: {
      isLocked: false,
      lockoutExpiresAt: null,
      failedAttempts: 0,
      attemptsRemaining: 5
    },
    emailVerificationState: {
      isVerificationSent: false,
      sentToEmail: null,
      canResend: true,
      resendAvailableAt: null
    },
    passwordResetState: {
      isResetSent: false,
      sentToEmail: null,
      resetToken: null,
      resetExpiresAt: null
    }
  })
}));
```

### React Query Integration

#### Custom Hooks with State Management

```typescript
// hooks/useEmailVerification.ts
export const useEmailVerification = () => {
  const { setEmailVerificationState } = useSecurityStore();
  
  const verifyEmailMutation = useMutation({
    mutationFn: ({ token }: { token: string }) => 
      apiClient.auth.verifyEmail(token),
    onSuccess: () => {
      setEmailVerificationState({
        isVerificationSent: false,
        sentToEmail: null
      });
      // Navigate to success page or login
    },
    onError: (error) => {
      // Handle verification failure
    }
  });
  
  const resendVerificationMutation = useMutation({
    mutationFn: ({ email }: { email: string }) => 
      apiClient.auth.resendVerification(email),
    onSuccess: (_, variables) => {
      setEmailVerificationState({
        isVerificationSent: true,
        sentToEmail: variables.email,
        canResend: false,
        resendAvailableAt: new Date(Date.now() + 60000) // 1 minute
      });
    }
  });
  
  return {
    verifyEmail: verifyEmailMutation.mutate,
    resendVerification: resendVerificationMutation.mutate,
    isLoading: verifyEmailMutation.isPending || resendVerificationMutation.isPending,
    error: verifyEmailMutation.error || resendVerificationMutation.error
  };
};
```

```typescript
// hooks/useAccountLockout.ts
export const useAccountLockout = () => {
  const { lockoutState, setLockoutState } = useSecurityStore();
  
  const handleLoginFailure = useCallback((errorResponse: any) => {
    if (errorResponse?.data?.lockout_info) {
      const { failed_attempts, locked_until, attempts_remaining } = errorResponse.data.lockout_info;
      
      setLockoutState({
        failedAttempts: failed_attempts,
        attemptsRemaining: attempts_remaining,
        isLocked: !!locked_until,
        lockoutExpiresAt: locked_until ? new Date(locked_until) : null
      });
    }
  }, [setLockoutState]);
  
  const handleLoginSuccess = useCallback(() => {
    setLockoutState({
      failedAttempts: 0,
      attemptsRemaining: 5,
      isLocked: false,
      lockoutExpiresAt: null
    });
  }, [setLockoutState]);
  
  return {
    lockoutState,
    handleLoginFailure,
    handleLoginSuccess
  };
};
```

### Error Handling Strategy

```typescript
// utils/errorHandling.ts
export const handleAuthError = (error: any, context: 'login' | 'registration' | 'verification' | 'reset') => {
  const { setLockoutState, setEmailVerificationState, setPasswordResetState } = useSecurityStore.getState();
  
  switch (context) {
    case 'login':
      if (error?.data?.code === 'ACCOUNT_LOCKED') {
        setLockoutState({
          isLocked: true,
          lockoutExpiresAt: new Date(error.data.locked_until),
          failedAttempts: error.data.failed_attempts,
          attemptsRemaining: 0
        });
      }
      break;
    
    case 'verification':
      if (error?.data?.code === 'TOKEN_EXPIRED') {
        // Handle expired verification token
        setEmailVerificationState({ canResend: true });
      }
      break;
    
    case 'reset':
      if (error?.data?.code === 'TOKEN_EXPIRED') {
        // Handle expired reset token
        setPasswordResetState({ resetToken: null, resetExpiresAt: null });
      }
      break;
  }
  
  return {
    message: error?.data?.message || 'An error occurred',
    code: error?.data?.code,
    context
  };
};
```

---

## API Client Integration

### Type-Safe API Client Functions

#### Email Verification API

```typescript
// lib/api/endpoints/emailVerification.ts
export interface EmailVerificationRequest {
  token: string;
}

export interface EmailVerificationResponse {
  success: boolean;
  message: string;
  user_activated: boolean;
}

export interface ResendVerificationRequest {
  email: string;
}

export interface ResendVerificationResponse {
  success: boolean;
  message: string;
  email_sent: boolean;
}

export const emailVerificationEndpoints = {
  verifyEmail: {
    url: '/api/v1/auth/verify-email',
    method: 'POST' as const,
    schema: z.object({
      token: z.string().min(1, 'Verification token is required')
    })
  },
  
  resendVerification: {
    url: '/api/v1/auth/resend-verification',
    method: 'POST' as const,
    schema: z.object({
      email: z.string().email('Valid email address is required')
    })
  }
};
```

#### Password Reset API

```typescript
// lib/api/endpoints/passwordReset.ts
export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetResponse {
  success: boolean;
  message: string;
  reset_email_sent: boolean;
}

export interface ResetPasswordRequest {
  token: string;
  new_password: string;
}

export interface ResetPasswordResponse {
  success: boolean;
  message: string;
  password_updated: boolean;
}

export const passwordResetEndpoints = {
  requestReset: {
    url: '/api/v1/auth/request-password-reset',
    method: 'POST' as const,
    schema: z.object({
      email: z.string().email('Valid email address is required')
    })
  },
  
  resetPassword: {
    url: '/api/v1/auth/reset-password',
    method: 'POST' as const,
    schema: z.object({
      token: z.string().min(1, 'Reset token is required'),
      new_password: z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number')
    })
  }
};
```

#### Enhanced Authentication API

```typescript
// lib/api/endpoints/authEnhanced.ts (extends existing auth.ts)
export interface LoginResponseEnhanced extends LoginResponse {
  lockout_info?: {
    failed_attempts: number;
    attempts_remaining: number;
    locked_until?: string;
  };
}

export interface RegistrationResponseEnhanced extends RegistrationResponse {
  email_verification_required: boolean;
  verification_email_sent: boolean;
}

// Enhanced login endpoint with lockout info
export const enhancedAuthEndpoints = {
  loginEnhanced: {
    url: '/api/v1/auth/login',
    method: 'POST' as const,
    schema: loginSchema,
    responseSchema: z.object({
      access_token: z.string().optional(),
      token_type: z.string().optional(),
      user: userSchema.optional(),
      message: z.string().optional(),
      lockout_info: z.object({
        failed_attempts: z.number(),
        attempts_remaining: z.number(),
        locked_until: z.string().optional()
      }).optional()
    })
  },
  
  registerEnhanced: {
    url: '/api/v1/auth/register',
    method: 'POST' as const,
    schema: registrationSchema,
    responseSchema: z.object({
      user: userSchema,
      message: z.string(),
      email_verification_required: z.boolean(),
      verification_email_sent: z.boolean()
    })
  }
};
```

### API Client Service Integration

```typescript
// services/securityService.ts
export class SecurityService {
  constructor(private apiClient: ApiClient) {}
  
  async verifyEmail(token: string): Promise<EmailVerificationResponse> {
    return this.apiClient.post(emailVerificationEndpoints.verifyEmail.url, { token });
  }
  
  async resendVerification(email: string): Promise<ResendVerificationResponse> {
    return this.apiClient.post(emailVerificationEndpoints.resendVerification.url, { email });
  }
  
  async requestPasswordReset(email: string): Promise<PasswordResetResponse> {
    return this.apiClient.post(passwordResetEndpoints.requestReset.url, { email });
  }
  
  async resetPassword(token: string, new_password: string): Promise<ResetPasswordResponse> {
    return this.apiClient.post(passwordResetEndpoints.resetPassword.url, { token, new_password });
  }
  
  async loginWithLockoutInfo(email: string, password: string): Promise<LoginResponseEnhanced> {
    return this.apiClient.post(enhancedAuthEndpoints.loginEnhanced.url, { email, password });
  }
  
  async registerWithVerification(userData: RegistrationRequest): Promise<RegistrationResponseEnhanced> {
    return this.apiClient.post(enhancedAuthEndpoints.registerEnhanced.url, userData);
  }
}
```

---

## Testing Strategy

### Unit Testing Approach

#### Component Testing with Vitest

```typescript
// __tests__/EmailVerificationPage.test.tsx
describe('EmailVerificationPage', () => {
  beforeEach(() => {
    useSecurityStore.getState().resetSecurityState();
  });
  
  it('should auto-verify email on mount with valid token', async () => {
    const mockVerifyEmail = vi.fn().mockResolvedValue({ success: true });
    vi.mocked(useEmailVerification).mockReturnValue({
      verifyEmail: mockVerifyEmail,
      isLoading: false,
      error: null
    });
    
    render(<EmailVerificationPage token="valid-token" onSuccess={vi.fn()} onError={vi.fn()} />);
    
    await waitFor(() => {
      expect(mockVerifyEmail).toHaveBeenCalledWith('valid-token');
    });
  });
  
  it('should display error message for invalid token', async () => {
    vi.mocked(useEmailVerification).mockReturnValue({
      verifyEmail: vi.fn(),
      isLoading: false,
      error: new Error('Invalid token')
    });
    
    render(<EmailVerificationPage token="invalid-token" onSuccess={vi.fn()} onError={vi.fn()} />);
    
    expect(screen.getByText(/verification failed/i)).toBeInTheDocument();
  });
});
```

```typescript
// __tests__/ForgotPasswordForm.test.tsx
describe('ForgotPasswordForm', () => {
  it('should validate email format', async () => {
    render(<ForgotPasswordForm />);
    
    const emailInput = screen.getByLabelText(/email/i);
    const submitButton = screen.getByRole('button', { name: /reset password/i });
    
    await user.type(emailInput, 'invalid-email');
    await user.click(submitButton);
    
    expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument();
  });
  
  it('should submit form with valid email', async () => {
    const mockRequestReset = vi.fn().mockResolvedValue({ success: true });
    vi.mocked(usePasswordReset).mockReturnValue({
      requestPasswordReset: mockRequestReset,
      isLoading: false,
      error: null
    });
    
    render(<ForgotPasswordForm />);
    
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.click(screen.getByRole('button', { name: /reset password/i }));
    
    await waitFor(() => {
      expect(mockRequestReset).toHaveBeenCalledWith('<EMAIL>');
    });
  });
});
```

#### Hook Testing with React Hooks Testing Library

```typescript
// __tests__/useAccountLockout.test.ts
describe('useAccountLockout', () => {
  beforeEach(() => {
    useSecurityStore.getState().resetSecurityState();
  });
  
  it('should handle login failure with lockout info', () => {
    const { result } = renderHook(() => useAccountLockout());
    
    const errorResponse = {
      data: {
        lockout_info: {
          failed_attempts: 3,
          attempts_remaining: 2,
          locked_until: null
        }
      }
    };
    
    act(() => {
      result.current.handleLoginFailure(errorResponse);
    });
    
    expect(result.current.lockoutState.failedAttempts).toBe(3);
    expect(result.current.lockoutState.attemptsRemaining).toBe(2);
    expect(result.current.lockoutState.isLocked).toBe(false);
  });
  
  it('should handle account lockout', () => {
    const { result } = renderHook(() => useAccountLockout());
    
    const errorResponse = {
      data: {
        lockout_info: {
          failed_attempts: 5,
          attempts_remaining: 0,
          locked_until: new Date(Date.now() + 30 * 60 * 1000).toISOString()
        }
      }
    };
    
    act(() => {
      result.current.handleLoginFailure(errorResponse);
    });
    
    expect(result.current.lockoutState.isLocked).toBe(true);
    expect(result.current.lockoutState.lockoutExpiresAt).toBeInstanceOf(Date);
  });
});
```

### Integration Testing

#### MSW (Mock Service Worker) Setup

```typescript
// mocks/handlers/securityHandlers.ts
export const securityHandlers = [
  http.post('/api/v1/auth/verify-email', async ({ request }) => {
    const { token } = await request.json();
    
    if (token === 'valid-token') {
      return HttpResponse.json({
        success: true,
        message: 'Email verified successfully',
        user_activated: true
      });
    }
    
    if (token === 'expired-token') {
      return HttpResponse.json({
        success: false,
        message: 'Verification token has expired',
        code: 'TOKEN_EXPIRED'
      }, { status: 400 });
    }
    
    return HttpResponse.json({
      success: false,
      message: 'Invalid verification token',
      code: 'INVALID_TOKEN'
    }, { status: 400 });
  }),
  
  http.post('/api/v1/auth/request-password-reset', async ({ request }) => {
    const { email } = await request.json();
    
    return HttpResponse.json({
      success: true,
      message: 'Password reset email sent',
      reset_email_sent: true
    });
  }),
  
  http.post('/api/v1/auth/login', async ({ request }) => {
    const { email, password } = await request.json();
    
    // Simulate failed attempts
    if (email === '<EMAIL>') {
      return HttpResponse.json({
        message: 'Account temporarily locked',
        lockout_info: {
          failed_attempts: 5,
          attempts_remaining: 0,
          locked_until: new Date(Date.now() + 30 * 60 * 1000).toISOString()
        }
      }, { status: 401 });
    }
    
    if (email === '<EMAIL>') {
      return HttpResponse.json({
        message: 'Invalid credentials',
        lockout_info: {
          failed_attempts: 3,
          attempts_remaining: 2
        }
      }, { status: 401 });
    }
    
    // Normal success response
    return HttpResponse.json({
      access_token: 'mock-token',
      token_type: 'Bearer',
      user: { id: 1, email, name: 'Test User' }
    });
  })
];
```

### End-to-End Testing with Playwright

```typescript
// tests/e2e/auth-security.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Email Verification Flow', () => {
  test('should complete full email verification workflow', async ({ page }) => {
    // Navigate to registration
    await page.goto('/auth/register');
    
    // Fill registration form
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'TestPassword123');
    await page.fill('[data-testid="name-input"]', 'Test User');
    await page.click('[data-testid="submit-button"]');
    
    // Should navigate to email sent page
    await expect(page).toHaveURL('/auth/email-sent');
    await expect(page.locator('[data-testid="email-sent-message"]')).toContainText('Check your email');
    
    // Simulate clicking verification link (token in URL)
    await page.goto('/auth/verify-email?token=valid-verification-token');
    
    // Should show success message and redirect to login
    await expect(page.locator('[data-testid="verification-success"]')).toBeVisible();
    await expect(page).toHaveURL('/auth/login');
  });
});

test.describe('Password Reset Flow', () => {
  test('should complete password reset workflow', async ({ page }) => {
    // Start at login page
    await page.goto('/auth/login');
    await page.click('[data-testid="forgot-password-link"]');
    
    // Request password reset
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.click('[data-testid="submit-button"]');
    
    // Should show reset email sent page
    await expect(page).toHaveURL('/auth/reset-sent');
    await expect(page.locator('[data-testid="reset-sent-message"]')).toBeVisible();
    
    // Simulate clicking reset link
    await page.goto('/auth/reset-password?token=valid-reset-token');
    
    // Set new password
    await page.fill('[data-testid="new-password-input"]', 'NewPassword123');
    await page.fill('[data-testid="confirm-password-input"]', 'NewPassword123');
    await page.click('[data-testid="submit-button"]');
    
    // Should show success and redirect to login
    await expect(page.locator('[data-testid="reset-success"]')).toBeVisible();
    await expect(page).toHaveURL('/auth/login');
  });
});

test.describe('Account Lockout Protection', () => {
  test('should show progressive warnings and lockout', async ({ page }) => {
    await page.goto('/auth/login');
    
    // First two attempts - no warning
    for (let i = 0; i < 2; i++) {
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrong-password');
      await page.click('[data-testid="submit-button"]');
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials');
    }
    
    // Third attempt - should show warning
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrong-password');
    await page.click('[data-testid="submit-button"]');
    await expect(page.locator('[data-testid="lockout-warning"]')).toContainText('attempts remaining');
    
    // Fifth attempt - should show lockout
    for (let i = 0; i < 2; i++) {
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="password-input"]', 'wrong-password');
      await page.click('[data-testid="submit-button"]');
    }
    
    await expect(page.locator('[data-testid="account-locked"]')).toContainText('temporarily locked');
    await expect(page.locator('[data-testid="lockout-timer"]')).toBeVisible();
  });
});
```

### Testing Coverage Requirements

- **Unit Tests**: 100% coverage for all security-related hooks and components
- **Integration Tests**: Complete workflow testing with MSW mocking
- **E2E Tests**: Full user journey validation with Playwright
- **Security Tests**: Token validation, error handling, and edge cases
- **Accessibility Tests**: WCAG compliance for all new components
- **Performance Tests**: Component rendering and state update performance

---

## Security Considerations

### Frontend Security Implementation

#### Token Handling Security

```typescript
// utils/tokenSecurity.ts
export const secureTokenHandling = {
  // Never store sensitive tokens in localStorage
  // Use secure HTTP-only cookies when possible
  // Implement token validation before API calls
  
  validateToken: (token: string): boolean => {
    if (!token || token.length < 32) return false;
    // Additional validation logic
    return true;
  },
  
  handleTokenExpiration: (error: any): void => {
    if (error?.data?.code === 'TOKEN_EXPIRED') {
      // Clear any stored token references
      // Navigate to appropriate error page
      // Provide clear user messaging
    }
  },
  
  sanitizeTokenForDisplay: (token: string): string => {
    // Never display full tokens in UI
    return `${token.slice(0, 4)}...${token.slice(-4)}`;
  }
};
```

#### Input Validation Security

```typescript
// validation/securitySchemas.ts
export const securityValidationSchemas = {
  emailVerificationToken: z.string()
    .min(32, 'Invalid token format')
    .max(128, 'Invalid token format')
    .regex(/^[A-Za-z0-9_-]+$/, 'Invalid token characters'),
  
  passwordResetToken: z.string()
    .min(32, 'Invalid token format')
    .max(128, 'Invalid token format')
    .regex(/^[A-Za-z0-9_-]+$/, 'Invalid token characters'),
  
  newPassword: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password too long')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain uppercase, lowercase, and number')
    .refine((password) => !commonPasswords.includes(password), 'Please choose a stronger password'),
  
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address too long')
    .refine((email) => !email.includes('<script>'), 'Invalid email format')
};
```

#### Error Message Security

```typescript
// utils/secureErrorHandling.ts
export const secureErrorMessages = {
  // Never expose sensitive system information
  // Provide helpful but non-revealing error messages
  // Log detailed errors server-side only
  
  sanitizeErrorMessage: (error: any): string => {
    const secureMessages = {
      'TOKEN_EXPIRED': 'Your verification link has expired. Please request a new one.',
      'INVALID_TOKEN': 'This verification link is invalid. Please check your email for the correct link.',
      'ACCOUNT_LOCKED': 'Your account has been temporarily locked for security. Please try again in 30 minutes.',
      'EMAIL_NOT_FOUND': 'If an account exists with this email, a reset link will be sent.',
      'VERIFICATION_FAILED': 'Email verification failed. Please try again or request a new verification email.',
      'RATE_LIMITED': 'Too many requests. Please wait before trying again.'
    };
    
    return secureMessages[error?.data?.code] || 'An error occurred. Please try again.';
  },
  
  getProgressiveWarningMessage: (attemptsRemaining: number): string => {
    if (attemptsRemaining === 2) {
      return 'Invalid credentials. You have 2 attempts remaining before your account is temporarily locked.';
    }
    if (attemptsRemaining === 1) {
      return 'Invalid credentials. You have 1 attempt remaining before your account is temporarily locked.';
    }
    return 'Invalid credentials. Please check your email and password.';
  }
};
```

### Client-Side Security Measures

- **XSS Prevention**: Proper sanitization of all user inputs and URL parameters
- **CSRF Protection**: Token validation for all state-changing operations
- **Information Disclosure**: No exposure of sensitive system information in error messages
- **Rate Limiting**: Client-side throttling for resend operations and form submissions
- **Token Security**: Secure handling and storage of verification and reset tokens
- **Password Security**: Strong password requirements with real-time validation

---

## Implementation Timeline

### Phase 1: Foundation Setup (Week 1)

**Day 1-2: Project Structure Setup**
- Create module directory structure
- Set up TypeScript types and interfaces
- Implement Zod validation schemas
- Configure testing setup with MSW handlers

**Day 3-4: State Management Implementation**
- Create Zustand security state store
- Implement custom hooks for security features
- Add error handling utilities
- Set up API client integration

**Day 5-7: Core Component Development**
- Build email verification components
- Create password reset components
- Implement account lockout components
- Basic component testing setup

### Phase 2: Feature Implementation (Week 2)

**Day 1-3: Email Verification Workflow**
- EmailVerificationPage with token validation
- EmailSentNotification with resend functionality
- VerificationFailedPage with error handling
- Integration with registration flow

**Day 4-5: Password Reset Workflow**
- ForgotPasswordForm with validation
- NewPasswordForm with strength validation
- ResetEmailSentPage and error pages
- Integration with login flow

**Day 6-7: Account Lockout Protection**
- LockoutWarningBanner with progressive messaging
- AccountLockedMessage with timer
- Integration with login form
- Comprehensive error handling

### Phase 3: Integration & Testing (Week 3)

**Day 1-3: Component Integration**
- Enhanced login and registration forms
- Complete workflow integration
- State management integration
- API client integration testing

**Day 4-5: Unit Testing**
- Component unit tests with Vitest
- Hook testing with React Testing Library
- State management testing
- Validation schema testing

**Day 6-7: Integration Testing**
- MSW integration testing
- Complete workflow testing
- Error scenario testing
- Performance optimization

### Phase 4: E2E Testing & Polish (Week 4)

**Day 1-3: End-to-End Testing**
- Playwright E2E test implementation
- Cross-browser compatibility testing
- Accessibility testing
- Performance testing

**Day 4-5: Security Validation**
- Security testing and validation
- Token handling security audit
- Error message security review
- Input validation testing

**Day 6-7: Final Polish**
- UI/UX refinements
- Loading state improvements
- Error message optimization
- Documentation completion

### Success Criteria

- ✅ All 14 new components implemented and tested
- ✅ 100% TypeScript compliance with strict mode
- ✅ 100% test coverage for security-related logic
- ✅ All E2E workflows passing in Playwright
- ✅ Zero accessibility violations (WCAG 2.1 AA)
- ✅ Performance benchmarks met (<100ms component render)
- ✅ Security audit completed with zero vulnerabilities
- ✅ Complete integration with backend security features

---

This technical design provides a comprehensive blueprint for implementing the frontend security features while maintaining the project's high standards for code quality, testing, and security.