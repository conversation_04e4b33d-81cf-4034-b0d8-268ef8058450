# Task Plan for Component Management Module

This document outlines the phased implementation plan for adding CRUD (Create, Read, Update, Delete) functionality for
`Component`, `ComponentType`, and `ComponentCategory` based on the provided technical design. The plan is broken down
into small, time-boxed tasks, adhering to the 5-Phase Implementation Methodology and project standards.

---

## 🛠️ Phase: Implementation

This phase focuses on the TDD-driven development of both backend and frontend components. All tasks must follow a
Test-Driven Development (TDD) approach.

### Sub-Phase: Backend

| Task ID   | Description                                                                                                                                                       | Estimated Time | Dependencies | References                                   |
| :-------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :----------- | :------------------------------------------- |
| **B-1.1** | Create TDD tests for the `ComponentCategory` SQLAlchemy model and Pydantic schemas. Write the corresponding model and schemas, ensuring **100% MyPy compliance**. | 30 minutes     | None         | [design.md](design.md), [rules.md](rules.md) |
| **B-1.2** | Implement the `ComponentCategory` repository layer with TDD, including all necessary methods for CRUD operations.                                                 | 30 minutes     | B-1.1        | [design.md](design.md), [rules.md](rules.md) |
| **B-1.3** | Implement the `ComponentCategory` service layer with TDD, including business logic and **unified error handling**.                                                | 30 minutes     | B-1.2        | [design.md](design.md), [rules.md](rules.md) |
| **B-1.4** | Use the `CRUD Endpoint Factory` to create the FastAPI router for `ComponentCategory`, with TDD tests for the API routes.                                          | 30 minutes     | B-1.3        | [design.md](design.md), [rules.md](rules.md) |
| **B-2.1** | Create TDD tests for the `ComponentType` SQLAlchemy model and Pydantic schemas. Write the corresponding model and schemas, ensuring **100% MyPy compliance**.     | 30 minutes     | B-1.4        | [design.md](design.md), [rules.md](rules.md) |
| **B-2.2** | Implement the `ComponentType` repository layer with TDD, including all necessary methods for CRUD operations.                                                     | 30 minutes     | B-2.1        | [design.md](design.md), [rules.md](rules.md) |
| **B-2.3** | Implement the `ComponentType` service layer with TDD, including business logic and **unified error handling**.                                                    | 30 minutes     | B-2.2        | [design.md](design.md), [rules.md](rules.md) |
| **B-2.4** | Use the `CRUD Endpoint Factory` to create the FastAPI router for `ComponentType`, with TDD tests for the API routes.                                              | 30 minutes     | B-2.3        | [design.md](design.md), [rules.md](rules.md) |
| **B-3.1** | Create TDD tests for the `Component` SQLAlchemy model and Pydantic schemas. Write the corresponding model and schemas, ensuring **100% MyPy compliance**.         | 30 minutes     | B-2.4        | [design.md](design.md), [rules.md](rules.md) |
| **B-3.2** | Implement the `Component` repository layer with TDD, including all necessary methods for CRUD operations.                                                         | 30 minutes     | B-3.1        | [design.md](design.md), [rules.md](rules.md) |
| **B-3.3** | Implement the `Component` service layer with TDD, including business logic and **unified error handling**.                                                        | 30 minutes     | B-3.2        | [design.md](design.md), [rules.md](rules.md) |
| **B-3.4** | Use the `CRUD Endpoint Factory` to create the FastAPI router for `Component`, with TDD tests for the API routes.                                                  | 30 minutes     | B-3.3        | [design.md](design.md), [rules.md](rules.md) |
| **B-3.5** | Generate Alembic migration scripts to update the database schema with the new tables for `Component`, `ComponentType`, and `ComponentCategory`.                   | 30 minutes     | B-3.4        | [tasks.md](tasks.md)                         |

### Sub-Phase: Frontend

| Task ID   | Description                                                                                                                                                                                             | Estimated Time | Dependencies        | References                                                                                     |
| :-------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ | :------------- | :------------------ | :--------------------------------------------------------------------------------------------- |
| **F-1.1** | Create the `ComponentCategory` data table page and related components using shadcn-ui and the provided `data-table-ref.tsx`.                                                                            | 30 minutes     | B-1.4               | [data-table-ref.tsx](data-table-ref.tsx), [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md) |
| **F-1.2** | Implement **React Query hooks** for fetching, creating, updating, and deleting `ComponentCategory` data via the new API.                                                                                | 30 minutes     | F-1.1               | [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md)                                           |
| **F-1.3** | Create forms for `ComponentCategory` creation and updates, implementing **client-side validation** with Zod and React Hook Form.                                                                        | 30 minutes     | F-1.2               | [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md)                                           |
| **F-2.1** | Create the `ComponentType` data table page and related components using shadcn-ui and the provided `data-table-ref.tsx`.                                                                                | 30 minutes     | B-2.4               | [data-table-ref.tsx](data-table-ref.tsx), [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md) |
| **F-2.2** | Implement **React Query hooks** for fetching, creating, updating, and deleting `ComponentType` data via the new API.                                                                                    | 30 minutes     | F-2.1               | [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md)                                           |
| **F-2.3** | Create forms for `ComponentType` creation and updates, implementing **client-side validation** with Zod and React Hook Form.                                                                            | 30 minutes     | F-2.2               | [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md)                                           |
| **F-3.1** | Create the main `Component` management page and data table using shadcn-ui.                                                                                                                             | 30 minutes     | B-3.4, F-1.3, F-2.3 | [data-table-ref.tsx](data-table-ref.tsx), [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md) |
| **F-3.2** | Implement **React Query hooks** for fetching, creating, updating, and deleting `Component` data via the new API.                                                                                        | 30 minutes     | F-3.1               | [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md)                                           |
| **F-3.3** | Create forms for `Component` creation and updates, including drop-downs populated with `ComponentType` and `ComponentCategory` data. Implement **client-side validation** with Zod and React Hook Form. | 30 minutes     | F-3.2               | [tech.md](tech.md), [TECH-DESIGN.md](TECH-DESIGN.md)                                           |

---

## 🧪 Phase: Verification

This phase ensures all newly implemented features meet the project's quality standards before deployment.

### Sub-Phase: Testing

| Task ID   | Description                                                                                                              | Estimated Time | Dependencies | References                                                 |
| :-------- | :----------------------------------------------------------------------------------------------------------------------- | :------------- | :----------- | :--------------------------------------------------------- |
| **V-1.1** | Run the full backend test suite with `pytest`. Ensure all TDD-written tests pass and all existing tests are still valid. | 30 minutes     | B-3.5        | [TESTING.md](TESTING.md)                                   |
| **V-1.2** | Run `mypy`, `ruff`, and `black` on the `server/` directory to enforce code quality rules.                                | 30 minutes     | V-1.1        | [rules.md](rules.md), [TESTING.md](TESTING.md)             |
| **V-1.3** | Write and run component-level tests with `Vitest` and `React Testing Library` for the new frontend components.           | 30 minutes     | F-3.3        | [TESTING.md](TESTING.md), [TECH-DESIGN.md](TECH-DESIGN.md) |
| **V-1.4** | Write and run end-to-end tests with `Playwright` to test the full CRUD workflow for all three models.                    | 30 minutes     | V-1.3        | [TESTING.md](TESTING.md), [TECH-DESIGN.md](TECH-DESIGN.md) |
| **V-1.5** | Run the full frontend test suite with `pnpm run test` to ensure all tests pass and coverage is maintained.               | 30 minutes     | V-1.4        | [TESTING.md](TESTING.md)                                   |
| **V-1.6** | Run `pnpm run lint` and `pnpm run type-check` to ensure there are no linting or TypeScript errors.                       | 30 minutes     | V-1.5        | [rules.md](rules.md)                                       |

### Sub-Phase: Quality Checks

| Task ID   | Description                                                                                                                                   | Estimated Time | Dependencies | References           |
| :-------- | :-------------------------------------------------------------------------------------------------------------------------------------------- | :------------- | :----------- | :------------------- |
| **V-2.1** | Perform a final quality check to ensure all **Zero Tolerance Policies** are met, including code style, security, and documentation standards. | 30 minutes     | V-1.6        | [rules.md](rules.md) |

---

## 📚 Phase: Documentation & Handover

This phase ensures the new features are properly documented for future development and maintenance.

### Sub-Phase: Documentation

| Task ID   | Description                                                                                     | Estimated Time | Dependencies | References                                   |
| :-------- | :---------------------------------------------------------------------------------------------- | :------------- | :----------- | :------------------------------------------- |
| **D-1.1** | Update the `README.md` and `docs/` folder with a brief summary of the new CRUD functionality.   | 30 minutes     | V-2.1        | [README.md](README.md)                       |
| **D-1.2** | Review and finalize API documentation generated by FastAPI for the new endpoints.               | 30 minutes     | D-1.1        | [design.md](design.md)                       |
| **D-1.3** | Update the `design.md` and `tasks.md` to reflect the completed work and implementation details. | 30 minutes     | D-1.2        | [design.md](design.md), [tasks.md](tasks.md) |
