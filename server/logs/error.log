2025-07-08 00:08:59.888 | ERROR    | MainProcess:MainThread | __main__:migrate:95 - Error during database migration: Could not find alembic.ini or backend directory
2025-07-08 00:11:19.674 | ERROR    | MainProcess:MainThread | __main__:migrate:95 - Error during database migration: Could not find alembic.ini or backend directory
2025-07-08 00:12:02.874 | ERROR    | MainProcess:MainThread | __main__:migrate:97 - Error during database migration: Could not find alembic.ini or backend directory
2025-07-08 00:16:38.808 | ERROR    | MainProcess:MainThread | __main__:migrate:93 - Error during database migration: Could not find alembic.ini or backend directory
2025-07-08 02:09:09.379 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:09:10.364 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:09:17.206 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:09:17.768 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 02:09:19.346 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 02:09:19.372 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 02:09:19.377 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 02:09:19.409 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 02:09:19.416 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 02:09:19.441 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 02:09:19.443 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 02:09:19.454 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 02:09:19.464 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 02:09:19.468 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 02:09:19.476 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 02:09:19.477 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 02:09:19.506 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 02:09:19.514 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 02:09:19.524 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 02:09:19.528 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 02:09:19.535 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 02:09:19.583 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 02:10:37.956 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:10:38.866 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:10:45.456 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:10:46.014 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 02:10:47.517 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 02:10:47.538 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 02:10:47.542 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 02:10:47.565 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 02:10:47.570 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 02:10:47.587 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 02:10:47.588 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 02:10:47.596 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 02:10:47.604 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 02:10:47.608 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 02:10:47.613 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 02:10:47.613 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 02:10:47.634 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 02:10:47.639 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 02:10:47.645 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 02:10:47.649 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 02:10:47.656 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 02:10:47.705 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 02:11:56.088 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:11:56.967 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:12:03.829 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:17:58.518 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:17:59.480 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:18:06.338 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:27:16.390 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:27:16.661 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:27:19.791 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:28:12.964 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:28:13.233 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:28:13.466 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:28:13.467 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:28:13.468 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:28:16.162 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:28:49.129 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:28:49.290 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:28:49.433 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:28:49.434 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:28:49.435 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:28:51.750 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:31:31.874 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:31:32.149 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:31:32.382 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:31:32.383 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:31:32.385 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:31:35.018 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:31:54.562 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:31:54.820 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:31:55.049 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:31:55.050 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:31:55.052 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:31:57.689 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:37:50.356 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:37:50.646 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:37:50.901 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:37:50.902 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:37:50.904 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:37:53.700 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:39:10.892 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:39:11.184 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:39:11.426 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:39:11.428 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:39:11.429 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:39:14.185 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:39:46.915 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:39:47.174 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:39:47.390 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:39:47.392 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:39:47.393 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:39:50.168 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:40:01.861 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:40:02.126 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:40:02.406 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:40:02.408 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:40:02.409 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:40:05.250 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:43:31.992 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:43:32.278 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:43:32.512 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:43:32.513 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:43:32.515 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:43:35.218 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:45:04.855 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:45:05.118 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:45:05.352 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:45:05.353 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:45:05.355 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:45:08.069 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:47:33.792 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:47:34.054 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:47:34.266 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:47:34.267 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:47:34.268 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:47:36.910 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:48:25.492 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:48:25.755 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:48:25.986 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:48:25.987 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:48:25.989 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:48:28.568 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:50:03.402 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:50:03.655 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:50:03.884 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:50:03.885 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:50:03.886 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:50:06.774 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:51:25.914 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:51:26.172 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:51:26.389 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:51:26.390 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:51:26.392 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:51:29.141 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:52:26.245 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:52:26.511 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:52:26.742 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:52:26.743 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:52:26.744 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:52:29.410 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:52:29.871 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: Malicious content detected
2025-07-08 02:53:30.791 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:53:31.100 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:53:31.338 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:53:31.339 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:53:31.341 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:53:34.293 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:53:34.641 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 02:54:35.816 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:54:36.083 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:54:36.309 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:54:36.310 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:54:36.312 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:54:39.087 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:54:39.399 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 02:54:39.451 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 02:55:41.551 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:55:41.811 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:55:42.039 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:55:42.040 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:55:42.042 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:55:44.892 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:55:45.192 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 02:55:45.234 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 02:56:44.574 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:56:44.871 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:56:45.119 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:56:45.120 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:56:45.122 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:56:47.970 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:56:48.278 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 02:56:48.322 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 02:56:48.348 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 02:57:51.755 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:57:52.009 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:57:52.231 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:57:52.232 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:57:52.234 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:57:54.839 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:57:55.144 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 02:57:55.183 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 02:57:55.207 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 02:59:07.682 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:59:07.933 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 02:59:08.152 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 02:59:08.153 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 02:59:08.155 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:59:10.846 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 02:59:11.174 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 02:59:11.214 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 02:59:11.237 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 03:00:38.346 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 03:00:38.595 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 03:00:38.820 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 03:00:38.821 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 03:00:38.823 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 03:00:41.589 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 03:00:41.877 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 03:00:41.916 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 03:00:41.940 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 03:01:49.450 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 03:01:49.707 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 03:01:49.948 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 03:01:49.949 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 03:01:49.951 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 03:01:52.550 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 03:01:52.844 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 03:01:52.883 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 03:01:52.906 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 10:14:48.386 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 10:14:48.680 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 10:14:48.890 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 10:14:48.891 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 10:14:48.893 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 10:14:51.471 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 10:14:51.759 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 10:14:51.797 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 10:14:51.820 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 10:14:52.493 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 10:14:54.507 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 10:14:54.527 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 10:14:54.531 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 10:14:54.551 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 10:14:54.555 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 10:14:54.573 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 10:14:54.573 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 10:14:54.581 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 10:14:54.588 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 10:14:54.592 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 10:14:54.597 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 10:14:54.598 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 10:14:54.617 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 10:14:54.622 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 10:14:54.629 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 10:14:54.632 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 10:14:54.638 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 10:14:54.686 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 10:43:19.909 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 10:43:19.928 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 10:43:19.932 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 10:43:19.952 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 10:43:19.956 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 10:43:19.972 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 10:43:19.973 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 10:43:19.981 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 10:43:19.988 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 10:43:19.991 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 10:43:19.997 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 10:43:19.997 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 10:43:20.016 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 10:43:20.020 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 10:43:20.026 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 10:43:20.030 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 10:43:20.035 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 10:43:20.082 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 10:43:20.238 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 10:43:20.479 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 10:43:20.690 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 10:43:20.691 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 10:43:20.693 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 10:43:23.478 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 10:43:23.766 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 10:43:23.802 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 10:43:23.826 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 10:43:24.354 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 11:04:53.650 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 11:04:53.669 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 11:04:53.673 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 11:04:53.692 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 11:04:53.696 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 11:04:53.714 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 11:04:53.715 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 11:04:53.722 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 11:04:53.729 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 11:04:53.732 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 11:04:53.737 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 11:04:53.738 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 11:04:53.756 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 11:04:53.760 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 11:04:53.766 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 11:04:53.769 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 11:04:53.775 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 11:04:53.820 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 11:04:53.973 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 11:04:54.207 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 11:04:54.406 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 11:04:54.407 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 11:04:54.409 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 11:04:56.984 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 11:04:57.269 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 11:04:57.305 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 11:04:57.327 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 11:04:57.839 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 11:32:17.695 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 11:32:17.702 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 11:32:17.706 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 11:32:17.712 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 11:32:17.716 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 11:32:17.721 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 11:32:17.721 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 11:32:17.729 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 11:32:17.735 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 11:32:17.739 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 11:32:17.744 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 11:32:17.745 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 11:32:17.764 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 11:32:17.768 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 11:32:17.774 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 11:32:17.777 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 11:32:17.783 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 11:32:17.997 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 11:32:18.151 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 11:32:18.384 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 11:32:18.594 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 11:32:18.595 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 11:32:18.597 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 11:32:21.116 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 11:32:21.409 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 11:32:21.447 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 11:32:21.470 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 11:32:21.680 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 12:08:50.880 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 12:08:50.888 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:08:50.892 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 12:08:50.900 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:08:50.904 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:08:50.908 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 12:08:50.909 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 12:08:50.917 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:08:50.924 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 12:08:50.928 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 12:08:50.933 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 12:08:50.934 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:08:50.954 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 12:08:50.959 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 12:08:50.965 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 12:08:50.968 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:08:51.203 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 12:08:51.371 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:08:51.640 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 12:08:51.855 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 12:08:51.857 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:08:51.858 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:08:54.527 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:08:54.808 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: Malicious content detected
2025-07-08 12:08:54.845 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 12:08:54.867 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:386 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 12:08:55.091 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 12:12:04.163 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 12:12:04.170 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:12:04.174 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 12:12:04.181 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:12:04.185 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:12:04.189 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 12:12:04.190 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 12:12:04.197 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:12:04.204 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 12:12:04.208 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 12:12:04.213 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 12:12:04.213 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:12:04.232 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 12:12:04.236 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 12:12:04.242 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 12:12:04.245 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:12:04.251 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:12:04.262 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 12:12:04.380 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:12:04.613 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 12:12:04.810 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 12:12:04.811 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:12:04.812 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:12:07.253 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:12:07.520 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:383 - Error sanitizing request data: Malicious content detected
2025-07-08 12:12:07.555 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 12:12:07.577 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:383 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 12:12:07.952 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 12:12:30.303 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 12:12:30.311 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:12:30.315 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 12:12:30.321 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:12:30.325 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:12:30.330 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 12:12:30.330 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 12:12:30.338 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:12:30.344 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 12:12:30.348 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 12:12:30.352 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 12:12:30.353 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:12:30.371 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 12:12:30.375 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 12:12:30.381 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 12:12:30.384 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:12:30.389 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:12:30.400 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 12:12:30.517 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:12:30.749 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 12:12:30.958 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 12:12:30.959 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:12:30.960 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:12:33.442 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:12:33.707 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:383 - Error sanitizing request data: Malicious content detected
2025-07-08 12:12:33.742 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 12:12:33.764 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:383 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 12:12:34.136 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 12:14:00.096 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 12:14:00.102 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:14:00.106 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 12:14:00.113 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:14:00.117 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:14:00.121 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 12:14:00.121 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 12:14:00.129 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:14:00.135 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 12:14:00.139 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 12:14:00.144 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 12:14:00.144 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:14:00.163 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 12:14:00.167 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 12:14:00.173 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 12:14:00.176 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:14:00.182 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:14:00.192 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 12:14:00.314 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:14:00.554 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 12:14:00.767 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 12:14:00.768 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:14:00.769 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:14:03.219 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:14:03.503 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: Malicious content detected
2025-07-08 12:14:03.540 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 12:14:03.561 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 12:14:03.927 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 12:16:31.900 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 12:16:31.907 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:16:31.911 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 12:16:31.918 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:16:31.922 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:16:31.926 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 12:16:31.927 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 12:16:31.934 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:16:31.941 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 12:16:31.944 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 12:16:31.949 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 12:16:31.950 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 12:16:31.968 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 12:16:31.972 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 12:16:31.978 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 12:16:31.981 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:16:31.986 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 12:16:31.999 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 12:16:32.120 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:16:32.353 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 12:16:32.561 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 12:16:32.562 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:16:32.564 | ERROR    | MainProcess:Thread-162 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:16:35.014 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:16:35.285 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: Malicious content detected
2025-07-08 12:16:35.321 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 12:16:35.344 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 12:16:35.693 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 12:17:38.789 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:17:39.020 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 12:17:39.217 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 12:17:39.218 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:17:39.220 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:17:41.648 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:17:41.915 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: Malicious content detected
2025-07-08 12:17:41.951 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 12:17:41.973 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 12:18:08.555 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:18:08.697 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 12:18:08.826 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 12:18:08.827 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:18:08.827 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:18:10.520 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:18:10.685 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: Malicious content detected
2025-07-08 12:18:10.708 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 12:18:10.726 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 12:19:53.442 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:19:53.581 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 12:19:53.703 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 12:19:53.704 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 12:19:53.705 | ERROR    | MainProcess:Thread-120 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:19:55.409 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 12:19:55.577 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: Malicious content detected
2025-07-08 12:19:55.599 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 12:19:55.612 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:387 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 18:17:50.278 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:50.279 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:50.743 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:50.744 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:50.813 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x00000243F9AA8280>}

2025-07-08 18:17:50.814 | ERROR    | MainProcess:Thread-3 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: KeyError: "'type'"
2025-07-08 18:17:50.815 | ERROR    | MainProcess:Thread-3 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: "'type'"
2025-07-08 18:17:51.096 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.097 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:51.173 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.175 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:51.247 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.248 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:51.355 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.357 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:51.467 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.468 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:51.552 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.553 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:51.682 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.683 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:51.774 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.775 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:51.839 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:51.840 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:53.144 | ERROR    | MainProcess:Thread-35 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: [TypeError("'coroutine' object is not iterable"), TypeError('vars() argument must have __dict__ attribute')]
2025-07-08 18:17:53.433 | ERROR    | MainProcess:Thread-37 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: [TypeError("'coroutine' object is not iterable"), TypeError('vars() argument must have __dict__ attribute')]
2025-07-08 18:17:55.765 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:55.766 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:55.890 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:55.891 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:55.994 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:55.994 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.070 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.071 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.141 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.142 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.216 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.217 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.290 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.291 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.365 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.366 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.453 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.454 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.537 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.538 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.615 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.616 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.685 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.686 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.756 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.757 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.880 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.881 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:56.965 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:56.967 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:57.040 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:57.041 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:57.113 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:57.114 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:57.186 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:57.187 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:57.268 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:57.270 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:17:57.357 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service failed
2025-07-08 18:17:57.363 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:57.367 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: Internal error: Debug error
2025-07-08 18:17:57.373 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 18:17:57.377 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 18:17:57.382 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 1
2025-07-08 18:17:57.383 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Error 2
2025-07-08 18:17:57.389 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:57.396 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Test service error
2025-07-08 18:17:57.400 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
2025-07-08 18:17:57.405 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] DB_OPERATION_FAILED: Database error: DB error
2025-07-08 18:17:57.406 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service error
2025-07-08 18:17:57.422 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database connection failed
2025-07-08 18:17:57.426 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DUPLICATE_ENTRY: A resource with the given unique constraint already exists.
2025-07-08 18:17:57.431 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] DB_OPERATION_FAILED: Database error: Database operation failed: Generic SQLAlchemy error
2025-07-08 18:17:57.434 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:57.440 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [CALCULATION] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:17:57.453 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: Input validation failed.
2025-07-08 18:17:57.551 | ERROR    | MainProcess:MainThread | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 18:17:57.701 | ERROR    | MainProcess:MainThread | src.middleware.context_middleware:dispatch:109 - Context middleware error: Simulated error
2025-07-08 18:17:57.869 | ERROR    | MainProcess:Thread-237 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: Test error
2025-07-08 18:17:57.869 | ERROR    | MainProcess:Thread-237 (run_blocking_portal) | src.middleware.caching_middleware:dispatch:162 - Caching middleware error: Test error
2025-07-08 18:17:57.871 | ERROR    | MainProcess:Thread-237 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 18:17:59.467 | ERROR    | MainProcess:MainThread | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: Test error
2025-07-08 18:17:59.665 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:398 - Error sanitizing request data: Malicious content detected
2025-07-08 18:17:59.691 | ERROR    | MainProcess:MainThread | test_security_middleware:mock_dispatch:614 - Error sanitizing request data: Malicious content detected
2025-07-08 18:17:59.707 | ERROR    | MainProcess:MainThread | src.middleware.security_middleware:_sanitize_request_data:398 - Error sanitizing request data: 400: JSON nesting too deep
2025-07-08 18:17:59.842 | ERROR    | MainProcess:MainThread | src.core.security.input_validators:validate:411 - Payload size validation error: Object of type NonSerializable is not JSON serializable
2025-07-08 18:23:03.202 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:23:03.203 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:24:11.127 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:24:11.128 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:24:52.104 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:24:52.105 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:25:20.334 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:25:47.607 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:26:18.037 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:29:33.111 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x000001B7FAE27880>}

2025-07-08 18:29:33.112 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: KeyError: "'type'"
2025-07-08 18:29:33.113 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: "'type'"
2025-07-08 18:29:33.114 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:31:03.077 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x0000024099E3B640>}

2025-07-08 18:31:03.077 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x0000024099E3B640>}

2025-07-08 18:31:03.078 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: ResponseValidationError: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x0000024099E3B640>}

2025-07-08 18:31:03.079 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:31:35.718 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x00000196F6BC7640>}

2025-07-08 18:31:35.719 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x00000196F6BC7640>}

2025-07-08 18:31:35.720 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: ResponseValidationError: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x00000196F6BC7640>}

2025-07-08 18:31:35.721 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: 1 validation errors:
  {'type': 'model_attributes_type', 'loc': ('response',), 'msg': 'Input should be a valid dictionary or object to extract fields from', 'input': <coroutine object login at 0x00000196F6BC7640>}

2025-07-08 18:31:35.722 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:34:14.629 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:34:14.629 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:34:14.631 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:34:14.632 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:34:14.633 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:34:14.634 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:35:49.235 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [REPOSITORY] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:35:49.236 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:35:49.237 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Repository operation failed: An unexpected internal error occurred.
2025-07-08 18:35:49.238 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:35:49.239 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:35:49.240 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:35:49.241 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:35:49.243 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:35:49.245 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:07.337 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:37:07.337 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service operation failed: An unexpected internal error occurred.
2025-07-08 18:37:07.339 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:07.339 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:07.340 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:07.341 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:07.342 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:07.344 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:36.914 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:37:36.915 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service operation failed: An unexpected internal error occurred.
2025-07-08 18:37:36.916 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:36.917 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:36.918 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:36.919 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:36.920 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:37:36.922 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:38:25.454 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-08 18:38:25.456 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [SERVICE LAYER] SERVICE_ERROR: Service operation failed: An unexpected internal error occurred.
2025-07-08 18:38:25.457 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:38:25.457 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:38:25.458 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:38:25.459 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:38:25.460 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:38:25.462 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:15.792 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:15.792 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:15.793 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:15.794 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:15.795 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:15.800 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:53.560 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:53.561 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:53.562 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:53.563 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:53.564 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:42:53.566 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:43:42.849 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:43:42.849 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:43:42.850 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:43:42.851 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:43:42.852 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:43:42.854 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:44:10.747 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:44:10.748 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:44:10.749 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:44:10.751 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:44:10.753 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:44:10.755 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:45:17.750 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:async_wrapper:699 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:45:17.750 | ERROR    | MainProcess:AnyIO worker thread | src.core.database.session:get_db_session:149 - Database session error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:45:17.752 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.context_middleware:dispatch:109 - Context middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:45:17.753 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.logging_middleware:_log_error:214 - Request failed - POST /api/v1/auth/login Error: APIError: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:45:17.754 | ERROR    | MainProcess:Thread-1 (run_blocking_portal) | src.middleware.rate_limiting_middleware:dispatch:130 - Rate limiting middleware error: API operation 'user_login' failed: 401: Invalid email or password
2025-07-08 18:45:17.756 | Level 40 | MainProcess:Thread-1 (run_blocking_portal) | src.core.errors.unified_error_handler:_log_error:379 - [API] API_USER_LOGIN_ERROR: API operation 'user_login' failed: 401: Invalid email or password
2025-07-17 18:50:00.332 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-17 18:50:00.333 | ERROR    | SpawnProcess-3:MainThread | src.core.services.general.health_service:get_simple_health:122 - Database health check failed: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-17 18:51:43.771 | Level 40 | SpawnProcess-4:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-17 18:51:43.772 | ERROR    | SpawnProcess-4:MainThread | src.core.services.general.health_service:get_simple_health:122 - Database health check failed: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-17 18:54:04.055 | Level 40 | SpawnProcess-5:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-17 18:54:04.056 | ERROR    | SpawnProcess-5:MainThread | src.core.services.general.health_service:get_simple_health:122 - Database health check failed: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-17 18:54:05.499 | Level 40 | SpawnProcess-5:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-17 18:54:05.500 | ERROR    | SpawnProcess-5:MainThread | src.core.services.general.health_service:get_simple_health:122 - Database health check failed: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-18 20:31:59.907 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:37:51.979 | Level 40 | SpawnProcess-2:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:38:51.667 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:39:47.336 | Level 40 | SpawnProcess-4:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) no such column: category
[SQL: CREATE INDEX "ix_Component_category" ON "Component" (category)]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-18 20:40:31.877 | Level 40 | SpawnProcess-5:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) table "_alembic_tmp_Component" already exists
[SQL: 
CREATE TABLE "_alembic_tmp_Component" (
	manufacturer VARCHAR(100) NOT NULL, 
	model_number VARCHAR(100) NOT NULL, 
	des
2025-07-18 20:47:11.154 | Level 40 | SpawnProcess-6:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:47:43.919 | Level 40 | SpawnProcess-7:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:47:52.134 | Level 40 | SpawnProcess-8:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:48:11.957 | Level 40 | SpawnProcess-9:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:48:20.193 | Level 40 | SpawnProcess-10:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:48:35.755 | Level 40 | SpawnProcess-11:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:48:45.467 | Level 40 | SpawnProcess-12:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:49:42.162 | Level 40 | SpawnProcess-13:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-18 20:49:52.027 | Level 40 | SpawnProcess-14:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-19 18:56:27.246 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("alembic_version")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 18:56:27.250 | ERROR    | SpawnProcess-1:MainThread | src.core.database.session:create_tables:435 - Failed to create database tables: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("ActivityLog")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 18:56:27.250 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("ActivityLog")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 18:56:27.250 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database infrastructure operation failed: table_creation
2025-07-19 18:56:27.251 | CRITICAL | SpawnProcess-1:MainThread | src.app:lifespan:68 - Application startup failed: Database error: Database infrastructure operation failed: table_creation
2025-07-19 18:58:49.099 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("alembic_version")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 18:58:49.104 | ERROR    | SpawnProcess-3:MainThread | src.core.database.session:create_tables:435 - Failed to create database tables: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("ActivityLog")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 18:58:49.104 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("ActivityLog")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 18:58:49.105 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database infrastructure operation failed: table_creation
2025-07-19 18:58:49.105 | CRITICAL | SpawnProcess-3:MainThread | src.app:lifespan:68 - Application startup failed: Database error: Database infrastructure operation failed: table_creation
2025-07-19 19:00:42.617 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("alembic_version")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 19:00:42.622 | ERROR    | SpawnProcess-1:MainThread | src.core.database.session:create_tables:435 - Failed to create database tables: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("ActivityLog")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 19:00:42.622 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("ActivityLog")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 19:00:42.623 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database infrastructure operation failed: table_creation
2025-07-19 19:00:42.623 | CRITICAL | SpawnProcess-1:MainThread | src.app:lifespan:69 - Application startup failed: Database error: Database infrastructure operation failed: table_creation
2025-07-19 19:02:37.552 | Level 40 | SpawnProcess-2:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("alembic_version")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 19:02:37.556 | ERROR    | SpawnProcess-2:MainThread | src.core.database.session:create_tables:435 - Failed to create database tables: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("ActivityLog")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 19:02:37.557 | Level 40 | SpawnProcess-2:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: (sqlite3.OperationalError) disk I/O error
[SQL: PRAGMA main.table_info("ActivityLog")]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-19 19:02:37.557 | Level 40 | SpawnProcess-2:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database infrastructure operation failed: table_creation
2025-07-19 19:02:37.557 | CRITICAL | SpawnProcess-2:MainThread | src.app:lifespan:69 - Application startup failed: Database error: Database infrastructure operation failed: table_creation
2025-07-21 15:17:58.591 | ERROR    | SpawnProcess-1:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 15:18:51.965 | ERROR    | SpawnProcess-2:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 15:20:23.651 | ERROR    | SpawnProcess-3:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 15:20:58.360 | ERROR    | SpawnProcess-2:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 15:26:22.706 | ERROR    | SpawnProcess-4:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 15:27:52.325 | ERROR    | SpawnProcess-5:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 15:29:20.202 | ERROR    | SpawnProcess-6:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 15:46:28.953 | ERROR    | SpawnProcess-1:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 15:48:24.515 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 15:48:24.516 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:24.519 | ERROR    | SpawnProcess-1:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:24.521 | ERROR    | SpawnProcess-1:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:24.525 | ERROR    | SpawnProcess-1:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:24.529 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:24.530 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:24.530 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:24.530 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:24.531 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 15:48:24.531 | ERROR    | SpawnProcess-1:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7f4a211c1d30>
  |                │     └ <function create_task_group at 0x7f4a5252a0c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f4a524d38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22ac91...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7f4a232c3770>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f4a524d38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22ac91...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22ac91...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f4a22c4d7f0>
    |           └ <fastapi.applications.FastAPI object at 0x7f4a232c3770>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f4a523baac0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f4a22c4d7f0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f4a523baac0>
    |           │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x7f4a50d922a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>>, ...
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7f4a50eb49a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7f4a22aa0a00>
    |     └ <contextlib._GeneratorContextManager object at 0x7f4a21193af0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7f4a211c63e0>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7f4a211c1400>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 8
               └ <function _main at 0x7f4a52bc5080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7f4a52afede0>
           └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7f4a52afe340>
    └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7f4a52bd9010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
    │    └ <function subprocess_started at 0x7f4a526328e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7f4a524d34d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7f4a52631580>
           │       │   └ <uvicorn.server.Server object at 0x7f4a524d34d0>
           │       └ <function run at 0x7f4a527759e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7f4a52386340>
           │      └ <function Runner.run at 0x7f4a527f9580>
           └ <asyncio.runners.Runner object at 0x7f4a52418050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7f4a523f31d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7f4a52418050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f4a524d38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22ac91...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7f4a232c3770>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f4a524d38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22ac91...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22ac91...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f4a22c4d7f0>
          └ <fastapi.applications.FastAPI object at 0x7f4a232c3770>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f4a523baac0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f4a22c4d7f0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f4a523baac0>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x7f4a50d922a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>>, ...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a22a...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7f4a50eb49a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7f4a22aa0a00>
    └ <contextlib._GeneratorContextManager object at 0x7f4a21193af0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7f4a211c63e0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7f4a211c1400>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.844 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 15:48:25.845 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.848 | ERROR    | SpawnProcess-1:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.850 | ERROR    | SpawnProcess-1:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.853 | ERROR    | SpawnProcess-1:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.857 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.858 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.858 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.858 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 15:48:25.858 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 15:48:25.859 | ERROR    | SpawnProcess-1:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7f4a12fa8dd0>
  |                │     └ <function create_task_group at 0x7f4a5252a0c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f4a524d38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212244...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7f4a232c3770>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f4a524d38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212244...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212244...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f4a22c4d7f0>
    |           └ <fastapi.applications.FastAPI object at 0x7f4a232c3770>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f4a21216160>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f4a22c4d7f0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f4a21216160>
    |           │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x7f4a50d922a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>>, ...
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7f4a50eb49a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7f4a12f56400>
    |     └ <contextlib._GeneratorContextManager object at 0x7f4a12f75240>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7f4a212165c0>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7f4a12eb0150>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 8
               └ <function _main at 0x7f4a52bc5080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7f4a52afede0>
           └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7f4a52afe340>
    └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7f4a52bd9010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
    │    └ <function subprocess_started at 0x7f4a526328e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=86736 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7f4a524d34d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7f4a52631580>
           │       │   └ <uvicorn.server.Server object at 0x7f4a524d34d0>
           │       └ <function run at 0x7f4a527759e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7f4a52386340>
           │      └ <function Runner.run at 0x7f4a527f9580>
           └ <asyncio.runners.Runner object at 0x7f4a52418050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7f4a523f31d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7f4a52418050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f4a524d38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212244...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7f4a232c3770>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f4a524d38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212244...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212244...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f4a22c4d7f0>
          └ <fastapi.applications.FastAPI object at 0x7f4a232c3770>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f4a21216160>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f4a22c4d7f0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f4a21216160>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x7f4a50d922a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>>, ...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f4a212...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7f4a22c4d6a0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7f4a50eb49a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7f4a12f56400>
    └ <contextlib._GeneratorContextManager object at 0x7f4a12f75240>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7f4a212165c0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7f4a12eb0150>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f4a22c4d400>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:11:30.878 | ERROR    | SpawnProcess-1:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 22:14:20.681 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 22:14:20.682 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:20.685 | ERROR    | SpawnProcess-1:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:20.687 | ERROR    | SpawnProcess-1:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:20.691 | ERROR    | SpawnProcess-1:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:20.694 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:20.695 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:20.695 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:20.696 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:20.696 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 22:14:20.696 | ERROR    | SpawnProcess-1:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7fcba8225010>
  |                │     └ <function create_task_group at 0x7fcbdb74a0c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabdb8f...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabdb8f...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabdb8f...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
    |           └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcbaa3ea8e0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcbaa3ea8e0>
    |           │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x7fcbd9fba2a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>>, ...
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7fcbda0dc9a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7fcbaa3ba4d0>
    |     └ <contextlib._GeneratorContextManager object at 0x7fcbaa40d6a0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7fcbaa3ea980>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7fcbaa38bb60>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 8
               └ <function _main at 0x7fcbdbdf5080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7fcbdbd2ede0>
           └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7fcbdbd2e340>
    └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7fcbdbe09010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
    │    └ <function subprocess_started at 0x7fcbdb85a8e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7fcbdb6f34d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7fcbdb859580>
           │       │   └ <uvicorn.server.Server object at 0x7fcbdb6f34d0>
           │       └ <function run at 0x7fcbdb9a19e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7fcbdb5a6340>
           │      └ <function Runner.run at 0x7fcbdba25580>
           └ <asyncio.runners.Runner object at 0x7fcbdb634050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7fcbdb60f1d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7fcbdb634050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabdb8f...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabdb8f...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabdb8f...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
          └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcbaa3ea8e0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcbaa3ea8e0>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x7fcbd9fba2a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>>, ...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcbabd...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7fcbda0dc9a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7fcbaa3ba4d0>
    └ <contextlib._GeneratorContextManager object at 0x7fcbaa40d6a0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7fcbaa3ea980>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7fcbaa38bb60>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.087 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 22:14:22.088 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.091 | ERROR    | SpawnProcess-1:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.093 | ERROR    | SpawnProcess-1:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.096 | ERROR    | SpawnProcess-1:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.102 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.102 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.102 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.103 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:14:22.103 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 22:14:22.103 | ERROR    | SpawnProcess-1:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7fcba819d7d0>
  |                │     └ <function create_task_group at 0x7fcbdb74a0c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba824ae...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba824ae...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba824ae...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
    |           └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcba827b740>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcba827b740>
    |           │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x7fcbd9fba2a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>>, ...
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7fcbda0dc9a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7fcba8125be0>
    |     └ <contextlib._GeneratorContextManager object at 0x7fcba82d2890>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7fcba827ba60>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7fcba82acf50>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 8
               └ <function _main at 0x7fcbdbdf5080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7fcbdbd2ede0>
           └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7fcbdbd2e340>
    └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7fcbdbe09010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
    │    └ <function subprocess_started at 0x7fcbdb85a8e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7fcbdb6f34d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7fcbdb859580>
           │       │   └ <uvicorn.server.Server object at 0x7fcbdb6f34d0>
           │       └ <function run at 0x7fcbdb9a19e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7fcbdb5a6340>
           │      └ <function Runner.run at 0x7fcbdba25580>
           └ <asyncio.runners.Runner object at 0x7fcbdb634050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7fcbdb60f1d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7fcbdb634050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba824ae...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba824ae...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba824ae...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
          └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcba827b740>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcba827b740>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x7fcbd9fba2a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>>, ...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7fcbda0dc9a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7fcba8125be0>
    └ <contextlib._GeneratorContextManager object at 0x7fcba82d2890>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7fcba827ba60>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7fcba82acf50>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.595 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 22:30:02.597 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.599 | ERROR    | SpawnProcess-1:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.602 | ERROR    | SpawnProcess-1:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.607 | ERROR    | SpawnProcess-1:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.614 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.615 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.615 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.615 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:30:02.616 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 22:30:02.617 | ERROR    | SpawnProcess-1:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7fcba81ad320>
  |                │     └ <function create_task_group at 0x7fcbdb74a0c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba8271b...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba8271b...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba8271b...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
    |           └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcba82799e0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcba82799e0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7fcbda0dc9a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7fcbabda4e10>
    |     └ <contextlib._GeneratorContextManager object at 0x7fcba81ad780>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7fcba827b100>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7fcbaa344c50>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 8
               └ <function _main at 0x7fcbdbdf5080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7fcbdbd2ede0>
           └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7fcbdbd2e340>
    └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7fcbdbe09010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
    │    └ <function subprocess_started at 0x7fcbdb85a8e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=2611 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7fcbdb6f34d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7fcbdb859580>
           │       │   └ <uvicorn.server.Server object at 0x7fcbdb6f34d0>
           │       └ <function run at 0x7fcbdb9a19e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7fcbdb5a6340>
           │      └ <function Runner.run at 0x7fcbdba25580>
           └ <asyncio.runners.Runner object at 0x7fcbdb634050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7fcbdb60f1d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7fcbdb634050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba8271b...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7fcbdb6f38c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba8271b...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba8271b...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
          └ <fastapi.applications.FastAPI object at 0x7fcbac55ba10>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcba82799e0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7fcbac135a90>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 85, in __call__
    await self.app(scope, receive, send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7fcba82799e0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7fcba82...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7fcbac135940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7fcbda0dc9a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7fcbabda4e10>
    └ <contextlib._GeneratorContextManager object at 0x7fcba81ad780>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7fcba827b100>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7fcbaa344c50>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7fcbac1356a0>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:32:37.703 | ERROR    | SpawnProcess-3:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 22:37:53.850 | Level 40 | SpawnProcess-3:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 22:37:53.853 | Level 40 | SpawnProcess-3:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:53.856 | ERROR    | SpawnProcess-3:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:53.859 | ERROR    | SpawnProcess-3:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:53.863 | ERROR    | SpawnProcess-3:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:53.867 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:53.867 | ERROR    | SpawnProcess-3:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:53.868 | ERROR    | SpawnProcess-3:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:53.868 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:53.868 | ERROR    | SpawnProcess-3:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 22:37:53.869 | ERROR    | SpawnProcess-3:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7ffa4c4952b0>
  |                │     └ <function create_task_group at 0x7ffa7d90e0c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7ffa7d8b78c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0310...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7ffa4e7c7cb0>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7ffa7d8b78c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0310...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0310...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7ffa4e1b1d30>
    |           └ <fastapi.applications.FastAPI object at 0x7ffa4e7c7cb0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7ffa4c45aca0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7ffa4e1b1d30>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7ffa4c45aca0>
    |           │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x7ffa7c1722a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>>, ...
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7ffa7c29c9a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7ffa4c42e5a0>
    |     └ <contextlib._GeneratorContextManager object at 0x7ffa4c4797f0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7ffa4c45ad40>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7ffa4c727e00>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 10
               └ <function _main at 0x7ffa7dfb1080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7ffa7deeade0>
           └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7ffa7deea340>
    └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7ffa7dfc5010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
    │    └ <function subprocess_started at 0x7ffa7da1a8e0>
    └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7ffa7d8b74d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7ffa7da19580>
           │       │   └ <uvicorn.server.Server object at 0x7ffa7d8b74d0>
           │       └ <function run at 0x7ffa7db599e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7ffa7d772340>
           │      └ <function Runner.run at 0x7ffa7dbdd580>
           └ <asyncio.runners.Runner object at 0x7ffa7d7fc050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7ffa7d7d71d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7ffa7d7fc050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7ffa7d8b78c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0310...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7ffa4e7c7cb0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7ffa7d8b78c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0310...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0310...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7ffa4e1b1d30>
          └ <fastapi.applications.FastAPI object at 0x7ffa4e7c7cb0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7ffa4c45aca0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7ffa4e1b1d30>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7ffa4c45aca0>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x7ffa7c1722a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>>, ...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4e0...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7ffa7c29c9a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7ffa4c42e5a0>
    └ <contextlib._GeneratorContextManager object at 0x7ffa4c4797f0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7ffa4c45ad40>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7ffa4c727e00>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.217 | Level 40 | SpawnProcess-3:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 22:37:55.218 | Level 40 | SpawnProcess-3:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.221 | ERROR    | SpawnProcess-3:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.223 | ERROR    | SpawnProcess-3:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.226 | ERROR    | SpawnProcess-3:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.230 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.231 | ERROR    | SpawnProcess-3:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.231 | ERROR    | SpawnProcess-3:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.232 | Level 40 | SpawnProcess-3:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:37:55.232 | ERROR    | SpawnProcess-3:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 22:37:55.232 | ERROR    | SpawnProcess-3:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7ffa4c411850>
  |                │     └ <function create_task_group at 0x7ffa7d90e0c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7ffa7d8b78c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4bef...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7ffa4e7c7cb0>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7ffa7d8b78c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4bef...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4bef...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7ffa4e1b1d30>
    |           └ <fastapi.applications.FastAPI object at 0x7ffa4e7c7cb0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7ffa4c45aa20>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7ffa4e1b1d30>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7ffa4c45aa20>
    |           │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x7ffa7c1722a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>>, ...
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7ffa7c29c9a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7ffa4c399cb0>
    |     └ <contextlib._GeneratorContextManager object at 0x7ffa4c34eac0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7ffa4c4efe20>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7ffa4c324e50>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 10
               └ <function _main at 0x7ffa7dfb1080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7ffa7deeade0>
           └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7ffa7deea340>
    └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7ffa7dfc5010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
    │    └ <function subprocess_started at 0x7ffa7da1a8e0>
    └ <SpawnProcess name='SpawnProcess-3' parent=2611 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7ffa7d8b74d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7ffa7da19580>
           │       │   └ <uvicorn.server.Server object at 0x7ffa7d8b74d0>
           │       └ <function run at 0x7ffa7db599e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7ffa7d772340>
           │      └ <function Runner.run at 0x7ffa7dbdd580>
           └ <asyncio.runners.Runner object at 0x7ffa7d7fc050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7ffa7d7d71d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7ffa7d7fc050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7ffa7d8b78c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4bef...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7ffa4e7c7cb0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7ffa7d8b78c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4bef...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4bef...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7ffa4e1b1d30>
          └ <fastapi.applications.FastAPI object at 0x7ffa4e7c7cb0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7ffa4c45aa20>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7ffa4e1b1d30>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7ffa4c45aa20>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x7ffa7c1722a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>>, ...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7ffa4c4...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7ffa4e1b1be0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7ffa7c29c9a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7ffa4c399cb0>
    └ <contextlib._GeneratorContextManager object at 0x7ffa4c34eac0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7ffa4c4efe20>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7ffa4c324e50>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7ffa4e1b1940>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:38:54.234 | ERROR    | SpawnProcess-1:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 22:39:33.862 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 22:39:33.863 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:33.865 | ERROR    | SpawnProcess-1:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:33.868 | ERROR    | SpawnProcess-1:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:33.871 | ERROR    | SpawnProcess-1:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:33.875 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:33.876 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:33.876 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:33.876 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:33.877 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 22:39:33.877 | ERROR    | SpawnProcess-1:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7f7e38230ec0>
  |                │     └ <function create_task_group at 0x7f7e6a7b60c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f7e6a75f8c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394cf7...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7f7e3b577a10>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f7e6a75f8c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394cf7...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394cf7...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f7e3af51a90>
    |           └ <fastapi.applications.FastAPI object at 0x7f7e3b577a10>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f7e3952e7a0>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f7e3af51a90>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f7e3952e7a0>
    |           │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x7f7e6902e2a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>>, ...
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7f7e691549a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7f7e3adc0a00>
    |     └ <contextlib._GeneratorContextManager object at 0x7f7e3821d6a0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7f7e3952e8e0>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7f7e394cfa10>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 8
               └ <function _main at 0x7f7e6ae51080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7f7e6ad8ade0>
           └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7f7e6ad8a340>
    └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7f7e6ae65010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
    │    └ <function subprocess_started at 0x7f7e6a8be8e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7f7e6a75f4d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7f7e6a8bd580>
           │       │   └ <uvicorn.server.Server object at 0x7f7e6a75f4d0>
           │       └ <function run at 0x7f7e6aa019e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7f7e6a612340>
           │      └ <function Runner.run at 0x7f7e6aa85580>
           └ <asyncio.runners.Runner object at 0x7f7e6a6a4050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7f7e6a67f1d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7f7e6a6a4050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f7e6a75f8c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394cf7...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7f7e3b577a10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f7e6a75f8c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394cf7...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394cf7...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f7e3af51a90>
          └ <fastapi.applications.FastAPI object at 0x7f7e3b577a10>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f7e3952e7a0>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f7e3af51a90>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f7e3952e7a0>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x7f7e6902e2a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>>, ...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e394...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7f7e691549a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7f7e3adc0a00>
    └ <contextlib._GeneratorContextManager object at 0x7f7e3821d6a0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7f7e3952e8e0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7f7e394cfa10>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.209 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-21 22:39:35.210 | Level 40 | SpawnProcess-1:AnyIO worker thread | src.core.errors.unified_error_handler:_log_error:382 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.213 | ERROR    | SpawnProcess-1:MainThread | src.middleware.context_middleware:dispatch:122 - Context middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.215 | ERROR    | SpawnProcess-1:MainThread | src.middleware.logging_middleware:_log_error:235 - Request failed - POST /api/v1/auth/login Error: DatabaseError: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.218 | ERROR    | SpawnProcess-1:MainThread | src.middleware.rate_limiting_middleware:dispatch:159 - Rate limiting middleware error: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.222 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [SECURITY] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.223 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:151 - Global exception handler caught exception for POST /api/v1/auth/login: HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.223 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:154 - Exception details: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.223 | Level 40 | SpawnProcess-1:MainThread | src.core.errors.unified_error_handler:_log_error:382 - [API] HTTP_500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:39:35.223 | ERROR    | SpawnProcess-1:MainThread | src.app:global_exception_handler:162 - Global exception handler returning status code: 500
2025-07-21 22:39:35.224 | ERROR    | SpawnProcess-1:MainThread | logging:callHandlers:1744 - Exception in ASGI application

  + Exception Group Traceback (most recent call last):
  |
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 77, in collapse_excgroups
  |     yield
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 183, in __call__
  |     async with anyio.create_task_group() as task_group:
  |                │     │                      └ <anyio._backends._asyncio.TaskGroup object at 0x7f7e3819b950>
  |                │     └ <function create_task_group at 0x7f7e6a7b60c0>
  |                └ <module 'anyio' from '/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/__init__.py'>
  |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/anyio/_backends/_asyncio.py", line 772, in __aexit__
  |     raise BaseExceptionGroup(
  |
  | ExceptionGroup: unhandled errors in a TaskGroup (1 sub-exception)
  +-+---------------- 1 ----------------
    | Traceback (most recent call last):
    |
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    |     result = await app(  # type: ignore[func-returns-value]
    |                    └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f7e6a75f8c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    |     return await self.app(scope, receive, send)
    |                  │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e3827ad...
    |                  │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
    |                  │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |                  │    └ <fastapi.applications.FastAPI object at 0x7f7e3b577a10>
    |                  └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f7e6a75f8c0>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    |     await super().__call__(scope, receive, send)
    |                            │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e3827ad...
    |                            │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
    |                            └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    |     await self.middleware_stack(scope, receive, send)
    |           │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e3827ad...
    |           │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
    |           │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f7e3af51a90>
    |           └ <fastapi.applications.FastAPI object at 0x7f7e3b577a10>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    |     await self.app(scope, receive, _send)
    |           │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f7e38292700>
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
    |           └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f7e3af51a90>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    |     await self.simple_response(scope, receive, send, request_headers=headers)
    |           │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
    |           │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f7e38292700>
    |           │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
    |           │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <function CORSMiddleware.simple_response at 0x7f7e6902e2a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    |     await self.app(scope, receive, send)
    |           │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>>, ...
    |           │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
    |           │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
    |           │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>
    |           └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    |     with recv_stream, send_stream, collapse_excgroups():
    |          │            │            └ <function collapse_excgroups at 0x7f7e691549a0>
    |          │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
    |          └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
    |   File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    |     self.gen.throw(value)
    |     │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    |     │    │   └ <method 'throw' of 'generator' objects>
    |     │    └ <generator object collapse_excgroups at 0x7f7e38139b10>
    |     └ <contextlib._GeneratorContextManager object at 0x7f7e382eea50>
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    |     raise exc
    |   File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    |     response = await self.dispatch_func(request, call_next)
    |                      │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7f7e382937e0>
    |                      │    │             └ <starlette.middleware.base._CachedRequest object at 0x7f7e382c0e50>
    |                      │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>>
    |                      └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>
    |
    |   File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    |     raise HTTPException(
    |           └ <class 'fastapi.exceptions.HTTPException'>
    |
    | fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
    +------------------------------------


During handling of the above exception, another exception occurred:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 5
               │     └ 8
               └ <function _main at 0x7f7e6ae51080>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 5
           │    └ <function BaseProcess._bootstrap at 0x7f7e6ad8ade0>
           └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7f7e6ad8a340>
    └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7f7e6ae65010>, 'target': <bound method Server.run of <uvicorn.server.Server obj...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
    │    └ <function subprocess_started at 0x7f7e6a8be8e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=25403 started>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x7f7e6a75f4d0>>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=4, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7f7e6a8bd580>
           │       │   └ <uvicorn.server.Server object at 0x7f7e6a75f4d0>
           │       └ <function run at 0x7f7e6aa019e0>
           └ <module 'asyncio' from '/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/__init__.py'>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7f7e6a612340>
           │      └ <function Runner.run at 0x7f7e6aa85580>
           └ <asyncio.runners.Runner object at 0x7f7e6a6a4050>
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /mnt/d/Projects/ued/server/.venv/lib/pyt...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7f7e6a67f1d0>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7f7e6a6a4050>
> File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f7e6a75f8c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e3827ad...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x7f7e3b577a10>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7f7e6a75f8c0>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e3827ad...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e3827ad...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f7e3af51a90>
          └ <fastapi.applications.FastAPI object at 0x7f7e3b577a10>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f7e38292700>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7f7e3af51a90>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': 'localhost:8000', 'connection': 'keep-alive', 'content-length': '66', 'sec-ch-ua-platform': '"Windows"', 'us...
          │    │               │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7f7e38292700>
          │    │               │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x7f7e6902e2a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>>, ...
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7f7e382...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8000), 'cl...
          │    └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x7f7e3af51940>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 182, in __call__
    with recv_stream, send_stream, collapse_excgroups():
         │            │            └ <function collapse_excgroups at 0x7f7e691549a0>
         │            └ MemoryObjectSendStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_receive...
         └ MemoryObjectReceiveStream(_state=MemoryObjectStreamState(max_buffer_size=0, buffer=deque([]), open_send_channels=0, open_rece...
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 162, in __exit__
    self.gen.throw(value)
    │    │   │     └ ExceptionGroup('unhandled errors in a TaskGroup', [HTTPException(status_code=500, detail='Database error: Database operation ...
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object collapse_excgroups at 0x7f7e38139b10>
    └ <contextlib._GeneratorContextManager object at 0x7f7e382eea50>
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/_utils.py", line 83, in collapse_excgroups
    raise exc
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/middleware/base.py", line 184, in __call__
    response = await self.dispatch_func(request, call_next)
                     │    │             │        └ <function BaseHTTPMiddleware.__call__.<locals>.call_next at 0x7f7e382937e0>
                     │    │             └ <starlette.middleware.base._CachedRequest object at 0x7f7e382c0e50>
                     │    └ <bound method SecurityMiddleware.dispatch of <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>>
                     └ <src.middleware.security_middleware.SecurityMiddleware object at 0x7f7e3af516a0>

  File "/mnt/d/Projects/ued/server/src/core/errors/unified_error_handler.py", line 920, in async_wrapper
    raise HTTPException(
          └ <class 'fastapi.exceptions.HTTPException'>

fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-21 22:48:33.908 | ERROR    | SpawnProcess-2:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 22:48:34.309 | ERROR    | SpawnProcess-2:MainThread | src.core.database.engine:create_engine:90 - Failed to create synchronous engine for postgresql://user:password@localhost:5432/ultimate_electrical_designer: (psycopg2.OperationalError) connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-21 22:50:10.934 | ERROR    | SpawnProcess-2:MainThread | src.core.errors.unified_error_handler:async_wrapper:759 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-21 22:50:10.936 | ERROR    | SpawnProcess-2:AnyIO worker thread | src.core.database.session:get_db_session:171 - Database session error: 401: Invalid email or password
2025-07-21 22:50:12.024 | ERROR    | SpawnProcess-2:MainThread | src.core.errors.unified_error_handler:async_wrapper:759 - API operation 'user_login' failed: 401: Invalid email or password
2025-07-21 22:50:12.025 | ERROR    | SpawnProcess-2:AnyIO worker thread | src.core.database.session:get_db_session:171 - Database session error: 401: Invalid email or password
2025-07-21 23:58:25.412 | ERROR    | SpawnProcess-3:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 23:58:25.566 | ERROR    | SpawnProcess-3:MainThread | src.core.database.dual_engine:_initialize_sqlite_legacy:112 - Failed to initialize SQLite legacy engine: Database error: Failed to test database connection: (sqlite3.OperationalError) disk I/O error
[SQL: SELECT 1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-21 23:58:25.567 | CRITICAL | SpawnProcess-3:MainThread | src.app:lifespan:90 - Application startup failed: Database error: Failed to test database connection: (sqlite3.OperationalError) disk I/O error
[SQL: SELECT 1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-21 23:58:25.667 | ERROR    | SpawnProcess-3:MainThread | logging:callHandlers:1744 - Traceback (most recent call last):
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py", line 149, in execute
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py", line 300, in _handle_exception
    raise error
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py", line 131, in execute
    self.await_(_cursor.execute(operation, parameters))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/aiosqlite/cursor.py", line 40, in execute
    await self._execute(self._cursor.execute, sql, parameters)
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/aiosqlite/cursor.py", line 32, in _execute
    return await self._conn._execute(fn, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/aiosqlite/core.py", line 122, in _execute
    return await future
           ^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/aiosqlite/core.py", line 105, in run
    result = function()
sqlite3.OperationalError: disk I/O error

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/d/Projects/ued/server/src/core/database/dual_engine.py", line 150, in _create_async_engine
    await conn.execute(text("SELECT 1"))
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/ext/asyncio/engine.py", line 658, in execute
    result = await greenlet_spawn(
             ^^^^^^^^^^^^^^^^^^^^^
    ...<5 lines>...
    )
    ^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 201, in greenlet_spawn
    result = context.throw(*sys.exc_info())
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1415, in execute
    return meth(
        self,
        distilled_parameters,
        execution_options or NO_OPTIONS,
    )
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/sql/elements.py", line 523, in _execute_on_connection
    return connection._execute_clauseelement(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        self, distilled_params, execution_options
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1637, in _execute_clauseelement
    ret = self._execute_context(
        dialect,
    ...<8 lines>...
        cache_hit=cache_hit,
    )
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1842, in _execute_context
    return self._exec_single_context(
           ~~~~~~~~~~~~~~~~~~~~~~~~~^
        dialect, context, statement, parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1982, in _exec_single_context
    self._handle_dbapi_exception(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        e, str_statement, effective_parameters, cursor, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 2351, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/base.py", line 1963, in _exec_single_context
    self.dialect.do_execute(
    ~~~~~~~~~~~~~~~~~~~~~~~^
        cursor, str_statement, effective_parameters, context
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/engine/default.py", line 943, in do_execute
    cursor.execute(statement, parameters)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py", line 149, in execute
    self._adapt_connection._handle_exception(error)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py", line 300, in _handle_exception
    raise error
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/dialects/sqlite/aiosqlite.py", line 131, in execute
    self.await_(_cursor.execute(operation, parameters))
    ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/sqlalchemy/util/_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
            ^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/aiosqlite/cursor.py", line 40, in execute
    await self._execute(self._cursor.execute, sql, parameters)
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/aiosqlite/cursor.py", line 32, in _execute
    return await self._conn._execute(fn, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/aiosqlite/core.py", line 122, in _execute
    return await future
           ^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/aiosqlite/core.py", line 105, in run
    result = function()
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) disk I/O error
[SQL: SELECT 1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/starlette/routing.py", line 694, in lifespan
    async with self.lifespan_context(app) as maybe_state:
               ~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/.venv/lib/python3.13/site-packages/fastapi/routing.py", line 134, in merged_lifespan
    async with original_context(app) as maybe_original_state:
               ~~~~~~~~~~~~~~~~^^^^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.13.5-linux-x86_64-gnu/lib/python3.13/contextlib.py", line 214, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/src/app.py", line 53, in lifespan
    dual_manager = await initialize_dual_database_system()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/mnt/d/Projects/ued/server/src/core/database/dual_engine.py", line 312, in initialize_dual_database_system
    await manager.initialize(db_mode)
  File "/mnt/d/Projects/ued/server/src/core/database/dual_engine.py", line 63, in initialize
    await self._initialize_sqlite_legacy()
  File "/mnt/d/Projects/ued/server/src/core/database/dual_engine.py", line 105, in _initialize_sqlite_legacy
    self._sqlite_legacy_engine = await self._create_async_engine(
                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<3 lines>...
    )
    ^
  File "/mnt/d/Projects/ued/server/src/core/database/dual_engine.py", line 153, in _create_async_engine
    raise DatabaseError(
        reason=f"Failed to test database connection: {e}", original_exception=e
    )
src.core.errors.exceptions.DatabaseError: Database error: Failed to test database connection: (sqlite3.OperationalError) disk I/O error
[SQL: SELECT 1]
(Background on this error at: https://sqlalche.me/e/20/e3q8)

2025-07-21 23:58:25.668 | ERROR    | SpawnProcess-3:MainThread | logging:callHandlers:1744 - Application startup failed. Exiting.
2025-07-21 23:59:49.232 | ERROR    | SpawnProcess-1:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-21 23:59:49.699 | ERROR    | SpawnProcess-1:MainThread | src.core.database.engine:create_engine:90 - Failed to create synchronous engine for postgresql://user:password@localhost:5432/ultimate_electrical_designer: (psycopg2.OperationalError) connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-22 00:03:14.214 | ERROR    | SpawnProcess-2:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-22 00:03:14.641 | ERROR    | SpawnProcess-2:MainThread | src.core.database.engine:create_engine:90 - Failed to create synchronous engine for postgresql://user:password@localhost:5432/ultimate_electrical_designer: (psycopg2.OperationalError) connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-22 00:04:53.200 | ERROR    | SpawnProcess-3:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-22 00:04:53.654 | ERROR    | SpawnProcess-3:MainThread | src.core.database.engine:create_engine:90 - Failed to create synchronous engine for postgresql://user:password@localhost:5432/ultimate_electrical_designer: (psycopg2.OperationalError) connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-22 00:06:33.732 | ERROR    | SpawnProcess-4:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-22 00:06:34.151 | ERROR    | SpawnProcess-4:MainThread | src.core.database.engine:create_engine:90 - Failed to create synchronous engine for postgresql://user:password@localhost:5432/ultimate_electrical_designer: (psycopg2.OperationalError) connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-22 00:08:12.698 | ERROR    | SpawnProcess-5:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-22 00:08:13.121 | ERROR    | SpawnProcess-5:MainThread | src.core.database.engine:create_engine:90 - Failed to create synchronous engine for postgresql://user:password@localhost:5432/ultimate_electrical_designer: (psycopg2.OperationalError) connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-22 00:09:51.924 | ERROR    | SpawnProcess-6:MainThread | src.core.database.dual_engine:_initialize_postgresql:80 - Failed to initialize PostgreSQL engine: Database error: Failed to test database connection: [Errno 111] Connection refused
2025-07-22 00:09:52.343 | ERROR    | SpawnProcess-6:MainThread | src.core.database.engine:create_engine:90 - Failed to create synchronous engine for postgresql://user:password@localhost:5432/ultimate_electrical_designer: (psycopg2.OperationalError) connection to server at "localhost" (127.0.0.1), port 5432 failed: Connection refused
	Is the server running on that host and accepting TCP/IP connections?

(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-24 21:57:42.461 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:381 - [DATABASE] INTERNAL_ERROR: An unexpected internal error occurred.
2025-07-24 21:57:42.461 | Level 40 | MainProcess:MainThread | src.core.errors.unified_error_handler:_log_error:381 - [DATABASE] DB_OPERATION_FAILED: Database error: Database operation failed: An unexpected internal error occurred.
2025-07-24 21:57:42.463 | ERROR    | MainProcess:MainThread | __main__:seed_general_data_command:153 - Error seeding general data: Database error: Database operation failed: An unexpected internal error occurred.
