# server/Dockerfile
# This Dockerfile is for building the backend server image

# Commands to get the image running locally
#
# create and start the database and backend containers:
# ```bash
# docker compose up -d db backend
# ```
#
# view the logs for the backend container:
# ```bash
# docker logs -f backend
# ```

# Use an official Python runtime as a parent image
FROM python:3.11-slim-bookworm

# Set the working directory in the container
WORKDIR /app

# Copy the requirements file to the working directory
RUN pip install poetry
COPY poetry.lock pyproject.toml ./
RUN poetry config virtualenvs.create false && poetry install --no-interaction --no-ansi --no-root


# Copy the rest of your application code to the working directory
COPY . .

# Expose the port your FastAPI application runs on
EXPOSE 8000

# Command to run the application
# This should match the `command` in your docker-compose.yml
CMD ["bash", "-c", "alembic -c src/alembic.ini upgrade head && uvicorn src.main:app --host 0.0.0.0 --port 8000"]