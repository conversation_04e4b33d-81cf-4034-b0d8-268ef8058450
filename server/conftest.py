"""
Pytest configuration.

This file configures pytest to log only failing tests and errors
to a dedicated log file (`test_failures.log`) for easier debugging.
"""

import logging
import os

import pytest
from _pytest.reports import TestReport

# Configure a logger specifically for test failures
failure_logger = logging.getLogger("failures")
failure_logger.setLevel(logging.INFO)

# Create a file handler to write to a log file.
# This will create 'test_failures.log' in your project root.
# mode='w' will overwrite the log file on each run.
handler = logging.FileHandler("test_failures.log", mode="w")
handler.setLevel(logging.INFO)

# Define the format for the log messages
formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
handler.setFormatter(formatter)

# Add the handler to the logger (avoid duplicate handlers)
if not failure_logger.handlers:
    failure_logger.addHandler(handler)


def pytest_collection_modifyitems(config, items):
    performance_dir = os.path.join("tests", "performance")
    integration_dir = os.path.join("tests", "integration")  # Define the integration directory

    for item in items:
        # Mark tests in the 'tests/performance' directory
        if str(item.fspath).startswith(performance_dir):
            item.add_marker(pytest.mark.performance)

        # Mark tests in the 'tests/integration' directory
        if str(item.fspath).startswith(integration_dir):
            item.add_marker(pytest.mark.integration)


@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """
    Pytest hook to process test reports.
    """
    # Execute all other hooks to obtain the report object
    outcome = yield
    report = outcome.get_result()

    # Log failures and errors from the 'call' phase (the test execution)
    if report.when == "call" and report.failed:
        message = f"Test Failed: {report.nodeid}\n"
        message += f"Failure Details:\n{report.longreprtext}"
        failure_logger.error(message)

    # Also log errors from the setup and teardown phases
    if report.when in ("setup", "teardown") and report.failed:
        message = f"Error in {report.when}: {report.nodeid}\n"
        message += f"Error Details:\n{report.longreprtext}"
        failure_logger.critical(message)
