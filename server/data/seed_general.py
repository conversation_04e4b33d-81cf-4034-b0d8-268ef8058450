"""
This script, `seed_general.py`, provides a comprehensive and idempotent mechanism
for seeding the Ultimate Electrical Designer's database with foundational data
across various general models. It ensures a consistent and ready-to-use
development environment by populating essential entities such as user roles,
user accounts, project data, and the core electrical component catalog.

The seeding process is designed to be idempotent, meaning it can be executed
multiple times without creating duplicate records, making it safe for
repeated runs during development or testing phases. It adheres to a strict
order of operations to satisfy data dependencies (e.g., roles are seeded
before users, and component infrastructure before individual components).

Key functions include:
- `seed_roles`: Populates default user roles (<PERSON><PERSON>, Engineer, Viewer).
- `seed_users`: Creates sample user accounts with assigned roles and preferences.
- `seed_projects_and_members`: Seeds sample projects and assigns users as members.
- `seed_component_infrastructure`: Establishes the foundational component
  classification system by seeding `ComponentCategory` and `ComponentType`
  records based on defined enums and mappings.
- `seed_components`: Generates one dummy `Component` entry for each
  `ComponentType` enum member, providing initial catalog data.
- `seed_logs`: Adds sample activity and audit trail logs for system monitoring
  and history.

This script is critical for setting up a functional local development
or testing environment quickly and reliably, aligning with the project's
emphasis on engineering-grade quality and structured data management.
"""

import asyncio
import logging
from datetime import date, datetime, timezone
from typing import Any, Dict, List

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.config.settings import settings
from src.core.database.session import get_async_db_session
from src.core.enums.electrical_enums import (
    COMPONENT_TYPE_TO_CATEGORY_MAPPING,
    ComponentCategoryType,
    ComponentType as ComponentTypeEnum,
)
from src.core.models.general.activity_log import ActivityLog, AuditTrail
from src.core.models.general.component import Component
from src.core.models.general.component_category import ComponentCategory
from src.core.models.general.component_type import ComponentType
from src.core.models.general.project import Project, ProjectMember
from src.core.models.general.user import User, UserPreference
from src.core.models.general.user_role import UserRole, UserRoleAssignment
from src.core.security.password_handler import PasswordHandler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def seed_roles(db: AsyncSession) -> None:
    """Seeds the UserRole table if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking roles...")
    existing_roles_result = await db.execute(select(UserRole.name))
    existing_roles = set(existing_roles_result.scalars().all())

    roles_to_add = [
        UserRole(name="Admin", description="Administrator with full access"),  # type: ignore
        UserRole(name="Engineer", description="Engineer with project access"),  # type: ignore
        UserRole(name="Viewer", description="Viewer with read-only access"),  # type: ignore
    ]

    new_roles = [role for role in roles_to_add if role.name not in existing_roles]
    if new_roles:
        db.add_all(new_roles)
        await db.commit()
        logger.info(f"Seeded {len(new_roles)} new roles.")
    else:
        logger.info("Roles already seeded.")


async def seed_users(db: AsyncSession) -> None:
    """Seeds users with various roles if they don't already exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking users...")

    users_to_seed: List[Dict[str, Any]] = [
        {
            "name": "admin",
            "email": "<EMAIL>",
            "password": settings.SECRET_KEY,
            "is_superuser": True,
            "role_name": "Admin",
        },
        {
            "name": "engineer",
            "email": "<EMAIL>",
            "password": "EngineerPassword123",
            "is_superuser": False,
            "role_name": "Engineer",
        },
        {
            "name": "viewer",
            "email": "<EMAIL>",
            "password": "ViewerPassword123",
            "is_superuser": False,
            "role_name": "Viewer",
        },
    ]

    for user_data in users_to_seed:
        user_result = await db.execute(select(User).filter_by(name=user_data["name"]))
        if user_result.scalars().first():
            logger.info(f"User '{user_data['name']}' already exists.")
            continue

        logger.info(f"Seeding user: {user_data['name']}")
        hashed_password = PasswordHandler.hash_password(str(user_data["password"]))
        new_user = User()
        new_user.name = user_data["name"]
        new_user.email = user_data["email"]
        new_user.password_hash = hashed_password
        new_user.is_active = True
        new_user.is_superuser = user_data["is_superuser"]
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)

        role_result = await db.execute(select(UserRole).filter_by(name=user_data["role_name"]))
        role = role_result.scalars().first()
        if role:
            assignment = UserRoleAssignment()
            assignment.user_id = new_user.id
            assignment.role_id = role.id
            assignment.name = f"{new_user.name}-{role.name} Assignment"
            db.add(assignment)

        preference = UserPreference()
        preference.user_id = new_user.id
        preference.name = f"{new_user.name}'s Preferences"
        preference.ui_theme = "light"
        db.add(preference)

        await db.commit()
        logger.info(f"User '{user_data['name']}' seeded successfully.")


async def seed_projects_and_members(db: AsyncSession) -> None:
    """Seeds sample projects and assigns members.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking for sample projects...")

    projects_to_seed: List[Dict[str, str]] = [
        {
            "name": "Industrial Plant Expansion",
            "project_number": "PROJ-001",
            "client": "Global Manufacturing Co.",
            "description": "Electrical design for the new manufacturing wing.",
        },
        {
            "name": "Commercial Building Upgrade",
            "project_number": "PROJ-002",
            "client": "City Properties LLC",
            "description": "Lighting and power system upgrade for a 10-story office building.",
        },
    ]

    users_result = await db.execute(select(User))
    users = {user.name: user for user in users_result.scalars().all()}

    roles_result = await db.execute(select(UserRole))
    roles = {role.name: role for role in roles_result.scalars().all()}

    for project_data in projects_to_seed:
        project_result = await db.execute(select(Project).filter_by(project_number=project_data["project_number"]))
        project = project_result.scalars().first()

        if not project:
            logger.info(f"Seeding project: {project_data['name']}")
            project = Project(  # type: ignore
                name=project_data["name"],
                project_number=project_data["project_number"],  # type: ignore
                client=project_data["client"],
                description=project_data["description"],  # type: ignore
                status="active",
                available_voltages_json='{"voltages": [230, 400, 480]}',  # type: ignore
            )
            db.add(project)
            await db.commit()
            await db.refresh(project)
            logger.info(f"Project '{project.name}' seeded.")

            if project.project_number == "PROJ-001":
                if users.get("admin") and roles.get("Admin"):
                    member = ProjectMember()
                    member.user_id = users["admin"].id
                    member.project_id = project.id
                    member.role_id = roles["Admin"].id
                    member.name = f"{users['admin'].name}-{project.name} Membership"
                    db.add(member)
                if users.get("engineer") and roles.get("Engineer"):
                    member = ProjectMember()
                    member.user_id = users["engineer"].id
                    member.project_id = project.id
                    member.role_id = roles["Engineer"].id
                    member.name = f"{users['engineer'].name}-{project.name} Membership"
                    db.add(member)
            elif project.project_number == "PROJ-002":
                if users.get("engineer") and roles.get("Engineer"):
                    member = ProjectMember()
                    member.user_id = users["engineer"].id
                    member.project_id = project.id
                    member.role_id = roles["Engineer"].id
                    member.name = f"{users['engineer'].name}-{project.name} Membership"
                    db.add(member)
                if users.get("viewer") and roles.get("Viewer"):
                    member = ProjectMember()
                    member.user_id = users["viewer"].id
                    member.project_id = project.id
                    member.role_id = roles["Viewer"].id
                    member.name = f"{users['viewer'].name}-{project.name} Membership"
                    db.add(member)

            await db.commit()
            logger.info(f"Members assigned to project '{project.name}'.")
        else:
            logger.info(f"Project '{project_data['name']}' already exists.")


async def seed_components(db: AsyncSession) -> None:
    """Seeds one dummy component for each ComponentType enum member.

    This function ensures that every defined component type in the enum has at
    least one corresponding dummy component entry in the database. It is
    idempotent and will not create duplicates if a component for a given
    type already exists.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Preparing to seed dummy components for all component types...")

    # 1. Fetch existing component types and categories from DB
    types_result = await db.execute(select(ComponentType))
    type_map = {comp_type.name: comp_type for comp_type in types_result.scalars().all()}

    categories_result = await db.execute(select(ComponentCategory))
    category_map = {cat.name: cat for cat in categories_result.scalars().all()}

    # 2. Idempotence Check: Get all component_type_ids already in the Component table
    existing_components_result = await db.execute(select(Component.component_type_id).distinct())
    seeded_type_ids = set(existing_components_result.scalars().all())

    components_to_add = []
    # 3. Iterate through all ComponentType enums
    for component_type_enum in ComponentTypeEnum:
        # 4. Retrieve associated data from maps
        component_type_db = type_map.get(component_type_enum.value)

        if not component_type_db:
            logger.warning(
                f"ComponentType '{component_type_enum.value}' not found in the database. "
                "Skipping. Ensure seed_component_infrastructure has run."
            )
            continue

        # Idempotence check: if a component of this type exists, skip
        if component_type_db.id in seeded_type_ids:
            continue

        category_enum = COMPONENT_TYPE_TO_CATEGORY_MAPPING.get(component_type_enum)
        if not category_enum:
            logger.warning(f"No category mapping found for ComponentType '{component_type_enum.value}'. Skipping.")
            continue

        category_db = category_map.get(category_enum.value)
        if not category_db:
            logger.warning(
                f"ComponentCategory '{category_enum.value}' not found in the database for "
                f"ComponentType '{component_type_enum.value}'. Skipping."
            )
            continue

        # 5. Construct Dummy Component Object
        logger.info(f"Preparing dummy component for type: {component_type_enum.value}")
        dummy_component = Component(  # type: ignore
            name=f"Dummy {component_type_enum.value}",  # type: ignore
            manufacturer="Generic Corp",  # type: ignore
            model_number=f"DUMMY-{component_type_enum.name.replace('_', '-')}",  # type: ignore
            description=f"A generic, auto-seeded component for {component_type_enum.value}.",  # type: ignore
            component_type_id=component_type_db.id,  # type: ignore
            category_id=category_db.id,  # type: ignore
            specifications="{}",  # type: ignore
            unit_price=1.00,  # type: ignore
            currency="USD",  # type: ignore
            is_active=True,  # type: ignore
        )
        components_to_add.append(dummy_component)

    # 6. Batch Insertion
    if components_to_add:
        logger.info(f"Adding {len(components_to_add)} new dummy components to the session...")
        db.add_all(components_to_add)
        await db.commit()
        logger.info("Batch commit complete. Dummy components seeded successfully.")
    else:
        logger.info("All component types already have at least one dummy component seeded.")


async def seed_logs(db: AsyncSession) -> None:
    """Seeds a variety of sample activity and audit trail logs.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Checking for sample logs...")

    log_check = await db.execute(select(ActivityLog).filter_by(action_type="USER_LOGIN"))
    if log_check.scalars().first():
        logger.info("Sample logs already exist.")
        return

    logger.info("Seeding sample logs...")

    users_result = await db.execute(select(User))
    users = {user.name: user for user in users_result.scalars().all()}

    projects_result = await db.execute(select(Project))
    projects = list(projects_result.scalars().all())

    if not users or not projects:
        logger.warning("Users or projects not found, skipping log seeding.")
        return

    admin = users.get("admin")
    engineer = users.get("engineer")
    project1 = projects[0]

    if not admin:
        logger.warning("Admin user not found, cannot seed logs.")
        return

    login_activity = ActivityLog()
    login_activity.user_id = admin.id
    login_activity.action_type = "USER_LOGIN"
    login_activity.name = f"{admin.name} - USER_LOGIN"
    login_activity.action_description = f"User '{admin.name}' logged in successfully."
    login_activity.status = "SUCCESS"
    login_activity.request_ip = "127.0.0.1"
    login_activity.is_security_related = True
    db.add(login_activity)
    await db.commit()
    await db.refresh(login_activity)

    project_creation_audit = AuditTrail()
    project_creation_audit.activity_log_id = login_activity.id
    project_creation_audit.user_id = admin.id
    project_creation_audit.table_name = "Project"
    project_creation_audit.record_id = project1.id
    project_creation_audit.operation = "INSERT"
    project_creation_audit.name = f"Project - INSERT - {project1.id}"
    project_creation_audit.new_value = f"Project '{project1.name}' created."
    project_creation_audit.change_reason = "Initial seeding"
    db.add(project_creation_audit)

    component_result = await db.execute(select(Component).filter_by(model_number="DUMMY-CABLE_POWER"))
    component = component_result.scalars().first()
    if component and engineer:
        update_activity = ActivityLog()
        update_activity.user_id = engineer.id
        update_activity.action_type = "COMPONENT_UPDATE"
        update_activity.name = f"{engineer.name} - COMPONENT_UPDATE"
        update_activity.action_description = f"Component '{component.name}' updated."
        update_activity.target_id = component.id
        update_activity.target_type = "Component"
        update_activity.status = "SUCCESS"
        db.add(update_activity)
        await db.commit()
        await db.refresh(update_activity)

        update_audit = AuditTrail()
        update_audit.activity_log_id = update_activity.id
        update_audit.user_id = engineer.id
        update_audit.table_name = "Component"
        update_audit.record_id = component.id
        update_audit.operation = "UPDATE"
        update_audit.name = f"Component - UPDATE - {component.id}"
        update_audit.field_name = "description"
        update_audit.old_value = component.description
        update_audit.new_value = "4-core XLPE insulated power cable (updated spec)."
        update_audit.change_reason = "Specification update"
        db.add(update_audit)

    await db.commit()
    logger.info("Sample logs seeded.")


async def seed_component_infrastructure(db: AsyncSession) -> None:
    """Seeds component categories and types from enums if they don't exist.

    Args:
        db: The SQLAlchemy async session.
    """
    logger.info("Seeding component infrastructure...")

    existing_categories_result = await db.execute(select(ComponentCategory.name))
    existing_categories = set(existing_categories_result.scalars().all())

    categories_to_add = [
        ComponentCategory(name=cat_enum.value, description=f"Category for {cat_enum.value}")  # type: ignore
        for cat_enum in ComponentCategoryType
        if cat_enum.value not in existing_categories
    ]

    if categories_to_add:
        db.add_all(categories_to_add)
        await db.commit()
        logger.info(f"Seeded {len(categories_to_add)} new component categories.")

    all_categories_result = await db.execute(select(ComponentCategory))
    category_map = {cat.name: cat for cat in all_categories_result.scalars().all()}

    existing_types_result = await db.execute(select(ComponentType.name, ComponentType.category_id))
    existing_types = {(name, cat_id) for name, cat_id in existing_types_result.all()}

    types_to_add = []
    for component_enum, category_enum in COMPONENT_TYPE_TO_CATEGORY_MAPPING.items():
        category_obj = category_map.get(category_enum.value)
        if not category_obj:
            logger.warning(
                f"Category '{category_enum.value}' not found. Skipping component type '{component_enum.value}'."
            )
            continue

        if (component_enum.value, category_obj.id) not in existing_types:
            types_to_add.append(
                ComponentType(  # type: ignore
                    name=component_enum.value,  # type: ignore
                    description=f"Component type for {component_enum.value}",  # type: ignore
                    category_id=category_obj.id,  # type: ignore
                )
            )

    if types_to_add:
        db.add_all(types_to_add)
        await db.commit()
        logger.info(f"Seeded {len(types_to_add)} new component types.")

    if not categories_to_add and not types_to_add:
        logger.info("Component infrastructure already seeded.")


async def seed_general_data() -> None:
    """Main function to run all seeding operations."""
    logger.info("Starting database seeding for general models...")
    async with get_async_db_session() as db:
        await seed_roles(db)
        await seed_users(db)
        await seed_component_infrastructure(db)
        await seed_projects_and_members(db)
        await seed_components(db)
        await seed_logs(db)
    logger.info("Database seeding completed.")


if __name__ == "__main__":
    # It's recommended to run this script using `python -m src.data.seed_general`
    # from the `server` directory to ensure correct module resolution.
    asyncio.run(seed_general_data())
