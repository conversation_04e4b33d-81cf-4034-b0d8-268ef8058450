alembic==1.16.4
annotated-types==0.7.0
anyio==4.9.0
argon2-cffi==25.1.0
argon2-cffi-bindings==21.2.0
asyncpg==0.30.0
attrs==25.3.0
babel==2.17.0
backrefs==5.9
bandit==1.8.6
bcrypt==4.3.0
bidict==0.23.1
blinker==1.9.0
brotli==1.1.0
certifi==2025.7.14
cffi==1.17.1
cfgv==3.4.0
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
colorama==0.4.6
configargparse==1.7.1
coverage==7.9.2
cryptography==45.0.5
cssselect2==0.8.0
distlib==0.4.0
dnspython==2.7.0
ecdsa==0.19.1
email-validator==2.2.0
et-xmlfile==2.0.0
execnet==2.1.1
factory-boy==3.3.3
faker==37.4.2
fastapi==0.116.1
fastapi-cli==0.0.8
fastapi-cloud-cli==0.1.4
filelock==3.12.4
flask==3.1.1
flask-cors==6.0.1
flask-login==0.6.3
fonttools==4.59.0
gevent==25.5.1
geventhttpclient==2.3.4
ghp-import==2.1.0
greenlet==3.2.3
griffe==1.7.3
h11==0.16.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
hypothesis==6.136.1
identify==2.6.12
idna==3.10
iniconfig==2.1.0
itsdangerous==2.2.0
jinja2==3.1.6
jsonpath-ng==1.7.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
locust==2.37.14
locust-cloud==1.26.3
loguru==0.7.3
mako==1.3.10
markdown==3.8.2
markdown-it-py==3.0.0
markupsafe==3.0.2
mdurl==0.1.2
memory-profiler==0.61.0
mergedeep==1.3.4
mkdocs==1.6.1
mkdocs-autorefs==1.4.2
mkdocs-get-deps==0.2.0
mkdocs-material==9.6.15
mkdocs-material-extensions==1.3.1
mkdocstrings==0.29.1
mkdocstrings-python==1.16.12
msgpack==1.1.1
mypy==1.17.0
mypy-extensions==1.1.0
networkx==3.5
nodeenv==1.9.1
numpy==2.3.1
openpyxl==3.1.5
packaging==25.0
paginate==0.5.7
pandas==2.3.1
pandas-stubs==2.3.0.250703
pathspec==0.12.1
pbr==6.1.1
pillow==11.3.0
platformdirs==4.3.8
pluggy==1.6.0
ply==3.11
pre-commit==4.2.0
psutil==7.0.0
psycopg2-binary==2.9.10
py-cpuinfo==9.0.0
pyasn1==0.6.1
pycparser==2.22
pydantic==2.11.7
pydantic-core==2.33.2
pydantic-settings==2.10.1
pydyf==0.11.0
pygments==2.19.2
pymdown-extensions==10.16
pyphen==0.17.2
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-benchmark==5.1.0
pytest-cov==6.2.1
pytest-env==1.1.5
pytest-html==4.1.1
pytest-metadata==3.1.1
pytest-mock==3.14.1
pytest-timeout==2.4.0
pytest-xdist==3.8.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-engineio==4.12.2
python-jose==3.5.0
python-multipart==0.0.20
python-socketio==5.13.0
pytz==2025.2
pywin32==311
pyyaml==6.0.2
pyyaml-env-tag==1.1
pyzmq==27.0.0
redis==6.2.0
referencing==0.36.2
requests==2.32.4
rich==14.0.0
rich-toolkit==0.14.8
rignore==0.6.4
rpds-py==0.26.0
rsa==4.9.1
ruff==0.12.4
scipy==1.16.0
sentry-sdk==2.33.0
setuptools==80.9.0
shellingham==1.5.4
simple-websocket==1.1.0
six==1.17.0
sniffio==1.3.1
sortedcontainers==2.4.0
sqlalchemy==2.0.41
starlette==0.47.2
stevedore==5.4.1
structlog==25.4.0
tinycss2==1.4.0
tinyhtml5==2.0.0
typer==0.16.0
types-psutil==7.0.0.20250601
types-pyasn1==0.6.0.20250516
types-python-jose==3.5.0.20250531
types-pytz==2025.2.0.20250516
typing-extensions==4.14.1
typing-inspection==0.4.1
tzdata==2025.2
-e file:///D:/Projects/ued/server
urllib3==2.5.0
uuid6==2025.0.1
uvicorn==0.35.0
virtualenv==20.31.2
watchdog==6.0.0
watchfiles==1.1.0
weasyprint==65.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
werkzeug==3.1.3
win32-setctime==1.2.0
wsproto==1.2.0
xlsxwriter==3.2.5
zope-event==5.1
zope-interface==7.2
zopfli==0.2.3.post1
