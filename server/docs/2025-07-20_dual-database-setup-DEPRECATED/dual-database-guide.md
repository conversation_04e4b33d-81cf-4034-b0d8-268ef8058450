# Dual Database System Guide

## Overview

The Ultimate Electrical Designer implements a sophisticated dual-database architecture supporting both **PostgreSQL** (online) and **SQLite** (offline) databases. This system enables seamless operation in both connected and disconnected environments while maintaining data consistency.

## Architecture Components

### 1. Database Engine Management

- **DualDatabaseManager**: Central coordinator for all database engines
- **DatabaseMode**: Enum defining operation modes (ONLINE, OFFLINE, HYBRID, AUTO)
- **Automatic Fallback**: Graceful degradation when primary database unavailable

### 2. Session Management

- **DualDatabaseSessionManager**: Context-aware session creation
- **Automatic Routing**: Intelligent database selection based on operation context
- **Hybrid Sessions**: Simultaneous access to both databases

### 3. Routing System

- **DatabaseRoutingEngine**: Intelligent operation routing
- **Data Classification**: Automatic categorization of models for routing decisions
- **Conflict Resolution**: Built-in conflict handling strategies

### 4. Synchronization

- **DatabaseSyncManager**: Bi-directional sync between PostgreSQL and SQLite
- **Sync Outbox Pattern**: Reliable offline change tracking
- **Conflict Resolution**: Last-write-wins with user notification

### 5. Migration Support

- **DualDatabaseMigrationManager**: Coordinated migrations across databases
- **Separate Migration Scripts**: Database-specific migration handling
- **Status Monitoring**: Real-time migration status tracking

## Configuration

### Environment Variables

```bash
# Primary database (PostgreSQL)
DATABASE_URL=postgresql://user:pass@localhost/electrical_designer

# Offline database (SQLite)
SQLITE_OFFLINE_DATABASE_PATH=offline_app.db

# Legacy fallback (development/testing)
SQLITE_DATABASE_PATH=app_dev.db

# Operation mode
DATABASE_MODE=online|offline|hybrid|auto
```

### Database Modes

- **ONLINE**: PostgreSQL only (production default)
- **OFFLINE**: SQLite only (disconnected mode)
- **HYBRID**: Both databases active (development/testing)
- **AUTO**: Runtime detection and fallback

## Usage

### Basic Operations

```python
from src.core.database.dual_engine import initialize_dual_database_system

# Initialize the system
await initialize_dual_database_system()

# Get the manager
manager = await get_dual_database_manager()
```

### Session Management

```python
from src.core.database.dual_session import get_dual_session_manager

# Get session manager
session_manager = await get_dual_session_manager()

# Get PostgreSQL session
async with session_manager.get_postgresql_session() as session:
    # PostgreSQL operations
    pass

# Get SQLite session
async with session_manager.get_sqlite_session() as session:
    # SQLite operations
    pass

# Get hybrid sessions
async with session_manager.get_hybrid_sessions() as sessions:
    pg_session = sessions["postgresql"]
    sqlite_session = sessions["sqlite"]
```

### Service Layer Integration

```python
from src.core.services.general.dual_database_service import get_dual_database_service

# Get service for a specific model
service = await get_dual_database_service(Project)

# Create entity
created = await service.create({"name": "Test Project"})

# Get entity
project = await service.get_by_id(1)

# List entities
projects = await service.list_all()
```

### Synchronization

```python
from src.core.database.sync_manager import get_sync_manager

# Get sync manager
sync_manager = await get_sync_manager()

# Perform full sync
result = await sync_manager.perform_full_sync()

# Get sync status
status = await sync_manager.get_sync_status()
```

## API Endpoints

### Database Management

- `GET /api/v1/database/status` - Database system status
- `POST /api/v1/database/switch-mode` - Switch database mode
- `GET /api/v1/database/sync/status` - Sync status
- `POST /api/v1/database/sync/trigger` - Trigger synchronization
- `GET /api/v1/database/migration/status` - Migration status

### Health Monitoring

- `GET /api/v1/database/health` - Comprehensive health check
- `GET /api/v1/database/health/detailed` - Detailed health metrics

## Model Classification

### Offline-Enabled Models

- Project
- Component
- UserPreference
- ProjectComponent
- CalculationResult
- DesignTemplate

### Online-Only Models

- User
- UserRole
- AuditLog
- SystemSetting
- ComponentCatalog

## Migration Strategy

### Initial Setup

```bash
# Run migrations for all databases
make db-migrate-all

# Check migration status
make db-status-all

# Create new migration
make db-create-migration MESSAGE="Add new feature"
```

### Database-Specific Migrations

- **PostgreSQL**: `src/alembic/`
- **SQLite Offline**: `src/alembic_sqlite_offline/`
- **SQLite Legacy**: `src/alembic_sqlite_legacy/`

## Testing

### Running Tests

```bash
# Run dual-database integration tests
pytest tests/integration/test_dual_database.py -v

# Run all database tests
make test-server-integration
```

### Test Configuration

- Uses in-memory databases for speed
- Tests both PostgreSQL and SQLite paths
- Validates synchronization workflows
- Tests mode switching and fallback behavior

## Monitoring and Debugging

### Health Checks

```bash
# Check database health
curl http://localhost:8000/api/v1/database/health

# Get detailed status
curl http://localhost:8000/api/v1/database/status
```

### Logging

- **Engine Initialization**: `src.core.database.dual_engine`
- **Session Management**: `src.core.database.dual_session`
- **Routing Decisions**: `src.core.database.database_router`
- **Synchronization**: `src.core.database.sync_manager`

### Performance Metrics

- Connection pool utilization
- Query execution times
- Synchronization throughput
- Conflict resolution statistics

## Best Practices

### 1. Mode Selection

- Use `ONLINE` for production (PostgreSQL)
- Use `OFFLINE` for field work (SQLite)
- Use `HYBRID` for development/testing
- Use `AUTO` for dynamic environments

### 2. Data Consistency

- Always use service layer abstractions
- Implement proper error handling
- Monitor sync status regularly
- Handle conflicts appropriately

### 3. Performance Optimization

- Use connection pooling
- Implement query caching
- Monitor resource usage
- Optimize sync intervals

### 4. Security

- Validate all database inputs
- Use parameterized queries
- Implement proper access controls
- Secure database connections

## Troubleshooting

### Common Issues

#### Connection Failures

```python
# Check connection status
manager = await get_dual_database_manager()
status = manager.get_connection_status()

# Test connections
test_results = await manager.test_connections()
```

#### Sync Failures

```python
# Check sync status
sync_manager = await get_sync_manager()
status = await sync_manager.get_sync_status()

# Manual sync trigger
result = await sync_manager.perform_full_sync()
```

#### Migration Issues

```python
# Check migration status
migration_manager = await get_migration_manager()
status = await migration_manager.check_migration_status()
```

### Debug Commands

```bash
# Check database mode
curl http://localhost:8000/api/v1/database/modes

# Switch to offline mode
curl -X POST http://localhost:8000/api/v1/database/switch-mode \
  -H "Content-Type: application/json" \
  -d '{"mode": "offline"}'

# Trigger sync
curl -X POST http://localhost:8000/api/v1/database/sync/trigger
```

## Integration Examples

### Web Application Integration

```javascript
// JavaScript client example
const syncDatabase = async () => {
  const response = await fetch('/api/v1/database/sync/trigger', {
    method: 'POST'
  });
  return response.json();
};

const switchMode = async (mode) => {
  const response = await fetch('/api/v1/database/switch-mode', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ mode })
  });
  return response.json();
};
```

### CLI Tool Integration

```bash
#!/bin/bash
# Database management script

# Check status
db_status() {
    curl -s http://localhost:8000/api/v1/database/status | jq .
}

# Switch mode
db_mode() {
    curl -s -X POST http://localhost:8000/api/v1/database/switch-mode \
        -H "Content-Type: application/json" \
        -d "{\"mode\": \"$1\"}" | jq .
}

# Sync databases
db_sync() {
    curl -s -X POST http://localhost:8000/api/v1/database/sync/trigger | jq .
}
```

## Future Enhancements

### Planned Features

- **Real-time sync monitoring** via WebSocket
- **Advanced conflict resolution** with user prompts
- **Database sharding** for horizontal scaling
- **Cross-region synchronization** for distributed teams
- **Automated backup and recovery** workflows
- **Performance dashboards** and alerting

### Performance Optimizations

- **Query result caching** with Redis
- **Connection pooling** optimization
- **Bulk operation** support
- **Parallel sync processing**
- **Incremental sync** improvements

## Support and Maintenance

### Regular Tasks

1. **Monitor sync status** daily
2. **Check migration status** weekly
3. **Review performance metrics** monthly
4. **Update database schema** as needed
5. **Test failover scenarios** quarterly

### Emergency Procedures

1. **Database failover** documentation
2. **Data recovery** procedures
3. **Sync conflict resolution** guides
4. **Performance troubleshooting** checklist

This comprehensive dual-database system provides enterprise-grade reliability and flexibility for the Ultimate Electrical Designer, ensuring seamless operation in both connected and disconnected environments.
