# Dual Database Setup: Discovery & Analysis

## Executive Summary

**SQLite will be used in production for offline use**. This is a crucial piece of information that significantly alters the database strategy we've been building towards.


### ⚠️ Revised Database Strategy: Dual Production Databases

The prior guidance assumed PostgreSQL as the *sole* production database. With SQLite now serving a dedicated **offline production role**, our database strategy for the Ultimate Electrical Designer must be re-evaluated and designed to accommodate **two concurrent production databases**:

1.  **PostgreSQL**: For primary, online operations.
2.  **SQLite**: For specific offline functionality in production.

This dual-database approach introduces **significant complexity** that needs careful architectural consideration, aligning with our **5-layer architecture** and **Domain-Driven Design (DDD) principles** outlined in `design.md` and `rules.md`.

---

## 1. Design Implications for Production Environment 🏗️

The current `effective_database_url` property and `initialize_database_engine` logic are designed for a single primary database (PostgreSQL) with an optional fallback to SQLite *only for development/testing*. They **do not** support:

* **Simultaneous Connections**: Establishing and managing connections to *both* PostgreSQL and SQLite in a single production application instance.
* **Contextual Switching**: Dynamically deciding which database to use (PostgreSQL for online features, SQLite for offline features) based on the application's current mode or network availability.
* **Data Synchronization**: Handling how data from offline SQLite use gets synchronized back to the primary PostgreSQL database when connectivity is restored.

---

## 2. Recommended Approach for Dual Database Setup (Phase 1: Core Infrastructure) 💡

Given this new requirement, a dedicated **Discovery & Analysis (Phase 1)** effort is immediately necessary to define the architecture for this dual-database production setup.

### a. Define Data Separation & Access Patterns

* **Identify Offline Data Domains**: Clearly define *which* data (e.g., specific domain aggregates, entities, or value objects) will be stored and managed exclusively or primarily by SQLite for offline use. For example, local user preferences, cached project data for offline viewing, or limited local modifications.
* **Establish Access Boundaries**: Determine which services and repositories will interact with the PostgreSQL database, and which will interact with the SQLite database. Avoid direct mixing within a single repository if possible, or create explicit interfaces for database-agnostic operations.
* **Repository Layer Abstraction**: Reinforce the **Repository Layer** (`structure.md`) to abstract the underlying database. You might need to implement *two distinct repository implementations* for the same domain, one for PostgreSQL and one for SQLite, with a strategy pattern to select the appropriate one based on the application's online/offline status.

### b. Design Synchronization Mechanisms

* **Offline-to-Online Sync**: Crucial for data consistency. How will data modified in SQLite (offline) be pushed to PostgreSQL (online)?
    * **Timestamp-based Sync**: Track `last_modified_at` timestamps on entities.
    * **Conflict Resolution**: How will conflicts be resolved if the same data is modified both offline and online? (e.g., last-write-wins, user-prompted resolution).
    * **Background Sync Jobs**: Implement background tasks or event-driven mechanisms to handle synchronization when the application regains connectivity.
* **Online-to-Offline Sync (Initial Download/Caching)**: How will the necessary data be populated into the SQLite database when the application first goes offline or is started in offline mode?

### c. Update Database Connection Management

The current `initialize_database_engine` will need to be fundamentally refactored:

* **Multiple Engine Instances**: Instead of a single `async_engine`, you'll likely need distinct engines for PostgreSQL and SQLite.
    * `pg_async_engine: AsyncEngine`
    * `sqlite_async_engine: AsyncEngine`
* **Runtime Database Selection**: The `get_async_db_session()` dependency will need to receive context (e.g., an `is_offline_mode` flag or a specific `db_type` enum) to determine which engine's session to yield. This will require changes to your dependency injection strategy.
* **Error Handling Refinement**: Specific error handling for each database type will be critical. If PostgreSQL is down in production, the application might still run in a limited offline-only mode via SQLite.

### d. Configuration and Environment Variables

* **Clearer Naming**: Ensure environment variables clearly distinguish between the primary online DB and the offline DB, e.g., `PG_DATABASE_URL` and `SQLITE_OFFLINE_PATH`.
* **Mode Switches**: You might need an `APP_MODE` environment variable (e.g., `online`, `offline`, `hybrid`) to dictate runtime database behavior.

---

## 3. Immediate Action: Dedicated Design Session 🧑‍💻

This new requirement is a significant architectural decision. Before making any code changes, I strongly recommend a dedicated **design session** (part of **Phase 1: Discovery & Analysis**) involving key stakeholders to:

1.  **Define Scope**: Precisely what data needs offline access and why.
2.  **Choose Synchronization Strategy**: Decide on the mechanism for data flow between PostgreSQL and SQLite.
3.  **Refine Architectural Pattern**: Detail how the 5-layer architecture will adapt to support dual databases, specifically in the **Service Layer** and **Repository Layer**.
4.  **Update `design.md`**: Document the chosen approach thoroughly, including diagrams for data flow and database interaction.

This ensures we apply **Domain-Driven Design (DDD) principles** and maintain **engineering-grade quality** while building a robust solution for offline production capabilities.