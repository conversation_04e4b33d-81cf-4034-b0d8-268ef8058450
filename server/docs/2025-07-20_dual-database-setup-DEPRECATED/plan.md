# Dual Database Setup: Plan of Attack

## 1. Define Scope: Offline Data Access Needs 🌐

The core principle for offline data is to provide **essential functionality** when network connectivity is absent. This means not all data needs to be available offline, only what's critical for ongoing work or basic functionality.

* **Core Project Data**:
  * **Need Offline Access**: Current project details (name, description, unique ID, status), active design components (selected from the catalog, placed in a design), basic calculation parameters (e.g., ambient temperature, voltage levels for the specific project).
  * **Why**: Engineers must be able to continue working on an active project's design, modify component properties, and potentially perform preliminary calculations even without internet access. This ensures continuity and productivity in varied environments (e.g., onsite, remote locations with poor connectivity).
* **User-Specific Local Data**:
  * **Need Offline Access**: User preferences, recent project list (metadata only), perhaps basic authentication token for session persistence (though full re-authentication might require online).
  * **Why**: Enhances user experience by providing quick access to frequently used items and personalized settings.
* **Reference Data (Limited Subset)**:
  * **Need Offline Access**: A cached, read-only subset of the most commonly used electrical component catalog items, standard templates, or frequently accessed regulatory snippets.
  * **Why**: To allow for component selection and design validation against a local, up-to-date (as of last sync) set of data, rather than requiring online access for every lookup.
* **No Offline Access**:
  * **Sensitive/Voluminous Data**: Historical project archives, comprehensive component catalogs (beyond the cached subset), audit logs, real-time collaboration data, billing/user management details.
  * **Why**: These are either too large, too sensitive, or require real-time synchronization that's impractical for intermittent offline modes.

-----

## 2. Choose Synchronization Strategy 🔄

A **bi-directional, timestamp-based synchronization with "last-write wins" conflict resolution** is generally a robust and manageable approach for this scenario.

### **Mechanism: Event-Driven & Polling Hybrid**

* **Change Tracking**:

  * In the **offline SQLite database**, every modification to an "offline-enabled" entity will update a `last_modified_at` timestamp and potentially a `sync_status` flag (e.g., `PENDING_UPLOAD`, `UPLOADED`).
  * Consider an **"outbox" pattern** for offline changes: when an offline modification occurs, instead of directly writing to the table, an entry is added to an `OfflineSyncOutbox` table detailing the operation (e.g., `entity_type`, `entity_id`, `operation_type` (CREATE/UPDATE/DELETE), `payload`, `timestamp`). This ensures atomic operations and simplifies retry logic.

* **Detection of Connectivity**: A background process in the backend service with embedded backend will periodically check for internet connectivity.

* **Synchronization Flow**:

    1. **Online Check**: When connectivity is detected, a background synchronization process is triggered.
    2. **Push Offline Changes (SQLite to PostgreSQL)**:
          * The system identifies all records in SQLite with `sync_status = PENDING_UPLOAD` (or reads from `OfflineSyncOutbox`).
          * These changes are sent to a dedicated **Synchronization API Endpoint** on the backend.
          * The backend processes these changes:
              * For **new records**: Inserts into PostgreSQL.
              * For **updated records**: Compares `last_modified_at` timestamps between the incoming offline data and the existing PostgreSQL record. If the offline timestamp is newer, it applies the update ("last-write wins"). If the online record is newer, it records a conflict (see below).
              * For **deleted records**: Deletes from PostgreSQL.
          * Upon successful processing by PostgreSQL, the `sync_status` in SQLite is updated to `UPLOADED` (or outbox entries are marked as `processed`).
    3. **Pull Online Changes (PostgreSQL to SQLite)**:
          * After pushing offline changes, the client requests updates from PostgreSQL.
          * The Synchronization API provides all changes (new, updated, deleted) from PostgreSQL that have a `last_modified_at` timestamp *newer* than the client's last successful sync timestamp.
          * The client applies these changes to its local SQLite database.

* **Conflict Resolution ("Last-Write Wins")**:

  * If a record in PostgreSQL has a `last_modified_at` that is *newer* than the incoming offline change for the same record, it signifies a conflict.
  * With "last-write wins," the online version simply **overwrites** the offline change.
  * **User Notification**: Crucially, the user *must* be notified that their offline changes to specific items were superseded by online updates. For high-stakes data, a more complex merge/user-prompted resolution would be needed, but "last-write wins" simplifies implementation significantly for a first iteration.
  * **Error Handling**: If synchronization fails (e.g., network drops, server error), the `sync_status` remains `PENDING_UPLOAD`, and the process will retry.

-----

## 3. Refine Architectural Pattern: Adapting the 5-Layer Architecture 🏛️

The 5-layer architecture provides an excellent foundation. The main adaptations will be in the **Service Layer** and **Repository Layer**.

### High-Level Architecture Diagram (Conceptual)

```
┌───────────────────────────────────────────────┐
│                 Client Layer                  │
├───────────────────────────────────────────────┤
│ React Frontend (Offline/Online Mode Switching)│
│  ├── Local State (Zustand)                    │
│  ├── Cached Data (React Query)                │
│  └── Offline Sync Manager (Frontend Logic)    │
└───────────────────────────────────────────────┘
          │ (HTTP/REST)                  │
          │ (API Requests)             (Sync API)
          ▼                              ▼
┌───────────────────────────────────────────────┐
│                   API Layer                   │
├───────────────────────────────────────────────┤
│ FastAPI Endpoints (Auth, Validation)          │
│  ├── Core Endpoints                           │
│  └── Synchronization Endpoints                │
└───────────────────────────────────────────────┘
          │ (Dependency Injection)
          ▼
┌───────────────────────────────────────────────┐
│               Service Layer                   │
├───────────────────────────────────────────────┤
│ Business Logic & Workflow Orchestration       │
│  ├── ProjectService                           │
│  ├── ComponentService                         │
│  ├── **SynchronizationService** │   <-- NEW/ADAPTED
│  │   (Determines online/offline data source)  │
│  └── Other Domain Services                    │
└───────────────────────────────────────────────┘
          │ (Interface/Abstraction)
          ▼
┌───────────────────────────────────────────────┐
│               Repository Layer                │
├───────────────────────────────────────────────┤
│ Data Access Abstraction                       │
│  ├── **IProjectRepository** (Interface)       │   <-- NEW/ADAPTED (Interfaces for all offline-enabled domains)
│  │   ├── **PgProjectRepository** │   <-- PostgreSQL Implementation
│  │   └── **SqliteProjectRepository** │   <-- SQLite Implementation (via embedded client)
│  ├── IComponentRepository                     │
│  ├── PgComponentRepository                    │
│  └── SqliteComponentRepository                │
│  └── Other Repository Implementations         │
└───────────────────────────────────────────────┘
          │
          ▼
┌───────────────────────────────────────────────┐
│                 Model Layer                   │
├───────────────────────────────────────────────┤
│ SQLAlchemy Models (PostgreSQL & SQLite)       │
│  ├── Base ORM Definitions (shared)            │
│  └── Specific table definitions (if needed)   │
└───────────────────────────────────────────────┘
          │                   │
          ▼                   ▼
┌─────────────────┐ ┌──────────────────────────┐
│   PostgreSQL    │ │  SQLite (Embedded DB)    │
│  (Online DB)    │ │ (Offline, local DB file) │
└─────────────────┘ └──────────────────────────┘
```

### a. Service Layer Refinements

* **`SynchronizationService` (New)**: This will be a critical new service responsible for orchestrating the push and pull of data between the online and offline databases. It will interact with both the online (PostgreSQL-backed) and offline (SQLite-backed) repositories.
* **Context-Aware Domain Services**: Existing domain services (e.g., `ProjectService`, `ComponentService`) will need to be made aware of the application's online/offline status (perhaps passed via dependency injection or a global context).
  * They will then request the appropriate repository *implementation* (PostgreSQL or SQLite) from the Dependency Injection container.
  * For operations that can occur offline, they will typically interact with the local SQLite-backed repositories. For operations strictly online (e.g., retrieving historical archives), they will always target PostgreSQL-backed repositories.
* **Business Logic Consistency**: Ensure that business rules applied offline are the same as those applied online to prevent data inconsistencies (e.g., the same string length validations, temperature constraints).

### b. Repository Layer Refinements

* **Repository Interfaces**: Define clear **interfaces** (e.g., `IProjectRepository`, `IComponentRepository`) for all domain entities that need offline access. These interfaces will define the CRUD and query methods that domain services will call.
* **Concrete Implementations**:
  * **`PgXxxRepository`**: Concrete implementations of these interfaces that connect to and interact with the PostgreSQL database.
  * **`SqliteXxxRepository`**: Concrete implementations that connect to and interact with the SQLite database.
* **Dependency Injection Strategy**:
  * At application startup (in your `lifespan` context), based on the application's *current mode* or primary connection success, configure the Dependency Injection (DI) container (e.g., FastAPI's `Depends`) to provide the *correct* repository implementation (`PgXxxRepository` or `SqliteXxxRepository`) when `IProjectRepository` (or other interfaces) is requested.
  * For the `SynchronizationService`, it will explicitly depend on *both* `PgXxxRepository` and `SqliteXxxRepository` instances to perform its data transfer.

### c. Database Connection Management (Revisited)

* You'll initialize *both* a PostgreSQL engine (`pg_async_engine`) and an SQLite engine (`sqlite_async_engine`) at application startup.
* The decision logic for which engine's session to provide will reside in a new dependency or a factory function, potentially based on a runtime flag (e.g., `is_online`).

-----

## 4. Update `design.md` 📄

The `design.md` document needs a new dedicated section, possibly titled "**Offline Capabilities & Dual Database Architecture**" or similar.

### Key Sections to Add/Update

1. **Overview**: Briefly explain the motivation (offline production use) and the chosen dual-database strategy.
2. **Data Segregation**:
      * List specific data domains/entities that will be accessible offline via SQLite.
      * Detail data that remains online-only in PostgreSQL.
3. **Synchronization Strategy**:
      * Elaborate on the "bi-directional, timestamp-based, last-write wins" approach.
      * Include a diagram illustrating the data flow:
          * Client -> SQLite (Offline modifications)
          * SQLite -> Backend Sync API (Push)
          * Backend Sync API -> PostgreSQL (Apply/Conflict Resolution)
          * PostgreSQL -> Backend Sync API (Pull)
          * Backend Sync API -> SQLite (Apply updates)
      * Describe the `OfflineSyncOutbox` pattern if adopted.
4. **Architectural Adaptation (5-Layer)**:
      * **Service Layer**: Detail the role of `SynchronizationService` and how other domain services will use contextual repository selection.
      * **Repository Layer**: Explain the use of interfaces (`IProjectRepository`) and concrete implementations (`PgProjectRepository`, `SqliteProjectRepository`).
      * **Dependency Injection**: Describe how the DI system will provide the correct repository instance based on context.
      * **Model Layer**: Note that SQLAlchemy models will largely be shared, but specific table arguments might differ (e.g., `sqlite_autoincrement=True` for SQLite primary keys).
5. **Error Handling & Conflict Resolution**: Formalize the "last-write wins" strategy for conflicts and how users will be notified.
6. **Diagrams**:
      * An updated High-Level Architecture Diagram showing both PostgreSQL and SQLite, and the `SynchronizationService`.
      * A detailed Data Flow Diagram for the synchronization process.
      * A class diagram illustrating the repository interfaces and their concrete implementations.

This comprehensive design will ensure the dual-database setup is robust, maintainable, and fully integrated into the Ultimate Electrical Designer's **engineering-grade architecture**.
