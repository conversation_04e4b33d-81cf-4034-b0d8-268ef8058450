# Discovery & Analysis: `is_offline` Project Feature

**Document Version:** 1.0  
**Last Updated:** July 21, 2025

---

## 1. Feature Objective 🎯

To introduce an `is_offline` boolean field to the `Project` model, allowing administrators to explicitly designate a project as "offline-only." When `is_offline` is set to `True`, the project will **exclusively operate on its local, embedded SQLite database instance**, preventing any synchronization with the central PostgreSQL database. Core project functionalities (e.g., calculations, design, report generation) must remain fully accessible and operational under this offline mode, strictly utilizing the local data.

---

## 2. "Offline Only" Behavior Definition 🌐↔️💻

When `is_offline` is `True`:

* **Data Storage**: The project's data will be read from and written to the **local SQLite database only**.
* **Synchronization Prevention**:
  * **No outbound synchronization**: Changes made to an `is_offline` project locally will **not be pushed** to the central PostgreSQL database. The `OfflineSyncOutbox` for such projects will not be processed.
  * **No inbound synchronization**: The system will **not attempt to pull** updates for `is_offline` projects from the central PostgreSQL database.
* **Conflict Resolution**: This state implicitly **removes the need for conflict resolution** for `is_offline` projects, as they are isolated from the central database. The existing `last-write wins` mechanism will not be invoked for these projects.
* **Real-time Collaboration**: **Real-time collaboration features will be disabled** for projects marked `is_offline`, as they rely on a shared online state and WebSocket connections. Attempts to initiate collaboration on such projects will result in an informative error message.
* **Visibility**: Projects marked `is_offline` will still be **visible in the online project dashboard** but will be clearly indicated as "Offline Only" (e.g., greyed out, or with a specific icon). Users (including admins) attempting to open or modify an "offline only" project while connected online will interact with their local copy. If no local copy exists, the system should prompt the user to download it first or indicate that it's an offline-only project not available for direct online interaction.
* **Bringing Online**: An explicit administrator action (setting `is_offline` to `False`) will be required to transition the project back to an online-eligible state. Upon this transition, the project will become eligible for the existing bi-directional synchronization process, where local changes will then attempt to sync with PostgreSQL and vice-versa.

---

## 3. User Interface (Admin) Requirements ⚙️👩‍💻

* **Setting the Flag**: Administrators must have a clear and intuitive way to toggle the `is_offline` status of a project. This could be a checkbox or a dedicated toggle switch on the project's settings or edit page within the administrative interface.
* **Confirmation**: A confirmation prompt should appear when an admin attempts to change the `is_offline` status, especially when switching from `False` to `True` (to highlight the synchronization isolation) and `True` to `False` (to indicate upcoming synchronization).
* **Permissions**: Only users with the **`Admin` role** (as per FR-1.3: Authorization Control) shall be authorized to modify the `is_offline` flag. The system must enforce this role-based access control.
* **Visual Feedback**:
  * The project list should visually distinguish projects that are `is_offline=True`.
  * When an `is_offline` project is opened, a clear indicator should be present within the project view to remind the user of its offline status.

---

## 4. Data Model & Backend Impact Analysis 📊🗄️

* **New Field**: A boolean field named `is_offline` will be added to the `Project` model.
  * **Default Value**: The default value for new projects should be `False` (online).
  * **Database Migration**: An Alembic migration script will be required to add this column to the `projects` table.
* **Service Layer (`ProjectService`, `SynchronizationService`)**:
  * `ProjectService` methods for updating project details must incorporate logic to set/get the `is_offline` flag.
  * The `SynchronizationService` (or relevant synchronization logic within the Business Services Layer) must be updated to conditionally skip synchronization operations for projects where `is_offline` is `True`. This will involve checking the flag before processing any synchronization queues or initiating data transfers for a given project.
* **API Layer (`ProjectRoutes`)**:
  * The API endpoint for updating project details (e.g., `PUT /projects/{project_id}`) will need to accept `is_offline` as a parameter.
  * **Authorization**: Strict **Role-Based Access Control (RBAC)** must be enforced to ensure only authenticated `Admin` users can modify this field.
  * Pydantic models for request bodies will need to be updated to include the `is_offline` field.

---

## 5. Edge Cases & Considerations ⚠️

* **Simultaneous Operations**: What if a project is marked `is_offline` while an active synchronization is in progress? The system should handle this gracefully, potentially allowing the current sync to complete but preventing subsequent ones.
* **Partial Offline State**: Ensure that an `is_offline` project cannot inadvertently pick up partial online data or attempt partial synchronization. The isolation must be complete.
* **Performance**: The introduction of this flag and its checks within the synchronization logic should not introduce significant performance overhead, especially given the existing **Performance Optimizer** and **Query Optimizer** in our `tech.md`.
* **Data Consistency**: Verify that if an `is_offline` project is brought back online, its local changes are correctly prioritized and synchronized according to the established `last-write wins` strategy.
* **Error Handling**: Implement specific error messages and logging for scenarios where users or systems attempt to perform online actions (e.g., real-time collaboration) on an `is_offline` project.
* **Scalability**: Ensure the approach scales with a large number of projects and users, as per the **Scalable Architecture** requirement in `README.md`.
