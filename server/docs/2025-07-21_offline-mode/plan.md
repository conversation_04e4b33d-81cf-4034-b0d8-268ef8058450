# Plan Of Attack: `is_offline` Project Feature

**Document Version:** 1.0  
**Last Updated:** July 21, 2025

---

## Task Planning 🗓️

**Objective**: Break down the implementation into granular, actionable tasks, assign ownership (implicitly developer, as per standard workflow), and estimate effort in 30-minute work batches.

### I. Backend Implementation

#### A. Data Model & Migrations (Repository/Model Layer)

* **Task**: Add `is_offline` field to `Project` model.
  * **Description**: Modify `server/app/models/project.py` to include `is_offline: Mapped[bool] = sa.Column(sa.Bo<PERSON>an, default=False)`.
  * **Work Batch**: 1 (30 mins)
* **Task**: Generate Alembic migration script.
  * **Description**: Run `alembic revision --autogenerate -m "Add is_offline to Project"` to create the migration file. Verify the generated script correctly adds the column with a default of `False`.
  * **Work Batch**: 1 (30 mins)
* **Task**: Apply Alembic migration.
  * **Description**: Run `alembic upgrade head` to apply the migration to the development database.
  * **Work Batch**: 1 (30 mins)

#### B. Business Logic (Service Layer)

* **Task**: Update `ProjectService` for `is_offline` management.
  * **Description**: Modify methods in `server/app/services/project_service.py` that handle project creation and updates to accept and persist the `is_offline` flag. Ensure proper type hints and validation.
  * **Work Batch**: 2 (60 mins)
* **Task**: Integrate `is_offline` into `SynchronizationService`.
  * **Description**: In `server/app/services/synchronization_service.py` (or relevant sync logic), add conditional checks. When processing `OfflineSyncOutbox` entries for projects, or when fetching updates, **skip any operations for projects where `is_offline` is `True`**. This ensures no outbound or inbound syncs for such projects.
  * **Work Batch**: 4 (120 mins)
* **Task**: Disable real-time collaboration for offline projects.
  * **Description**: In the `CollaborationService` (as referenced in `design.md`), implement a check during WebSocket connection attempts or message sending. If the `projectId` corresponds to an `is_offline=True` project, prevent the connection or send an informative error back to the client.
  * **Work Batch**: 2 (60 mins)

#### C. API Endpoints (API Layer)

* **Task**: Update `ProjectRoutes` for `is_offline` parameter.
  * **Description**: Modify the `PUT /projects/{project_id}` endpoint in `server/app/api/routes/project_routes.py` (or similar) to accept `is_offline: bool` in its request body Pydantic model.
  * **Work Batch**: 1 (30 mins)
* **Task**: Implement **Role-Based Access Control (RBAC)**.
  * **Description**: Apply a dependency injection or decorator to the `PUT /projects/{project_id}` endpoint (specifically for the `is_offline` field) to ensure only users with the **`Admin` role** can modify this field.
  * **Work Batch**: 2 (60 mins)
* **Task**: Update Pydantic models.
  * **Description**: Ensure all relevant Pydantic models (request and response) include the `is_offline` field with correct typing.
  * **Work Batch**: 1 (30 mins)

### II. Frontend Implementation

#### A. State Management & API Integration

* **Task**: Update TypeScript interfaces for `Project`.
  * **Description**: Add `is_offline: boolean;` to the `Project` interface in `client/src/types/project.ts` (or equivalent).
  * **Work Batch**: 1 (30 mins)
* **Task**: Modify React Query hooks.
  * **Description**: Update `useQuery` and `useMutation` hooks related to project data (`client/src/hooks/useProjects.ts` or similar) to handle the new `is_offline` field in data fetching and updates.
  * **Work Batch**: 1 (30 mins)
* **Task**: Update API calls.
  * **Description**: Ensure API service functions in `client/src/services/api/project.ts` (or similar) correctly send and receive the `is_offline` status to/from the backend.
  * **Work Batch**: 1 (30 mins)

#### B. User Interface (Presentation Layer)

* **Task**: Implement admin UI for `is_offline` toggle.
  * **Description**: Add a new UI component (e.g., a toggle switch or checkbox) to the admin's project settings or edit page, allowing them to modify the `is_offline` flag. Ensure proper state management (Zustand).
  * **Work Batch**: 3 (90 mins)
* **Task**: Display visual indicators for offline projects.
  * **Description**: In the project list component (`client/src/components/ProjectList.tsx` or similar), add a clear visual indicator (e.g., a "lock" icon, "Offline Only" badge, or greyed-out text) for projects where `is_offline` is `True`.
  * **Work Batch**: 2 (60 mins)
* **Task**: Implement in-project offline indicator.
  * **Description**: When an `is_offline` project is opened, display a prominent, non-intrusive indicator within the project's view (e.g., in a header or sidebar) to remind the user of its offline status.
  * **Work Batch**: 1 (30 mins)
* **Task**: Add confirmation prompts.
  * **Description**: Implement modal confirmation dialogs when an admin attempts to change the `is_offline` status, especially for transitions to/from `True`.
  * **Work Batch**: 1 (30 mins)
* **Task**: Handle real-time collaboration attempt for offline projects.
  * **Description**: If a user attempts to activate real-time collaboration on an `is_offline` project, display an informative message indicating that collaboration is disabled for offline projects.
  * **Work Batch**: 1 (30 mins)

### III. Testing

* **Task**: Write Unit Tests (Backend).
  * **Description**:
    * Test `Project` model field addition.
    * Test `ProjectService` methods for setting/getting `is_offline`.
    * Test `SynchronizationService` behavior, ensuring `is_offline` projects are skipped for sync.
    * Test `ProjectRoutes` for accepting `is_offline` and enforcing admin-only access.
  * **Tools**: **Pytest**
  * **Work Batch**: 4 (120 mins)
* **Task**: Write Unit Tests (Frontend).
  * **Description**:
    * Test React components for rendering the `is_offline` toggle and visual indicators.
    * Test React Query hooks for correct data handling.
  * **Tools**: **Vitest**, **React Testing Library**
  * **Work Batch**: 3 (90 mins)
* **Task**: Write Integration Tests.
  * **Description**: Test the full flow from frontend action to backend processing for updating the `is_offline` flag and verifying its effect on synchronization logic without a full E2E setup.
  * **Work Batch**: 2 (60 mins)
* **Task**: Write End-to-End (E2E) Tests.
  * **Description**:
    * Simulate an admin logging in, navigating to project settings, and toggling `is_offline`.
    * Verify the project's sync behavior changes as expected (no network activity for sync).
    * Verify visual indicators appear correctly.
    * Test attempts to open an `is_offline` project from various states (local, online).
  * **Tools**: **Playwright**
  * **Work Batch**: 4 (120 mins)

---

## Implementation 🔨

**Objective**: Develop the code for the `is_offline` feature across all relevant layers, applying **SOLID principles**, **DRY**, and **KISS**.

* **Task**: Backend Development (Data Model, Service, API Layers).
  * **Work Batch**: 8 (240 mins)
* **Task**: Frontend Development (State, UI, API Integration).
  * **Work Batch**: 7 (210 mins)
* **Task**: Write all tests (Unit, Integration, E2E) concurrently with development.
  * **Work Batch**: 13 (390 mins)
* **Task**: Initial Refactoring and Code Review Preparation.
  * **Work Batch**: 2 (60 mins)

---

## Verification ✅

**Objective**: Rigorously test the implemented feature to ensure it meets all requirements, adheres to quality standards, and demonstrates no regressions.

* **Task**: Execute all **Unit Tests**. Fix all failures.
  * **Work Batch**: 1 (30 mins)
* **Task**: Execute all **Integration Tests**. Fix all failures.
  * **Work Batch**: 1 (30 mins)
* **Task**: Execute all **End-to-End Tests**. Fix all failures.
  * **Work Batch**: 2 (60 mins)
* **Task**: Run **Code Quality Checks**: `make type-check`, `make lint-server`, `make format-server`, `make lint-client`, `make format-client`, `lint-client-type-check`. Ensure **zero errors** and **100% MyPy compliance**. Fix all violations.
  * **Work Batch**: 3 (90 mins)
* **Task**: Perform **Manual QA & Edge Case Testing**.
  * **Description**: Test scenarios identified in Discovery & Analysis, particularly transitions between online/offline, network interruptions, and administrator actions. Verify real-time collaboration attempts on offline projects provide correct feedback.
  * **Work Batch**: 3 (90 mins)
* **Task**: Review **Code Coverage** and ensure it meets defined thresholds.
  * **Work Batch**: 1 (30 mins)

---

## Documentation & Handover 📚

**Objective**: Update all relevant documentation and prepare for deployment and ongoing maintenance.

* **Task**: Update `design.md`.
  * **Description**: Detail the implementation of the `is_offline` field, its impact on the dual-database architecture, and its interaction with the synchronization strategy.
  * **Work Batch**: 1 (30 mins)
* **Task**: Update `requirements.md`.
  * **Description**: Add a new Functional Requirement (FR) clearly defining the `is_offline` feature, its behavior, and the administrative controls.
  * **Work Batch**: 1 (30 mins)
* **Task**: Update API Documentation (OpenAPI/Swagger).
  * **Description**: Ensure the `Project` model and related endpoints in the API documentation reflect the `is_offline` field and its allowed operations.
  * **Work Batch**: 1 (30 mins)
* **Task**: Create or update relevant sections in a Maintenance Runbook/Operational Guide.
  * **Description**: Document any specific monitoring or troubleshooting steps related to `is_offline` projects.
  * **Work Batch**: 1 (30 mins)
* **Task**: Prepare for knowledge transfer.
  * **Description**: Prepare a brief summary for the team on the new feature, its implementation, and operational considerations.
  * **Work Batch**: 1 (30 mins)
