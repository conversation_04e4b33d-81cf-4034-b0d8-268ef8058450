# Discovery & Analysis: Unified Local Database Data Access 🔍

This document outlines the "Discovery & Analysis" phase for implementing the new "Unified Local Database Data Access" architecture. This phase is critical for re-aligning our project's data strategy with the non-negotiable requirement for a single-source database per project that supports shared local network access via a client-server model, synchronizing with the central online PostgreSQL. This necessitates a fundamental shift from the previous individual SQLite-based offline capability.

---

## 1. Goal & Objectives

The primary goal of this phase is to thoroughly define the new architecture, identify all impacted components, assess technical feasibility, and establish a clear roadmap for implementation, ensuring adherence to engineering-grade quality standards.

**Objectives:**

* **Architectural Redefinition**: Clearly define the high-level and detailed architecture for the shared local PostgreSQL instance, its client access, and its synchronization with the central online PostgreSQL.
* **Impact Assessment**: Identify all affected backend, frontend, and deployment components, and understand the scope of changes required.
* **Technical Feasibility & Risk Analysis**: Evaluate the technical challenges, potential risks, and propose mitigation strategies for the new architecture.
* **Resource & Timeline Estimation**: Provide preliminary estimates for the resources (personnel, tools) and time required for subsequent phases.
* **Documentation Baseline**: Create foundational documentation for the new architectural patterns and changes.

---

## 2. Detailed Architectural Design

This section focuses on updating our core architectural blueprint (`design.md`) to reflect the new unified local database model.

### 2.1. High-Level Architecture Diagram Update

* **Action**: Revise the "High-Level Architecture Diagram" in `design.md` to:
  * Replace the "SQLite Embedded Database" with a "Shared Local PostgreSQL Server" within the client-side/local network scope.
  * Illustrate client applications connecting directly to this "Shared Local PostgreSQL Server."
  * Show a clear **bi-directional synchronization path** between the "Shared Local PostgreSQL Server" and the "Online Central PostgreSQL Server."
* **Expected Outcome**: A revised `design.md` diagram that visually represents the new client-server local database model.

### 2.2. Data Flow & Synchronization Protocol Definition

* **Action**: Detail the precise data flow and synchronization mechanisms between the shared local PostgreSQL and the central online PostgreSQL.
  * **Synchronization Triggers**: Define events or schedules that initiate synchronization (e.g., periodic sync, on-demand sync, webhook triggers from central server).
  * **Change Data Capture (CDC)**: Investigate and specify how changes will be captured in both the local and central PostgreSQL instances (e.g., logical replication, custom triggers, external CDC tools like Debezium).
  * **Data Consistency Model**: Reconfirm `last-write wins` conflict resolution for sync operations. Define how conflicts will be detected and resolved when both local and central databases have made changes to the same data.
  * **Partial Synchronization**: Define if only specific subsets of data will be synchronized, similar to the previous offline model, or if the entire project dataset will be replicated locally.
  * **Initial Data Seeding**: Define the process for populating the shared local PostgreSQL instance when a new project is created locally or accessed for the first time.
* **Expected Outcome**: New sections or significant updates in `design.md` detailing the synchronization strategy, including a high-level algorithm or flow chart.

### 2.3. Client-Side Access & Caching Design

* **Action**: Define how client applications will interact with the shared local PostgreSQL and whether any client-side caching will be implemented for individual "offline from local network" scenarios.
  * **Direct Connection**: Specify the connection parameters and libraries (e.g., SQLAlchemy on backend, suitable ORM/drivers for client applications if direct DB access is needed from frontend) for clients to connect to the shared local PostgreSQL.
  * **Client-Side "Offline" Mode**: If true individual client-side offline work (when disconnected from the *local network server*) is still a requirement, propose a lightweight caching mechanism (e.g., in-memory store, IndexedDB for frontend, or a small SQLite cache *purely for transient local client data*) that synchronizes with the *shared local PostgreSQL* when the local network connection is restored. This is distinct from the previous SQLite *primary data store* role.
* **Expected Outcome**: Updates to `design.md` detailing client-side connection patterns and a specific proposal for optional individual client-side caching.

---

## 3. Deployment Strategy Refinement

This phase will also outline the revised deployment and operational considerations for the shared local database.

### 3.1. Local PostgreSQL Installation & Configuration

* **Action**: Define the process for installing and configuring PostgreSQL on the designated local server machine.
  * **Silent Installation**: Research and specify tools/scripts for silent, automated installation of PostgreSQL.
  * **Configuration**: Detail essential configuration parameters (e.g., `postgresql.conf`, `pg_hba.conf`) for local network access, security, and performance.
  * **User Management**: Outline default user roles and permissions for the application's connection to the local PostgreSQL.
* **Expected Outcome**: A draft section for a new deployment guide or an update to an existing operations document, outlining local PostgreSQL setup.

### 3.2. Network & Security Considerations

* **Action**: Specify network and security requirements for the shared local database.
  * **Firewall Rules**: Detail necessary firewall rules on the server machine to allow client connections (e.g., default port 5432).
  * **Authentication**: Reconfirm robust authentication mechanisms for client applications connecting to the local PostgreSQL.
  * **Encryption**: Specify if SSL/TLS encryption is required for local network communication with the database.
* **Expected Outcome**: A checklist of network and security configurations for local deployments.

### 3.3. Backup & Recovery Strategy

* **Action**: Define a preliminary backup and recovery strategy for the shared local PostgreSQL instances.
  * **Backup Frequency**: Recommend backup frequency (e.g., daily, weekly).
  * **Backup Methods**: Suggest tools and methods (e.g., `pg_dump`, continuous archiving with WAL files).
  * **Recovery Procedures**: High-level overview of disaster recovery steps.
* **Expected Outcome**: A preliminary backup and recovery plan to be formalized in later phases.

---

## 4. Impact & Risk Assessment

This critical step identifies the ripple effect of the architectural change and potential pitfalls.

* **Action**: Conduct a thorough analysis of how the new architecture impacts existing components.
  * **Backend Services**: Identify specific service layers, repositories, and models that require modification (e.g., `SynchronizationService`, data access layer).
  * **Frontend Components**: Assess changes needed in how the frontend retrieves and pushes data, including potential changes to state management (React Query, Zustand) if client-side caching is implemented.
  * **Testing Infrastructure**: Determine necessary updates to Pytest, Vitest, React Testing Library, and Playwright suites to cover new synchronization and data access paths.
  * **Performance Implications**: Estimate the potential performance impact of local network latency and increased load on the single local PostgreSQL instance.
  * **Security Vulnerabilities**: Identify any new security risks introduced by exposing PostgreSQL over a local network.
* **Action**: Identify potential risks and propose mitigation strategies.
  * **Migration Complexity**: Risk of data migration issues from existing SQLite instances (if any live user data exists there, which is unlikely given current design).
  * **Deployment Challenges**: Risk of complex local setup for users.
  * **Synchronization Errors**: Risk of data inconsistencies or deadlocks during synchronization.
  * **Performance Bottlenecks**: Risk of the local PostgreSQL becoming a bottleneck for large teams or complex operations.
* **Expected Outcome**: A detailed impact analysis matrix and a risk register with proposed mitigation strategies.

---

## 5. Preliminary Resource & Timeline Estimation

* **Action**: Based on the architectural design and impact assessment, provide preliminary estimates.
  * **Personnel**: Identify required roles and approximate effort (e.g., Backend Dev, Frontend Dev, DevOps, QA).
  * **Tools**: List any new tools or technologies required (e.g., specific PostgreSQL extensions, CDC tools, advanced deployment scripting).
  * **Timeline**: Provide an initial high-level timeline for the subsequent phases (Task Planning, Implementation, Verification, Documentation & Handover) for this architectural change.
* **Expected Outcome**: A high-level project plan for the architectural transition.
