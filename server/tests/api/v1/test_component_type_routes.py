"""Tests for Component Type functionality.

This module provides comprehensive tests for component type management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentType model validation and business logic
- ComponentTypeRepository data access operations
- ComponentTypeService business logic and validation
- Component Type API endpoints and error handling
- Category relationships and validation
- Specifications template management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch
import httpx

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_type_repository import (
    ComponentTypeRepository,
)
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeUpdateSchema,
    ComponentTypeSearchSchema,
)
from src.core.errors.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)
from src.core.utils.pagination_utils import PaginationParams


class TestComponentTypeAPI:
    """Test Component Type API endpoints."""

    async def test_create_type_endpoint(
        self, authenticated_client: httpx.AsyncClient, sample_category_id: int, test_project
    ):
        """Test POST /component-types/ endpoint."""
        type_data = {
            "name": "API Test Type",
            "description": "API test description",
            "category_id": sample_category_id,
            "is_active": True,
        }

        response = await authenticated_client.post(
            f"/api/v1/projects/{test_project.id}/components/component-types/",
            json=type_data,
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "API Test Type"
        assert data["description"] == "API test description"
        assert data["category_id"] == sample_category_id
        assert data["is_active"] is True

    async def test_get_type_endpoint(
        self, authenticated_client: httpx.AsyncClient, sample_category_id: int, test_project
    ):
        """Test GET /component-types/{id} endpoint."""
        # First create a type
        type_data = {
            "name": "Get Test Type",
            "description": "Get test description",
            "category_id": sample_category_id,
            "is_active": True,
        }

        create_response = await authenticated_client.post(
            f"/api/v1/projects/{test_project.id}/components/component-types/",
            json=type_data,
        )
        type_id = create_response.json()["id"]

        # Then retrieve it
        response = await authenticated_client.get(
            f"/api/v1/projects/{test_project.id}/components/component-types/{type_id}",
        )

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == type_id
        assert data["name"] == "Get Test Type"

    async def test_list_types_endpoint(self, authenticated_client: httpx.AsyncClient, test_project):
        """Test GET /component-types/ endpoint."""
        response = await authenticated_client.get(
            f"/api/v1/projects/{test_project.id}/components/component-types/",
        )

        assert response.status_code == 200
        data = response.json()
        assert "component_types" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data

    async def test_get_types_by_category_endpoint(
        self, authenticated_client: httpx.AsyncClient, sample_category_id: int, test_project
    ):
        """Test GET /component-types/by-category/{category_id} endpoint."""
        response = await authenticated_client.get(
            f"/api/v1/projects/{test_project.id}/components/component-types/by-category/{sample_category_id}",
        )

        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    async def test_update_specifications_template_endpoint(
        self, authenticated_client: httpx.AsyncClient, sample_category_id: int, test_project
    ):
        """Test PUT /component-types/{id}/specifications-template endpoint."""
        # First create a type
        type_data = {
            "name": "Template Test Type",
            "description": "Template test description",
            "category_id": sample_category_id,
            "is_active": True,
        }

        create_response = await authenticated_client.post(
            f"/api/v1/projects/{test_project.id}/components/component-types/",
            json=type_data,
        )
        type_id = create_response.json()["id"]

        # Then update its template
        template = {
            "electrical": {
                "voltage": {"type": "number", "required": True},
                "current": {"type": "number", "required": True},
            }
        }

        response = await authenticated_client.put(
            f"/api/v1/projects/{test_project.id}/components/component-types/{type_id}/specifications-template",
            json=template,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["specifications_template"] == template
