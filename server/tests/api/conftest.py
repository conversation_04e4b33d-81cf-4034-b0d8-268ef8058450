"""Test fixtures for API layer tests."""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List
from unittest.mock import MagicMock, Mock

import pytest
import httpx
from fastapi import Request, Response
from fastapi.testclient import TestClient
from src.core.schemas.base_schemas import PaginationSchema
from src.core.schemas.general.component_schemas import (
    ComponentAdvancedSearchResponseSchema,
    ComponentDimensionsSchema,
    ComponentReadSchema,
    ComponentSearchResultSchema,
)
from src.core.schemas.general.user_schemas import UserCreateSchema
from src.core.services.general.user_service import UserService


@pytest.fixture
def mock_app():
    """Create a mock ASGI app for testing."""
    return Mock()


@pytest.fixture
def mock_request():
    """Create a mock request object with proper attributes."""
    request = Mock(spec=Request)
    request.method = "GET"
    request.url = Mock()
    request.url.path = "/api/v1/test"
    request.url.query = ""
    request.query_params = {}
    request.headers = {}
    request.client.host = "127.0.0.1"
    return request


@pytest.fixture
def mock_response():
    """Create a mock response object."""
    response = Mock(spec=Response)
    response.status_code = 200
    response.headers = {}
    response.body = b'{"result": "success"}'
    return response


@pytest.fixture
def mock_call_next():
    """Create a mock call_next function that returns a response."""

    async def call_next(request):
        # Directly create a mock response within the fixture
        response = Mock(spec=Response)
        response.status_code = 200
        response.headers = {}
        # Add a default body that can be overridden in tests if needed
        response.body = b'{"message": "test"}'
        return response

    return call_next


@pytest.fixture
def mock_advanced_search_request() -> Dict[str, Any]:
    """Sample advanced search request data."""
    return {
        "search_term": "circuit breaker",
        "fuzzy_search": False,
        "basic_filters": [
            {
                "field": "manufacturer",
                "operator": "eq",
                "value": "Schneider Electric",
                "logical_operator": "and",
            }
        ],
        "specification_filters": [
            {
                "path": "electrical.voltage_rating",
                "operator": "gte",
                "value": 120,
                "data_type": "number",
                "logical_operator": "and",
            }
        ],
        "range_filters": [
            {
                "field": "unit_price",
                "min_value": 10.0,
                "max_value": 100.0,
                "include_min": True,
                "include_max": True,
            }
        ],
        "sort_by": "name",
        "sort_order": "asc",
        "include_inactive": False,
    }


@pytest.fixture
def mock_search_response() -> ComponentAdvancedSearchResponseSchema:
    """Sample search response data."""
    component_schema = ComponentReadSchema(
        id=1,
        manufacturer="Schneider Electric",
        model_number="QO120",
        name="Circuit Breaker 20A",
        description="Single pole circuit breaker",
        category_id=1,
        component_type_id=1,
        unit_price=Decimal("25.99"),
        currency="EUR",
        supplier="Test Supplier",
        part_number="QO120-TS",
        weight_kg=0.5,
        dimensions=ComponentDimensionsSchema(length=10, width=20, height=30, diameter=None, unit="mm"),
        specifications={"electrical": {"voltage_rating": 120, "current_rating": 20}},
        is_active=True,
        is_preferred=False,
        stock_status="in_stock",
        version="1.0",
        metadata={},
        full_name="Schneider Electric QO120",
        display_name="Circuit Breaker 20A (QO120)",
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )

    search_result = ComponentSearchResultSchema(
        component=component_schema,
        relevance_score=0.95,
        matched_fields=["name", "manufacturer"],
    )

    return ComponentAdvancedSearchResponseSchema(
        items=[search_result],
        pagination=PaginationSchema(page=1, size=20, total=1, pages=1),
        search_metadata={
            "query_time": "< 1ms",
            "total_filters_applied": 3,
            "search_type": "advanced_builder",
            "fuzzy_search_enabled": False,
        },
        suggestions=[],
    )


@pytest.fixture
def mock_bulk_create_request(sample_category_id: int, sample_component_type_id: int) -> List[Dict[str, Any]]:
    """Sample bulk create request data."""
    return [
        {
            "manufacturer": "Schneider Electric",
            "model_number": "QO120",
            "name": "Circuit Breaker 20A",
            "description": "Single pole circuit breaker",
            "category_id": sample_category_id,
            "component_type_id": sample_component_type_id,
            "unit_price": 25.99,
            "specifications": {"electrical": {"voltage_rating": 120, "current_rating": 20}},
        },
        {
            "manufacturer": "ABB",
            "model_number": "S201-B16",
            "name": "MCB 16A Type B",
            "description": "Miniature circuit breaker",
            "category_id": sample_category_id,
            "component_type_id": sample_component_type_id,
            "unit_price": 18.50,
            "specifications": {"electrical": {"voltage_rating": 230, "current_rating": 16}},
        },
    ]


@pytest.fixture
def mock_bulk_update_request() -> List[Dict[str, Any]]:
    """Sample bulk update request data."""
    return [
        {
            "id": 1,
            "name": "Updated Circuit Breaker 20A",
            "unit_price": 28.99,
            "specifications": {"electrical": {"voltage_rating": 120, "current_rating": 20}},
        },
        {
            "id": 2,
            "name": "Updated MCB 16A Type B",
            "unit_price": 19.50,
            "specifications": {"electrical": {"voltage_rating": 230, "current_rating": 16}},
        },
    ]


@pytest.fixture
def mock_component_service():
    """Create a mock component service for testing."""
    service = MagicMock()
    service.create_component.return_value = Mock()
    service.get_component.return_value = Mock()
    service.update_component.return_value = Mock()
    service.delete_component.return_value = True
    service.search_components.return_value = Mock()
    service.get_components_by_category.return_value = []
    service.get_components_by_type.return_value = []
    service.get_preferred_components.return_value = []
    service.get_component_stats.return_value = Mock()
    service.search_components_with_builder.return_value = Mock()
    service.search_components_with_relevance.return_value = Mock()
    service.bulk_create_with_validation.return_value = Mock()
    service.bulk_update_selective.return_value = Mock()
    service.bulk_delete_components.return_value = Mock()
    service.get_performance_metrics.return_value = Mock()
    service.optimize_system_performance.return_value = Mock()
    service.invalidate_component_cache.return_value = Mock()
    return service


@pytest.fixture
def mock_auth_user() -> Dict[str, Any]:
    """Create a mock authenticated user for testing."""
    return {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "is_active": True,
        "is_admin": False,
        "roles": ["user"],
    }


@pytest.fixture
def mock_search_result() -> Dict[str, Any]:
    """Create a mock search result for testing."""
    from datetime import datetime

    # Create timestamp for consistency
    now = datetime.now()

    return {
        "items": [
            {
                "id": 1,
                "name": "Test Circuit Breaker",
                "manufacturer": "ABB",
                "model_number": "S203-C16",
                "description": "Test circuit breaker for electrical protection",
                "component_type": "Circuit Breaker",
                "category": "Protection Devices",
                "specifications": {},
                "unit_price": 25.50,
                "currency": "EUR",
                "supplier": "Test Supplier",
                "part_number": "TS-CB-001",
                "weight_kg": 0.5,
                "dimensions": {"length": 100, "width": 50, "height": 75},
                "is_active": True,
                "is_preferred": False,
                "stock_status": "available",
                "version": "1.0",
                "created_at": now.isoformat(),
                "updated_at": now.isoformat(),
            },
            {
                "id": 2,
                "name": "Test MCB",
                "manufacturer": "Schneider Electric",
                "model_number": "C60N-C16",
                "description": "Test miniature circuit breaker",
                "component_type": "Circuit Breaker",
                "category": "Protection Devices",
                "specifications": {},
                "unit_price": 22.75,
                "currency": "EUR",
                "supplier": "Test Supplier 2",
                "part_number": "TS-MCB-002",
                "weight_kg": 0.3,
                "dimensions": {"length": 80, "width": 40, "height": 60},
                "is_active": True,
                "is_preferred": True,
                "stock_status": "available",
                "version": "1.0",
                "created_at": now.isoformat(),
                "updated_at": now.isoformat(),
            },
        ],
        "total": 2,
        "page": 1,
        "size": 10,
        "pages": 1,
    }


@pytest.fixture
async def sample_category_id(authenticated_client: httpx.AsyncClient, test_project) -> int:
    """Create a sample category and return its ID."""
    category_data = {
        "name": "API Test Category",
        "description": "API test category description",
        "is_active": True,
    }

    response = await authenticated_client.post(
        f"/api/v1/projects/{test_project.id}/components/component-categories/",
        json=category_data,
    )
    return response.json()["id"]


# --------------------------------Admin User Fixtures--------------------------------#


@pytest.fixture
def admin_user_data():
    """Create admin user data."""
    unique_suffix = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Admin {unique_suffix}",
        "email": f"admin.{unique_suffix}@example.com",
        "password": "SecurePass123",
    }


@pytest.fixture
async def test_admin_user(async_db_session, admin_user_data):
    """Create an admin user in the database."""

    from src.core.repositories.general.user_repository import UserRepository
    from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

    user_repo = UserRepository(async_db_session)
    preference_repo = UserPreferenceRepository(async_db_session)
    user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

    user_create = UserCreateSchema(**admin_user_data)
    user = await user_service.create_user(user_create)

    # Get the actual user model to activate it and verify email for testing
    from src.core.models.general.user import User
    from sqlalchemy import select

    stmt = select(User).where(User.id == user.id)
    result = await async_db_session.execute(stmt)
    user_model = result.scalar_one()

    # Set user as active and email verified for testing
    user_model.is_active = True
    user_model.is_email_verified = True
    user_model.email_verified_at = user_model.created_at
    await async_db_session.commit()
    await async_db_session.refresh(user_model)

    # Create admin role if it doesn't exist and assign it
    from src.core.models.general.user_role import UserRole, UserRoleAssignment

    admin_role = await async_db_session.execute(select(UserRole).where(UserRole.name == "ADMIN"))
    admin_role = admin_role.scalar_one_or_none()
    if not admin_role:
        admin_role = UserRole(
            name="ADMIN",
            description="Administrator role",
            is_system_role=True,
            is_active=True,
            priority=100,
        )
        async_db_session.add(admin_role)
        await async_db_session.commit()
        await async_db_session.refresh(admin_role)

    # Assign admin role to user
    role_assignment = UserRoleAssignment(
        name=f"Admin Role Assignment {user.id}",
        user_id=user.id,
        role_id=admin_role.id,
        is_active=True,
        notes=f"User {user.id} - ADMIN Role Assignment",
    )
    async_db_session.add(role_assignment)
    await async_db_session.commit()

    # Additional synchronization attempt: force connection sharing
    # This is a workaround for the session isolation issue
    await async_db_session.flush()
    # Note: user is a schema, not a model, so we can't refresh it
    # The flush should ensure visibility across sessions

    return user


# --------------------------------Regular User Fixtures--------------------------------#


@pytest.fixture
def test_user_data():
    """Create test user data."""
    unique_suffix = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Viewer {unique_suffix}",
        "email": f"viewer.{unique_suffix}@example.com",
        "password": "SecurePass123",
    }


@pytest.fixture
async def test_user(async_db_session, test_user_data):
    """Create a test user in the database."""

    from src.core.repositories.general.user_repository import UserRepository
    from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

    user_repo = UserRepository(async_db_session)
    preference_repo = UserPreferenceRepository(async_db_session)
    user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

    user_create = UserCreateSchema(**test_user_data)
    user = await user_service.create_user(user_create)

    # Get the actual user model to activate it and verify email for testing
    from src.core.models.general.user import User
    from sqlalchemy import select

    stmt = select(User).where(User.id == user.id)
    result = await async_db_session.execute(stmt)
    user_model = result.scalar_one()

    # Set user as active and email verified for testing
    user_model.is_active = True
    user_model.is_email_verified = True
    user_model.email_verified_at = user_model.created_at
    await async_db_session.commit()
    await async_db_session.refresh(user_model)

    # Create viewer role if it doesn't exist and assign it
    from src.core.models.general.user_role import UserRole, UserRoleAssignment

    viewer_role_result = await async_db_session.execute(select(UserRole).where(UserRole.name == "VIEWER"))
    viewer_role = viewer_role_result.scalar_one_or_none()
    if not viewer_role:
        viewer_role = UserRole(
            name="VIEWER",
            description="Viewer role",
            is_system_role=True,
            is_active=True,
            priority=10,
        )
        async_db_session.add(viewer_role)
        await async_db_session.commit()
        await async_db_session.refresh(viewer_role)

    # Assign viewer role to user
    role_assignment = UserRoleAssignment(
        name=f"Viewer Role Assignment {user.id}",
        user_id=user.id,
        role_id=viewer_role.id,
        is_active=True,
        notes=f"User {user.id} - VIEWER Role Assignment",
    )
    async_db_session.add(role_assignment)
    await async_db_session.commit()

    # Ensure all changes are flushed and visible to other sessions
    await async_db_session.flush()

    # Additional synchronization attempt: force connection sharing
    # This is a workaround for the session isolation issue
    await async_db_session.commit()
    # Note: user is a schema, not a model, so we can't refresh it
    # The commit should ensure visibility across sessions

    return user


# --------------------------------Inactive User Fixtures--------------------------------#


@pytest.fixture
def test_inactive_user_data():
    """Create test user data."""
    unique_suffix = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Inactive {unique_suffix}",
        "email": f"inactive.{unique_suffix}@example.com",
        "password": "SecurePass123",
    }


@pytest.fixture
async def test_inactive_user(async_db_session, test_inactive_user_data):
    """Create a test inactive user in the database."""

    from src.core.repositories.general.user_repository import UserRepository
    from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

    user_repo = UserRepository(async_db_session)
    preference_repo = UserPreferenceRepository(async_db_session)
    user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

    user_create = UserCreateSchema(**test_inactive_user_data)
    user_schema = await user_service.create_user(user_create)

    # Get the actual user model to modify it
    from src.core.models.general.user import User
    from sqlalchemy import select

    stmt = select(User).where(User.id == user_schema.id)
    result = await async_db_session.execute(stmt)
    user = result.scalar_one()

    # Set user as inactive
    user.is_active = False
    await async_db_session.commit()
    await async_db_session.refresh(user)

    # Create viewer role if it doesn't exist and assign it
    from src.core.models.general.user_role import UserRole, UserRoleAssignment

    viewer_role_result = await async_db_session.execute(select(UserRole).where(UserRole.name == "VIEWER"))
    viewer_role = viewer_role_result.scalar_one_or_none()
    if not viewer_role:
        viewer_role = UserRole(
            name="VIEWER",
            description="Viewer role",
            is_system_role=True,
            is_active=True,
            priority=10,
        )
        async_db_session.add(viewer_role)
        await async_db_session.commit()
        await async_db_session.refresh(viewer_role)

    # Assign viewer role to user (use user_schema.id to avoid async access)
    role_assignment = UserRoleAssignment(
        name=f"Inactive Viewer Role Assignment {user_schema.id}",
        user_id=user_schema.id,
        role_id=viewer_role.id,
        is_active=True,
        notes=f"User {user_schema.id} - VIEWER Role Assignment",
    )
    async_db_session.add(role_assignment)
    await async_db_session.commit()

    # Additional synchronization attempt: force connection sharing
    # This is a workaround for the session isolation issue
    await async_db_session.flush()
    # Note: user_schema is a schema, not a model, so we can't refresh it
    # The flush should ensure visibility across sessions

    return user_schema


# --------------------------------Authentication Token Fixtures--------------------------------#


@pytest.fixture
async def admin_token(async_http_client: httpx.AsyncClient, test_admin_user):
    """Get admin authentication token."""
    login_data = {
        "username": test_admin_user.email,
        "password": "SecurePass123",
    }
    response = await async_http_client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


@pytest.fixture
async def user_token(async_http_client: httpx.AsyncClient, test_user):
    """Get regular user authentication token."""
    login_data = {
        "username": test_user.email,
        "password": "SecurePass123",
    }
    response = await async_http_client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


# Alias fixtures for backward compatibility
@pytest.fixture
def sample_advanced_search_request(mock_advanced_search_request):
    """Alias for mock_advanced_search_request."""
    return mock_advanced_search_request


@pytest.fixture
def sample_search_response(mock_search_response):
    """Alias for mock_search_response."""
    return mock_search_response


@pytest.fixture
def sample_bulk_create_request(mock_bulk_create_request):
    """Alias for mock_bulk_create_request."""
    return mock_bulk_create_request


@pytest.fixture
async def sample_component_type_id(
    authenticated_client: httpx.AsyncClient, sample_category_id: int, test_project
) -> int:
    """Create a sample component type and return its ID."""
    type_data = {
        "name": "Test Circuit Breaker",
        "description": "Test circuit breaker type",
        "category_id": sample_category_id,
        "is_active": True,
    }

    response = await authenticated_client.post(
        f"/api/v1/projects/{test_project.id}/components/component-types/",
        json=type_data,
    )

    # Debug: Print response details if it fails
    if response.status_code != 201:
        print(f"Component Type creation failed with status {response.status_code}")
        print(f"Response: {response.text}")
        raise Exception(f"Failed to create component type: {response.text}")

    return response.json()["id"]


@pytest.fixture
def sample_component_data(sample_category_id: int, sample_component_type_id: int) -> Dict[str, Any]:
    """Sample component data for testing."""
    return {
        "name": "Circuit Breaker 16A",
        "manufacturer": "ABB",
        "model_number": "S203-C16",
        "description": "3-pole miniature circuit breaker, 16A, C-curve",
        "component_type_id": sample_component_type_id,
        "category_id": sample_category_id,
        "specifications": {
            "rated_current": 16,
            "rated_voltage": 230,
            "breaking_capacity": 6000,
            "curve_type": "C",
            "poles": 3,
        },
        "unit_price": 125.99,
        "currency": "EUR",
        "supplier": "Electrical Supply Co",
        "part_number": "ESC-S203-C16",
        "weight_kg": 0.5,
        "dimensions": {"length": 54.0, "width": 18.0, "height": 85.0, "unit": "mm"},
        "is_active": True,
        "is_preferred": False,
        "stock_status": "available",
        "version": "1.0",
    }


@pytest.fixture
async def test_user_role(async_db_session):
    """Create a test user role in the database using async session."""
    from src.core.models.general.user_role import UserRole
    import uuid

    unique_suffix = str(uuid.uuid4())[:8]
    role = UserRole(
        name=f"TEST_ROLE_{unique_suffix}",
        description="Test role for unit tests",
        is_system_role=False,
        is_active=True,
        priority=50,
    )
    async_db_session.add(role)
    await async_db_session.commit()
    await async_db_session.refresh(role)
    return role
