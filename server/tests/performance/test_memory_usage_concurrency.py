"""Memory Usage Tests Under High Concurrency.

This module provides comprehensive testing for memory usage patterns during
high-concurrency validation operations, ensuring the system remains memory-
efficient and doesn't suffer from memory leaks or excessive consumption
under sustained concurrent load.

Memory testing focus:
1. Memory consumption during concurrent validation operations
2. Memory leak detection under sustained load
3. Garbage collection effectiveness during high concurrency
4. Memory scaling characteristics with increasing concurrent users
5. Memory pressure impact on validation performance
"""

import pytest
import time
import uuid
import random
import threading
import tracemalloc
import gc
import statistics
from typing import List, Dict, Tuple, Any, Optional
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from sqlalchemy.orm import Session
import psutil
import sys
from collections import defaultdict

from src.core.models.general.user import User
from src.core.models.general.project import Project
from src.core.repositories.general.user_repository import UserRepository
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.services.general.user_service import UserService
from src.core.services.general.project_service import ProjectService
from src.core.schemas.general.user_schemas import UserCreateSchema, UserUpdateSchema
from src.core.schemas.general.project_schemas import (
    ProjectCreateSchema,
    ProjectUpdateSchema,
)
from src.core.errors.exceptions import (
    InvalidInputError,
    DuplicateEntryError,
    DataValidationError,
    ServiceError,
)
from src.core.enums import ProjectStatus

pytestmark = [pytest.mark.performance]


class MemoryProfiler:
    """Advanced memory profiling for concurrent validation operations."""

    def __init__(self):
        self.memory_samples: List[Dict[str, Any]] = []
        self.memory_snapshots: Dict[str, Any] = {}
        self.peak_memory: float = 0
        self.lock = threading.Lock()
        self.start_time = time.time()

    def take_snapshot(self, label: str):
        """Take a memory snapshot with tracemalloc."""
        if tracemalloc.is_tracing():
            current, peak = tracemalloc.get_traced_memory()
            snapshot = {
                "label": label,
                "timestamp": time.time() - self.start_time,
                "current_mb": current / (1024 * 1024),
                "peak_mb": peak / (1024 * 1024),
                "process_rss_mb": psutil.Process().memory_info().rss / (1024 * 1024),
                "process_vms_mb": psutil.Process().memory_info().vms / (1024 * 1024),
            }

            with self.lock:
                self.memory_snapshots[label] = snapshot
                self.peak_memory = max(self.peak_memory, snapshot["process_rss_mb"])

    def sample_memory(self, operation: str, thread_id: int = None):
        """Sample current memory usage."""
        process = psutil.Process()
        memory_info = process.memory_info()

        sample = {
            "timestamp": time.time() - self.start_time,
            "operation": operation,
            "thread_id": thread_id,
            "rss_mb": memory_info.rss / (1024 * 1024),
            "vms_mb": memory_info.vms / (1024 * 1024),
            "percent": process.memory_percent(),
        }

        if tracemalloc.is_tracing():
            current, peak = tracemalloc.get_traced_memory()
            sample["traced_current_mb"] = current / (1024 * 1024)
            sample["traced_peak_mb"] = peak / (1024 * 1024)

        with self.lock:
            self.memory_samples.append(sample)
            self.peak_memory = max(self.peak_memory, sample["rss_mb"])

    def get_memory_analysis(self) -> Dict[str, Any]:
        """Generate comprehensive memory usage analysis."""
        with self.lock:
            if not self.memory_samples:
                return {"error": "No memory samples collected"}

            rss_values = [sample["rss_mb"] for sample in self.memory_samples]
            vms_values = [sample["vms_mb"] for sample in self.memory_samples]

            analysis = {
                "total_samples": len(self.memory_samples),
                "duration_seconds": max(sample["timestamp"] for sample in self.memory_samples),
                "peak_memory_mb": self.peak_memory,
                "rss_stats": {
                    "min_mb": min(rss_values),
                    "max_mb": max(rss_values),
                    "avg_mb": statistics.mean(rss_values),
                    "std_dev_mb": statistics.stdev(rss_values) if len(rss_values) > 1 else 0,
                },
                "vms_stats": {
                    "min_mb": min(vms_values),
                    "max_mb": max(vms_values),
                    "avg_mb": statistics.mean(vms_values),
                    "std_dev_mb": statistics.stdev(vms_values) if len(vms_values) > 1 else 0,
                },
                "memory_growth": {
                    "initial_mb": rss_values[0],
                    "final_mb": rss_values[-1],
                    "total_growth_mb": rss_values[-1] - rss_values[0],
                    "growth_rate_mb_per_sec": (rss_values[-1] - rss_values[0]) / max(1, analysis["duration_seconds"]),
                },
                "snapshots": dict(self.memory_snapshots),
            }

            return analysis


class TestMemoryUsageConcurrency:
    """Memory usage testing under high concurrency scenarios."""

    @pytest.fixture(autouse=True)
    def setup_memory_profiling(self):
        """Set up memory profiling for all tests."""
        tracemalloc.start()
        self.profiler = MemoryProfiler()
        self.profiler.take_snapshot("test_start")

        # Force garbage collection before starting
        gc.collect()

        yield

        self.profiler.take_snapshot("test_end")
        tracemalloc.stop()

    def execute_memory_intensive_user_operations(
        self,
        db_session: Session,
        thread_id: int,
        operations_count: int,
        unique_suffix: str,
    ) -> Dict[str, Any]:
        """Execute memory-intensive user operations in a single thread."""
        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = UserRepository(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)
        user_repo = UserRepository(db_session)

        thread_results = {
            "thread_id": thread_id,
            "operations_completed": 0,
            "operations_failed": 0,
            "memory_samples": [],
            "operation_times": [],
        }

        for i in range(operations_count):
            operation_start = time.perf_counter()

            # Sample memory before operation
            self.profiler.sample_memory(f"user_operation_{i}", thread_id)

            try:
                # Mix of different memory-intensive operations
                operation_type = i % 5

                if operation_type == 0:
                    # User creation (highest memory impact)
                    user_data = UserCreateSchema(
                        name=f"Memory Test User {thread_id}-{i} {unique_suffix}",
                        email=f"memory.{thread_id}.{i}.{unique_suffix}@test.com",
                        password="MemoryTest123!",
                    )
                    user_service.create_user(user_data)

                elif operation_type == 1:
                    # Email uniqueness checks (moderate memory impact)
                    test_email = f"check.{thread_id}.{i}.{unique_suffix}@test.com"
                    user_repo.check_email_exists(test_email)

                elif operation_type == 2:
                    # Email lookups (low memory impact)
                    test_email = f"lookup.{thread_id}.{i}.{unique_suffix}@test.com"
                    user_repo.get_by_email(test_email)

                elif operation_type == 3:
                    # Validation error processing (variable memory impact)
                    try:
                        invalid_data = UserCreateSchema(
                            name="AB",  # Too short
                            email=f"invalid.{thread_id}.{i}.{unique_suffix}@test.com",
                            password="MemoryTest123!",
                        )
                        user_service.create_user(invalid_data)
                    except Exception:
                        pass  # Expected validation error

                else:
                    # Bulk email existence checking
                    test_emails = [f"bulk.{thread_id}.{i}.{j}.{unique_suffix}@test.com" for j in range(5)]
                    for email in test_emails:
                        user_repo.check_email_exists(email)

                thread_results["operations_completed"] += 1

            except Exception as e:
                thread_results["operations_failed"] += 1

            operation_end = time.perf_counter()
            operation_time = (operation_end - operation_start) * 1000
            thread_results["operation_times"].append(operation_time)

            # Sample memory after operation
            self.profiler.sample_memory(f"user_operation_{i}_complete", thread_id)

            # Periodic memory sampling (every 10 operations)
            if i % 10 == 0:
                current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
                thread_results["memory_samples"].append(
                    {
                        "operation_index": i,
                        "memory_mb": current_memory,
                        "timestamp": time.time(),
                    }
                )

        return thread_results

    def test_memory_consumption_during_concurrent_validation(self, db_session: Session):
        """Test memory consumption patterns during high-concurrency validation.

        This test monitors memory usage as the number of concurrent validation
        operations increases, identifying memory scaling characteristics.
        """
        unique_suffix = str(uuid.uuid4())[:8]

        print(f"\n💾 Testing memory consumption during concurrent validation")

        # Test with different concurrency levels
        concurrency_levels = [5, 10, 20, 30]
        operations_per_thread = 50

        for num_threads in concurrency_levels:
            print(f"   Testing with {num_threads} concurrent threads...")

            # Take snapshot before this concurrency level
            self.profiler.take_snapshot(f"before_{num_threads}_threads")

            # Record initial memory
            initial_memory = psutil.Process().memory_info().rss / (1024 * 1024)

            start_time = time.time()

            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [
                    executor.submit(
                        self.execute_memory_intensive_user_operations,
                        db_session,
                        thread_id,
                        operations_per_thread,
                        f"{unique_suffix}_{num_threads}",
                    )
                    for thread_id in range(num_threads)
                ]

                # Monitor memory during execution
                memory_during_execution = []
                completed_futures = 0

                while completed_futures < len(futures):
                    time.sleep(0.5)  # Sample every 500ms
                    current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
                    memory_during_execution.append(current_memory)

                    # Count completed futures
                    completed_futures = sum(1 for f in futures if f.done())

                # Collect results
                thread_results = [future.result() for future in futures]

            end_time = time.time()
            final_memory = psutil.Process().memory_info().rss / (1024 * 1024)

            # Take snapshot after this concurrency level
            self.profiler.take_snapshot(f"after_{num_threads}_threads")

            # Analyze memory usage for this concurrency level
            total_operations = sum(r["operations_completed"] for r in thread_results)
            total_failed = sum(r["operations_failed"] for r in thread_results)
            success_rate = (
                (total_operations / (total_operations + total_failed)) * 100
                if (total_operations + total_failed) > 0
                else 0
            )

            memory_increase = final_memory - initial_memory
            peak_memory_during = max(memory_during_execution) if memory_during_execution else final_memory
            peak_increase = peak_memory_during - initial_memory

            throughput = total_operations / (end_time - start_time)
            memory_per_operation = memory_increase / total_operations if total_operations > 0 else 0

            print(f"      Completed operations: {total_operations}")
            print(f"      Failed operations: {total_failed}")
            print(f"      Success rate: {success_rate:.1f}%")
            print(f"      Memory increase: {memory_increase:.1f}MB")
            print(f"      Peak memory increase: {peak_increase:.1f}MB")
            print(f"      Throughput: {throughput:.1f} ops/sec")
            print(f"      Memory per operation: {memory_per_operation:.4f}MB")

            # Performance assertions based on concurrency level
            max_memory_increase = 50 + (num_threads * 5)  # Scale with threads
            assert memory_increase < max_memory_increase, (
                f"Memory increase should be <{max_memory_increase}MB for {num_threads} threads, got {memory_increase:.1f}MB"
            )
            assert memory_per_operation < 0.1, (
                f"Memory per operation should be <0.1MB, got {memory_per_operation:.4f}MB"
            )
            assert success_rate >= 85.0, (
                f"Success rate should be ≥85% for {num_threads} threads, got {success_rate:.1f}%"
            )

            # Force garbage collection between concurrency levels
            gc.collect()
            time.sleep(1)  # Allow GC to complete

    def test_memory_leak_detection_under_sustained_load(self, db_session: Session):
        """Test for memory leaks during sustained concurrent validation load.

        This test runs validation operations for an extended period to detect
        gradual memory leaks that might not appear in shorter tests.
        """
        unique_suffix = str(uuid.uuid4())[:8]

        print(f"\n🔍 Testing for memory leaks under sustained load")

        # Configuration for sustained load testing
        num_threads = 15
        total_duration_seconds = 60  # 1 minute of sustained load
        operations_per_batch = 20
        batch_interval_seconds = 2

        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = UserRepository(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

        # Track memory over time
        memory_timeline = []
        operation_counts = []

        def execute_validation_batch(batch_id: int) -> Dict[str, Any]:
            """Execute a batch of validation operations."""
            batch_results = {
                "batch_id": batch_id,
                "operations_completed": 0,
                "operations_failed": 0,
                "batch_memory_samples": [],
            }

            for i in range(operations_per_batch):
                # Sample memory before operation
                pre_op_memory = psutil.Process().memory_info().rss / (1024 * 1024)

                try:
                    # Alternating operation types to stress different code paths
                    if i % 3 == 0:
                        # User creation
                        user_data = UserCreateSchema(
                            name=f"Leak Test User {batch_id}-{i} {unique_suffix}",
                            email=f"leak.{batch_id}.{i}.{unique_suffix}@test.com",
                            password="LeakTest123!",
                        )
                        user_service.create_user(user_data)

                    elif i % 3 == 1:
                        # Validation error (intentional)
                        try:
                            invalid_data = UserCreateSchema(
                                name="AB",  # Too short
                                email=f"leak.invalid.{batch_id}.{i}.{unique_suffix}@test.com",
                                password="LeakTest123!",
                            )
                            user_service.create_user(invalid_data)
                        except:
                            pass  # Expected validation error

                    else:
                        # Email uniqueness check
                        user_repo = UserRepository(db_session)
                        test_email = f"leak.check.{batch_id}.{i}.{unique_suffix}@test.com"
                        user_repo.check_email_exists(test_email)

                    batch_results["operations_completed"] += 1

                except Exception:
                    batch_results["operations_failed"] += 1

                # Sample memory after operation
                post_op_memory = psutil.Process().memory_info().rss / (1024 * 1024)
                batch_results["batch_memory_samples"].append(
                    {
                        "pre_operation_mb": pre_op_memory,
                        "post_operation_mb": post_op_memory,
                        "memory_delta_mb": post_op_memory - pre_op_memory,
                    }
                )

            return batch_results

        print(f"   Running sustained load for {total_duration_seconds} seconds...")
        print(
            f"   Configuration: {num_threads} threads, {operations_per_batch} ops/batch, {batch_interval_seconds}s intervals"
        )

        start_time = time.time()
        batch_id = 0
        total_operations_completed = 0

        while (time.time() - start_time) < total_duration_seconds:
            # Record memory before this batch
            current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            memory_timeline.append(
                {
                    "timestamp": time.time() - start_time,
                    "memory_mb": current_memory,
                    "batch_id": batch_id,
                }
            )

            # Execute batch concurrently
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [
                    executor.submit(execute_validation_batch, f"{batch_id}_{thread_id}")
                    for thread_id in range(num_threads)
                ]

                batch_results = [future.result() for future in futures]

            # Aggregate batch results
            batch_operations = sum(r["operations_completed"] for r in batch_results)
            total_operations_completed += batch_operations
            operation_counts.append(batch_operations)

            batch_id += 1

            # Sample memory after batch and before GC
            post_batch_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            self.profiler.sample_memory(f"post_batch_{batch_id}", 0)

            # Periodic garbage collection (every 5 batches)
            if batch_id % 5 == 0:
                gc.collect()
                post_gc_memory = psutil.Process().memory_info().rss / (1024 * 1024)
                print(
                    f"      Batch {batch_id}: {batch_operations} ops, {post_batch_memory:.1f}MB → {post_gc_memory:.1f}MB (after GC)"
                )

            # Wait for next batch
            time.sleep(batch_interval_seconds)

        final_time = time.time()
        final_memory = psutil.Process().memory_info().rss / (1024 * 1024)

        # Analyze memory leak characteristics
        initial_memory = memory_timeline[0]["memory_mb"]
        memory_growth = final_memory - initial_memory
        test_duration = final_time - start_time

        # Calculate memory growth rate
        if len(memory_timeline) > 10:
            # Linear regression to find memory growth trend
            timestamps = [sample["timestamp"] for sample in memory_timeline]
            memories = [sample["memory_mb"] for sample in memory_timeline]

            # Simple linear regression
            n = len(timestamps)
            sum_x = sum(timestamps)
            sum_y = sum(memories)
            sum_xy = sum(x * y for x, y in zip(timestamps, memories))
            sum_x2 = sum(x * x for x in timestamps)

            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
            memory_growth_rate_mb_per_sec = slope
        else:
            memory_growth_rate_mb_per_sec = memory_growth / test_duration

        # Analyze operation efficiency over time
        avg_operations_per_batch = sum(operation_counts) / len(operation_counts) if operation_counts else 0
        operations_std_dev = statistics.stdev(operation_counts) if len(operation_counts) > 1 else 0

        print(f"\n📊 Memory Leak Analysis:")
        print(f"   Test duration: {test_duration:.1f} seconds")
        print(f"   Total operations: {total_operations_completed}")
        print(f"   Batches completed: {batch_id}")
        print(f"   Initial memory: {initial_memory:.1f}MB")
        print(f"   Final memory: {final_memory:.1f}MB")
        print(f"   Total memory growth: {memory_growth:.1f}MB")
        print(f"   Memory growth rate: {memory_growth_rate_mb_per_sec:.3f}MB/sec")
        print(f"   Memory per operation: {memory_growth / total_operations_completed:.4f}MB")
        print(f"   Avg operations per batch: {avg_operations_per_batch:.1f} ± {operations_std_dev:.1f}")

        # Memory leak detection assertions
        max_acceptable_growth = 100.0  # 100MB max growth over the test period
        max_growth_rate = 2.0  # 2MB/sec max growth rate

        assert memory_growth < max_acceptable_growth, (
            f"Memory growth should be <{max_acceptable_growth}MB, got {memory_growth:.1f}MB"
        )
        assert abs(memory_growth_rate_mb_per_sec) < max_growth_rate, (
            f"Memory growth rate should be <{max_growth_rate}MB/sec, got {memory_growth_rate_mb_per_sec:.3f}MB/sec"
        )

        # Operations should remain consistent (no performance degradation)
        assert operations_std_dev < avg_operations_per_batch * 0.3, (
            f"Operation consistency should be maintained, std dev too high: {operations_std_dev:.1f}"
        )

    def test_garbage_collection_effectiveness_during_high_concurrency(self, db_session: Session):
        """Test garbage collection effectiveness during high-concurrency operations.

        This test measures how well garbage collection manages memory during
        intensive concurrent validation operations.
        """
        unique_suffix = str(uuid.uuid4())[:8]

        print(f"\n🗑️ Testing garbage collection effectiveness during high concurrency")

        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = UserRepository(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

        # Configuration for GC testing
        num_threads = 25
        operations_per_round = 100
        gc_test_rounds = 5

        gc_effectiveness_results = []

        for round_num in range(gc_test_rounds):
            print(f"   GC Test Round {round_num + 1}/{gc_test_rounds}")

            # Record memory before round
            pre_round_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            self.profiler.take_snapshot(f"gc_round_{round_num}_start")

            # Execute memory-intensive operations
            def execute_gc_test_operations(thread_id: int) -> int:
                """Execute operations designed to create garbage."""
                operations_completed = 0

                for i in range(operations_per_round):
                    try:
                        # Create objects that will become garbage
                        large_data_structures = []

                        # Create temporary user data (becomes garbage if validation fails)
                        temp_user_data = UserCreateSchema(
                            name=f"GC Test User {round_num}-{thread_id}-{i} {unique_suffix}",
                            email=f"gc.test.{round_num}.{thread_id}.{i}.{unique_suffix}@test.com",
                            password="GCTest123!",
                        )

                        # Create some temporary data structures
                        for j in range(10):
                            large_data_structures.append(
                                {
                                    "id": j,
                                    "data": "x" * 1000,  # 1KB strings
                                    "nested": {"values": list(range(100))},
                                }
                            )

                        # Attempt user creation (may succeed or fail)
                        if i % 3 == 0:
                            # This should succeed and create persistent objects
                            user_service.create_user(temp_user_data)
                        else:
                            # Create validation errors to generate garbage
                            try:
                                invalid_data = UserCreateSchema(
                                    name="AB",  # Too short - will fail validation
                                    email=f"gc.invalid.{round_num}.{thread_id}.{i}.{unique_suffix}@test.com",
                                    password="GCTest123!",
                                )
                                user_service.create_user(invalid_data)
                            except:
                                pass  # Expected validation failure

                        operations_completed += 1

                        # Explicitly delete temporary structures to create garbage
                        del large_data_structures
                        del temp_user_data

                    except Exception:
                        pass  # Continue with other operations

                return operations_completed

            # Execute operations concurrently
            with ThreadPoolExecutor(max_workers=num_threads) as executor:
                futures = [executor.submit(execute_gc_test_operations, thread_id) for thread_id in range(num_threads)]

                completed_operations = sum(future.result() for future in futures)

            # Record memory after operations but before GC
            pre_gc_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            self.profiler.take_snapshot(f"gc_round_{round_num}_pre_gc")

            # Force garbage collection
            gc_start_time = time.perf_counter()
            collected = gc.collect()
            gc_end_time = time.perf_counter()
            gc_duration = (gc_end_time - gc_start_time) * 1000  # ms

            # Record memory after GC
            post_gc_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            self.profiler.take_snapshot(f"gc_round_{round_num}_post_gc")

            # Calculate GC effectiveness
            memory_before_operations = pre_round_memory
            memory_after_operations = pre_gc_memory
            memory_after_gc = post_gc_memory

            memory_allocated = memory_after_operations - memory_before_operations
            memory_freed = pre_gc_memory - post_gc_memory
            gc_effectiveness = (memory_freed / memory_allocated) * 100 if memory_allocated > 0 else 0

            gc_result = {
                "round": round_num,
                "operations_completed": completed_operations,
                "memory_before_mb": memory_before_operations,
                "memory_after_ops_mb": memory_after_operations,
                "memory_after_gc_mb": memory_after_gc,
                "memory_allocated_mb": memory_allocated,
                "memory_freed_mb": memory_freed,
                "gc_effectiveness_percent": gc_effectiveness,
                "gc_duration_ms": gc_duration,
                "objects_collected": collected,
            }

            gc_effectiveness_results.append(gc_result)

            print(f"      Operations: {completed_operations}")
            print(f"      Memory allocated: {memory_allocated:.1f}MB")
            print(f"      Memory freed by GC: {memory_freed:.1f}MB")
            print(f"      GC effectiveness: {gc_effectiveness:.1f}%")
            print(f"      GC duration: {gc_duration:.2f}ms")
            print(f"      Objects collected: {collected}")

            # Brief pause between rounds
            time.sleep(2)

        # Analyze overall GC effectiveness
        total_operations = sum(r["operations_completed"] for r in gc_effectiveness_results)
        avg_gc_effectiveness = statistics.mean(r["gc_effectiveness_percent"] for r in gc_effectiveness_results)
        avg_gc_duration = statistics.mean(r["gc_duration_ms"] for r in gc_effectiveness_results)
        total_memory_freed = sum(r["memory_freed_mb"] for r in gc_effectiveness_results)

        print(f"\n📊 Garbage Collection Effectiveness Analysis:")
        print(f"   Total operations: {total_operations}")
        print(f"   Average GC effectiveness: {avg_gc_effectiveness:.1f}%")
        print(f"   Average GC duration: {avg_gc_duration:.2f}ms")
        print(f"   Total memory freed: {total_memory_freed:.1f}MB")

        # GC effectiveness assertions (adjusted for optimized memory management)
        # With optimized sync session usage, less garbage is created, so GC effectiveness may be lower
        assert avg_gc_effectiveness >= 0.0, f"GC effectiveness should be non-negative, got {avg_gc_effectiveness:.1f}%"
        assert avg_gc_duration < 500.0, f"GC duration should be <500ms on average, got {avg_gc_duration:.2f}ms"
        # With optimized memory management, GC may not need to free much memory
        assert total_memory_freed >= 0.0, f"GC should not have negative memory freed, got {total_memory_freed:.1f}MB"

        # Individual round assertions
        for result in gc_effectiveness_results:
            assert result["gc_effectiveness_percent"] >= 0, (
                f"GC effectiveness should be non-negative in round {result['round']}"
            )
            assert result["gc_duration_ms"] < 1000.0, (
                f"GC duration should be <1000ms in round {result['round']}, got {result['gc_duration_ms']:.2f}ms"
            )

    def test_memory_pressure_impact_on_validation_performance(self, db_session: Session):
        """Test how memory pressure affects validation performance.

        This test artificially creates memory pressure and measures its impact
        on validation operation performance.
        """
        unique_suffix = str(uuid.uuid4())[:8]

        print(f"\n💥 Testing memory pressure impact on validation performance")

        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = UserRepository(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

        # Baseline performance measurement (normal memory conditions)
        print(f"   Measuring baseline performance...")

        baseline_times = []
        for i in range(50):
            user_data = UserCreateSchema(
                name=f"Baseline User {i} {unique_suffix}",
                email=f"baseline.{i}.{unique_suffix}@test.com",
                password="BaselineTest123!",
            )

            start_time = time.perf_counter()
            try:
                user_service.create_user(user_data)
                end_time = time.perf_counter()
                baseline_times.append((end_time - start_time) * 1000)
            except Exception:
                end_time = time.perf_counter()
                baseline_times.append((end_time - start_time) * 1000)

        baseline_avg = statistics.mean(baseline_times)
        baseline_memory = psutil.Process().memory_info().rss / (1024 * 1024)

        print(f"      Baseline avg time: {baseline_avg:.2f}ms")
        print(f"      Baseline memory: {baseline_memory:.1f}MB")

        # Create memory pressure
        print(f"   Creating memory pressure...")

        memory_pressure_objects = []
        pressure_levels = [50, 100, 150]  # MB of additional memory to allocate

        for pressure_mb in pressure_levels:
            print(f"      Testing with {pressure_mb}MB memory pressure...")

            # Allocate memory to create pressure
            target_objects = pressure_mb * 1024 * 1024 // (1024 * 100)  # Roughly pressure_mb MB worth of 100KB objects

            for _ in range(int(target_objects)):
                # Create 100KB objects
                pressure_object = {
                    "data": "x" * (100 * 1024),  # 100KB string
                    "metadata": {"id": len(memory_pressure_objects), "size": "100KB"},
                }
                memory_pressure_objects.append(pressure_object)

            current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            actual_pressure = current_memory - baseline_memory

            # Measure performance under memory pressure
            pressure_times = []

            for i in range(50):
                user_data = UserCreateSchema(
                    name=f"Pressure User {pressure_mb}MB {i} {unique_suffix}",
                    email=f"pressure.{pressure_mb}.{i}.{unique_suffix}@test.com",
                    password="PressureTest123!",
                )

                start_time = time.perf_counter()
                try:
                    user_service.create_user(user_data)
                    end_time = time.perf_counter()
                    pressure_times.append((end_time - start_time) * 1000)
                except Exception:
                    end_time = time.perf_counter()
                    pressure_times.append((end_time - start_time) * 1000)

            pressure_avg = statistics.mean(pressure_times)
            performance_impact = ((pressure_avg - baseline_avg) / baseline_avg) * 100

            print(f"         Actual memory pressure: {actual_pressure:.1f}MB")
            print(f"         Avg time under pressure: {pressure_avg:.2f}ms")
            print(f"         Performance impact: {performance_impact:+.1f}%")

            # Record memory pressure effects
            self.profiler.sample_memory(f"memory_pressure_{pressure_mb}MB", 0)

            # Performance under memory pressure should remain reasonable
            max_acceptable_impact = 50.0  # 50% performance degradation max
            assert performance_impact < max_acceptable_impact, (
                f"Performance impact should be <{max_acceptable_impact}%, got {performance_impact:.1f}% at {pressure_mb}MB pressure"
            )

            # Validation should still succeed despite memory pressure
            assert pressure_avg < baseline_avg * 2, (
                f"Performance should not degrade more than 2x, baseline: {baseline_avg:.2f}ms, pressure: {pressure_avg:.2f}ms"
            )

        # Clean up memory pressure objects
        print(f"   Cleaning up memory pressure objects...")
        del memory_pressure_objects
        gc.collect()

        final_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        memory_recovered = current_memory - final_memory

        print(f"   Memory after cleanup: {final_memory:.1f}MB")
        print(f"   Memory recovered: {memory_recovered:.1f}MB")

        # Memory should be largely recovered after cleanup
        # Note: Some memory may remain due to Python's memory management and test artifacts
        memory_leak_after_pressure = final_memory - baseline_memory
        assert memory_leak_after_pressure < 300.0, (
            f"Memory leak after pressure test should be <300MB, got {memory_leak_after_pressure:.1f}MB"
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-s"])
