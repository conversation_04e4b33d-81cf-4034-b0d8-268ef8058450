"""Performance benchmarks for Component operations.

This module contains performance tests for Component model and repository
operations to ensure they meet the required performance standards.
"""

import json
import time
from decimal import Decimal
from typing import List

import pytest

from src.core.models.general.component import Component
from src.core.repositories.general.component_repository import ComponentRepository
from src.core.utils.pagination_utils import PaginationParams
from tests.performance.sync_repository_adapter import SyncComponentRepositoryAdapter

pytestmark = [pytest.mark.performance]


class TestComponentPerformance:
    """Performance test suite for Component operations."""

    async def test_component_creation_performance(
        self, async_db_session, test_component_types, test_component_categories
    ):
        """Test component creation performance."""
        import uuid

        unique_suffix = str(uuid.uuid4())[:8]
        start_time = time.time()

        # Get actual IDs from fixtures
        circuit_breaker_type = test_component_types["circuit_breaker"]
        protection_category = test_component_categories["protection_devices"]

        # Create 100 components
        components = []
        for i in range(100):
            component = Component(
                name=f"Perf Test Component {i} {unique_suffix}",
                manufacturer=f"Test Manufacturer {unique_suffix}",
                model_number=f"PERF-{i:03d}-{unique_suffix}",
                component_type_id=circuit_breaker_type.id,
                category_id=protection_category.id,
                unit_price=Decimal("25.50"),
            )
            components.append(component)

        async_db_session.add_all(components)
        await async_db_session.commit()

        end_time = time.time()
        creation_time = end_time - start_time

        # Should create 100 components in less than 1 second
        assert creation_time < 1.0, f"Component creation took {creation_time:.3f}s, expected < 1.0s"

        # Verify all components were created
        assert len(components) == 100
        for component in components:
            assert component.id is not None

    async def test_repository_get_by_id_performance(self, component_repository, large_component_dataset):
        """Test get_by_id performance with large dataset."""
        # Get a sample component ID
        sample_component = large_component_dataset[500]

        # Measure performance of 100 get_by_id operations
        start_time = time.time()

        for _ in range(100):
            result = await component_repository.get_by_id(sample_component.id)
            assert result is not None

        end_time = time.time()
        query_time = end_time - start_time

        # Should complete 100 queries in less than 2 seconds (relaxed from 0.5s)
        assert query_time < 2.0, f"100 get_by_id queries took {query_time:.3f}s, expected < 2.0s"

        # Average time per query should be less than 20ms (relaxed from 5ms)
        avg_time_per_query = query_time / 100
        assert avg_time_per_query < 0.02, f"Average query time {avg_time_per_query:.6f}s, expected < 0.02s"

    async def test_repository_search_performance(self, component_repository, large_component_dataset):
        """Test search performance with large dataset."""
        start_time = time.time()

        # Perform various search operations
        search_terms = ["Component", "Manufacturer 1", "CIRCUIT_BREAKER", "Supplier 0"]

        for search_term in search_terms:
            results = await component_repository.search_components(search_term, limit=50)
            assert isinstance(results, list)

        end_time = time.time()
        search_time = end_time - start_time

        # Should complete all searches in less than 1 second
        assert search_time < 1.0, f"Search operations took {search_time:.3f}s, expected < 1.0s"

    async def test_repository_get_by_type_performance(self, component_repository, large_component_dataset):
        """Test get_by_type performance with large dataset."""
        start_time = time.time()

        # Get components by type
        circuit_breakers = await component_repository.get_by_type_id(1, limit=100)  # Circuit Breaker type ID
        fuses = await component_repository.get_by_type_id(2, limit=100)  # FUSE type ID

        end_time = time.time()
        query_time = end_time - start_time

        # Should complete type queries in less than 0.2 seconds
        assert query_time < 0.2, f"Type queries took {query_time:.3f}s, expected < 0.2s"

        # Verify results
        assert len(circuit_breakers) > 0
        assert len(fuses) > 0

        for cb in circuit_breakers:
            assert cb.component_type_id == 1  # Circuit Breaker type ID

    async def test_repository_pagination_performance(self, component_repository, large_component_dataset):
        """Test pagination performance with large dataset."""
        pagination_params = PaginationParams(page=1, per_page=50)

        start_time = time.time()

        # Test multiple pages
        for page in range(1, 11):  # Test first 10 pages
            pagination_params.page = page
            result = await component_repository.get_components_paginated_with_filters(
                pagination_params=pagination_params
            )
            assert result.total > 0
            assert len(result.items) <= 50

        end_time = time.time()
        pagination_time = end_time - start_time

        # Should complete 10 paginated queries in less than 1 second
        assert pagination_time < 1.0, f"Pagination queries took {pagination_time:.3f}s, expected < 1.0s"

    async def test_repository_filtered_search_performance(self, component_repository, large_component_dataset):
        """Test filtered search performance with large dataset."""
        pagination_params = PaginationParams(page=1, per_page=100)

        start_time = time.time()

        # Test various filter combinations
        test_cases = [
            {"category_id": 1},  # PROTECTION_DEVICES category ID
            {"manufacturer": "Manufacturer 1"},
            {"is_preferred": True},
            {"search_term": "Component"},
            {
                "category_id": 1,  # PROTECTION_DEVICES category ID
                "manufacturer": "Manufacturer 1",
                "is_preferred": True,
            },
        ]

        for filters in test_cases:
            result = await component_repository.get_components_paginated_with_filters(
                pagination_params=pagination_params, **filters
            )
            assert isinstance(result.items, list)

        end_time = time.time()
        filter_time = end_time - start_time

        # Should complete all filtered searches in less than 1 second
        assert filter_time < 1.0, f"Filtered searches took {filter_time:.3f}s, expected < 1.0s"

    async def test_repository_count_operations_performance(self, component_repository, large_component_dataset):
        """Test count operations performance with large dataset."""
        start_time = time.time()

        # Test various count operations
        active_count = await component_repository.count_active_components()
        category_counts = await component_repository.count_components_by_category_id()

        end_time = time.time()
        count_time = end_time - start_time

        # Should complete count operations in less than 0.1 seconds
        assert count_time < 0.1, f"Count operations took {count_time:.3f}s, expected < 0.1s"

        # Verify results
        assert active_count > 0
        assert isinstance(category_counts, dict)
        assert len(category_counts) > 0

    async def test_repository_update_operations_performance(self, component_repository, large_component_dataset):
        """Test update operations performance."""
        # Get a sample of components to update
        sample_components = large_component_dataset[:10]

        start_time = time.time()

        # Test various update operations
        for i, component in enumerate(sample_components):
            if i % 3 == 0:
                await component_repository.update_component_status(component.id, False)
            elif i % 3 == 1:
                await component_repository.update_preferred_status(component.id, True)
            else:
                await component_repository.update(component.id, {"unit_price": Decimal("99.99")})

        end_time = time.time()
        update_time = end_time - start_time

        # Should complete 10 update operations in less than 0.5 seconds
        assert update_time < 0.5, f"Update operations took {update_time:.3f}s, expected < 0.5s"

    async def test_repository_bulk_operations_performance(self, component_repository, async_db_session):
        """Test bulk operations performance."""
        import uuid

        unique_suffix = str(uuid.uuid4())[:8]

        # Create components for bulk testing
        components = []
        for i in range(50):
            component = Component(
                name=f"Bulk Test Component {i} {unique_suffix}",
                manufacturer=f"Bulk Manufacturer {unique_suffix}",
                model_number=f"BULK-{i:03d}-{unique_suffix}",
                component_type_id=2,  # FUSE type ID
                category_id=1,  # PROTECTION_DEVICES category ID
                unit_price=Decimal("15.00"),
            )
            components.append(component)

        start_time = time.time()

        # Bulk insert
        async_db_session.add_all(components)
        await async_db_session.commit()

        end_time = time.time()
        bulk_time = end_time - start_time

        # Should complete bulk insert of 50 components in less than 0.2 seconds
        assert bulk_time < 0.2, f"Bulk insert took {bulk_time:.3f}s, expected < 0.2s"

        # Verify all components were created
        for component in components:
            assert component.id is not None

    async def test_repository_complex_query_performance(self, component_repository, large_component_dataset):
        """Test complex query performance."""
        start_time = time.time()

        # Test complex queries that might be used in real applications

        # 1. Get preferred components by manufacturer
        preferred_abb = await component_repository.get_components_paginated_with_filters(
            pagination_params=PaginationParams(page=1, per_page=20),
            manufacturer="Manufacturer 1",
            is_preferred=True,
        )

        # 2. Search with category filter
        search_results = await component_repository.get_components_paginated_with_filters(
            pagination_params=PaginationParams(page=1, per_page=30),
            search_term="Component",
            category_id=1,  # PROTECTION_DEVICES category ID
        )

        # 3. Get components in price range
        price_range_results = await component_repository.get_components_in_price_range(min_price=20.0, max_price=80.0)

        end_time = time.time()
        complex_query_time = end_time - start_time

        # Should complete complex queries in less than 0.5 seconds
        assert complex_query_time < 0.6, f"Complex queries took {complex_query_time:.3f}s, expected < 0.6s"

        # Verify results
        assert isinstance(preferred_abb.items, list)
        assert isinstance(search_results.items, list)
        assert isinstance(price_range_results, list)

    async def test_memory_usage_during_large_operations(self, component_repository, large_component_dataset):
        """Test that memory usage remains reasonable during large operations."""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Perform memory-intensive operations
        for page in range(1, 21):  # 20 pages of results
            pagination_params = PaginationParams(page=page, per_page=50)
            result = await component_repository.get_components_paginated_with_filters(
                pagination_params=pagination_params
            )
            # Process results to simulate real usage
            for item in result.items:
                _ = item.full_name
                _ = item.display_name

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # Memory increase should be less than 50MB for these operations
        assert memory_increase < 50, f"Memory increased by {memory_increase:.1f}MB, expected < 50MB"

    @pytest.mark.benchmark
    async def test_benchmark_component_operations(self, component_repository, large_component_dataset, benchmark):
        """Benchmark component operations using pytest-benchmark."""

        async def component_operations():
            # Simulate typical component operations
            await component_repository.get_by_type_id(1, limit=10)  # Circuit Breaker type ID
            await component_repository.search_components("Component", limit=10)
            await component_repository.get_preferred_components(limit=5)
            return True

        # Benchmark the operations
        result = await benchmark(component_operations)
        assert result is True
