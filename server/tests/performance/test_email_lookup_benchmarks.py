"""Email Lookup Performance Benchmarks with Large Datasets.

This module provides comprehensive benchmarking for email lookup operations
with progressively larger datasets to establish performance baselines and
identify scaling characteristics for case-insensitive email operations.

Benchmark categories:
1. Linear scaling analysis (1K → 100K users)
2. Index effectiveness measurement
3. Query pattern performance comparison
4. Database optimization validation
5. Memory efficiency analysis during large-scale lookups
"""

import pytest
import time
import uuid
import random
import string
import statistics
from typing import List, Dict, Tuple, Any
from sqlalchemy.orm import Session
from sqlalchemy import (
    text,
    create_engine,
    MetaData,
    Table,
    Column,
    Integer,
    String,
    Boolean,
)
from sqlalchemy import func as sql_func
import psutil
import tracemalloc

from src.core.models.general.user import User
from src.core.repositories.general.user_repository import UserRepository
from src.core.schemas.general.user_schemas import UserCreateSchema
from src.core.services.general.user_service import UserService
from tests.performance.sync_repository_adapter import SyncUserRepositoryAdapter

pytestmark = [pytest.mark.performance]


class EmailLookupBenchmarkSuite:
    """Comprehensive email lookup performance benchmark suite."""

    def __init__(self):
        self.benchmark_results: Dict[str, Dict[str, Any]] = {}
        self.memory_profiles: Dict[str, List[float]] = {}

    def record_benchmark(
        self,
        test_name: str,
        dataset_size: int,
        operation: str,
        times: List[float],
        memory_usage: float = None,
    ):
        """Record benchmark results for analysis."""
        if test_name not in self.benchmark_results:
            self.benchmark_results[test_name] = {}

        self.benchmark_results[test_name][f"{dataset_size}_{operation}"] = {
            "dataset_size": dataset_size,
            "operation": operation,
            "sample_count": len(times),
            "avg_time_ms": statistics.mean(times),
            "median_time_ms": statistics.median(times),
            "min_time_ms": min(times),
            "max_time_ms": max(times),
            "std_dev_ms": statistics.stdev(times) if len(times) > 1 else 0,
            "p95_time_ms": times[int(len(times) * 0.95)] if len(times) > 20 else max(times),
            "p99_time_ms": times[int(len(times) * 0.99)] if len(times) > 100 else max(times),
            "memory_usage_mb": memory_usage,
        }

    def generate_realistic_email_dataset(self, size: int, prefix: str = "benchmark") -> List[str]:
        """Generate realistic email dataset with varied domains and patterns."""
        emails = []
        unique_suffix = str(uuid.uuid4())[:8]

        # Realistic domain distributions
        domains = [
            "gmail.com",
            "yahoo.com",
            "hotmail.com",
            "outlook.com",
            "company.com",
            "enterprise.org",
            "business.net",
            "corp.co.uk",
            "engineering.io",
            "tech.dev",
            "startup.ai",
            "industrial.com",
        ]

        # Realistic name patterns
        name_patterns = [
            "firstname.lastname",
            "firstname_lastname",
            "firstnamelastname",
            "f.lastname",
            "firstname",
            "firstname.middle.lastname",
            "lastname.firstname",
        ]

        for i in range(size):
            # Generate realistic email components
            name_pattern = random.choice(name_patterns)
            domain = random.choice(domains)

            # Create varied email structures
            if name_pattern == "firstname.lastname":
                email = f"{prefix}.user{i:06d}.{unique_suffix}@{domain}"
            elif name_pattern == "firstname_lastname":
                email = f"{prefix}_user{i:06d}_{unique_suffix}@{domain}"
            elif name_pattern == "firstnamelastname":
                email = f"{prefix}user{i:06d}{unique_suffix}@{domain}"
            elif name_pattern == "f.lastname":
                email = f"{prefix[0]}.user{i:06d}.{unique_suffix}@{domain}"
            elif name_pattern == "firstname":
                email = f"{prefix}{i:06d}{unique_suffix}@{domain}"
            elif name_pattern == "firstname.middle.lastname":
                email = f"{prefix}.mid.user{i:06d}.{unique_suffix}@{domain}"
            else:
                email = f"user{i:06d}.{prefix}.{unique_suffix}@{domain}"

            emails.append(email)

        return emails

    def bulk_create_users_optimized(self, db_session: Session, emails: List[str]) -> List[User]:
        """Optimized bulk user creation for benchmark datasets."""
        users = []
        batch_size = 1000

        print(f"🔧 Creating {len(emails)} users in batches of {batch_size}...")

        for i in range(0, len(emails), batch_size):
            batch_emails = emails[i : i + batch_size]
            batch_users = []

            for j, email in enumerate(batch_emails):
                # Generate unique identifier to prevent constraint violations
                import uuid

                unique_id = uuid.uuid4().hex[:8]
                user = User(
                    name=f"Benchmark User {i + j:06d} {unique_id}",
                    email=email,
                    password_hash=f"hash_{i + j:06d}",
                    is_active=True,
                )
                db_session.add(user)
                batch_users.append(user)

            # Commit in batches for better performance
            db_session.commit()
            users.extend(batch_users)

            if (i // batch_size + 1) % 10 == 0:
                print(f"   Created {i + len(batch_emails)} users...")

        print(f"✅ Successfully created {len(users)} users")
        return users


class TestEmailLookupBenchmarks:
    """Email lookup performance benchmark tests."""

    @pytest.fixture(autouse=True)
    def setup_benchmark_suite(self):
        """Set up benchmark suite for each test."""
        self.benchmark_suite = EmailLookupBenchmarkSuite()
        tracemalloc.start()
        self.start_memory = psutil.Process().memory_info().rss
        yield
        tracemalloc.stop()

    @pytest.mark.parametrize("dataset_size", [1000, 5000, 10000, 25000, 50000])
    def test_email_lookup_scaling_analysis(self, db_session: Session, dataset_size: int):
        """Analyze email lookup performance scaling with dataset size.

        This benchmark measures how lookup performance degrades as the dataset
        grows, helping establish scaling characteristics and identify bottlenecks.
        """
        user_repo = SyncUserRepositoryAdapter(db_session)
        unique_suffix = str(uuid.uuid4())[:8]

        print(f"\n📊 Benchmarking email lookups with {dataset_size} users")

        # Generate and create realistic dataset
        start_time = time.time()
        test_emails = self.benchmark_suite.generate_realistic_email_dataset(
            dataset_size, f"scale_{dataset_size}_{unique_suffix}"
        )
        created_users = self.benchmark_suite.bulk_create_users_optimized(db_session, test_emails)
        creation_time = time.time() - start_time

        print(f"⏱️ Dataset creation time: {creation_time:.2f}s ({dataset_size / creation_time:.1f} users/sec)")

        # Memory usage after dataset creation
        current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        memory_increase = current_memory - (self.start_memory / (1024 * 1024))

        # Benchmark different lookup patterns
        sample_size = min(200, dataset_size // 10)
        sample_users = random.sample(created_users, sample_size)

        print(f"🔍 Testing {sample_size} email lookups with various patterns...")

        # 1. Exact email lookup performance
        exact_lookup_times = []
        for user in sample_users:
            start = time.perf_counter()
            found_user = user_repo.get_by_email(user.email)
            end = time.perf_counter()

            lookup_time = (end - start) * 1000
            exact_lookup_times.append(lookup_time)

            assert found_user is not None, "Should find existing user"
            assert found_user.id == user.id, "Should return correct user"

        # 2. Case-insensitive lookup performance
        case_insensitive_times = []
        for user in sample_users:
            # Test with uppercase email
            test_email = user.email.upper()

            start = time.perf_counter()
            found_user = user_repo.get_by_email(test_email)
            end = time.perf_counter()

            lookup_time = (end - start) * 1000
            case_insensitive_times.append(lookup_time)

            assert found_user is not None, "Should find user with case-insensitive lookup"
            assert found_user.id == user.id, "Should return correct user"

        # 3. Non-existing email lookup performance
        non_existing_times = []
        for i in range(sample_size):
            fake_email = f"nonexistent_{i}_{unique_suffix}@fake-domain-{dataset_size}.com"

            start = time.perf_counter()
            found_user = user_repo.get_by_email(fake_email)
            end = time.perf_counter()

            lookup_time = (end - start) * 1000
            non_existing_times.append(lookup_time)

            assert found_user is None, "Should not find non-existing user"

        # 4. Email existence check performance
        existence_check_times = []
        for user in sample_users:
            start = time.perf_counter()
            exists = user_repo.check_email_exists(user.email)
            end = time.perf_counter()

            lookup_time = (end - start) * 1000
            existence_check_times.append(lookup_time)

            assert exists is True, "Should detect existing email"

        # Record benchmark results
        self.benchmark_suite.record_benchmark(
            "email_lookup_scaling",
            dataset_size,
            "exact_lookup",
            exact_lookup_times,
            memory_increase,
        )
        self.benchmark_suite.record_benchmark(
            "email_lookup_scaling",
            dataset_size,
            "case_insensitive",
            case_insensitive_times,
        )
        self.benchmark_suite.record_benchmark("email_lookup_scaling", dataset_size, "non_existing", non_existing_times)
        self.benchmark_suite.record_benchmark(
            "email_lookup_scaling",
            dataset_size,
            "existence_check",
            existence_check_times,
        )

        # Performance analysis and reporting
        exact_avg = statistics.mean(exact_lookup_times)
        case_avg = statistics.mean(case_insensitive_times)
        non_existing_avg = statistics.mean(non_existing_times)
        existence_avg = statistics.mean(existence_check_times)

        print(f"📈 Performance results for {dataset_size} users:")
        print(f"   Exact lookups: {exact_avg:.2f}ms avg, {max(exact_lookup_times):.2f}ms max")
        print(f"   Case-insensitive: {case_avg:.2f}ms avg, {max(case_insensitive_times):.2f}ms max")
        print(f"   Non-existing: {non_existing_avg:.2f}ms avg, {max(non_existing_times):.2f}ms max")
        print(f"   Existence checks: {existence_avg:.2f}ms avg, {max(existence_check_times):.2f}ms max")
        print(f"   Memory increase: {memory_increase:.1f}MB")

        # Performance assertions based on dataset size
        if dataset_size <= 5000:
            max_acceptable_avg = 50.0
        elif dataset_size <= 25000:
            max_acceptable_avg = 100.0
        else:
            max_acceptable_avg = 200.0

        assert exact_avg < max_acceptable_avg, (
            f"Exact lookup avg should be <{max_acceptable_avg}ms for {dataset_size} users, got {exact_avg:.2f}ms"
        )
        assert case_avg < max_acceptable_avg * 1.5, (
            f"Case-insensitive avg should be <{max_acceptable_avg * 1.5}ms for {dataset_size} users, got {case_avg:.2f}ms"
        )

        # Case-insensitive should not be more than 2x slower than exact lookup
        case_overhead = (case_avg / exact_avg) * 100 - 100
        assert case_overhead < 100.0, f"Case-insensitive overhead should be <100%, got {case_overhead:.1f}%"

    def test_index_effectiveness_benchmark(self, db_session: Session):
        """Benchmark the effectiveness of database indexes for email lookups.

        This test measures query performance with and without indexes to
        validate that database optimization is working correctly.
        """
        unique_suffix = str(uuid.uuid4())[:8]
        dataset_size = 10000

        print(f"\n🔍 Benchmarking index effectiveness with {dataset_size} users")

        # Create test dataset
        test_emails = self.benchmark_suite.generate_realistic_email_dataset(dataset_size, f"index_test_{unique_suffix}")
        created_users = self.benchmark_suite.bulk_create_users_optimized(db_session, test_emails)

        # Sample users for testing
        sample_users = random.sample(created_users, 100)

        # Test 1: Query with existing index (normal repository method)
        user_repo = UserRepository(db_session)
        indexed_lookup_times = []

        print("🔍 Testing indexed email lookups...")
        for user in sample_users:
            start = time.perf_counter()
            found_user = user_repo.get_by_email(user.email)
            end = time.perf_counter()

            indexed_lookup_times.append((end - start) * 1000)
            assert found_user is not None, "Should find user with indexed lookup"

        # Test 2: Raw SQL query to simulate non-indexed behavior
        # Note: This simulates what performance would be like without proper indexing
        raw_query_times = []

        print("🔍 Testing raw SQL queries...")
        for user in sample_users:
            start = time.perf_counter()

            # Raw SQL query that may not use indexes optimally
            result = db_session.execute(
                text("SELECT * FROM users WHERE LOWER(email) = LOWER(:email) AND is_active = true"),
                {"email": user.email},
            ).fetchone()

            end = time.perf_counter()
            raw_query_times.append((end - start) * 1000)
            assert result is not None, "Should find user with raw query"

        # Test 3: Bulk lookup performance (simulate heavy usage)
        bulk_lookup_times = []
        bulk_sample = random.sample(created_users, 500)

        print("🔍 Testing bulk email lookups...")
        start = time.perf_counter()

        for user in bulk_sample:
            found_user = user_repo.get_by_email(user.email)
            assert found_user is not None, "Should find user in bulk lookup"

        end = time.perf_counter()
        bulk_total_time = (end - start) * 1000
        bulk_avg_time = bulk_total_time / len(bulk_sample)

        # Performance analysis
        indexed_avg = statistics.mean(indexed_lookup_times)
        raw_avg = statistics.mean(raw_query_times)

        print(f"📊 Index effectiveness analysis:")
        print(f"   Indexed lookups: {indexed_avg:.2f}ms avg")
        print(f"   Raw SQL queries: {raw_avg:.2f}ms avg")
        print(f"   Bulk lookups: {bulk_avg_time:.2f}ms avg")
        print(f"   Index performance benefit: {((raw_avg - indexed_avg) / raw_avg) * 100:.1f}%")

        # Record benchmark results
        self.benchmark_suite.record_benchmark(
            "index_effectiveness", dataset_size, "indexed_lookup", indexed_lookup_times
        )
        self.benchmark_suite.record_benchmark("index_effectiveness", dataset_size, "raw_query", raw_query_times)

        # Performance assertions
        assert indexed_avg < 100.0, f"Indexed lookups should be <100ms, got {indexed_avg:.2f}ms"
        assert bulk_avg_time < 50.0, f"Bulk lookup average should be <50ms, got {bulk_avg_time:.2f}ms"

        # Index should provide significant performance benefit
        performance_improvement = ((raw_avg - indexed_avg) / raw_avg) * 100
        print(f"🚀 Index performance improvement: {performance_improvement:.1f}%")

    def test_query_pattern_performance_comparison(self, db_session: Session):
        """Compare performance of different email query patterns and optimizations.

        This benchmark tests various query approaches to identify the most
        efficient patterns for email lookups at scale.
        """
        unique_suffix = str(uuid.uuid4())[:8]
        dataset_size = 20000

        print(f"\n🔍 Benchmarking query patterns with {dataset_size} users")

        # Create test dataset
        test_emails = self.benchmark_suite.generate_realistic_email_dataset(
            dataset_size, f"query_patterns_{unique_suffix}"
        )
        created_users = self.benchmark_suite.bulk_create_users_optimized(db_session, test_emails)

        # Sample for testing different patterns
        sample_users = random.sample(created_users, 150)

        # Pattern 1: Direct sync ORM query (simulating repository pattern)
        repository_times = []

        print("🔍 Testing repository pattern...")
        for user in sample_users:
            start = time.perf_counter()
            # Direct sync query to avoid session isolation issues
            found_user = (
                db_session.query(User)
                .filter(sql_func.lower(User.email) == sql_func.lower(user.email), User.is_active == True)
                .first()
            )
            end = time.perf_counter()

            repository_times.append((end - start) * 1000)
            assert found_user is not None, "Repository lookup should succeed"

        # Pattern 2: Direct ORM query
        orm_query_times = []

        print("🔍 Testing direct ORM queries...")
        for user in sample_users:
            start = time.perf_counter()

            from sqlalchemy import func, and_

            found_user = (
                db_session.query(User)
                .filter(
                    and_(
                        func.lower(User.email) == user.email.lower(),
                        User.is_active == True,
                    )
                )
                .first()
            )

            end = time.perf_counter()
            orm_query_times.append((end - start) * 1000)
            assert found_user is not None, "ORM query should succeed"

        # Pattern 3: Raw SQL with parameterized queries
        raw_sql_times = []

        print("🔍 Testing raw SQL queries...")
        for user in sample_users:
            start = time.perf_counter()

            result = db_session.execute(
                text("""
                    SELECT id, name, email, is_active
                    FROM users
                    WHERE LOWER(email) = LOWER(:email)
                    AND is_active = true
                    LIMIT 1
                """),
                {"email": user.email},
            ).fetchone()

            end = time.perf_counter()
            raw_sql_times.append((end - start) * 1000)
            assert result is not None, "Raw SQL query should succeed"

        # Pattern 4: Bulk existence checks
        bulk_emails = [user.email for user in sample_users]

        print("🔍 Testing bulk existence checks...")
        start = time.perf_counter()

        # Simulate bulk checking (direct sync query)
        bulk_results = []
        for email in bulk_emails:
            count = db_session.query(User).filter(sql_func.lower(User.email) == sql_func.lower(email)).count()
            exists = count > 0
            bulk_results.append(exists)

        end = time.perf_counter()
        bulk_existence_total = (end - start) * 1000
        bulk_existence_avg = bulk_existence_total / len(bulk_emails)

        assert all(bulk_results), "All bulk existence checks should return True"

        # Pattern 5: Case variation performance
        case_variation_times = []

        print("🔍 Testing case variation handling...")
        for user in sample_users:
            test_cases = [
                user.email.upper(),
                user.email.lower(),
                user.email.capitalize(),
                user.email.swapcase(),
            ]

            case_times = []
            for test_email in test_cases:
                start = time.perf_counter()
                found_user = (
                    db_session.query(User)
                    .filter(sql_func.lower(User.email) == sql_func.lower(test_email), User.is_active == True)
                    .first()
                )
                end = time.perf_counter()

                case_times.append((end - start) * 1000)
                assert found_user is not None, f"Should find user with email: {test_email}"

            case_variation_times.extend(case_times)

        # Performance analysis
        repository_avg = statistics.mean(repository_times)
        orm_avg = statistics.mean(orm_query_times)
        raw_sql_avg = statistics.mean(raw_sql_times)
        case_variation_avg = statistics.mean(case_variation_times)

        print(f"📊 Query pattern performance comparison:")
        print(f"   Repository pattern: {repository_avg:.2f}ms avg")
        print(f"   Direct ORM queries: {orm_avg:.2f}ms avg")
        print(f"   Raw SQL queries: {raw_sql_avg:.2f}ms avg")
        print(f"   Bulk existence checks: {bulk_existence_avg:.2f}ms avg")
        print(f"   Case variations: {case_variation_avg:.2f}ms avg")

        # Record all benchmark results
        patterns = {
            "repository": repository_times,
            "orm_direct": orm_query_times,
            "raw_sql": raw_sql_times,
            "case_variations": case_variation_times,
        }

        for pattern_name, times in patterns.items():
            self.benchmark_suite.record_benchmark("query_patterns", dataset_size, pattern_name, times)

        # Performance assertions
        assert repository_avg < 100.0, f"Repository pattern should be <100ms, got {repository_avg:.2f}ms"
        assert orm_avg < 120.0, f"ORM queries should be <120ms, got {orm_avg:.2f}ms"
        assert raw_sql_avg < 80.0, f"Raw SQL should be <80ms, got {raw_sql_avg:.2f}ms"
        assert case_variation_avg < 150.0, f"Case variations should be <150ms, got {case_variation_avg:.2f}ms"
        assert bulk_existence_avg < 50.0, f"Bulk existence checks should be <50ms avg, got {bulk_existence_avg:.2f}ms"

        # Repository pattern should be reasonably competitive
        repository_overhead = ((repository_avg - raw_sql_avg) / raw_sql_avg) * 100
        assert repository_overhead < 200.0, f"Repository overhead should be <200%, got {repository_overhead:.1f}%"

    def test_memory_efficiency_during_large_lookups(self, db_session: Session):
        """Benchmark memory efficiency during large-scale email lookup operations.

        This test ensures that email lookup operations don't cause memory
        leaks or excessive memory usage, even with large datasets.
        """
        unique_suffix = str(uuid.uuid4())[:8]
        dataset_size = 30000

        print(f"\n💾 Testing memory efficiency with {dataset_size} users")

        # Monitor initial memory
        initial_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        print(f"💾 Initial memory: {initial_memory:.1f}MB")

        # Create large dataset
        test_emails = self.benchmark_suite.generate_realistic_email_dataset(
            dataset_size, f"memory_test_{unique_suffix}"
        )
        created_users = self.benchmark_suite.bulk_create_users_optimized(db_session, test_emails)

        after_creation_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        creation_memory_delta = after_creation_memory - initial_memory
        print(f"💾 Memory after dataset creation: {after_creation_memory:.1f}MB (+{creation_memory_delta:.1f}MB)")

        # Perform intensive lookup operations
        user_repo = SyncUserRepositoryAdapter(db_session)
        memory_samples = []
        lookup_times = []

        print("🔍 Performing intensive lookup operations...")

        # Test with different lookup intensities
        lookup_rounds = [
            ("light", 500, "Light lookup load"),
            ("medium", 1500, "Medium lookup load"),
            ("heavy", 3000, "Heavy lookup load"),
        ]

        for round_name, num_lookups, description in lookup_rounds:
            print(f"   {description} ({num_lookups} lookups)...")

            round_start_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            round_lookup_times = []

            sample_users = random.sample(created_users, num_lookups)

            for i, user in enumerate(sample_users):
                start = time.perf_counter()

                # Mix of lookup types
                if i % 4 == 0:
                    found_user = user_repo.get_by_email(user.email)
                elif i % 4 == 1:
                    found_user = user_repo.get_by_email(user.email.upper())
                elif i % 4 == 2:
                    exists = user_repo.check_email_exists(user.email)
                    found_user = exists  # Just to keep consistent return type checking
                else:
                    found_user = user_repo.get_by_email(user.email.lower())

                end = time.perf_counter()
                lookup_time = (end - start) * 1000
                round_lookup_times.append(lookup_time)

                # Sample memory every 100 operations
                if i % 100 == 0:
                    current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
                    memory_samples.append(current_memory)

            round_end_memory = psutil.Process().memory_info().rss / (1024 * 1024)
            round_memory_delta = round_end_memory - round_start_memory
            round_avg_time = statistics.mean(round_lookup_times)

            print(f"      Avg lookup time: {round_avg_time:.2f}ms")
            print(f"      Memory change: {round_memory_delta:+.1f}MB")

            lookup_times.extend(round_lookup_times)

            # Record benchmark for this round
            self.benchmark_suite.record_benchmark(
                "memory_efficiency",
                dataset_size,
                round_name,
                round_lookup_times,
                round_memory_delta,
            )

        # Final memory analysis
        final_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        total_memory_increase = final_memory - after_creation_memory

        # Force garbage collection and measure impact
        import gc

        gc.collect()

        after_gc_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        gc_memory_reduction = final_memory - after_gc_memory

        print(f"💾 Memory efficiency analysis:")
        print(f"   Total lookups performed: {len(lookup_times)}")
        print(f"   Memory before lookups: {after_creation_memory:.1f}MB")
        print(f"   Memory after lookups: {final_memory:.1f}MB")
        print(f"   Memory increase from lookups: {total_memory_increase:+.1f}MB")
        print(f"   Memory after GC: {after_gc_memory:.1f}MB")
        print(f"   GC memory reduction: {gc_memory_reduction:.1f}MB")

        # Performance analysis
        overall_avg_time = statistics.mean(lookup_times)
        max_memory = max(memory_samples) if memory_samples else final_memory
        peak_memory_increase = max_memory - after_creation_memory

        print(f"   Overall avg lookup time: {overall_avg_time:.2f}ms")
        print(f"   Peak memory increase: {peak_memory_increase:.1f}MB")

        # Memory efficiency assertions
        assert total_memory_increase < 100.0, (
            f"Lookup operations should not increase memory by >100MB, got {total_memory_increase:.1f}MB"
        )
        assert peak_memory_increase < 150.0, f"Peak memory increase should be <150MB, got {peak_memory_increase:.1f}MB"
        assert gc_memory_reduction >= 0, f"Garbage collection should reduce memory, got {gc_memory_reduction:.1f}MB"

        # Performance assertions
        assert overall_avg_time < 100.0, f"Overall avg lookup time should be <100ms, got {overall_avg_time:.2f}ms"

        # Memory should not grow significantly per lookup
        memory_per_lookup = total_memory_increase / len(lookup_times) if lookup_times else 0
        assert memory_per_lookup < 0.01, f"Memory per lookup should be <0.01MB, got {memory_per_lookup:.4f}MB"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-s"])
