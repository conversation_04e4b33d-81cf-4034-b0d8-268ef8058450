"""Locust Load Tests for Validation Logic Under High Concurrency.

This module implements comprehensive load testing for validation-heavy endpoints
using Locust to simulate realistic concurrent user scenarios and measure
system performance under stress.

Load test scenarios:
1. User registration validation load
2. Project creation validation load
3. Mixed validation operations under concurrent load
4. Sustained load testing for validation pipeline stability
"""

import json
import random
import string
import time
import uuid
import pytest
from typing import Dict, List, Optional

from locust import HttpUser, task, between, events
from locust.env import Environment

# Load test configuration
BASE_URL = "http://localhost:8000"  # Adjust for your environment
API_PREFIX = "/api/v1"

pytestmark = [pytest.mark.performance]


class ValidationLoadTestUser(HttpUser):
    """Locust user class for validation endpoint load testing."""

    wait_time = between(1, 3)  # Wait 1-3 seconds between requests
    host = BASE_URL

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.auth_token: Optional[str] = None
        self.user_id: Optional[int] = None
        self.project_ids: List[int] = []
        self.test_session_id = str(uuid.uuid4())[:8]

    def on_start(self):
        """Called when a user starts - perform authentication."""
        self.authenticate_user()

    def authenticate_user(self):
        """Authenticate user and store auth token for subsequent requests."""
        # Create a test user for this load test session
        unique_id = f"{self.test_session_id}_{random.randint(1000, 9999)}"
        user_data = {
            "name": f"Load Test User {unique_id}",
            "email": f"loadtest_{unique_id}@testdomain.com",
            "password": "LoadTest123!",
        }

        # Register user
        with self.client.post(
            f"{API_PREFIX}/auth/register",
            json=user_data,
            catch_response=True,
            name="auth_register",
        ) as response:
            if response.status_code == 201:
                response.success()
                user_info = response.json()
                self.user_id = user_info.get("data", {}).get("id")
            elif response.status_code == 400 and "already registered" in response.text:
                # User already exists, try to login instead
                response.success()
                self.login_existing_user(user_data["email"], user_data["password"])
                return
            else:
                response.failure(f"Registration failed: {response.text}")
                return

        # Login to get auth token
        login_data = {"email": user_data["email"], "password": user_data["password"]}

        with self.client.post(
            f"{API_PREFIX}/auth/login",
            json=login_data,
            catch_response=True,
            name="auth_login",
        ) as response:
            if response.status_code == 200:
                response.success()
                auth_info = response.json()
                self.auth_token = auth_info.get("access_token")
                if not self.auth_token:
                    response.failure("No access token in login response")
            else:
                response.failure(f"Login failed: {response.text}")

    def login_existing_user(self, email: str, password: str):
        """Login with existing user credentials."""
        login_data = {"email": email, "password": password}

        with self.client.post(
            f"{API_PREFIX}/auth/login",
            json=login_data,
            catch_response=True,
            name="auth_login_existing",
        ) as response:
            if response.status_code == 200:
                response.success()
                auth_info = response.json()
                self.auth_token = auth_info.get("access_token")
                # Get user ID from response
                user_data = auth_info.get("user", {})
                self.user_id = user_data.get("id")
            else:
                response.failure(f"Existing user login failed: {response.text}")

    def get_auth_headers(self) -> Dict[str, str]:
        """Get authorization headers for authenticated requests."""
        if not self.auth_token:
            return {}
        return {"Authorization": f"Bearer {self.auth_token}"}

    @task(3)
    def test_user_registration_validation_load(self):
        """Test user registration endpoint under load with various validation scenarios."""
        scenarios = [
            # Valid registration
            {
                "name": f"Valid User {uuid.uuid4().hex[:8]}",
                "email": f"valid_{uuid.uuid4().hex[:8]}@testload.com",
                "password": "ValidPass123!",
                "expected_status": 201,
                "test_name": "user_registration_valid",
            },
            # Email validation failures
            {
                "name": "Invalid Email User",
                "email": "invalid-email-format",
                "password": "ValidPass123!",
                "expected_status": 422,
                "test_name": "user_registration_invalid_email",
            },
            # Duplicate email validation
            {
                "name": "Duplicate Email User",
                "email": f"loadtest_{self.test_session_id}<EMAIL>",  # Likely duplicate
                "password": "ValidPass123!",
                "expected_status": 400,
                "test_name": "user_registration_duplicate_email",
            },
            # Name validation failures
            {
                "name": "AB",  # Too short
                "email": f"shortname_{uuid.uuid4().hex[:8]}@testload.com",
                "password": "ValidPass123!",
                "expected_status": 422,
                "test_name": "user_registration_short_name",
            },
            # Password validation failures
            {
                "name": f"Weak Password User {uuid.uuid4().hex[:8]}",
                "email": f"weakpass_{uuid.uuid4().hex[:8]}@testload.com",
                "password": "123",  # Too weak
                "expected_status": 422,
                "test_name": "user_registration_weak_password",
            },
        ]

        scenario = random.choice(scenarios)

        with self.client.post(
            f"{API_PREFIX}/auth/register",
            json={
                "name": scenario["name"],
                "email": scenario["email"],
                "password": scenario["password"],
            },
            catch_response=True,
            name=scenario["test_name"],
        ) as response:
            if response.status_code == scenario["expected_status"]:
                response.success()
            else:
                response.failure(
                    f"Expected status {scenario['expected_status']}, got {response.status_code}: {response.text}"
                )

    @task(2)
    def test_project_creation_validation_load(self):
        """Test project creation endpoint under load with various validation scenarios."""
        if not self.auth_token:
            return

        scenarios = [
            # Valid project creation
            {
                "name": f"Load Test Project {uuid.uuid4().hex[:8]}",
                "description": "A test project created during load testing",
                "status": "DRAFT",
                "expected_status": 201,
                "test_name": "project_creation_valid",
            },
            # Name validation failures
            {
                "name": "AB",  # Too short
                "description": "Project with short name",
                "status": "DRAFT",
                "expected_status": 422,
                "test_name": "project_creation_short_name",
            },
            # Long description validation
            {
                "name": f"Long Desc Project {uuid.uuid4().hex[:8]}",
                "description": "A" * 2001,  # Exceeds 2000 char limit
                "status": "DRAFT",
                "expected_status": 422,
                "test_name": "project_creation_long_description",
            },
            # Duplicate name validation
            {
                "name": f"Load Test Project {self.test_session_id}_duplicate",
                "description": "Testing duplicate name validation",
                "status": "DRAFT",
                "expected_status": [201, 400],  # First time 201, subsequent 400
                "test_name": "project_creation_duplicate_name",
            },
        ]

        scenario = random.choice(scenarios)

        with self.client.post(
            f"{API_PREFIX}/projects",
            json={
                "name": scenario["name"],
                "description": scenario["description"],
                "status": scenario["status"],
            },
            headers=self.get_auth_headers(),
            catch_response=True,
            name=scenario["test_name"],
        ) as response:
            expected_status = scenario["expected_status"]
            if isinstance(expected_status, list):
                success = response.status_code in expected_status
            else:
                success = response.status_code == expected_status

            if success:
                response.success()
                if response.status_code == 201:
                    # Store project ID for cleanup
                    project_data = response.json()
                    project_id = project_data.get("data", {}).get("id")
                    if project_id:
                        self.project_ids.append(project_id)
            else:
                response.failure(f"Expected status {expected_status}, got {response.status_code}: {response.text}")

    @task(1)
    def test_email_uniqueness_validation_load(self):
        """Test email uniqueness checking under load."""
        if not self.auth_token:
            return

        # Test scenarios for email uniqueness validation
        test_emails = [
            f"loadtest_{self.test_session_id}<EMAIL>",  # Known existing
            f"nonexistent_{uuid.uuid4().hex[:8]}@testload.com",  # Known non-existing
            f"LOADTEST_{self.test_session_id}<EMAIL>",  # Case variation of existing
            f"loadtest_{uuid.uuid4().hex[:8]}@testload.com",  # Random new email
        ]

        test_email = random.choice(test_emails)

        # Simulate user trying to register with potentially existing email
        with self.client.post(
            f"{API_PREFIX}/auth/register",
            json={
                "name": f"Uniqueness Test User {uuid.uuid4().hex[:8]}",
                "email": test_email,
                "password": "TestPass123!",
            },
            catch_response=True,
            name="email_uniqueness_validation",
        ) as response:
            if response.status_code in [201, 400, 422]:  # All are valid responses
                response.success()
            else:
                response.failure(f"Unexpected status {response.status_code}: {response.text}")

    @task(1)
    def test_user_profile_update_validation_load(self):
        """Test user profile update validation under load."""
        if not self.auth_token or not self.user_id:
            return

        scenarios = [
            # Valid update
            {
                "name": f"Updated User {uuid.uuid4().hex[:8]}",
                "expected_status": 200,
                "test_name": "profile_update_valid",
            },
            # Name too short
            {
                "name": "AB",
                "expected_status": 422,
                "test_name": "profile_update_short_name",
            },
            # Name too long
            {
                "name": "A" * 51,
                "expected_status": 422,
                "test_name": "profile_update_long_name",
            },
        ]

        scenario = random.choice(scenarios)

        with self.client.put(
            f"{API_PREFIX}/users/{self.user_id}",
            json={"name": scenario["name"]},
            headers=self.get_auth_headers(),
            catch_response=True,
            name=scenario["test_name"],
        ) as response:
            if response.status_code == scenario["expected_status"]:
                response.success()
            else:
                response.failure(
                    f"Expected status {scenario['expected_status']}, got {response.status_code}: {response.text}"
                )

    @task(1)
    def test_bulk_validation_operations(self):
        """Test multiple validation operations in sequence to stress the validation pipeline."""
        if not self.auth_token:
            return

        operations_count = random.randint(3, 7)
        success_count = 0
        start_time = time.time()

        for i in range(operations_count):
            # Alternate between different validation-heavy operations
            if i % 3 == 0:
                self.test_user_registration_validation_load()
            elif i % 3 == 1:
                self.test_project_creation_validation_load()
            else:
                self.test_email_uniqueness_validation_load()

            success_count += 1
            # Brief pause between operations
            time.sleep(0.1)

        total_time = time.time() - start_time

        # Log the bulk operation performance
        with self.client.get(
            "/health",  # Use health endpoint as a dummy for logging
            catch_response=True,
            name="bulk_validation_operations",
        ) as response:
            if success_count == operations_count:
                response.success()
            else:
                response.failure(f"Only {success_count}/{operations_count} operations succeeded")


class HighConcurrencyValidationUser(ValidationLoadTestUser):
    """Specialized user class for high-concurrency validation testing."""

    wait_time = between(0.1, 0.5)  # Much shorter wait times for high concurrency

    @task(5)
    def rapid_fire_email_validation(self):
        """Rapid-fire email validation requests to stress the system."""
        if not self.auth_token:
            return

        # Generate multiple email variants quickly
        base_email = f"rapidfire_{uuid.uuid4().hex[:8]}"
        email_variants = [
            f"{base_email}@test.com",
            f"{base_email.upper()}@TEST.COM",
            f"{base_email.capitalize()}@Test.Com",
            f"  {base_email}@test.com  ",  # With whitespace
        ]

        for email in email_variants:
            with self.client.post(
                f"{API_PREFIX}/auth/register",
                json={
                    "name": f"Rapid User {uuid.uuid4().hex[:6]}",
                    "email": email,
                    "password": "RapidTest123!",
                },
                catch_response=True,
                name="rapid_email_validation",
            ) as response:
                if response.status_code in [201, 400, 422]:
                    response.success()
                else:
                    response.failure(f"Unexpected status: {response.status_code}")

            # Minimal delay between requests
            time.sleep(0.05)


# Locust event handlers for performance monitoring
@events.request.add_listener
def on_request(request_type, name, response_time, response_length, exception, context, **kwargs):
    """Log slow validation requests for analysis."""
    if exception:
        return

    # Log slow validation operations (>2 seconds)
    if response_time > 2000 and "validation" in name:
        print(f"SLOW VALIDATION: {name} took {response_time}ms")


@events.test_start.add_listener
def on_test_start(environment: Environment, **kwargs):
    """Initialize test environment and log test configuration."""
    print("🚀 Starting Validation Load Testing")
    print(f"📊 Target URL: {BASE_URL}")
    print(f"👥 Users will be spawned with validation-focused scenarios")
    print(f"🔧 Test scenarios include:")
    print(f"   - User registration validation under load")
    print(f"   - Project creation validation stress testing")
    print(f"   - Email uniqueness validation concurrency")
    print(f"   - Bulk validation operation testing")


@events.test_stop.add_listener
def on_test_stop(environment: Environment, **kwargs):
    """Clean up after test completion and provide summary."""
    print("🏁 Validation Load Testing Completed")
    print("📈 Check Locust web UI for detailed performance metrics")
    print("🔍 Review server logs for validation-specific performance data")


# Custom load testing scenarios for different validation stress levels
class LightValidationLoad(ValidationLoadTestUser):
    """Light validation load for baseline performance testing."""

    wait_time = between(3, 8)
    weight = 3


class MediumValidationLoad(ValidationLoadTestUser):
    """Medium validation load for realistic usage simulation."""

    wait_time = between(1, 3)
    weight = 2


class HeavyValidationLoad(HighConcurrencyValidationUser):
    """Heavy validation load for stress testing."""

    wait_time = between(0.1, 0.5)
    weight = 1


if __name__ == "__main__":
    # Example of running the load test programmatically
    print("🔧 To run these load tests, use the following commands:")
    print()
    print("1. Basic validation load test:")
    print(f"   locust -f {__file__} --host {BASE_URL}")
    print()
    print("2. High concurrency validation test:")
    print(f"   locust -f {__file__} HighConcurrencyValidationUser --host {BASE_URL}")
    print()
    print("3. Mixed load test with different user types:")
    print(f"   locust -f {__file__} LightValidationLoad MediumValidationLoad HeavyValidationLoad --host {BASE_URL}")
    print()
    print("4. Quick stress test (10 users, 30 seconds):")
    print(f"   locust -f {__file__} --headless -u 10 -r 2 -t 30s --host {BASE_URL}")
    print()
    print("💡 Tip: Monitor server CPU, memory, and database connections during testing")
    print("📊 Access the web UI at http://localhost:8089 after starting Locust")
