"""Unit tests for advanced validation system.

This module contains comprehensive tests for the advanced validation features
including electrical parameter validation, unit conversion, and cross-entity validation.
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, List, Any

from src.core.validation.advanced_validators import (
    AdvancedElectricalValidator,
    EnhancedTemperatureValidator,
    ElectricalUnitConverter,
    ValidationResult,
)
from src.core.validation.cross_entity_validator import (
    CrossEntityDependencyValidator,
    DependencyValidationResult,
    ValidationSeverity,
)


class TestElectricalUnitConverter:
    """Tests for electrical unit converter."""

    def test_basic_conversion(self):
        """Test basic unit conversions."""
        converter = ElectricalUnitConverter()

        # Voltage conversions
        assert converter.convert(1.0, "V", "kV") == 0.001
        assert converter.convert(1000, "V", "kV") == 1.0
        assert converter.convert(1, "kV", "V") == 1000.0

        # Current conversions
        assert converter.convert(1.0, "A", "mA") == 1000.0
        assert converter.convert(1000, "mA", "A") == 1.0

        # Power conversions
        assert converter.convert(1.0, "W", "kW") == 0.001
        assert converter.convert(1000, "W", "kW") == 1.0

    def test_precision_conversion(self):
        """Test high-precision conversions."""
        converter = ElectricalUnitConverter()

        # Test precise decimal calculations
        result = converter.convert(123.456, "V", "kV")
        assert abs(result - 0.123456) < 1e-6

    def test_invalid_unit_handling(self):
        """Test handling of invalid units."""
        converter = ElectricalUnitConverter()

        with pytest.raises(ValueError):
            converter.convert(1.0, "INVALID", "V")


class TestAdvancedElectricalValidator:
    """Tests for advanced electrical parameter validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = AdvancedElectricalValidator()

    def test_valid_electrical_parameters(self):
        """Test validation with valid electrical parameters."""
        params = {
            "voltage": 480,
            "current": 100,
            "power": 43200,  # 480 * 100 * 0.9 = 43200
            "power_factor": 0.9,
        }

        result = self.validator.validate_electrical_parameters(params, "industrial", "north_america")

        assert result.is_valid == True
        assert len(result.errors) == 0
        assert "voltage_volts" in result.normalized_values
        assert "current_amps" in result.normalized_values
        assert "power_watts" in result.normalized_values

    def test_voltage_unit_conversion(self):
        """Test voltage validation with unit conversion."""
        params = {
            "voltage": (34.5, "kV"),
            "current": 100,
            "power": 3277500,  # 34500 * 100 * 0.95 = 3277500
            "power_factor": 0.95,
        }

        result = self.validator.validate_electrical_parameters(params, "industrial", "north_america")

        assert result.is_valid == True
        assert result.normalized_values["voltage_volts"] == 34500.0

    def test_invalid_power_factor(self):
        """Test validation with invalid power factor."""
        params = {
            "voltage": 480,
            "current": 100,
            "power": 48000,
            "power_factor": 1.5,  # Invalid, > 1
        }

        result = self.validator.validate_electrical_parameters(params, "industrial", "north_america")

        assert result.is_valid == False
        assert any("power_factor" in error.lower() for error in result.errors)

    def test_electrical_consistency_check(self):
        """Test electrical parameter consistency validation."""
        params = {
            "voltage": 480,
            "current": 100,
            "power": 43200,  # Should be 480 * 100 * 0.9 = 43200
            "power_factor": 0.9,
        }

        result = self.validator.validate_electrical_parameters(params, "industrial", "north_america")

        assert result.is_valid == True

    def test_inconsistent_parameters(self):
        """Test detection of inconsistent electrical parameters."""
        params = {
            "voltage": 480,
            "current": 100,
            "power": 60000,  # Inconsistent with P = V * I * PF
            "power_factor": 0.9,
        }

        result = self.validator.validate_electrical_parameters(params, "industrial", "north_america")

        assert result.is_valid == False
        assert any("inconsistent" in error.lower() for error in result.errors)

    def test_application_specific_validation(self):
        """Test application-specific validation rules."""
        params = {
            "voltage": 120,
            "current": 25,
            "power": 1800,
            "power_factor": 0.6,  # Low for residential
        }

        result = self.validator.validate_electrical_parameters(params, "residential", "north_america")

        assert any("power factor" in warning.lower() for warning in result.warnings)


class TestEnhancedTemperatureValidator:
    """Tests for enhanced temperature validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = EnhancedTemperatureValidator()

    def test_valid_temperature_range(self):
        """Test validation with valid temperature range."""
        result = self.validator.validate_temperature_range(-10, 40, "industrial", "ambient")

        assert result.is_valid == True
        assert len(result.errors) == 0
        assert result.normalized_values["min_temp"] == -10
        assert result.normalized_values["max_temp"] == 40

    def test_invalid_temperature_range(self):
        """Test validation with invalid temperature range."""
        result = self.validator.validate_temperature_range(50, 40, "industrial", "ambient")

        assert result.is_valid == False
        assert any("must be less than" in error.lower() for error in result.errors)

    def test_extreme_temperature_warnings(self):
        """Test warnings for extreme temperatures."""
        result = self.validator.validate_temperature_range(-60, 70, "industrial", "ambient")

        assert len(result.warnings) > 0
        # Check for warnings about safety margins since these are extreme temperatures
        assert any("safety margin" in warning.lower() for warning in result.warnings)

    def test_application_specific_safety_margins(self):
        """Test application-specific safety margins."""
        result = self.validator.validate_temperature_range(35, 55, "hazardous", "ambient")

        assert len(result.warnings) > 0
        assert any("safety margin" in warning.lower() for warning in result.warnings)


class TestCrossEntityDependencyValidator:
    """Tests for cross-entity dependency validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = CrossEntityDependencyValidator()

    @pytest.mark.asyncio
    async def test_project_component_voltage_compatibility(self):
        """Test project-component voltage compatibility."""
        project_data = {
            "id": "project-1",
            "system_voltage": 480,
            "environmental_conditions": {"ip_rating": "IP54"},
        }

        components = [
            {
                "id": "comp-1",
                "voltage_rating": 480,
                "rated_current": 100,
                "environmental_rating": {"ip_rating": "IP54"},
            },
            {
                "id": "comp-2",
                "voltage_rating": 240,  # Incompatible
                "rated_current": 50,
                "environmental_rating": {"ip_rating": "IP54"},
            },
        ]

        results = await self.validator.validate_project_components(project_data, components)

        # Should have at least one voltage compatibility error
        voltage_errors = [r for r in results if "voltage" in r.message.lower()]
        assert len(voltage_errors) > 0

    @pytest.mark.asyncio
    async def test_component_compatibility_pairs(self):
        """Test component compatibility checking."""
        components = [
            {"id": "comp-1", "voltage_rating": 480, "current_rating": 100},
            {
                "id": "comp-2",
                "voltage_rating": 240,  # Mismatch
                "current_rating": 50,
            },
        ]

        results = await self.validator.validate_component_compatibility(components)

        # Should detect voltage mismatch
        assert len(results) > 0

    @pytest.mark.asyncio
    async def test_dependency_impact_analysis(self):
        """Test dependency impact analysis."""
        changed_entity = {"type": "component", "id": "comp-1", "voltage_rating": 480}

        affected_entities = [{"type": "component", "id": "comp-2", "voltage_rating": 480}]

        results = await self.validator.validate_dependency_impact(changed_entity, affected_entities)

        assert isinstance(results, list)

    @pytest.mark.asyncio
    async def test_environmental_rating_validation(self):
        """Test environmental rating validation."""
        project_data = {
            "id": "project-1",
            "system_voltage": 480,
            "environmental_conditions": {"ip_rating": "IP65"},
        }

        components = [
            {
                "id": "comp-1",
                "voltage_rating": 480,
                "environmental_rating": {"ip_rating": "IP54"},  # Insufficient
            }
        ]

        results = await self.validator.validate_project_components(project_data, components)

        # Should have environmental rating error
        env_errors = [r for r in results if "ip rating" in r.message.lower()]
        assert len(env_errors) > 0

    @pytest.mark.asyncio
    async def test_standards_compliance(self):
        """Test standards compliance validation."""
        project_data = {
            "id": "project-1",
            "system_voltage": 480,
            "applicable_standards": ["IEEE-1584", "NEC"],
        }

        components = [
            {
                "id": "comp-1",
                "voltage_rating": 480,
                "compliance_standards": ["IEEE-1584"],  # Missing NEC
            }
        ]

        results = await self.validator.validate_project_components(project_data, components)

        # Should have standards compliance error
        std_errors = [r for r in results if "standard" in r.message.lower()]
        assert len(std_errors) > 0


class TestValidationIntegration:
    """Integration tests for validation system."""

    @pytest.mark.asyncio
    async def test_full_project_validation_workflow(self):
        """Test complete project validation workflow."""
        # Initialize validators
        electrical_validator = AdvancedElectricalValidator()
        cross_validator = CrossEntityDependencyValidator()

        # Project data
        project_data = {
            "id": "project-1",
            "system_voltage": 480,
            "total_load_current": 200,
            "environmental_conditions": {"ip_rating": "IP54"},
            "applicable_standards": ["IEEE-1584", "NEC"],
        }

        # Components
        components = [
            {
                "id": "comp-1",
                "voltage_rating": 480,
                "rated_current": 250,
                "environmental_rating": {"ip_rating": "IP54"},
                "compliance_standards": ["IEEE-1584", "NEC"],
            },
            {
                "id": "comp-2",
                "voltage_rating": 480,
                "rated_current": 100,
                "environmental_rating": {"ip_rating": "IP54"},
                "compliance_standards": ["IEEE-1584", "NEC"],
            },
        ]

        # Validate electrical parameters for each component
        for component in components:
            electrical_result = electrical_validator.validate_electrical_parameters(
                component, "industrial", "north_america"
            )
            assert electrical_result.is_valid

        # Validate project-component compatibility
        cross_results = await cross_validator.validate_project_components(project_data, components)

        # Check for critical errors (excluding circular dependency which might be expected)
        errors = [r for r in cross_results if r.severity == ValidationSeverity.ERROR]
        critical_errors = [e for e in errors if "circular dependency" not in e.message.lower()]

        # Should have no critical errors (circular dependency detection is working as intended)
        assert len(critical_errors) == 0, f"Critical errors found: {critical_errors}"

        # If there are circular dependency errors, they should be properly formatted
        circular_errors = [e for e in errors if "circular dependency" in e.message.lower()]
        for error in circular_errors:
            assert "comp-" in error.message  # Should reference component IDs
            assert len(error.affected_entities) >= 2  # Should list affected components


class TestValidationCache:
    """Tests for validation caching."""

    def test_cache_operations(self):
        """Test cache operations."""
        validator = CrossEntityDependencyValidator()

        # Initial cache stats
        stats = validator.get_cache_stats()
        assert "cache_size" in stats
        assert "graph_nodes" in stats

        # Clear cache
        validator.clear_cache()
        stats_after = validator.get_cache_stats()
        assert stats_after["cache_size"] == 0


@pytest.mark.asyncio
async def test_concurrent_validation():
    """Test concurrent validation operations."""
    validator = CrossEntityDependencyValidator()

    project_data = {
        "id": "project-1",
        "system_voltage": 480,
        "environmental_conditions": {"ip_rating": "IP54"},
    }

    components = [
        {
            "id": f"comp-{i}",
            "voltage_rating": 480,
            "environmental_rating": {"ip_rating": "IP54"},
        }
        for i in range(5)
    ]

    # Run multiple validations concurrently
    tasks = [validator.validate_project_components(project_data, components) for _ in range(10)]

    results = await asyncio.gather(*tasks)

    # All should complete successfully
    assert len(results) == 10
    for result in results:
        assert isinstance(result, list)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
