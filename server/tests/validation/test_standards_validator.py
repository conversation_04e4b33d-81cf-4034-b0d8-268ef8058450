"""Unit tests for standards compliance validation system.

This module contains comprehensive tests for the dynamic standards compliance
validation including IEEE, IEC, NEC, EN, and regional variations.
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, List, Any

from src.core.validation.standards_validator import (
    StandardsValidator,
    StandardType,
    ComplianceLevel,
    RequirementType,
)


class TestStandardsValidator:
    """Tests for standards compliance validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = StandardsValidator()

        # Test component data
        self.test_component = {
            "id": "test-breaker-1",
            "voltage_rating": 480,
            "rated_current": 100,
            "frequency_rating": 60,
            "ip_rating": "IP65",
            "operating_temperature": 40,
            "ground_resistance": 5,
            "temperature_rise": 30,
            "compliance_standards": ["IEEE", "IEC", "NEC"],
            "certifications": ["UL", "CE"],
            "safety_features": ["overload_protection", "short_circuit_protection"],
            "environmental_rating": {
                "ip_rating": "IP65",
                "min_operating_temp": -25,
                "max_operating_temp": 60,
            },
            "dimensions": {"width": 300, "height": 200, "depth": 150},
        }

    @pytest.mark.asyncio
    async def test_ieee_compliance(self):
        """Test IEEE standards compliance."""
        result = await self.validator.validate_standards_compliance(
            self.test_component, ["IEEE"], "north_america", "industrial"
        )

        assert len(result) > 0
        ieee_result = next(r for r in result if r.standard_type == StandardType.IEEE)
        assert ieee_result.overall_compliance != ComplianceLevel.NOT_COMPLIANT

    @pytest.mark.asyncio
    async def test_iec_compliance(self):
        """Test IEC standards compliance."""
        result = await self.validator.validate_standards_compliance(
            self.test_component, ["IEC"], "europe", "industrial"
        )

        assert len(result) > 0
        iec_result = next(r for r in result if r.standard_type == StandardType.IEC)
        assert iec_result.overall_compliance != ComplianceLevel.NOT_COMPLIANT

    @pytest.mark.asyncio
    async def test_nec_compliance(self):
        """Test NEC standards compliance."""
        result = await self.validator.validate_standards_compliance(
            self.test_component, ["NEC"], "north_america", "industrial"
        )

        assert len(result) > 0
        nec_result = next(r for r in result if r.standard_type == StandardType.NEC)
        assert nec_result.overall_compliance != ComplianceLevel.NOT_COMPLIANT

    @pytest.mark.asyncio
    async def test_en_compliance(self):
        """Test EN standards compliance."""
        result = await self.validator.validate_standards_compliance(self.test_component, ["EN"], "europe", "industrial")

        assert len(result) > 0
        en_result = next(r for r in result if r.standard_type == StandardType.EN)
        assert en_result.overall_compliance != ComplianceLevel.NOT_COMPLIANT

    @pytest.mark.asyncio
    async def test_voltage_compliance_check(self):
        """Test voltage compliance checking."""
        # Test within IEEE range
        component = dict(self.test_component)
        component["voltage_rating"] = 480

        result = await self.validator.validate_standards_compliance(component, ["IEEE"], "north_america", "industrial")

        ieee_result = next(r for r in result if r.standard_type == StandardType.IEEE)
        voltage_compliant = any(
            nc.requirement_id == "IEEE-1584-arc-flash" and nc.is_compliant
            for nc in ieee_result.non_compliant_requirements
        )
        assert voltage_compliant or ieee_result.overall_compliance != ComplianceLevel.NOT_COMPLIANT

    @pytest.mark.asyncio
    async def test_voltage_out_of_range(self):
        """Test voltage out of range."""
        component = dict(self.test_component)
        component["voltage_rating"] = 20000  # Above IEEE range

        result = await self.validator.validate_standards_compliance(component, ["IEEE"], "north_america", "industrial")

        ieee_result = next(r for r in result if r.standard_type == StandardType.IEEE)
        # Should have non-compliant voltage requirements
        voltage_issues = [nc for nc in ieee_result.non_compliant_requirements if "voltage" in nc.requirement_id.lower()]
        assert len(voltage_issues) > 0

    @pytest.mark.asyncio
    async def test_ip_rating_compliance(self):
        """Test IP rating compliance."""
        component = dict(self.test_component)
        component["ip_rating"] = "IP44"  # Below recommended

        result = await self.validator.validate_standards_compliance(component, ["IEC"], "europe", "industrial")

        iec_result = next(r for r in result if r.standard_type == StandardType.IEC)
        # IP44 should still be compliant for basic applications
        assert iec_result.overall_compliance != ComplianceLevel.NOT_COMPLIANT

    @pytest.mark.asyncio
    async def test_temperature_compliance(self):
        """Test temperature compliance."""
        component = dict(self.test_component)
        component["operating_temperature"] = 50  # High but within limits

        result = await self.validator.validate_standards_compliance(component, ["EN"], "europe", "industrial")

        en_result = next(r for r in result if r.standard_type == StandardType.EN)
        assert en_result.overall_compliance != ComplianceLevel.NOT_COMPLIANT

    @pytest.mark.asyncio
    async def test_multi_standard_validation(self):
        """Test validation against multiple standards."""
        result = await self.validator.validate_standards_compliance(
            self.test_component, ["IEEE", "IEC", "NEC"], "north_america", "industrial"
        )

        assert len(result) >= 3
        standards = [r.standard_type for r in result]
        assert StandardType.IEEE in standards
        assert StandardType.IEC in standards
        assert StandardType.NEC in standards

    @pytest.mark.asyncio
    async def test_regional_variations(self):
        """Test regional variations."""
        # Test North America
        na_result = await self.validator.validate_standards_compliance(
            self.test_component, ["NEC"], "north_america", "industrial"
        )

        # Test Europe
        eu_result = await self.validator.validate_standards_compliance(
            self.test_component, ["EN"], "europe", "industrial"
        )

        assert len(na_result) > 0
        assert len(eu_result) > 0

        # Should have different regional variations
        na_nec = next(r for r in na_result if r.standard_type == StandardType.NEC)
        eu_en = next(r for r in eu_result if r.standard_type == StandardType.EN)

        assert "north_america" in str(na_nec.regional_variations)
        assert "europe" in str(eu_en.regional_variations)

    def test_get_applicable_standards(self):
        """Test getting applicable standards for region."""
        na_standards = self.validator.get_applicable_standards("north_america")
        eu_standards = self.validator.get_applicable_standards("europe")

        assert len(na_standards) > 0
        assert len(eu_standards) > 0

        # North America should include NEC
        nec_in_na = any(s["standard"] == "NEC" for s in na_standards)
        assert nec_in_na

        # Europe should include EN and IEC
        en_in_eu = any(s["standard"] == "EN" for s in eu_standards)
        iec_in_eu = any(s["standard"] == "IEC" for s in eu_standards)
        assert en_in_eu
        assert iec_in_eu

    def test_standards_summary(self):
        """Test getting standards summary."""
        summary = self.validator.get_standards_summary()

        assert "total_requirements" in summary
        assert "standards_by_type" in summary
        assert "regional_coverage" in summary
        assert "latest_updates" in summary

        assert summary["total_requirements"] > 0
        assert len(summary["standards_by_type"]) > 0
        assert len(summary["regional_coverage"]) > 0

    @pytest.mark.asyncio
    async def test_empty_component_data(self):
        """Test handling of empty component data."""
        result = await self.validator.validate_standards_compliance({}, ["IEEE"], "north_america", "industrial")

        assert len(result) > 0
        # Should handle gracefully with appropriate error messages

    @pytest.mark.asyncio
    async def test_unknown_standard_type(self):
        """Test handling of unknown standard type."""
        result = await self.validator.validate_standards_compliance(
            self.test_component, ["UNKNOWN"], "north_america", "industrial"
        )

        # Should handle gracefully, possibly with warning
        assert isinstance(result, list)

    @pytest.mark.asyncio
    async def test_compliance_level_determination(self):
        """Test compliance level determination."""
        # Test high compliance
        high_compliance = self.validator._determine_compliance_level(0.98)
        assert high_compliance == ComplianceLevel.FULLY_COMPLIANT

        # Test medium compliance
        medium_compliance = self.validator._determine_compliance_level(0.75)
        assert medium_compliance == ComplianceLevel.MOSTLY_COMPLIANT

        # Test low compliance
        low_compliance = self.validator._determine_compliance_level(0.45)
        assert low_compliance == ComplianceLevel.PARTIALLY_COMPLIANT

        # Test non-compliance
        no_compliance = self.validator._determine_compliance_level(0.25)
        assert no_compliance == ComplianceLevel.NOT_COMPLIANT

    def test_cache_operations(self):
        """Test cache operations."""
        cache_key = "test-key"
        cached_result = [
            {
                "component_id": "test",
                "standard_type": StandardType.IEEE,
                "overall_compliance": ComplianceLevel.FULLY_COMPLIANT,
            }
        ]

        # Cache should be initially empty
        assert cache_key not in self.validator.cache

        # Update standards to clear cache
        self.validator.update_standards([])
        assert len(self.validator.cache) == 0  # Cache should be cleared

    @pytest.mark.asyncio
    async def test_grounding_compliance(self):
        """Test grounding resistance compliance."""
        # Test compliant grounding
        component = dict(self.test_component)
        component["ground_resistance"] = 5  # Well below 25 ohm limit

        result = await self.validator.validate_standards_compliance(component, ["NEC"], "north_america", "industrial")

        nec_result = next(r for r in result if r.standard_type == StandardType.NEC)
        grounding_compliant = all(
            nc.requirement_id != "NEC-250-grounding" or nc.is_compliant for nc in nec_result.non_compliant_requirements
        )
        assert grounding_compliant

    @pytest.mark.asyncio
    async def test_frequency_compatibility(self):
        """Test frequency compatibility."""
        # Test 60Hz compliance
        component = dict(self.test_component)
        component["frequency_rating"] = 60

        result = await self.validator.validate_standards_compliance(
            component, ["IEEE", "IEC"], "north_america", "industrial"
        )

        # Should be compliant for 60Hz in North America
        assert len(result) > 0

        # Test 50Hz compliance
        component["frequency_rating"] = 50

        result = await self.validator.validate_standards_compliance(component, ["IEC", "EN"], "europe", "industrial")

        # Should be compliant for 50Hz in Europe
        assert len(result) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
