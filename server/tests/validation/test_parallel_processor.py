"""Unit tests for parallel validation processing system.

This module contains comprehensive tests for parallel validation processing
including async processing, batch validation, load balancing, and performance optimization.
"""

import pytest
import asyncio
import time
from typing import Dict, List, Any
from unittest.mock import AsyncMock, patch

from src.core.validation.parallel_processor import (
    ParallelValidationProcessor,
    ProcessingStrategy,
    LoadBalancingStrategy,
    TaskPriority,
    ValidationTask,
    ProcessingResult,
    PerformanceMetrics,
)


class TestParallelValidationProcessor:
    """Tests for parallel validation processor."""

    @pytest.fixture
    def processor(self):
        """Create processor instance for testing."""
        return ParallelValidationProcessor(
            max_workers=2,
            max_memory_mb=512,
            processing_strategy=ProcessingStrategy.HYBRID,
            load_balancing=LoadBalancingStrategy.RESOURCE_BASED,
        )

    @pytest.fixture
    def sample_data(self):
        """Sample validation data."""
        return {
            "voltage": 480,
            "current": 100,
            "power": 48000,
            "frequency": 60,
            "temperature": 25,
        }

    @pytest.mark.asyncio
    async def test_processor_initialization(self, processor):
        """Test processor initialization."""
        assert processor.max_workers == 2
        assert processor.max_memory_mb == 512
        assert processor.processing_strategy == ProcessingStrategy.HYBRID
        assert not processor.is_running

    @pytest.mark.asyncio
    async def test_start_stop_processor(self, processor):
        """Test starting and stopping processor."""
        await processor.start()
        assert processor.is_running

        await processor.stop()
        assert not processor.is_running

    @pytest.mark.asyncio
    async def test_submit_single_task(self, processor, sample_data):
        """Test submitting a single validation task."""
        await processor.start()

        task_id = await processor.submit_validation_task(
            "electrical_validation",
            sample_data,
            {"context": "industrial"},
            TaskPriority.NORMAL,
        )

        assert task_id is not None
        assert task_id in processor.active_tasks

        await processor.stop()

    @pytest.mark.asyncio
    async def test_submit_batch_tasks(self, processor, sample_data):
        """Test submitting batch validation tasks."""
        await processor.start()

        data_list = [sample_data] * 3
        task_ids = await processor.submit_batch_validation(
            "electrical_validation",
            data_list,
            {"context": "industrial"},
            TaskPriority.NORMAL,
        )

        assert len(task_ids) == 3
        for task_id in task_ids:
            assert task_id in processor.active_tasks

        await processor.stop()

    @pytest.mark.asyncio
    async def test_complexity_estimation(self, processor):
        """Test task complexity estimation."""
        simple_data = {"voltage": 120}
        complex_data = [{"voltage": 120, "current": 50}] * 1000

        simple_complexity = processor._estimate_complexity("electrical_validation", simple_data, {})
        complex_complexity = processor._estimate_complexity("electrical_validation", complex_data, {})

        assert simple_complexity < complex_complexity
        assert 0.0 <= simple_complexity <= 1.0
        assert 0.0 <= complex_complexity <= 1.0

    @pytest.mark.asyncio
    async def test_processing_strategy_selection(self, processor):
        """Test processing strategy selection."""
        simple_task = ValidationTask(
            task_id="test1",
            task_type="electrical_validation",
            data={"voltage": 120},
            parameters={},
            priority=TaskPriority.NORMAL,
            created_at=time.time(),
            estimated_complexity=0.1,
            dependencies=[],
        )

        complex_task = ValidationTask(
            task_id="test2",
            task_type="constraint_validation",
            data=[{"voltage": 120}] * 1000,
            parameters={},
            priority=TaskPriority.NORMAL,
            created_at=time.time(),
            estimated_complexity=0.9,
            dependencies=[],
        )

        simple_strategy = processor._select_processing_method(simple_task)
        complex_strategy = processor._select_processing_method(complex_task)

        assert simple_strategy in [
            ProcessingStrategy.ASYNC,
            ProcessingStrategy.THREAD_POOL,
        ]
        assert complex_strategy in [
            ProcessingStrategy.THREAD_POOL,
            ProcessingStrategy.PROCESS_POOL,
        ]

    @pytest.mark.asyncio
    async def test_dependency_checking(self, processor):
        """Test task dependency checking."""
        # Create tasks with dependencies
        task1 = ValidationTask(
            task_id="dep1",
            task_type="electrical_validation",
            data={"voltage": 120},
            parameters={},
            priority=TaskPriority.NORMAL,
            created_at=time.time(),
            estimated_complexity=0.5,
            dependencies=[],
        )

        task2 = ValidationTask(
            task_id="dep2",
            task_type="compatibility_check",
            data={"voltage": 120},
            parameters={},
            priority=TaskPriority.NORMAL,
            created_at=time.time(),
            estimated_complexity=0.5,
            dependencies=["dep1"],
        )

        # Task2 should not be ready until task1 is complete
        assert await processor._check_dependencies(task1) is True
        assert await processor._check_dependencies(task2) is False

        # Complete task1
        processor.completed_tasks["dep1"] = ProcessingResult(
            task_id="dep1",
            result={"valid": True},
            processing_time_ms=100,
            memory_usage_mb=10,
            cpu_usage_percent=50,
            success=True,
            error=None,
            processed_at=time.time(),
        )

        # Now task2 should be ready
        assert await processor._check_dependencies(task2) is True

    @pytest.mark.asyncio
    async def test_performance_metrics(self, processor):
        """Test performance metrics collection."""
        await processor.start()

        # Submit some tasks
        task_id = await processor.submit_validation_task(
            "electrical_validation", {"voltage": 120}, {"context": "industrial"}
        )

        metrics = await processor.get_performance_metrics()

        assert isinstance(metrics, PerformanceMetrics)
        assert metrics.total_tasks >= 1
        assert metrics.throughput_tasks_per_second >= 0.0

        await processor.stop()

    @pytest.mark.asyncio
    async def test_resource_monitoring(self, processor):
        """Test resource monitoring."""
        monitor = processor.resource_monitor

        # Update metrics
        monitor.update()

        # Check metrics are collected
        assert len(monitor.metrics_history) >= 0
        assert monitor.get_current_memory_usage() >= 0
        assert monitor.get_current_cpu_usage() >= 0.0

    @pytest.mark.asyncio
    async def test_load_balancing(self, processor):
        """Test load balancing strategies."""
        balancer = processor.load_balancer

        task = ValidationTask(
            task_id="test",
            task_type="electrical_validation",
            data={"voltage": 120},
            parameters={},
            priority=TaskPriority.NORMAL,
            created_at=time.time(),
            estimated_complexity=0.5,
            dependencies=[],
        )

        worker = balancer.select_worker(task)
        assert isinstance(worker, str)

    @pytest.mark.asyncio
    async def test_task_priority_handling(self, processor):
        """Test task priority handling."""
        await processor.start()

        # Submit tasks with different priorities
        low_priority_id = await processor.submit_validation_task(
            "electrical_validation", {"voltage": 120}, {}, TaskPriority.LOW
        )

        high_priority_id = await processor.submit_validation_task(
            "electrical_validation", {"voltage": 120}, {}, TaskPriority.HIGH
        )

        # Both tasks should be submitted
        assert low_priority_id in processor.active_tasks
        assert high_priority_id in processor.active_tasks

        await processor.stop()

    @pytest.mark.asyncio
    async def test_error_handling(self, processor):
        """Test error handling in task processing."""
        await processor.start()

        # Submit task with invalid data
        task_id = await processor.submit_validation_task(
            "unknown_task_type", {"invalid": "data"}, {}, TaskPriority.NORMAL
        )

        # Should handle gracefully
        assert task_id in processor.active_tasks

        await processor.stop()

    @pytest.mark.asyncio
    async def test_concurrent_processing(self, processor):
        """Test concurrent processing capabilities."""
        await processor.start()

        # Submit multiple tasks concurrently
        tasks = []
        for i in range(5):
            task_id = await processor.submit_validation_task(
                "electrical_validation",
                {"voltage": 120 + i, "current": 50 + i},
                {"context": "industrial"},
                TaskPriority.NORMAL,
            )
            tasks.append(task_id)

        # All tasks should be accepted
        assert len(tasks) == 5
        for task_id in tasks:
            assert task_id in processor.active_tasks

        await processor.stop()

    def test_processing_stats(self):
        """Test processing statistics."""
        from src.core.validation.parallel_processor import ProcessingStats

        stats = ProcessingStats()

        # Record some completions
        result = ProcessingResult(
            task_id="test",
            result={"valid": True},
            processing_time_ms=150,
            memory_usage_mb=10,
            cpu_usage_percent=50,
            success=True,
            error=None,
            processed_at=time.time(),
        )

        stats.record_completion(result)

        # Check stats are calculated
        avg_time = stats.get_average_processing_time()
        throughput = stats.get_throughput()

        assert avg_time == 150.0
        assert throughput >= 0.0

    @pytest.mark.asyncio
    async def test_batch_processing_performance(self, processor):
        """Test batch processing performance."""
        await processor.start()

        # Create batch of tasks
        data_list = [{"voltage": 120 + i, "current": 50 + i} for i in range(10)]

        start_time = time.time()
        task_ids = await processor.submit_batch_validation(
            "electrical_validation", data_list, {"context": "industrial"}
        )

        # Quick check that tasks are submitted efficiently
        assert len(task_ids) == 10
        processing_time = time.time() - start_time
        assert processing_time < 1.0  # Should be very fast

        await processor.stop()


class TestResourceMonitor:
    """Tests for resource monitoring."""

    def test_resource_monitor_initialization(self):
        """Test resource monitor initialization."""
        from src.core.validation.parallel_processor import ResourceMonitor

        monitor = ResourceMonitor()
        assert monitor.get_current_memory_usage() >= 0
        assert monitor.get_current_cpu_usage() >= 0.0

    def test_metrics_collection(self):
        """Test metrics collection."""
        from src.core.validation.parallel_processor import ResourceMonitor

        monitor = ResourceMonitor()

        initial_length = len(monitor.metrics_history)
        monitor.update()

        assert len(monitor.metrics_history) >= initial_length
        assert monitor.get_peak_cpu_usage() >= 0.0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
