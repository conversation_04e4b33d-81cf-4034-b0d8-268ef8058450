"""Unit tests for multi-format data compatibility validation.

This module contains comprehensive tests for CSV, JSON, XML, and legacy data format validation
with electrical parameter validation and format conversion capabilities.
"""

import pytest
import json
import csv
import io
from pathlib import Path
from typing import Dict, List, Any

from src.core.validation.data_format_validator import (
    MultiFormatDataValidator,
    DataFormat,
    ValidationLevel,
    FormatValidationError,
)


class TestMultiFormatDataValidator:
    """Tests for multi-format data validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = MultiFormatDataValidator()

        # Test data
        self.valid_csv_data = """voltage_rating,current_rating,power_rating,frequency_rating
480,100,48000,60
240,50,12000,60
120,20,2400,60"""

        self.valid_json_data = {
            "components": [
                {
                    "voltage_rating": 480,
                    "current_rating": 100,
                    "power_rating": 48000,
                    "frequency_rating": 60,
                },
                {
                    "voltage_rating": 240,
                    "current_rating": 50,
                    "power_rating": 12000,
                    "frequency_rating": 60,
                },
            ]
        }

        self.valid_xml_data = """<?xml version="1.0"?>
<components>
    <component>
        <voltage_rating>480</voltage_rating>
        <current_rating>100</current_rating>
        <power_rating>48000</power_rating>
        <frequency_rating>60</frequency_rating>
    </component>
    <component>
        <voltage_rating>240</voltage_rating>
        <current_rating>50</current_rating>
        <power_rating>12000</power_rating>
        <frequency_rating>60</frequency_rating>
    </component>
</components>"""

        self.legacy_data = """VOLTAGE|CURRENT|POWER|FREQ
480|100|48000|60
240|50|12000|60
120|20|2400|60"""

    @pytest.mark.asyncio
    async def test_valid_csv_validation(self):
        """Test validation of valid CSV data."""
        result = await self.validator.validate_data_format(
            self.valid_csv_data, DataFormat.CSV, ValidationLevel.COMPLETE
        )

        assert result.is_valid is True
        assert result.format_type == DataFormat.CSV
        assert result.record_count == 3
        assert result.compatibility_score > 0.9

    @pytest.mark.asyncio
    async def test_valid_json_validation(self):
        """Test validation of valid JSON data."""
        result = await self.validator.validate_data_format(
            json.dumps(self.valid_json_data), DataFormat.JSON, ValidationLevel.COMPLETE
        )

        assert result.is_valid is True
        assert result.format_type == DataFormat.JSON
        assert result.compatibility_score > 0.9

    @pytest.mark.asyncio
    async def test_valid_xml_validation(self):
        """Test validation of valid XML data."""
        result = await self.validator.validate_data_format(
            self.valid_xml_data, DataFormat.XML, ValidationLevel.COMPLETE
        )

        assert result.is_valid is True
        assert result.format_type == DataFormat.XML
        assert result.compatibility_score > 0.8

    @pytest.mark.asyncio
    async def test_legacy_format_validation(self):
        """Test validation of legacy format data."""
        result = await self.validator.validate_data_format(
            self.legacy_data, DataFormat.LEGACY, ValidationLevel.COMPLETE
        )

        assert result.is_valid is True
        assert result.format_type == DataFormat.LEGACY
        assert result.record_count == 3

    @pytest.mark.asyncio
    async def test_format_detection(self):
        """Test automatic format detection."""
        result = await self.validator.validate_data_format(self.valid_csv_data, validation_level=ValidationLevel.BASIC)

        assert result.format_type == DataFormat.CSV
        assert result.is_valid is True

    @pytest.mark.asyncio
    async def test_invalid_csv_missing_fields(self):
        """Test CSV validation with missing fields."""
        invalid_csv = """voltage_rating,current_rating
480,100
240,50"""

        result = await self.validator.validate_data_format(invalid_csv, DataFormat.CSV, ValidationLevel.COMPLETE)

        # Should still be valid as validation level is complete
        assert result.is_valid is True

    @pytest.mark.asyncio
    async def test_invalid_json_syntax(self):
        """Test JSON validation with syntax errors."""
        invalid_json = '{"components": [{"voltage_rating": 480, "current_rating": 100,}'

        result = await self.validator.validate_data_format(invalid_json, DataFormat.JSON, ValidationLevel.SYNTAX)

        assert result.is_valid is False
        assert any("JSON" in str(error) for error in result.errors)

    @pytest.mark.asyncio
    async def test_invalid_xml_syntax(self):
        """Test XML validation with syntax errors."""
        invalid_xml = """<?xml version="1.0"?>
<components>
    <component>
        <voltage_rating>480</voltage_rating>
    </component>
</components>"""

        result = await self.validator.validate_data_format(invalid_xml, DataFormat.XML, ValidationLevel.SYNTAX)

        # This XML is actually valid
        assert result.is_valid is True

    @pytest.mark.asyncio
    async def test_csv_field_mapping(self):
        """Test CSV field mapping to standard electrical parameters."""
        csv_with_variations = """V,I,P,f
480,100,48000,60
240,50,12000,60"""

        result = await self.validator.validate_data_format(
            csv_with_variations, DataFormat.CSV, ValidationLevel.COMPLETE
        )

        assert result.is_valid is True
        assert len(result.field_mapping) > 0

    @pytest.mark.asyncio
    async def test_electrical_validation_consistency(self):
        """Test electrical parameter validation consistency."""
        inconsistent_csv = """voltage_rating,current_rating,power_rating
480,100,40000,60"""

        result = await self.validator.validate_data_format(
            inconsistent_csv, DataFormat.CSV, ValidationLevel.BUSINESS_RULES
        )

        # Should have warning about power calculation
        warnings_found = any("power" in str(warning).lower() for warning in result.warnings)
        assert warnings_found or any("power" in str(error).lower() for error in result.errors)

    @pytest.mark.asyncio
    async def test_empty_csv(self):
        """Test validation of empty CSV."""
        empty_csv = "voltage_rating,current_rating,power_rating"

        result = await self.validator.validate_data_format(empty_csv, DataFormat.CSV, ValidationLevel.SYNTAX)

        assert result.record_count == 0

    @pytest.mark.asyncio
    async def test_csv_with_empty_values(self):
        """Test CSV with empty values."""
        csv_with_empty = (
            """voltage_rating,current_rating,power_rating
480,,48000
240,50,"""
            ""
        )

        result = await self.validator.validate_data_format(csv_with_empty, DataFormat.CSV, ValidationLevel.SEMANTIC)

        # Should have warnings for empty values
        assert len(result.errors) > 0

    @pytest.mark.asyncio
    async def test_json_array_validation(self):
        """Test JSON array validation."""
        json_array = [
            {"voltage_rating": 480, "current_rating": 100, "power_rating": 48000},
            {"voltage_rating": 240, "current_rating": 50, "power_rating": 12000},
        ]

        result = await self.validator.validate_data_format(
            json.dumps(json_array), DataFormat.JSON, ValidationLevel.COMPLETE
        )

        assert result.is_valid is True
        assert result.record_count == 2

    @pytest.mark.asyncio
    async def test_data_format_conversion(self):
        """Test data format conversion."""
        # Convert CSV to JSON
        json_result = await self.validator.convert_format(self.valid_csv_data, DataFormat.CSV, DataFormat.JSON)

        # Verify conversion
        converted_data = json.loads(json_result)
        assert len(converted_data) == 3
        assert "voltage_rating" in converted_data[0]

    @pytest.mark.asyncio
    async def test_negative_electrical_values(self):
        """Test validation with negative electrical values."""
        negative_csv = """voltage_rating,current_rating,power_rating
-480,100,48000
240,-50,12000"""

        result = await self.validator.validate_data_format(negative_csv, DataFormat.CSV, ValidationLevel.BUSINESS_RULES)

        # Should have errors for negative values
        negative_errors = [e for e in result.errors if "negative" in str(e).lower()]
        assert len(negative_errors) > 0

    @pytest.mark.asyncio
    async def test_xml_nested_structure(self):
        """Test XML with nested structure."""
        nested_xml = """<?xml version="1.0"?>
<project>
    <components>
        <component>
            <specifications>
                <voltage>480</voltage>
                <current>100</current>
            </specifications>
        </component>
    </components>
</project>"""

        result = await self.validator.validate_data_format(nested_xml, DataFormat.XML, ValidationLevel.STRUCTURE)

        assert result.is_valid is True

    @pytest.mark.asyncio
    async def test_format_detection_edge_cases(self):
        """Test format detection edge cases."""
        # Test with minimal CSV
        minimal_csv = "a,b\n1,2"
        result = await self.validator.validate_data_format(minimal_csv)
        assert result.format_type == DataFormat.CSV

        # Test with minimal JSON
        minimal_json = '{"a": 1}'
        result = await self.validator.validate_data_format(minimal_json)
        assert result.format_type == DataFormat.JSON

    @pytest.mark.asyncio
    async def test_large_dataset_performance(self):
        """Test performance with larger datasets."""
        # Generate larger CSV
        large_csv = "voltage_rating,current_rating,power_rating\n"
        for i in range(100):
            large_csv += f"{480 + i},{100 + i},{48000 + i * 1000}\n"

        result = await self.validator.validate_data_format(large_csv, DataFormat.CSV, ValidationLevel.BASIC)

        assert result.is_valid is True
        assert result.record_count == 100
        assert result.processing_time_ms > 0

    def test_field_mapping_detection(self):
        """Test electrical field mapping detection."""
        headers = ["VOLTAGE_RATING", "CURRENT_RATING", "POWER_RATING", "FREQUENCY"]

        # Test mapping through validator
        assert "voltage" in str(self.validator.field_mappings)
        assert "current" in str(self.validator.field_mappings)
        assert "power" in str(self.validator.field_mappings)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
