"""Test transaction isolation and predictable ID functionality."""

import pytest
from sqlalchemy.orm import Session


class TestTransactionIsolation:
    """Test that transaction isolation is working correctly."""

    def test_transaction_isolation_basic(self, db_session: Session, clean_test_data):
        """Test that data doesn't persist between tests."""
        from src.core.models.general.user import User

        # Create a user in this test
        user = User(name="Test User", email="<EMAIL>", password_hash="test_password")
        db_session.add(user)
        db_session.flush()

        # Verify user was created
        user_count = db_session.query(User).count()
        assert user_count > 0

        # The user should be rolled back after this test

    def test_transaction_isolation_verification(self, db_session: Session, clean_test_data):
        """Test that previous test data was rolled back."""
        from src.core.models.general.user import User

        # Check if the user from previous test exists
        test_user = db_session.query(User).filter_by(email="<EMAIL>").first()

        # The user should not exist due to transaction rollback
        # Note: We might see other users from previous tests that haven't been rolled back yet
        assert test_user is None, "User from previous test should not exist due to transaction isolation"

    def test_predictable_ids_helper(self, db_session: Session, predictable_ids, clean_test_data):
        """Test the predictable ID helper functionality."""
        # Test ID generation
        user_id_1 = predictable_ids.get_next_id("users")
        user_id_2 = predictable_ids.get_next_id("users")

        assert user_id_1 == 100000
        assert user_id_2 == 100001

        # Test user creation with predictable ID
        user_data = {"name": "Predictable User", "email": "<EMAIL>", "password_hash": "test_password"}

        user = predictable_ids.create_user_with_id(user_data, user_id=999999)
        assert user.id == 999999
        assert user.name == "Predictable User"

    def test_multiple_tests_isolation(self, db_session: Session, clean_test_data):
        """Test that multiple tests can create similar data without conflicts."""
        from src.core.models.general.user import User

        # Create a user with a common name
        user = User(name="Common Name", email="<EMAIL>", password_hash="test_password")
        db_session.add(user)
        db_session.flush()

        # Verify user was created
        created_user = db_session.query(User).filter_by(email="<EMAIL>").first()
        assert created_user is not None
        assert created_user.name == "Common Name"

    def test_multiple_tests_isolation_second(self, db_session: Session, clean_test_data):
        """Second test with same data to verify isolation."""
        from src.core.models.general.user import User

        # Create a user with the same common name as previous test
        user = User(
            name="Common Name",
            email="<EMAIL>",  # Same email as previous test
            password_hash="test_password",
        )
        db_session.add(user)
        db_session.flush()

        # This should work without conflicts due to transaction isolation
        created_user = db_session.query(User).filter_by(email="<EMAIL>").first()
        assert created_user is not None
        assert created_user.name == "Common Name"

    def test_session_state_isolation(self, db_session: Session, clean_test_data):
        """Test that session state is properly isolated."""
        from src.core.models.general.user import User

        # Create multiple users
        users = []
        for i in range(3):
            user = User(name=f"User {i}", email=f"user{i}@example.com", password_hash="test_password")
            db_session.add(user)
            users.append(user)

        db_session.flush()

        # Verify all users were created
        user_count = db_session.query(User).filter(User.email.in_([f"user{i}@example.com" for i in range(3)])).count()
        assert user_count == 3

        # Verify we can access all users
        for user in users:
            assert user.id is not None
            assert user.name.startswith("User")

    def test_database_constraints_work(self, db_session: Session, clean_test_data):
        """Test that database constraints still work with transaction isolation."""
        from src.core.models.general.user import User

        # Create a user
        user1 = User(name="Constraint Test User", email="<EMAIL>", password_hash="test_password")
        db_session.add(user1)
        db_session.flush()

        # Try to create another user with the same email (should fail)
        user2 = User(
            name="Duplicate Email User",
            email="<EMAIL>",  # Same email
            password_hash="test_password",
        )
        db_session.add(user2)

        # This should raise an integrity error
        with pytest.raises(Exception):  # Could be IntegrityError or similar
            db_session.flush()

    def test_async_sync_session_coordination(self, db_session: Session, async_db_session, clean_test_data):
        """Test that async and sync sessions can work together."""
        from src.core.models.general.user import User

        # Create user with sync session
        sync_user = User(name="Sync User", email="<EMAIL>", password_hash="test_password")
        db_session.add(sync_user)
        db_session.flush()

        # Note: In true transaction isolation, the async session might not see
        # the sync session's data until commit, but both will be rolled back

        # Verify sync session can see the user
        sync_count = db_session.query(User).filter_by(email="<EMAIL>").count()
        assert sync_count == 1
