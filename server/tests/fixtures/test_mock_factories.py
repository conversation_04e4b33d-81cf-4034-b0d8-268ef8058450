"""Tests for mock factories to ensure they work correctly."""

import pytest
from datetime import datetime
from decimal import Decimal

from tests.fixtures.mock_factories import MockDataFactory, MockSchemaFactory, MockRepositoryFactory
from tests.fixtures.assertion_helpers import <PERSON>sertionHelpers, MockAssertionHelpers


class TestMockDataFactory:
    """Test the MockDataFactory creates predictable mock objects."""

    def test_create_user_default_values(self):
        """Test creating user with default values."""
        user = MockDataFactory.create_user()

        assert user.id == 1
        assert user.name == "Test User"
        assert user.email == "<EMAIL>"
        assert user.password_hash == "mock_hash"
        assert user.is_active is True
        assert user.is_superuser is False
        assert isinstance(user.created_at, datetime)
        assert isinstance(user.updated_at, datetime)

    def test_create_user_custom_values(self):
        """Test creating user with custom values."""
        user = MockDataFactory.create_user(id=42, name="Custom User", email="<EMAIL>", is_superuser=True)

        assert user.id == 42
        assert user.name == "Custom User"
        assert user.email == "<EMAIL>"
        assert user.is_superuser is True

    def test_create_project_default_values(self):
        """Test creating project with default values."""
        project = MockDataFactory.create_project()

        assert project.id == 1
        assert project.name == "Test Project"
        assert project.project_number == "PRJ-0001"
        assert project.status == "ACTIVE"
        assert project.created_by_user_id == 1
        assert project.client == "Test Client"

    def test_create_component_category_default_values(self):
        """Test creating component category with default values."""
        category = MockDataFactory.create_component_category()

        assert category.id == 1
        assert category.name == "Test Category"
        assert category.description == "Test category description"
        assert category.is_active is True
        assert category.parent_category_id is None

    def test_create_component_default_values(self):
        """Test creating component with default values."""
        component = MockDataFactory.create_component()

        assert component.id == 1
        assert component.name == "Test Component"
        assert component.manufacturer == "Test Manufacturer"
        assert component.model_number == "TEST-001"
        assert component.component_type_id == 1
        assert component.category_id == 1
        assert component.unit_price == Decimal("25.50")
        assert component.currency == "EUR"
        assert component.is_active is True
        assert component.full_name == "Test Manufacturer TEST-001"


class TestMockSchemaFactory:
    """Test the MockSchemaFactory creates valid schema objects."""

    def test_create_user_create_schema(self):
        """Test creating UserCreateSchema."""
        schema = MockSchemaFactory.create_user_create_schema()

        assert schema.name == "Test User"
        assert schema.email == "<EMAIL>"
        assert schema.password == "SecurePass123"

    def test_create_component_category_create_schema(self):
        """Test creating ComponentCategoryCreateSchema."""
        schema = MockSchemaFactory.create_component_category_create_schema()

        assert schema.name == "Test Category"
        assert schema.description == "Test category description"
        assert schema.is_active is True
        assert schema.parent_category_id is None

    def test_create_component_create_schema(self):
        """Test creating ComponentCreateSchema."""
        schema = MockSchemaFactory.create_component_create_schema()

        assert schema.name == "Test Component"
        assert schema.manufacturer == "Test Manufacturer"
        assert schema.model_number == "TEST-001"
        assert schema.component_type_id == 1
        assert schema.category_id == 1
        assert schema.unit_price == Decimal("25.50")
        assert schema.currency == "EUR"


class TestMockRepositoryFactory:
    """Test the MockRepositoryFactory creates properly configured mocks."""

    def test_create_mock_user_repository(self):
        """Test creating mock user repository."""
        mock_repo = MockRepositoryFactory.create_mock_user_repository()

        # Test that it has the expected methods
        assert hasattr(mock_repo, "create")
        assert hasattr(mock_repo, "get_by_id")
        assert hasattr(mock_repo, "get_by_email")
        assert hasattr(mock_repo, "check_email_exists")
        assert hasattr(mock_repo, "db_session")

        # Test default return values
        user = mock_repo.create.return_value
        assert user.id == 1
        assert user.name == "Test User"

        # Test that check_email_exists returns False by default
        assert mock_repo.check_email_exists.return_value is False

    def test_create_mock_component_category_repository(self):
        """Test creating mock component category repository."""
        mock_repo = MockRepositoryFactory.create_mock_component_category_repository()

        # Test that it has the expected methods
        assert hasattr(mock_repo, "create")
        assert hasattr(mock_repo, "get_by_id")
        assert hasattr(mock_repo, "get_by_name")
        assert hasattr(mock_repo, "update")
        assert hasattr(mock_repo, "delete")
        assert hasattr(mock_repo, "db_session")

        # Test default return values
        category = mock_repo.create.return_value
        assert category.id == 1
        assert category.name == "Test Category"

        # Test that get_by_name returns None by default (no existing category)
        assert mock_repo.get_by_name.return_value is None


class TestAssertionHelpers:
    """Test the AssertionHelpers work correctly."""

    def test_assert_valid_id_success(self):
        """Test assert_valid_id with valid ID."""
        # Should not raise any exception
        AssertionHelpers.assert_valid_id(1)
        AssertionHelpers.assert_valid_id(42)
        AssertionHelpers.assert_valid_id(999)

    def test_assert_valid_id_failure(self):
        """Test assert_valid_id with invalid ID."""
        with pytest.raises(AssertionError, match="should be an integer"):
            AssertionHelpers.assert_valid_id("not_an_int")

        with pytest.raises(AssertionError, match="should be positive"):
            AssertionHelpers.assert_valid_id(0)

        with pytest.raises(AssertionError, match="should be positive"):
            AssertionHelpers.assert_valid_id(-1)

    def test_assert_user_properties_success(self):
        """Test assert_user_properties with valid user data."""
        user_data = {
            "id": 42,
            "name": "Test User",
            "email": "<EMAIL>",
            "is_active": True,
            "created_at": "2024-01-01T12:00:00",
            "updated_at": "2024-01-01T12:00:00",
        }

        # Should not raise any exception
        AssertionHelpers.assert_user_properties(user_data, "Test User", "<EMAIL>")

    def test_assert_user_properties_failure(self):
        """Test assert_user_properties with invalid user data."""
        user_data = {"id": 42, "name": "Wrong Name", "email": "<EMAIL>", "is_active": True}

        with pytest.raises(AssertionError, match="Expected name Test User"):
            AssertionHelpers.assert_user_properties(user_data, "Test User", "<EMAIL>")

    def test_assert_category_properties_success(self):
        """Test assert_category_properties with valid category data."""
        category_data = {
            "id": 1,
            "name": "Test Category",
            "description": "Test description",
            "is_active": True,
            "parent_category_id": None,
            "created_at": "2024-01-01T12:00:00",
            "updated_at": "2024-01-01T12:00:00",
        }

        # Should not raise any exception
        AssertionHelpers.assert_category_properties(category_data, "Test Category", "Test description")


class TestMockAssertionHelpers:
    """Test the MockAssertionHelpers work correctly."""

    def test_assert_mock_called_with_user_kwargs(self):
        """Test assert_mock_called_with_user with user_id in kwargs."""
        from unittest.mock import Mock

        mock_method = Mock()
        mock_method(component_id=1, user_id=42)

        # Should not raise any exception
        MockAssertionHelpers.assert_mock_called_with_user(mock_method.call_args, 42)

    def test_assert_mock_called_with_user_args(self):
        """Test assert_mock_called_with_user with user_id in args."""
        from unittest.mock import Mock

        mock_method = Mock()
        mock_method(1, 42)  # component_id, user_id

        # Should not raise any exception
        MockAssertionHelpers.assert_mock_called_with_user(mock_method.call_args, 42)

    def test_assert_mock_called_with_user_failure(self):
        """Test assert_mock_called_with_user with wrong user_id."""
        from unittest.mock import Mock

        mock_method = Mock()
        mock_method(component_id=1, user_id=99)

        with pytest.raises(AssertionError, match="Expected user_id 42"):
            MockAssertionHelpers.assert_mock_called_with_user(mock_method.call_args, 42)
