"""Mock data factories for unit tests.

This module provides standardized mock data creation for unit tests,
ensuring predictable IDs and consistent test data patterns.
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from unittest.mock import Mock

from src.core.models.general.user import User
from src.core.models.general.project import Project
from src.core.models.general.component_category import ComponentCategory
from src.core.models.general.component_type import ComponentType
from src.core.models.general.component import Component
from src.core.models.general.task import Task
from src.core.enums.project_management_enums import ProjectStatus, TaskStatus, TaskPriority


class MockDataFactory:
    """Standardized mock data creation for unit tests."""

    @staticmethod
    def create_user(
        id: int = 1,
        name: str = "Test User",
        email: str = "<EMAIL>",
        password_hash: str = "mock_hash",
        is_active: bool = True,
        is_superuser: bool = False,
    ) -> User:
        """Create predictable mock user with default ID 1."""
        user = Mock(spec=User)
        user.id = id
        user.name = name
        user.email = email
        user.password_hash = password_hash
        user.is_active = is_active
        user.is_superuser = is_superuser
        user.created_at = datetime(2024, 1, 1, 12, 0, 0)
        user.updated_at = datetime(2024, 1, 1, 12, 0, 0)
        return user

    @staticmethod
    def create_project(
        id: int = 1,
        user_id: int = 1,
        name: str = "Test Project",
        project_number: str = "PRJ-0001",
        status: str = "ACTIVE",
    ) -> Project:
        """Create predictable mock project with default ID 1."""
        project = Mock(spec=Project)
        project.id = id
        project.name = name
        project.description = "Test project description"
        project.project_number = project_number
        project.status = status
        project.client = "Test Client"
        project.created_by_user_id = user_id
        project.created_at = datetime(2024, 1, 1, 12, 0, 0)
        project.updated_at = datetime(2024, 1, 1, 12, 0, 0)
        return project

    @staticmethod
    def create_component_category(
        id: int = 1,
        name: str = "Test Category",
        description: str = "Test category description",
        is_active: bool = True,
        parent_category_id: Optional[int] = None,
    ) -> ComponentCategory:
        """Create predictable mock component category."""
        category = Mock(spec=ComponentCategory)
        category.id = id
        category.name = name
        category.description = description
        category.is_active = is_active
        category.parent_category_id = parent_category_id
        category.created_at = datetime(2024, 1, 1, 12, 0, 0)
        category.updated_at = datetime(2024, 1, 1, 12, 0, 0)
        return category

    @staticmethod
    def create_component_type(
        id: int = 1,
        name: str = "Test Component Type",
        description: str = "Test component type description",
        category_id: int = 1,
        is_active: bool = True,
    ) -> ComponentType:
        """Create predictable mock component type."""
        component_type = Mock(spec=ComponentType)
        component_type.id = id
        component_type.name = name
        component_type.description = description
        component_type.category_id = category_id
        component_type.is_active = is_active
        component_type.specifications_template = {}
        component_type.metadata = {}
        component_type.created_at = datetime(2024, 1, 1, 12, 0, 0)
        component_type.updated_at = datetime(2024, 1, 1, 12, 0, 0)
        return component_type

    @staticmethod
    def create_component(
        id: int = 1,
        name: str = "Test Component",
        manufacturer: str = "Test Manufacturer",
        model_number: str = "TEST-001",
        component_type_id: int = 1,
        category_id: int = 1,
        unit_price: Decimal = Decimal("25.50"),
        is_active: bool = True,
    ) -> Component:
        """Create predictable mock component."""
        component = Mock(spec=Component)
        component.id = id
        component.name = name
        component.manufacturer = manufacturer
        component.model_number = model_number
        component.description = "Test component description"
        component.component_type_id = component_type_id
        component.category_id = category_id
        component.specifications = '{"test": "specification"}'
        component.unit_price = unit_price
        component.currency = "EUR"
        component.supplier = "Test Supplier"
        component.part_number = f"PART-{id:04d}"
        component.weight_kg = 0.15
        component.dimensions_json = None
        component.is_active = is_active
        component.is_preferred = False
        component.is_deleted = False
        component.stock_status = "available"
        component.version = "1.0"
        component.metadata_json = None
        component.created_at = datetime(2024, 1, 1, 12, 0, 0)
        component.updated_at = datetime(2024, 1, 1, 12, 0, 0)

        # Add computed properties
        component.full_name = f"{manufacturer} {model_number}"
        component.display_name = f"{name} ({manufacturer} {model_number})"

        return component

    @staticmethod
    def create_task(
        id: int = 1,
        title: str = "Test Task",
        project_id: int = 1,
        assigned_to_user_id: int = 1,
        created_by_user_id: int = 1,
        status: str = "Not Started",
        priority: str = "Medium",
    ) -> Task:
        """Create predictable mock task."""
        task = Mock(spec=Task)
        task.id = id
        task.title = title
        task.description = "Test task description"
        task.project_id = project_id
        task.assigned_to_user_id = assigned_to_user_id
        task.created_by_user_id = created_by_user_id
        task.status = status
        task.priority = priority
        task.due_date = None
        task.estimated_hours = None
        task.actual_hours = None
        task.completion_percentage = 0
        task.created_at = datetime(2024, 1, 1, 12, 0, 0)
        task.updated_at = datetime(2024, 1, 1, 12, 0, 0)
        task.completed_at = None
        return task


class MockSchemaFactory:
    """Factory for creating mock schema objects for testing."""

    @staticmethod
    def create_user_create_schema(
        name: str = "Test User", email: str = "<EMAIL>", password: str = "SecurePass123"
    ):
        """Create UserCreateSchema for testing."""
        from src.core.schemas.general.user_schemas import UserCreateSchema

        return UserCreateSchema(name=name, email=email, password=password)

    @staticmethod
    def create_component_category_create_schema(
        name: str = "Test Category",
        description: str = "Test category description",
        is_active: bool = True,
        parent_category_id: Optional[int] = None,
    ):
        """Create ComponentCategoryCreateSchema for testing."""
        from src.core.schemas.general.component_category_schemas import ComponentCategoryCreateSchema

        return ComponentCategoryCreateSchema(
            name=name, description=description, is_active=is_active, parent_category_id=parent_category_id
        )

    @staticmethod
    def create_component_type_create_schema(
        name: str = "Test Component Type",
        description: str = "Test component type description",
        category_id: int = 1,
        is_active: bool = True,
    ):
        """Create ComponentTypeCreateSchema for testing."""
        from src.core.schemas.general.component_type_schemas import ComponentTypeCreateSchema

        return ComponentTypeCreateSchema(
            name=name,
            description=description,
            category_id=category_id,
            is_active=is_active,
            specifications_template={},
            metadata={},
        )

    @staticmethod
    def create_component_create_schema(
        name: str = "Test Component",
        manufacturer: str = "Test Manufacturer",
        model_number: str = "TEST-001",
        component_type_id: int = 1,
        category_id: int = 1,
        unit_price: Decimal = Decimal("25.50"),
    ):
        """Create ComponentCreateSchema for testing."""
        from src.core.schemas.general.component_schemas import ComponentCreateSchema

        return ComponentCreateSchema(
            name=name,
            manufacturer=manufacturer,
            model_number=model_number,
            description="Test component description",
            component_type_id=component_type_id,
            category_id=category_id,
            specifications={"electrical": {"voltage_rating": "230V", "current_rating": "16A"}},
            unit_price=unit_price,
            currency="EUR",
            supplier="Test Supplier",
            part_number=f"PART-{name.replace(' ', '-').upper()}",
            weight_kg=0.15,
            dimensions=None,
            is_active=True,
            is_preferred=False,
            stock_status="available",
            version="1.0",
            metadata=None,
        )


class MockRepositoryFactory:
    """Factory for creating standardized mock repositories."""

    @staticmethod
    def create_mock_user_repository():
        """Create standardized mock user repository."""
        from unittest.mock import AsyncMock
        from src.core.repositories.general.user_repository import UserRepository

        mock_repo = AsyncMock(spec=UserRepository)

        # Configure predictable return values
        mock_repo.create.return_value = MockDataFactory.create_user(id=1)
        mock_repo.get_by_id.return_value = MockDataFactory.create_user(id=1)
        mock_repo.get_by_email.return_value = MockDataFactory.create_user(id=1)
        mock_repo.check_email_exists.return_value = False
        mock_repo.update.return_value = MockDataFactory.create_user(id=1)
        mock_repo.delete.return_value = True
        mock_repo.update_password.return_value = True
        mock_repo.deactivate_user.return_value = True
        mock_repo.activate_user.return_value = True

        # Configure session methods
        mock_repo.db_session = AsyncMock()
        mock_repo.db_session.flush = AsyncMock()
        mock_repo.db_session.commit = AsyncMock()
        mock_repo.db_session.refresh = AsyncMock()
        mock_repo.db_session.rollback = AsyncMock()

        return mock_repo

    @staticmethod
    def create_mock_component_category_repository():
        """Create standardized mock component category repository."""
        from unittest.mock import AsyncMock
        from src.core.repositories.general.component_category_repository import ComponentCategoryRepository

        mock_repo = AsyncMock(spec=ComponentCategoryRepository)

        # Configure predictable return values
        mock_repo.create.return_value = MockDataFactory.create_component_category(id=1)
        mock_repo.get_by_id.return_value = MockDataFactory.create_component_category(id=1)
        mock_repo.get_by_name.return_value = None  # Default: no existing category
        mock_repo.update.return_value = MockDataFactory.create_component_category(id=1)
        mock_repo.delete.return_value = True
        mock_repo.get_all.return_value = [MockDataFactory.create_component_category(id=1)]

        # Configure session methods
        mock_repo.db_session = AsyncMock()
        mock_repo.db_session.flush = AsyncMock()
        mock_repo.db_session.commit = AsyncMock()
        mock_repo.db_session.refresh = AsyncMock()
        mock_repo.db_session.rollback = AsyncMock()

        return mock_repo
