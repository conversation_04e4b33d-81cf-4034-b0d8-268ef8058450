"""Integration data factories for API tests.

This module provides standardized real data creation for integration tests,
ensuring predictable test data patterns while using real database operations.
"""

import uuid
from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.models.general.user import User
from src.core.models.general.project import Project
from src.core.models.general.component_category import ComponentCategory
from src.core.models.general.component_type import ComponentType
from src.core.models.general.component import Component
from src.core.models.general.task import Task
from src.core.enums.project_management_enums import ProjectStatus, TaskStatus, TaskPriority
from src.core.schemas.general.user_schemas import UserCreateSchema
from src.core.schemas.general.component_category_schemas import ComponentCategoryCreateSchema
from src.core.schemas.general.component_type_schemas import ComponentTypeCreateSchema
from src.core.schemas.general.component_schemas import ComponentCreateSchema
from src.core.services.general.user_service import UserService
from src.core.repositories.general.user_repository import UserRepository
from src.core.repositories.general.user_preference_repository import UserPreferenceRepository


class IntegrationDataFactory:
    """Standardized real data creation for integration tests."""

    @staticmethod
    async def create_test_user(
        async_db_session: AsyncSession, name: str = None, email: str = None, password: str = "SecurePass123"
    ) -> User:
        """Create real test user with predictable data."""
        unique_suffix = str(uuid.uuid4())[:8]
        if name is None:
            name = f"Test User {unique_suffix}"
        if email is None:
            email = f"test.{unique_suffix}@example.com"

        user_repo = UserRepository(async_db_session)
        preference_repo = UserPreferenceRepository(async_db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)

        user_data = UserCreateSchema(name=name, email=email, password=password)
        return await user_service.create_user(user_data)

    @staticmethod
    def create_test_project(
        db_session: Session, user: User, name: str = None, project_number: str = None, status: str = "ACTIVE"
    ) -> Project:
        """Create real test project with predictable data."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"Test Project {unique_suffix}"

        if project_number is None:
            unique_suffix = str(uuid.uuid4())[:8]
            project_number = f"PRJ-{unique_suffix}"

        project = Project(
            name=name,
            description="Integration test project",
            project_number=project_number,
            status=status,
            client="Test Client",
            created_by_user_id=user.id,
        )

        db_session.add(project)
        db_session.flush()
        db_session.refresh(project)
        return project

    @staticmethod
    def create_test_component_category(
        db_session: Session,
        name: str = None,
        description: str = "Integration test category",
        is_active: bool = True,
        parent_category_id: Optional[int] = None,
    ) -> ComponentCategory:
        """Create real test component category with predictable data."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"Test Category {unique_suffix}"

        category = ComponentCategory(
            name=name,
            description=description,
            is_active=is_active,
            parent_category_id=parent_category_id,
        )

        db_session.add(category)
        db_session.flush()
        db_session.refresh(category)
        return category

    @staticmethod
    def create_test_component_type(
        db_session: Session,
        category: ComponentCategory,
        name: str = None,
        description: str = "Integration test component type",
        is_active: bool = True,
    ) -> ComponentType:
        """Create real test component type with predictable data."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"Test Component Type {unique_suffix}"

        component_type = ComponentType(
            name=name,
            description=description,
            category_id=category.id,
            is_active=is_active,
            specifications_template={
                "electrical": {
                    "voltage_rating": {"type": "string", "required": True},
                    "current_rating": {"type": "string", "required": True},
                }
            },
            metadata={"test": "metadata"},
        )

        db_session.add(component_type)
        db_session.flush()
        db_session.refresh(component_type)
        return component_type

    @staticmethod
    def create_test_component(
        db_session: Session,
        component_type: ComponentType,
        category: ComponentCategory,
        name: str = None,
        manufacturer: str = "Test Manufacturer",
        model_number: str = None,
        unit_price: Decimal = Decimal("25.50"),
    ) -> Component:
        """Create real test component with predictable data."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"Test Component {unique_suffix}"

        if model_number is None:
            unique_suffix = str(uuid.uuid4())[:8]
            model_number = f"MODEL-{unique_suffix}"

        component = Component(
            name=name,
            manufacturer=manufacturer,
            model_number=model_number,
            description="Integration test component",
            component_type_id=component_type.id,
            category_id=category.id,
            specifications='{"electrical": {"voltage_rating": "230V", "current_rating": "16A"}}',
            unit_price=unit_price,
            currency="EUR",
            supplier="Test Supplier",
            part_number=f"PART-{name.replace(' ', '-').upper()}",
            weight_kg=0.15,
            dimensions_json=None,
            is_active=True,
            is_preferred=False,
            stock_status="available",
            version="1.0",
            metadata_json='{"test": "metadata"}',
        )

        db_session.add(component)
        db_session.flush()
        db_session.refresh(component)
        return component

    @staticmethod
    def create_test_task(
        db_session: Session,
        project: Project,
        assigned_to_user: User,
        created_by_user: User,
        title: str = None,
        status: str = "Not Started",
        priority: str = "Medium",
    ) -> Task:
        """Create real test task with predictable data."""
        if title is None:
            unique_suffix = str(uuid.uuid4())[:8]
            title = f"Test Task {unique_suffix}"

        task = Task(
            title=title,
            description="Integration test task",
            project_id=project.id,
            assigned_to_user_id=assigned_to_user.id,
            created_by_user_id=created_by_user.id,
            status=status,
            priority=priority,
            due_date=None,
            estimated_hours=None,
            actual_hours=None,
            completion_percentage=0,
        )

        db_session.add(task)
        db_session.flush()
        db_session.refresh(task)
        return task


class IntegrationSchemaFactory:
    """Factory for creating schema objects for integration tests."""

    @staticmethod
    def create_user_create_data(name: str = None, email: str = None, password: str = "SecurePass123") -> dict:
        """Create user creation data for API requests."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"API Test User {unique_suffix}"

        if email is None:
            unique_suffix = str(uuid.uuid4())[:8]
            email = f"api.test.{unique_suffix}@example.com"

        return {"name": name, "email": email, "password": password}

    @staticmethod
    def create_component_category_data(
        name: str = None,
        description: str = "API test category",
        is_active: bool = True,
        parent_category_id: Optional[int] = None,
    ) -> dict:
        """Create component category data for API requests."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"API Test Category {unique_suffix}"

        return {
            "name": name,
            "description": description,
            "is_active": is_active,
            "parent_category_id": parent_category_id,
        }

    @staticmethod
    def create_component_type_data(
        name: str = None, description: str = "API test component type", category_id: int = 1, is_active: bool = True
    ) -> dict:
        """Create component type data for API requests."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"API Test Component Type {unique_suffix}"

        return {
            "name": name,
            "description": description,
            "category_id": category_id,
            "is_active": is_active,
            "specifications_template": {
                "electrical": {
                    "voltage_rating": {"type": "string", "required": True},
                    "current_rating": {"type": "string", "required": True},
                }
            },
            "metadata": {"test": "metadata"},
        }

    @staticmethod
    def create_component_data(
        name: str = None,
        manufacturer: str = "API Test Manufacturer",
        model_number: str = None,
        component_type_id: int = 1,
        category_id: int = 1,
        unit_price: float = 25.50,
    ) -> dict:
        """Create component data for API requests."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"API Test Component {unique_suffix}"

        if model_number is None:
            unique_suffix = str(uuid.uuid4())[:8]
            model_number = f"API-MODEL-{unique_suffix}"

        return {
            "name": name,
            "manufacturer": manufacturer,
            "model_number": model_number,
            "description": "API test component",
            "component_type_id": component_type_id,
            "category_id": category_id,
            "specifications": {"electrical": {"voltage_rating": "230V", "current_rating": "16A"}},
            "unit_price": unit_price,
            "currency": "EUR",
            "supplier": "API Test Supplier",
            "part_number": f"API-PART-{name.replace(' ', '-').upper()}",
            "weight_kg": 0.15,
            "dimensions": None,
            "is_active": True,
            "is_preferred": False,
            "stock_status": "available",
            "version": "1.0",
            "metadata": {"test": "metadata"},
        }

    @staticmethod
    def create_project_data(name: str = None, project_number: str = None, status: str = "ACTIVE") -> dict:
        """Create project data for API requests."""
        if name is None:
            unique_suffix = str(uuid.uuid4())[:8]
            name = f"API Test Project {unique_suffix}"

        if project_number is None:
            unique_suffix = str(uuid.uuid4())[:8]
            project_number = f"API-PRJ-{unique_suffix}"

        return {
            "name": name,
            "description": "API test project",
            "project_number": project_number,
            "status": status,
            "client": "API Test Client",
        }
