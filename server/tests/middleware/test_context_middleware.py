"""Unit tests for ContextMiddleware following robust test patterns.

Key Test Areas:
- Request ID generation and injection
- User context extraction and management
- Locale detection from headers
- Request timing and metadata
- Context variable access
- Error handling and graceful degradation
"""

import time
import uuid
from unittest.mock import Mock, patch

import pytest
from fastapi import Request, Response
from src.middleware.context_middleware import (
    ContextMiddleware,
    get_locale,
    get_request_duration,
    get_request_id,
    get_request_start_time,
    get_user_context,
)

pytestmark = [pytest.mark.integration]


class TestContextMiddleware:
    """Comprehensive test suite for ContextMiddleware."""

    def test_middleware_initialization_default_config(self, mock_app):
        """Test middleware initialization with default configuration."""
        middleware = ContextMiddleware(mock_app)

        assert middleware.enable_request_id is True
        assert middleware.enable_user_context is True
        assert middleware.enable_locale_detection is True
        assert middleware.enable_timing is True
        assert middleware.default_locale == "en"

    def test_middleware_initialization_custom_config(self, mock_app):
        """Test middleware initialization with custom configuration."""
        middleware = ContextMiddleware(
            app=mock_app,
            enable_request_id=False,
            enable_user_context=False,
            enable_locale_detection=False,
            enable_timing=False,
            default_locale="fr",
        )

        assert middleware.enable_request_id is False
        assert middleware.enable_user_context is False
        assert middleware.enable_locale_detection is False
        assert middleware.enable_timing is False
        assert middleware.default_locale == "fr"

    @pytest.mark.asyncio
    async def test_request_id_generation(self, context_middleware, mock_request, mock_call_next):
        """Test request ID generation and injection."""
        response = await context_middleware.dispatch(mock_request, mock_call_next)

        # Verify request ID was set in request state
        assert hasattr(mock_request.state, "request_id")
        assert mock_request.state.request_id is not None
        assert len(mock_request.state.request_id) == 36  # UUID length

        # Verify UUID format
        try:
            uuid.UUID(mock_request.state.request_id)
        except ValueError:
            pytest.fail("Request ID is not a valid UUID")

    @pytest.mark.asyncio
    async def test_request_timing(self, context_middleware, mock_request, mock_call_next):
        """Test request timing functionality."""
        start_time = time.time()

        response = await context_middleware.dispatch(mock_request, mock_call_next)

        end_time = time.time()

        # Verify timing was set in request state
        assert hasattr(mock_request.state, "start_time")
        assert mock_request.state.start_time is not None
        assert start_time <= mock_request.state.start_time <= end_time

    @pytest.mark.asyncio
    async def test_locale_detection_from_x_locale_header(self, context_middleware, mock_request, mock_call_next):
        """Test locale detection from X-Locale header."""
        mock_request.headers = {"x-locale": "fr"}

        response = await context_middleware.dispatch(mock_request, mock_call_next)

        # Verify locale was set correctly
        assert hasattr(mock_request.state, "locale")
        assert mock_request.state.locale == "fr"

    @pytest.mark.asyncio
    async def test_locale_detection_from_accept_language_header(self, context_middleware, mock_request, mock_call_next):
        """Test locale detection from Accept-Language header."""
        mock_request.headers = {"accept-language": "es-ES,es;q=0.9,en;q=0.8"}

        response = await context_middleware.dispatch(mock_request, mock_call_next)

        # Verify locale was extracted correctly (should be 'es')
        assert hasattr(mock_request.state, "locale")
        assert mock_request.state.locale == "es"

    @pytest.mark.asyncio
    async def test_locale_detection_default_fallback(self, context_middleware, mock_request, mock_call_next):
        """Test locale detection falls back to default when no headers present."""
        mock_request.headers = {}

        response = await context_middleware.dispatch(mock_request, mock_call_next)

        # Verify default locale was used
        assert hasattr(mock_request.state, "locale")
        assert mock_request.state.locale == "en"

    @pytest.mark.asyncio
    async def test_user_context_from_request_state(self, context_middleware, mock_request, mock_call_next):
        """Test user context extraction from request state."""
        # Mock user object in request state
        mock_user = Mock()
        mock_user.id = "user123"
        mock_user.username = "testuser"
        mock_user.email = "<EMAIL>"
        mock_user.is_admin = True
        mock_user.roles = ["engineer", "admin"]

        mock_request.state.user = mock_user

        response = await context_middleware.dispatch(mock_request, mock_call_next)

        # Verify user context was extracted correctly
        assert hasattr(mock_request.state, "user_context")
        user_context = mock_request.state.user_context
        assert user_context["id"] == "user123"
        assert user_context["username"] == "testuser"
        assert user_context["email"] == "<EMAIL>"
        assert user_context["is_admin"] is True
        assert user_context["roles"] == ["engineer", "admin"]

    @pytest.mark.asyncio
    async def test_user_context_from_authorization_header(self, context_middleware, mock_request, mock_call_next):
        """Test user context extraction from Authorization header."""
        mock_request.headers = {"authorization": "Bearer valid.jwt.token"}
        mock_request.state.user = None  # No user in state

        response = await context_middleware.dispatch(mock_request, mock_call_next)

        # Verify basic authenticated user context was created
        assert hasattr(mock_request.state, "user_context")
        user_context = mock_request.state.user_context
        assert user_context["id"] == "authenticated_user"
        assert user_context["authenticated"] is True
        assert user_context["token_present"] is True

    @pytest.mark.asyncio
    async def test_user_context_empty_when_no_auth(self, context_middleware, mock_request, mock_call_next):
        """Test user context is empty when no authentication present."""
        mock_request.headers = {}
        mock_request.state.user = None

        response = await context_middleware.dispatch(mock_request, mock_call_next)

        # Verify empty user context
        assert hasattr(mock_request.state, "user_context")
        assert mock_request.state.user_context == {}

    @pytest.mark.asyncio
    async def test_response_headers_added(self, context_middleware, mock_request, mock_call_next):
        """Test that context headers are added to response."""
        response = await context_middleware.dispatch(mock_request, mock_call_next)

        # Verify headers were added
        assert "X-Request-ID" in response.headers
        assert "X-Response-Time" in response.headers
        assert "X-Content-Language" in response.headers

        # Verify header values
        assert response.headers["X-Request-ID"] == mock_request.state.request_id
        assert response.headers["X-Content-Language"] == mock_request.state.locale
        assert "s" in response.headers["X-Response-Time"]  # Should contain 's' for seconds

    @pytest.mark.asyncio
    async def test_disabled_features(self, mock_app, mock_call_next):
        """Test middleware behavior when features are disabled."""
        middleware = ContextMiddleware(
            app=mock_app,
            enable_request_id=False,
            enable_user_context=False,
            enable_locale_detection=False,
            enable_timing=False,
        )

        # Create a fresh mock request without pre-configured state
        mock_request = Mock(spec=Request)
        mock_request.state = Mock()
        mock_request.headers = {}

        response = await middleware.dispatch(mock_request, mock_call_next)

        # Verify that the middleware completed successfully
        assert response is not None

        # Since features are disabled, the middleware should not set these attributes
        # We can't easily test hasattr with Mock, so we test that the response is valid
        # and no errors occurred during processing

    @pytest.mark.asyncio
    async def test_error_handling_graceful_degradation(self, context_middleware, mock_request):
        """Test graceful error handling when call_next raises exception."""

        async def failing_call_next(request):
            raise Exception("Simulated error")

        # The middleware should catch the exception and still call call_next
        # According to the middleware implementation, it catches exceptions in context setup
        # but still calls call_next, so the exception will propagate
        with pytest.raises(Exception, match="Simulated error"):
            await context_middleware.dispatch(mock_request, failing_call_next)

    def test_generate_request_id_uniqueness(self, context_middleware):
        """Test that generated request IDs are unique."""
        ids = set()
        for _ in range(1000):
            request_id = context_middleware._generate_request_id()
            assert request_id not in ids, "Request ID collision detected"
            ids.add(request_id)

    def test_determine_locale_priority_order(self, context_middleware, mock_request):
        """Test locale determination priority order."""
        # Test X-Locale header takes priority
        mock_request.headers = {
            "x-locale": "de",
            "accept-language": "fr-FR,fr;q=0.9,en;q=0.8",
        }

        locale = context_middleware._determine_locale(mock_request)
        assert locale == "de"

        # Test Accept-Language when X-Locale not present
        mock_request.headers = {"accept-language": "fr-FR,fr;q=0.9,en;q=0.8"}

        locale = context_middleware._determine_locale(mock_request)
        assert locale == "fr"

        # Test default when no headers
        mock_request.headers = {}

        locale = context_middleware._determine_locale(mock_request)
        assert locale == "en"


class TestContextUtilityFunctions:
    """Test context utility functions."""

    def test_context_functions_default_values(self):
        """Test context functions return default values when not in request context."""
        # These should return default values when not in request context
        assert get_request_id() == ""
        assert get_user_context() == {}
        assert get_locale() == "en"
        assert get_request_start_time() == 0.0
        assert get_request_duration() == 0.0

    @patch("src.middleware.context_middleware.request_id_var")
    def test_get_request_id(self, mock_request_id_var):
        """Test get_request_id function."""
        mock_request_id_var.get.return_value = "test-request-id"

        result = get_request_id()
        assert result == "test-request-id"
        mock_request_id_var.get.assert_called_once_with("")

    @patch("src.middleware.context_middleware.user_context_var")
    def test_get_user_context(self, mock_user_context_var):
        """Test get_user_context function."""
        test_context = {"id": "user123", "username": "testuser"}
        mock_user_context_var.get.return_value = test_context

        result = get_user_context()
        assert result == test_context
        mock_user_context_var.get.assert_called_once_with({})

    @patch("src.middleware.context_middleware.locale_context_var")
    def test_get_locale(self, mock_locale_var):
        """Test get_locale function."""
        mock_locale_var.get.return_value = "fr"

        result = get_locale()
        assert result == "fr"
        mock_locale_var.get.assert_called_once_with("en")

    @patch("src.middleware.context_middleware.request_start_time_var")
    @patch("src.middleware.context_middleware.time")
    def test_get_request_duration(self, mock_time, mock_start_time_var):
        """Test get_request_duration function."""
        mock_start_time_var.get.return_value = 100.0
        mock_time.time.return_value = 105.5

        result = get_request_duration()
        assert result == 5.5
        mock_start_time_var.get.assert_called_once_with(0.0)

    @patch("src.middleware.context_middleware.request_start_time_var")
    def test_get_request_duration_no_start_time(self, mock_start_time_var):
        """Test get_request_duration returns 0 when no start time."""
        mock_start_time_var.get.return_value = 0.0

        result = get_request_duration()
        assert result == 0.0
