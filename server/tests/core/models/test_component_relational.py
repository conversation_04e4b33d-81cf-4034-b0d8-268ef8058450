"""Unit tests for Component model with relational approach.

This module contains tests for the Component model using the new relational approach
with ComponentType and ComponentCategory tables.
"""

import json
import pytest
import uuid
from decimal import Decimal

from src.core.models.general.component import Component
from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory


class TestComponentRelationalModel:
    """Test suite for Component model with relational approach."""

    def generate_unique_component_data(self, base_manufacturer="ABB", base_model="S203-B16"):
        """Generate unique component data to avoid constraint violations."""
        unique_suffix = str(uuid.uuid4())[:8]
        return {"manufacturer": f"{base_manufacturer}_{unique_suffix}", "model_number": f"{base_model}_{unique_suffix}"}

    def create_test_component_type_and_category(
        self,
        db_session,
        type_name="Circuit Breaker",
        category_name="Protection Devices",
    ):
        """Helper method to create ComponentType and ComponentCategory for testing."""
        category = ComponentCategory(name=category_name, description=f"{category_name} category", is_active=True)
        db_session.add(category)
        db_session.flush()

        component_type = ComponentType(
            name=type_name,
            description=f"{type_name} type",
            category_id=category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        return component_type, category

    def test_component_creation_relational(self, db_session):
        """Test basic component creation with relational fields."""
        component_type, category = self.create_test_component_type_and_category(db_session)

        # Generate unique component data to avoid constraint violations
        unique_data = self.generate_unique_component_data("ABB", "S203-B16")

        component = Component(
            name="Test Circuit Breaker",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=category.id,
        )

        db_session.add(component)
        db_session.commit()

        assert component.id is not None
        assert component.name == "Test Circuit Breaker"
        assert component.manufacturer == unique_data["manufacturer"]
        assert component.model_number == unique_data["model_number"]
        assert component.component_type_id == component_type.id
        assert component.category_id == category.id
        assert component.is_active is True
        assert component.is_preferred is False
        assert component.currency == "EUR"
        assert component.stock_status == "available"
        assert component.version == "1.0"

    def test_component_relationships(self, db_session):
        """Test component relationships with type and category."""
        component_type, category = self.create_test_component_type_and_category(db_session)

        # Generate unique component data to avoid constraint violations
        unique_data = self.generate_unique_component_data("Test Manufacturer", "TEST-001")

        component = Component(
            name="Test Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=category.id,
        )

        db_session.add(component)
        db_session.commit()

        # Test relationships
        assert component.component_type_entity is not None
        assert component.component_type_entity.id == component_type.id
        assert component.component_type_entity.name == "Circuit Breaker"

        assert component.category_entity is not None
        assert component.category_entity.id == category.id
        assert component.category_entity.name == "Protection Devices"

    def test_component_category_validation(self, db_session):
        """Test category validation using relational approach."""
        component_type, category = self.create_test_component_type_and_category(db_session)

        # Generate unique component data to avoid constraint violations
        unique_data = self.generate_unique_component_data("Test Manufacturer", "TEST-002")

        component = Component(
            name="Test Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=category.id,
        )

        db_session.add(component)
        db_session.commit()

        # Test category validation
        assert component.is_category_valid() is True

    def test_component_required_fields(self, db_session):
        """Test that required fields are enforced."""
        # Test that component_type_id is required
        with pytest.raises(ValueError, match="component_type_id is required"):
            Component(
                name="Test Component",
                manufacturer="Test Manufacturer",
                model_number="TEST-001",
                category_id=1,
            )

        # Test that category_id is required
        with pytest.raises(ValueError, match="category_id is required"):
            Component(
                name="Test Component",
                manufacturer="Test Manufacturer",
                model_number="TEST-001",
                component_type_id=1,
            )

    def test_component_string_representations(self, db_session):
        """Test string representations with relational approach."""
        component_type, category = self.create_test_component_type_and_category(db_session)

        # Generate unique component data to avoid constraint violations
        unique_data = self.generate_unique_component_data("ABB", "S203-B16")

        component = Component(
            name="Test Circuit Breaker",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=category.id,
        )

        db_session.add(component)
        db_session.commit()

        # Test string representations
        assert "ABB" in str(component)
        assert unique_data["model_number"] in str(component)
        assert "Circuit Breaker" in str(component)

        assert "ABB" in repr(component)
        assert unique_data["model_number"] in repr(component)
        assert "Circuit Breaker" in repr(component)

    def test_component_with_specifications(self, db_session):
        """Test component creation with specifications."""
        specifications = {
            "electrical": {
                "voltage_rating": {"value": 400, "unit": "V"},
                "current_rating": {"value": 16, "unit": "A"},
            },
            "standards_compliance": ["IEC 60947-2"],
        }

        component_type, category = self.create_test_component_type_and_category(db_session)

        # Generate unique component data to avoid constraint violations
        unique_data = self.generate_unique_component_data("Schneider Electric", "C60N-B16")

        component = Component(
            name="Circuit Breaker with Specs",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=category.id,
            specifications=json.dumps(specifications),
        )

        db_session.add(component)
        db_session.commit()

        assert component.specifications is not None
        assert component.get_specification_value("electrical.voltage_rating.value") is None  # Basic test

        # Test specification helper methods
        component.set_specification_value("test_key", "test_value")
        assert component.get_specification_value("test_key") == "test_value"
