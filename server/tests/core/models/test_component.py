"""Unit tests for Component model.

This module contains comprehensive unit tests for the Component database model,
testing all functionality including validation, relationships, and business logic.
"""

import json
import pytest
import uuid
from decimal import Decimal
from datetime import datetime
from unittest.mock import Mock

from sqlalchemy.exc import IntegrityError

from src.core.models.general.component import Component
from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory


class TestComponentModel:
    """Test suite for Component model functionality."""

    def create_test_component_type_and_category(
        self,
        db_session,
        type_name="Circuit Breaker",
        category_name="Protection Devices",
    ):
        """Helper method to create ComponentType and ComponentCategory for testing."""
        # Make names unique to avoid conflicts
        unique_suffix = str(uuid.uuid4())[:8]
        unique_category_name = f"{category_name}_{unique_suffix}"
        unique_type_name = f"{type_name}_{unique_suffix}"

        category = ComponentCategory(name=unique_category_name, description=f"{category_name} category", is_active=True)
        db_session.add(category)
        db_session.flush()

        component_type = ComponentType(
            name=unique_type_name,
            description=f"{type_name} type",
            category_id=category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        return component_type, category

    def generate_unique_component_data(self, base_manufacturer="ABB", base_model="S203-B16"):
        """Generate unique component data to avoid constraint violations."""
        unique_suffix = str(uuid.uuid4())[:8]
        return {"manufacturer": f"{base_manufacturer}_{unique_suffix}", "model_number": f"{base_model}_{unique_suffix}"}

    def test_component_creation_basic(self, db_session):
        """Test basic component creation with required fields."""
        component_type, category = self.create_test_component_type_and_category(db_session)
        unique_data = self.generate_unique_component_data()

        component = Component(
            name="Test Circuit Breaker",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=category.id,
        )

        db_session.add(component)
        db_session.commit()

        assert component.id is not None
        assert component.name == "Test Circuit Breaker"
        assert component.manufacturer == unique_data["manufacturer"]
        assert component.model_number == unique_data["model_number"]
        assert component.component_type_id == component_type.id
        assert component.category_id == category.id
        assert component.is_active is True
        assert component.is_preferred is False
        assert component.currency == "EUR"
        assert component.stock_status == "available"
        assert component.version == "1.0"

    def test_component_auto_category_assignment(self, db_session):
        """Test relational approach - component type belongs to category."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        category = ComponentCategory(name=f"Loads_{unique_suffix}", description="Electrical loads", is_active=True)
        db_session.add(category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Electric Motor_{unique_suffix}",
            description="Electric motor load",
            category_id=category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Siemens", "1LA7090-4AA60")
        component = Component(
            name="Test Motor",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=category.id,
        )

        db_session.add(component)
        db_session.commit()

        assert component.component_type_entity.category_id == category.id

    def test_component_category_validation(self, db_session):
        """Test validation of category matching component type."""
        # Create categories and types that don't match
        unique_suffix = str(uuid.uuid4())[:8]
        load_category = ComponentCategory(name=f"Loads_{unique_suffix}", description="Electrical loads", is_active=True)
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add_all([load_category, protection_category])
        db_session.flush()

        motor_type = ComponentType(
            name=f"Electric Motor_{unique_suffix}",
            description="Electric motor load",
            category_id=load_category.id,  # Motor type should belong to load category
            is_active=True,
        )
        db_session.add(motor_type)
        db_session.flush()

        # This should work fine as we validate category consistency through the model
        unique_data = self.generate_unique_component_data("Siemens", "1LA7090-4AA60")
        component = Component(
            name="Test Motor",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=motor_type.id,
            category_id=load_category.id,  # Use the correct category
        )

        db_session.add(component)
        db_session.commit()

        # Verify the component is valid
        assert component.is_category_valid() is True

    def test_component_with_specifications(self, db_session):
        """Test component creation with electrical specifications."""
        specifications = {
            "electrical": {
                "voltage_rating": {"value": 400, "unit": "V"},
                "current_rating": {"value": 16, "unit": "A"},
                "power_rating": {"value": 3000, "unit": "W"},
            },
            "mechanical": {"mounting": "DIN rail", "protection_class": "IP20"},
            "standards_compliance": ["IEC 60947-2", "EN 60947-2"],
        }

        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Schneider Electric", "C60N-B16")
        component = Component(
            name="Circuit Breaker with Specs",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            category_id=protection_category.id,
            component_type_id=component_type.id,
            specifications=json.dumps(specifications),
            unit_price=Decimal("45.50"),
            currency="EUR",
        )

        db_session.add(component)
        db_session.commit()

        assert component.specifications is not None
        assert component.unit_price == Decimal("45.50")
        assert component.currency == "EUR"

    def test_component_with_physical_properties(self, db_session):
        """Test component with physical dimensions and weight."""
        dimensions = {
            "length": {"value": 85, "unit": "mm"},
            "width": {"value": 18, "unit": "mm"},
            "height": {"value": 78, "unit": "mm"},
        }

        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("ABB", "S201-B10")
        component = Component(
            name="Compact Circuit Breaker",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
            weight_kg=0.125,
            dimensions_json=json.dumps(dimensions),
        )

        db_session.add(component)
        db_session.commit()

        assert component.weight_kg == 0.125
        assert component.dimensions_json is not None

    def test_component_unique_constraint(self, db_session):
        """Test unique constraint on manufacturer + model_number."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        # Generate unique manufacturer/model for this test
        unique_data = self.generate_unique_component_data("ABB", "S203-B16")

        # Create first component
        component1 = Component(
            name="First Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
        )
        db_session.add(component1)
        db_session.commit()

        # Try to create duplicate with same manufacturer/model_number
        component2 = Component(
            name="Second Component",
            manufacturer=unique_data["manufacturer"],  # Same as component1
            model_number=unique_data["model_number"],  # Same as component1
            component_type_id=component_type.id,
            category_id=protection_category.id,
        )
        db_session.add(component2)

        with pytest.raises(IntegrityError):
            db_session.commit()

    def test_component_soft_delete(self, db_session):
        """Test soft delete functionality."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Test Manufacturer", "TEST-001")
        component = Component(
            name="Test Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
        )

        db_session.add(component)
        db_session.commit()

        # Soft delete
        component.is_deleted = True
        component.deleted_at = datetime.utcnow()
        db_session.commit()

        assert component.is_deleted is True
        assert component.deleted_at is not None

    def test_component_string_representations(self, db_session):
        """Test string representation methods."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("ABB", "S203-B16")
        component = Component(
            name="Test Circuit Breaker",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
        )

        db_session.add(component)
        db_session.commit()

        # Test __str__
        str_repr = str(component)
        assert unique_data["manufacturer"] in str_repr
        assert unique_data["model_number"] in str_repr
        assert "Circuit Breaker" in str_repr

        # Test __repr__
        repr_str = repr(component)
        assert "Component" in repr_str
        assert unique_data["manufacturer"] in repr_str
        assert unique_data["model_number"] in repr_str

    def test_component_properties(self, db_session):
        """Test component property methods."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("ABB", "S203-B16")
        component = Component(
            name="Custom Name",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
        )

        db_session.add(component)
        db_session.commit()

        # Test full_name property
        assert component.full_name == f"{unique_data['manufacturer']} {unique_data['model_number']}"

        # Test display_name property
        display_name = component.display_name
        assert "Custom Name" in display_name
        assert f"{unique_data['manufacturer']} {unique_data['model_number']}" in display_name

    def test_is_category_valid(self, db_session):
        """Test category validation method."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Siemens", "1LA7090-4AA60")
        component = Component(
            name="Test Motor",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
        )

        db_session.add(component)
        db_session.commit()

        assert component.is_category_valid() is True

    def test_specification_value_methods(self, db_session):
        """Test specification getter and setter methods."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Test Manufacturer", "TEST-001")
        component = Component(
            name="Test Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
        )

        db_session.add(component)
        db_session.commit()

        # Test setting specification value
        component.set_specification_value("voltage_rating", {"value": 230, "unit": "V"})
        db_session.commit()

        # Test getting specification value
        voltage_rating = component.get_specification_value("voltage_rating")
        assert voltage_rating == {"value": 230, "unit": "V"}

        # Test getting non-existent value with default
        non_existent = component.get_specification_value("non_existent", "default")
        assert non_existent == "default"

    def test_get_electrical_rating(self, db_session):
        """Test electrical rating retrieval method."""
        specifications = {
            "electrical": {
                "voltage_rating": {"value": 400, "unit": "V"},
                "current_rating": {"value": 16, "unit": "A"},
            }
        }

        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Test Manufacturer", "TEST-001")
        component = Component(
            name="Test Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
            specifications=json.dumps(specifications),
        )

        db_session.add(component)
        db_session.commit()

        voltage_rating = component.get_electrical_rating("voltage")
        assert voltage_rating == {"value": 400, "unit": "V"}

        # Test non-existent rating
        power_rating = component.get_electrical_rating("power")
        assert power_rating is None

    def test_is_compatible_with_standards(self, db_session):
        """Test standards compliance checking."""
        specifications = {"standards_compliance": ["IEC 60947-2", "EN 60947-2", "UL 489"]}

        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Test Manufacturer", "TEST-001")
        component = Component(
            name="Test Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
            specifications=json.dumps(specifications),
        )

        db_session.add(component)
        db_session.commit()

        # Test compatible standards
        assert component.is_compatible_with_standards(["IEC 60947-2"]) is True
        assert component.is_compatible_with_standards(["IEC 60947-2", "EN 60947-2"]) is True

        # Test incompatible standards
        assert component.is_compatible_with_standards(["IEC 60947-2", "UNKNOWN"]) is False
        assert component.is_compatible_with_standards(["UNKNOWN"]) is False

    def test_calculate_total_cost(self, db_session):
        """Test total cost calculation method."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Test Manufacturer", "TEST-001")
        component = Component(
            name="Test Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
            unit_price=Decimal("25.50"),
        )

        db_session.add(component)
        db_session.commit()

        # Test basic calculation
        total = component.calculate_total_cost(quantity=2)
        assert total == Decimal("51.00")

        # Test with tax
        total_with_tax = component.calculate_total_cost(quantity=2, include_tax=True, tax_rate=0.08)
        expected = Decimal("51.00") * Decimal("1.08")
        assert total_with_tax == expected.quantize(Decimal("0.01"))

        # Test with no unit price
        component.unit_price = None
        db_session.commit()

        total_no_price = component.calculate_total_cost(quantity=2)
        assert total_no_price is None

    def test_component_with_supplier_info(self, db_session):
        """Test component with supplier and part number information."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("ABB", "S203-B16")
        component = Component(
            name="Industrial Circuit Breaker",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
            supplier="RS Components",
            part_number="123-4567",
            unit_price=Decimal("42.75"),
            currency="GBP",
        )

        db_session.add(component)
        db_session.commit()

        assert component.supplier == "RS Components"
        assert component.part_number == "123-4567"
        assert component.unit_price == Decimal("42.75")
        assert component.currency == "GBP"

    def test_component_status_fields(self, db_session):
        """Test component status and preference fields."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Siemens", "3RT1015-1BB41")
        component = Component(
            name="Preferred Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
            is_active=True,
            is_preferred=True,
            stock_status="in_stock",
        )

        db_session.add(component)
        db_session.commit()

        assert component.is_active is True
        assert component.is_preferred is True
        assert component.stock_status == "in_stock"

    def test_component_version_and_metadata(self, db_session):
        """Test component version control and metadata fields."""
        metadata = {
            "import_source": "supplier_catalog",
            "last_updated": "2024-01-15",
            "quality_rating": 5,
            "notes": "High reliability component",
        }

        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Phoenix Contact", "REL-MR-24DC/21")
        component = Component(
            name="Versioned Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
            version="2.1",
            metadata_json=json.dumps(metadata),
        )

        db_session.add(component)
        db_session.commit()

        assert component.version == "2.1"
        assert component.metadata_json is not None

    def test_component_with_invalid_json_specifications(self, db_session):
        """Test component behavior with invalid JSON in specifications."""
        # Create necessary ComponentCategory and ComponentType entries
        unique_suffix = str(uuid.uuid4())[:8]
        protection_category = ComponentCategory(
            name=f"Protection Devices_{unique_suffix}",
            description="Electrical protection devices",
            is_active=True,
        )
        db_session.add(protection_category)
        db_session.flush()

        component_type = ComponentType(
            name=f"Circuit Breaker_{unique_suffix}",
            description="Circuit breaker protection device",
            category_id=protection_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.flush()

        unique_data = self.generate_unique_component_data("Test Manufacturer", "TEST-001")
        component = Component(
            name="Test Component",
            manufacturer=unique_data["manufacturer"],
            model_number=unique_data["model_number"],
            component_type_id=component_type.id,
            category_id=protection_category.id,
            specifications="invalid json string",
        )

        db_session.add(component)
        db_session.commit()

        # Should handle invalid JSON gracefully
        value = component.get_specification_value("test_key", "default")
        assert value == "default"

        # Setting value should create valid JSON
        component.set_specification_value("new_key", "new_value")
        db_session.commit()

        retrieved_value = component.get_specification_value("new_key")
        assert retrieved_value == "new_value"
