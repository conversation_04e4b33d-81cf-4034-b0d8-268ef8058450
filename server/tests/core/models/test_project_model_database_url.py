"""Unit tests for Project model database_url field functionality."""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.errors.exceptions import DataValidationError
from src.core.models.general.project import Project
from src.core.schemas.general.project_schemas import (
    ProjectCreateSchema,
    ProjectReadSchema,
    ProjectUpdateSchema,
)


class TestProjectDatabaseUrlModel:
    """Test cases for Project model database_url field."""

    def test_project_model_has_database_url_field(self) -> None:
        """Test that Project model has database_url field with correct type."""
        # Create a project instance
        project = Project(
            name="Test Project",
            project_number="TEST001",
            database_url="postgresql://user:pass@localhost/test_db",
        )

        # Verify field exists and has correct type
        assert hasattr(project, "database_url")
        assert project.database_url == "postgresql://user:pass@localhost/test_db"

    def test_project_model_database_url_nullable(self) -> None:
        """Test that database_url field can be None."""
        project = Project(name="Test Project", project_number="TEST001", database_url=None)

        assert project.database_url is None

    def test_project_model_database_url_empty_string(self) -> None:
        """Test that database_url field can be empty string."""
        project = Project(name="Test Project", project_number="TEST001", database_url="")

        assert project.database_url == ""


class TestProjectDatabaseUrlSchemas:
    """Test cases for Project schemas with database_url field."""

    def test_project_create_schema_with_database_url(self) -> None:
        """Test ProjectCreateSchema accepts database_url."""
        data = {
            "name": "Test Project",
            "description": "Test description",
            "database_url": "postgresql://user:pass@localhost/test_db",
        }

        schema = ProjectCreateSchema(**data)
        assert schema.database_url == "postgresql://user:pass@localhost/test_db"

    def test_project_create_schema_without_database_url(self) -> None:
        """Test ProjectCreateSchema works without database_url."""
        data = {"name": "Test Project", "description": "Test description"}

        schema = ProjectCreateSchema(**data)
        assert schema.database_url is None

    def test_project_update_schema_with_database_url(self) -> None:
        """Test ProjectUpdateSchema accepts database_url."""
        data = {"database_url": "postgresql+asyncpg://user:pass@localhost/test_db"}

        schema = ProjectUpdateSchema(**data)
        assert schema.database_url == "postgresql+asyncpg://user:pass@localhost/test_db"

    def test_project_read_schema_includes_database_url(self) -> None:
        """Test ProjectReadSchema includes database_url field."""
        # Create a mock project data
        project_data = {
            "id": 1,
            "name": "Test Project",
            "project_number": "TEST001",
            "description": "Test description",
            "status": "Draft",
            "client": None,
            "location": None,
            "is_offline": False,
            "database_url": "postgresql://user:pass@localhost/test_db",
            "members": [],
            "created_at": "2025-07-22T12:00:00Z",
            "updated_at": "2025-07-22T12:00:00Z",
        }

        schema = ProjectReadSchema(**project_data)
        assert schema.database_url == "postgresql://user:pass@localhost/test_db"


class TestProjectDatabaseUrlValidation:
    """Test cases for database_url field validation."""

    def test_valid_postgresql_url(self) -> None:
        """Test valid PostgreSQL URLs are accepted."""
        valid_urls = [
            "postgresql://user:pass@localhost/testdb",
            "postgresql+asyncpg://user:pass@localhost:5432/testdb",
            "postgresql://user@localhost/testdb",
            "postgresql://localhost/testdb",
        ]

        for url in valid_urls:
            data = {"name": "Test Project", "database_url": url}
            schema = ProjectCreateSchema(**data)
            assert schema.database_url == url

    def test_invalid_database_url_scheme(self) -> None:
        """Test invalid database URL schemes are rejected."""
        invalid_urls = [
            "mysql://user:pass@localhost/testdb",
            "http://localhost/testdb",
            "invalid://url",
            "just-a-string",
        ]

        for url in invalid_urls:
            with pytest.raises(ValueError) as exc_info:
                ProjectCreateSchema(name="Test Project", database_url=url)
            assert "Database URL must use supported schemes" in str(exc_info.value)

    def test_empty_database_url_normalized_to_none(self) -> None:
        """Test empty or whitespace-only database URL is normalized to None."""
        empty_values = ["", "   ", "\t\n"]

        for empty_value in empty_values:
            data = {"name": "Test Project", "database_url": empty_value}
            schema = ProjectCreateSchema(**data)
            assert schema.database_url is None

    def test_database_url_whitespace_stripped(self) -> None:
        """Test database URL whitespace is properly stripped."""
        data = {
            "name": "Test Project",
            "database_url": "  postgresql://user:pass@localhost/testdb  ",
        }

        schema = ProjectCreateSchema(**data)
        assert schema.database_url == "postgresql://user:pass@localhost/testdb"

    def test_update_schema_database_url_validation(self) -> None:
        """Test database URL validation in UpdateSchema."""
        # Valid URL
        data = {"database_url": "postgresql://user:pass@localhost/testdb"}
        schema = ProjectUpdateSchema(**data)
        assert schema.database_url == "postgresql://user:pass@localhost/testdb"

        # Invalid URL
        with pytest.raises(ValueError) as exc_info:
            ProjectUpdateSchema(database_url="invalid://url")
        assert "Database URL must use supported schemes" in str(exc_info.value)


class TestProjectDatabaseUrlModelValidation:
    """Test cases for model-level database_url validation."""

    def test_project_validates_with_valid_database_url(self) -> None:
        """Test Project model validation passes with valid database_url."""
        # This test would need to be run with actual SQLAlchemy session
        # For now, we test the basic field assignment
        project = Project(
            name="Valid Project",
            project_number="VALID001",
            database_url="postgresql://user:pass@localhost/valid_db",
        )

        # Basic validation - field assignment works
        assert project.database_url == "postgresql://user:pass@localhost/valid_db"
        assert project.name == "Valid Project"
        assert project.project_number == "VALID001"

    def test_project_model_repr_includes_database_info(self) -> None:
        """Test Project model string representation."""
        project = Project(
            name="Test Project",
            project_number="TEST001",
            database_url="postgresql://user:pass@localhost/test_db",
        )
        project.id = 123

        repr_str = repr(project)
        assert "Test Project" in repr_str
        assert "TEST001" in repr_str
        assert "123" in repr_str


class TestProjectDatabaseUrlIntegration:
    """Integration tests for database_url functionality."""

    @pytest.mark.asyncio
    async def test_project_creation_with_database_url(self) -> None:
        """Test creating project with database_url through full schema validation."""
        # Test data that would come from API request
        request_data = {
            "name": "Integration Test Project",
            "project_number": "INT001",
            "description": "Integration test with database URL",
            "database_url": "postgresql+asyncpg://user:pass@localhost:5432/integration_test",
        }

        # Validate through create schema
        create_schema = ProjectCreateSchema(**request_data)
        assert create_schema.database_url is not None
        assert create_schema.database_url.startswith("postgresql+asyncpg://")

        # Convert to model data (simulating service layer)
        model_data = create_schema.model_dump(exclude_unset=True)

        # Create model instance
        project = Project(**model_data)
        assert project.database_url == request_data["database_url"]

    def test_project_update_database_url(self) -> None:
        """Test updating project database_url through schema validation."""
        # Simulate updating a project's database_url
        update_data = {"database_url": "********************************************/new_db"}

        # Validate through update schema
        update_schema = ProjectUpdateSchema(**update_data)
        assert update_schema.database_url == update_data["database_url"]

        # Simulate applying update to existing project
        project = Project(
            name="Existing Project",
            project_number="EXIST001",
            database_url="********************************/old_db",
        )

        # Apply update
        for field, value in update_schema.model_dump(exclude_unset=True).items():
            setattr(project, field, value)

        assert project.database_url == update_data["database_url"]

    def test_project_disable_local_database(self) -> None:
        """Test removing local database URL (reverting to central database)."""
        # Create project with local database
        project = Project(
            name="Local DB Project",
            project_number="LOCAL001",
            database_url="postgresql://local:pass@localhost/local_db",
        )

        assert project.database_url is not None

        # Update to remove local database (use central database)
        update_data = {"database_url": None}
        update_schema = ProjectUpdateSchema(**update_data)

        # Apply update
        project.database_url = update_schema.database_url

        assert project.database_url is None
