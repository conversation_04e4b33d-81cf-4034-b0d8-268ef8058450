"""Unit tests for TaskRepository.

This module contains comprehensive unit tests for the TaskRepository class,
testing all database operations in isolation with proper mocking.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.enums import TaskPriority, TaskStatus
from src.core.models.general.task import Task, TaskAssignment
from src.core.models.general.project import Project
from src.core.models.general.user import User
from src.core.repositories.general.task_repository import TaskRepository
from src.core.utils.pagination_utils import PaginationParams, SortParams


@pytest.mark.asyncio
class TestTaskRepository:
    """Test suite for TaskRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def task_repository(self, mock_db_session):
        """Create a TaskRepository instance with mocked session."""
        return TaskRepository(mock_db_session)

    @pytest.fixture
    def sample_task(self):
        """Create a sample task for testing."""
        task = Task(
            id=1,
            task_id="test-task-uuid",
            project_id=1,
            title="Test Task",
            description="Test task description",
            priority=TaskPriority.MEDIUM,
            status=TaskStatus.NOT_STARTED,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            is_deleted=False,
        )
        task.assignments = []
        return task

    @pytest.fixture
    def sample_project(self):
        """Create a sample project for testing."""
        return Project(
            id=1,
            name="Test Project",
            project_number="TEST-001",
            description="Test project",
            status="active",
        )

    @pytest.fixture
    def sample_user(self):
        """Create a sample user for testing."""
        return User(
            id=1,
            name="Test User",
            email="<EMAIL>",
            is_active=True,
        )

    async def test_get_by_task_id_success(self, task_repository, mock_db_session, sample_task):
        """Test successful retrieval of task by task_id."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_task
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.get_by_task_id("test-task-uuid")

        # Assert
        assert result == sample_task
        mock_db_session.execute.assert_called_once()

    async def test_get_by_task_id_not_found(self, task_repository, mock_db_session):
        """Test retrieval of non-existent task by task_id."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.get_by_task_id("non-existent-uuid")

        # Assert
        assert result is None
        mock_db_session.execute.assert_called_once()

    async def test_get_all_by_project_id_success(self, task_repository, mock_db_session, sample_task):
        """Test successful retrieval of all tasks for a project."""
        # Setup
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = [sample_task]
        mock_result.scalars.return_value = mock_scalars
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.get_all_by_project_id(1)

        # Assert
        assert result == [sample_task]
        mock_db_session.execute.assert_called_once()

    async def test_get_all_by_project_id_include_deleted(self, task_repository, mock_db_session, sample_task):
        """Test retrieval of tasks including deleted ones."""
        # Setup
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = [sample_task]
        mock_result.scalars.return_value = mock_scalars
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.get_all_by_project_id(1, include_deleted=True)

        # Assert
        assert result == [sample_task]
        mock_db_session.execute.assert_called_once()

    @patch("src.core.repositories.general.task_repository.paginate_query_async")
    async def test_get_paginated_by_project_id(self, mock_paginate, task_repository, mock_db_session, sample_task):
        """Test paginated retrieval of tasks for a project."""
        # Setup
        pagination_params = PaginationParams(page=1, per_page=10)
        sort_params = SortParams(sort_by="created_at", sort_order="desc")
        filters = {"status": "Not Started"}

        mock_pagination_result = MagicMock()
        mock_pagination_result.items = [sample_task]
        mock_paginate.return_value = mock_pagination_result

        # Execute
        result = await task_repository.get_paginated_by_project_id(
            project_id=1, pagination_params=pagination_params, sort_params=sort_params, filters=filters
        )

        # Assert
        assert result == mock_pagination_result
        mock_paginate.assert_called_once()

    async def test_get_by_assigned_user(self, task_repository, mock_db_session, sample_task):
        """Test retrieval of tasks assigned to a specific user."""
        # Setup
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = [sample_task]
        mock_result.scalars.return_value = mock_scalars
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.get_by_assigned_user(1)

        # Assert
        assert result == [sample_task]
        mock_db_session.execute.assert_called_once()

    async def test_get_overdue_tasks(self, task_repository, mock_db_session, sample_task):
        """Test retrieval of overdue tasks."""
        # Setup
        sample_task.due_date = datetime.utcnow() - timedelta(days=1)  # Make it overdue
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = [sample_task]
        mock_result.scalars.return_value = mock_scalars
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.get_overdue_tasks()

        # Assert
        assert result == [sample_task]
        mock_db_session.execute.assert_called_once()

    async def test_get_overdue_tasks_with_project_filter(self, task_repository, mock_db_session, sample_task):
        """Test retrieval of overdue tasks filtered by project."""
        # Setup
        sample_task.due_date = datetime.utcnow() - timedelta(days=1)
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = [sample_task]
        mock_result.scalars.return_value = mock_scalars
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.get_overdue_tasks(project_id=1)

        # Assert
        assert result == [sample_task]
        mock_db_session.execute.assert_called_once()

    async def test_search_tasks(self, task_repository, mock_db_session, sample_task):
        """Test searching tasks by title and description."""
        # Setup
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = [sample_task]
        mock_result.scalars.return_value = mock_scalars
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.search_tasks("test")

        # Assert
        assert result == [sample_task]
        mock_db_session.execute.assert_called_once()

    async def test_search_tasks_with_project_filter(self, task_repository, mock_db_session, sample_task):
        """Test searching tasks with project filter."""
        # Setup
        mock_result = MagicMock()
        mock_scalars = MagicMock()
        mock_scalars.all.return_value = [sample_task]
        mock_result.scalars.return_value = mock_scalars
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.search_tasks("test", project_id=1)

        # Assert
        assert result == [sample_task]
        mock_db_session.execute.assert_called_once()

    async def test_update_task_status_success(self, task_repository, mock_db_session, sample_task):
        """Test successful task status update."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_task
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.update_task_status("test-task-uuid", "In Progress")

        # Assert
        assert result == sample_task
        assert sample_task.status == TaskStatus.IN_PROGRESS
        mock_db_session.flush.assert_called_once()

    async def test_update_task_status_not_found(self, task_repository, mock_db_session):
        """Test task status update for non-existent task."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.update_task_status("non-existent-uuid", "In Progress")

        # Assert
        assert result is None
        mock_db_session.flush.assert_not_called()

    async def test_soft_delete_by_task_id_success(self, task_repository, mock_db_session, sample_task):
        """Test successful soft deletion of task."""
        # Setup
        assignment = TaskAssignment(
            id=1,
            task_id=1,
            user_id=1,
            name="Test Assignment",
            is_active=True,
            is_deleted=False,
        )
        sample_task.assignments = [assignment]

        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = sample_task
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.soft_delete_by_task_id("test-task-uuid", 1)

        # Assert
        assert result is True
        assert sample_task.is_deleted is True
        assert sample_task.deleted_by_user_id == 1
        assert assignment.is_deleted is True
        assert assignment.deleted_by_user_id == 1
        mock_db_session.flush.assert_called_once()

    async def test_soft_delete_by_task_id_not_found(self, task_repository, mock_db_session):
        """Test soft deletion of non-existent task."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.soft_delete_by_task_id("non-existent-uuid", 1)

        # Assert
        assert result is False
        mock_db_session.flush.assert_not_called()

    async def test_get_task_statistics(self, task_repository, mock_db_session):
        """Test retrieval of task statistics for a project."""
        # Setup
        mock_result = MagicMock()
        mock_result.scalar.return_value = 5  # Mock count result
        mock_db_session.execute.return_value = mock_result

        # Execute
        result = await task_repository.get_task_statistics(1)

        # Assert
        assert isinstance(result, dict)
        assert "total_tasks" in result
        assert "status_counts" in result
        assert "priority_counts" in result
        assert "overdue_count" in result
        # Multiple execute calls for different statistics
        assert mock_db_session.execute.call_count > 1

    async def test_repository_initialization(self, mock_db_session):
        """Test TaskRepository initialization."""
        # Execute
        repository = TaskRepository(mock_db_session)

        # Assert
        assert repository.db_session == mock_db_session
        assert repository.model == Task
