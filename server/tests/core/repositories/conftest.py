"""Test fixtures for repository layer tests."""

import pytest
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal
from typing import Any, List
import json

from src.core.repositories.general.component_repository import ComponentRepository
from src.core.repositories.general.component_category_repository import (
    ComponentCategoryRepository,
)
from src.core.repositories.general.component_type_repository import (
    ComponentTypeRepository,
)
from src.core.models.general.component import Component
from src.core.models.general.component_category import ComponentCategory
from src.core.models.general.component_type import ComponentType as ComponentTypeModel
from src.core.repositories.general.project_member_repository import (
    ProjectMemberRepository,
)


@pytest.fixture
async def component_repository(async_db_session: AsyncSession) -> ComponentRepository:
    """Create a ComponentRepository instance for testing."""
    return ComponentRepository(async_db_session)


@pytest.fixture
async def component_category_repository(async_db_session: AsyncSession) -> ComponentCategoryRepository:
    """Create ComponentCategoryRepository instance."""
    return ComponentCategoryRepository(async_db_session)


@pytest.fixture
async def component_type_repository(async_db_session: AsyncSession) -> ComponentTypeRepository:
    """Create ComponentTypeRepository instance."""
    return ComponentTypeRepository(async_db_session)


@pytest.fixture
async def project_member_repository(async_db_session: AsyncSession) -> ProjectMemberRepository:
    """Create a ProjectMemberRepository instance for testing."""
    return ProjectMemberRepository(async_db_session)


@pytest.fixture
async def sample_category(async_db_session: AsyncSession) -> ComponentCategory:
    """Create a sample category for testing."""
    category = ComponentCategory(
        name="Sample Category",
        description="Sample description",
        is_active=True,
    )
    async_db_session.add(category)
    await async_db_session.commit()
    await async_db_session.refresh(category)
    # Eagerly load attributes to prevent lazy loading in other fixtures
    _ = category.id
    _ = category.name
    _ = category.description
    return category


@pytest.fixture
async def sample_component_type(
    async_db_session: AsyncSession, sample_category: ComponentCategory
) -> ComponentTypeModel:
    """Create a sample component type for testing."""
    # Merge the sample_category to ensure it's bound to the current session
    merged_category = await async_db_session.merge(sample_category)
    await async_db_session.flush()  # Ensure the merge is persisted

    component_type = ComponentTypeModel(
        name="Sample Type",
        description="Sample description",
        category_id=merged_category.id,
        is_active=True,
    )
    async_db_session.add(component_type)
    await async_db_session.commit()
    await async_db_session.refresh(component_type)
    # Eagerly load attributes to prevent lazy loading in other fixtures
    _ = component_type.id
    _ = component_type.name
    _ = component_type.description
    _ = component_type.category_id
    return component_type


@pytest.fixture
async def sample_component(async_db_session: AsyncSession, sample_component_type: ComponentTypeModel):
    """Create a sample component for testing."""
    # Merge the sample_component_type to ensure it's bound to the current session
    merged_component_type = await async_db_session.merge(sample_component_type)
    await async_db_session.flush()  # Ensure the merge is persisted

    unique_id = uuid.uuid4().hex[:8]
    component = Component(
        name="Test Circuit Breaker",
        manufacturer="ABB",
        model_number=f"S203-B16-{unique_id}",
        component_type_id=merged_component_type.id,
        category_id=merged_component_type.category_id,
        unit_price=Decimal("45.50"),
        currency="EUR",
        supplier="RS Components",
        part_number=f"123-4567-{unique_id}",
    )
    async_db_session.add(component)
    await async_db_session.commit()
    await async_db_session.refresh(component)
    # Eagerly load attributes to prevent lazy loading in other fixtures
    _ = component.id
    _ = component.name
    _ = component.manufacturer
    _ = component.component_type_id
    _ = component.category_id
    return component


@pytest.fixture
async def multiple_components(async_db_session: AsyncSession, multiple_component_categories, multiple_component_types):
    """Create multiple components for testing."""
    # Merge categories and types to ensure they're bound to the current session
    merged_categories = []
    for cat in multiple_component_categories:
        merged_cat = await async_db_session.merge(cat)
        merged_categories.append(merged_cat)

    merged_types = []
    for ct in multiple_component_types:
        merged_type = await async_db_session.merge(ct)
        merged_types.append(merged_type)
    await async_db_session.flush()  # Ensure all merges are persisted

    # Get the different categories and types
    protection_category = next(cat for cat in merged_categories if cat.name == "Protection Devices")
    control_category = next(cat for cat in merged_categories if cat.name == "Control Equipment")
    power_category = next(cat for cat in merged_categories if cat.name == "Power Distribution")

    # Get types
    mcb_type = next(t for t in merged_types if t.name == "Miniature Circuit Breaker")
    mccb_type = next(t for t in merged_types if t.name == "Molded Case Circuit Breaker")

    unique_id = uuid.uuid4().hex[:8]
    components = [
        Component(
            name="Circuit Breaker 1",
            manufacturer="ABB",
            model_number=f"S203-B16-{unique_id}",
            component_type_id=mcb_type.id,
            category_id=protection_category.id,
            unit_price=Decimal("45.50"),
            is_preferred=True,
        ),
        Component(
            name="Circuit Breaker 2",
            manufacturer="Schneider Electric",
            model_number=f"C60N-B20-{unique_id}",
            component_type_id=mcb_type.id,
            category_id=protection_category.id,
            unit_price=Decimal("52.00"),
            is_preferred=False,
        ),
        Component(
            name="Motor 1",
            manufacturer="Siemens",
            model_number=f"1LA7090-4AA60-{unique_id}",
            component_type_id=mccb_type.id,
            category_id=control_category.id,
            unit_price=Decimal("1250.00"),
            is_preferred=True,
        ),
        Component(
            name="Contactor 1",
            manufacturer="ABB",
            model_number=f"A26-30-10-{unique_id}",
            component_type_id=mccb_type.id,
            category_id=power_category.id,
            unit_price=Decimal("85.00"),
            is_preferred=False,
        ),
    ]

    for component in components:
        async_db_session.add(component)
    await async_db_session.commit()
    return components


@pytest.fixture(scope="function")
async def sample_component_data() -> dict[str, Any]:
    """Sample component data for testing."""
    unique_id = uuid.uuid4().hex[:8]
    return {
        "name": "Test Circuit Breaker",
        "manufacturer": "ABB",
        "model_number": f"S203-C16-{unique_id}",
        "description": "3-pole miniature circuit breaker, 16A, C-curve",
        "specifications": {
            "electrical": {
                "current_rating": "16A",
                "voltage_rating": "400V",
                "breaking_capacity": "6kA",
                "curve_type": "C",
            },
            "standards_compliance": ["IEC-60898-1", "EN-60898-1"],
        },
        "unit_price": "25.50",
        "currency": "EUR",
        "supplier": "Electrical Supplies Ltd",
        "part_number": f"ABB-S203-C16-{unique_id}",
        "weight_kg": 0.15,
        "dimensions": {"length": 18, "width": 85, "height": 78},
        "is_active": True,
        "is_preferred": False,
        "stock_status": "available",
        "version": "1.0",
        "metadata": {"test_data": True, "created_for": "unit_testing"},
    }


@pytest.fixture(scope="function")
async def large_component_dataset(async_db_session, sample_component_type: ComponentTypeModel):
    """Create a large dataset of components for performance testing."""
    components = []
    unique_id = uuid.uuid4().hex[:8]

    # Create 1000 components for performance testing
    for i in range(1000):
        component = Component(
            name=f"Component {i:04d}",
            manufacturer=f"Manufacturer {i % 10}",
            model_number=f"MODEL-{i:04d}-{unique_id}",
            component_type_id=sample_component_type.id,
            category_id=sample_component_type.category_id,
            unit_price=Decimal(f"{10 + (i % 100)}.50"),
            currency="EUR",
            supplier=f"Supplier {i % 5}",
            part_number=f"PART-{i:04d}-{unique_id}",
            description=f"Test component {i} for performance testing",
            specifications=json.dumps(
                {
                    "electrical": {
                        "voltage_rating": f"{400 + (i % 100)}V",
                        "current_rating": f"{16 + (i % 50)}A",
                    },
                    "physical": {
                        "width": f"{50 + (i % 20)}mm",
                        "height": f"{80 + (i % 30)}mm",
                    },
                }
            ),
            is_preferred=(i % 10 == 0),
            is_active=True,
        )
        components.append(component)

    # Batch insert for better performance
    async_db_session.add_all(components)
    await async_db_session.commit()

    return components


@pytest.fixture(scope="function")
async def sample_component_category_data() -> dict[str, Any]:
    """Sample component category data for testing."""
    return {
        "name": "Test Protection Devices",
        "description": "Test category for protection devices",
        "parent_category_id": None,
        "is_active": True,
    }


@pytest.fixture(scope="function")
async def sample_component_type_data() -> dict[str, Any]:
    """Sample component type data for testing."""
    return {
        "name": "Test Circuit Breaker Type",
        "description": "Test type for circuit breakers",
        "category_id": 1,  # Will be set dynamically in tests
        "is_active": True,
        "specifications_template": {
            "electrical": {
                "current_rating": {"type": "string", "required": True},
                "voltage_rating": {"type": "string", "required": True},
                "breaking_capacity": {"type": "string", "required": False},
            },
            "physical": {
                "width": {"type": "number", "required": False},
                "height": {"type": "number", "required": False},
                "depth": {"type": "number", "required": False},
            },
        },
        "metadata": {"test_data": True, "created_for": "unit_testing"},
    }


@pytest.fixture(scope="function")
async def component_category_fixture(async_db_session) -> ComponentCategory:
    """Create a test component category in the database."""
    category = ComponentCategory(
        name="Test Protection Devices",
        description="Test category for protection devices",
        parent_category_id=None,
        is_active=True,
    )
    async_db_session.add(category)
    await async_db_session.commit()
    async_db_session.refresh(category)
    return category


@pytest.fixture(scope="function")
async def component_type_fixture(async_db_session, component_category_fixture) -> ComponentTypeModel:
    """Create a test component type in the database."""
    component_type = ComponentTypeModel(
        name="Test Circuit Breaker Type",
        description="Test type for circuit breakers",
        category_id=component_category_fixture.id,
        is_active=True,
        specifications_template=json.dumps(
            {
                "electrical": {
                    "current_rating": {"type": "string", "required": True},
                    "voltage_rating": {"type": "string", "required": True},
                    "breaking_capacity": {"type": "string", "required": False},
                }
            }
        ),
        metadata_json=json.dumps({"test_data": True, "created_for": "unit_testing"}),
    )
    async_db_session.add(component_type)
    await async_db_session.commit()
    async_db_session.refresh(component_type)
    return component_type


@pytest.fixture(scope="function")
async def multiple_component_categories(async_db_session) -> List[ComponentCategory]:
    """Create multiple test component categories for testing hierarchies."""
    categories = [
        ComponentCategory(
            name="Power Distribution",
            description="Power distribution components",
            parent_category_id=None,
            is_active=True,
        ),
        ComponentCategory(
            name="Protection Devices",
            description="Electrical protection devices",
            parent_category_id=None,
            is_active=True,
        ),
        ComponentCategory(
            name="Control Equipment",
            description="Control and automation equipment",
            parent_category_id=None,
            is_active=True,
        ),
    ]

    async_db_session.add_all(categories)
    await async_db_session.commit()

    # Refresh to get IDs and load attributes
    for category in categories:
        await async_db_session.refresh(category)
        # Eagerly load attributes to prevent lazy loading in other fixtures
        _ = category.id
        _ = category.name
        _ = category.description

    # Add subcategories
    subcategories = [
        ComponentCategory(
            name="Circuit Breakers",
            description="Circuit breaker subcategory",
            parent_category_id=categories[1].id,  # Protection Devices
            is_active=True,
        ),
        ComponentCategory(
            name="Fuses",
            description="Fuse subcategory",
            parent_category_id=categories[1].id,  # Protection Devices
            is_active=True,
        ),
    ]

    async_db_session.add_all(subcategories)
    await async_db_session.commit()

    for subcategory in subcategories:
        await async_db_session.refresh(subcategory)
        # Eagerly load attributes to prevent lazy loading in other fixtures
        _ = subcategory.id
        _ = subcategory.name
        _ = subcategory.description

    # Combine and return all categories
    all_categories = categories + subcategories

    # Ensure all categories have their attributes loaded
    for category in all_categories:
        _ = category.id
        _ = category.name
        _ = category.description

    return all_categories


@pytest.fixture(scope="function")
async def multiple_component_types(async_db_session, multiple_component_categories) -> List[ComponentTypeModel]:
    """Create multiple test component types for testing."""
    # Merge categories to ensure they're bound to the current session
    merged_categories = []
    for cat in multiple_component_categories:
        merged_cat = await async_db_session.merge(cat)
        merged_categories.append(merged_cat)
    await async_db_session.flush()  # Ensure all merges are persisted

    # Get the Protection Devices category by accessing attributes safely
    protection_category = None
    for cat in merged_categories:
        if cat.name == "Protection Devices":
            protection_category = cat
            break

    if protection_category is None:
        raise ValueError("Protection Devices category not found")

    component_types = [
        ComponentTypeModel(
            name="Miniature Circuit Breaker",
            description="MCB for residential and commercial use",
            category_id=protection_category.id,
            is_active=True,
            specifications_template=json.dumps(
                {
                    "electrical": {
                        "current_rating": {"type": "string", "required": True},
                        "voltage_rating": {"type": "string", "required": True},
                        "curve_type": {"type": "string", "required": True},
                    }
                }
            ),
        ),
        ComponentTypeModel(
            name="Molded Case Circuit Breaker",
            description="MCCB for industrial applications",
            category_id=protection_category.id,
            is_active=True,
            specifications_template=json.dumps(
                {
                    "electrical": {
                        "current_rating": {"type": "string", "required": True},
                        "voltage_rating": {"type": "string", "required": True},
                        "breaking_capacity": {"type": "string", "required": True},
                    }
                }
            ),
        ),
        ComponentTypeModel(
            name="HRC Fuse",
            description="High rupturing capacity fuse",
            category_id=protection_category.id,
            is_active=True,
            specifications_template=json.dumps(
                {
                    "electrical": {
                        "current_rating": {"type": "string", "required": True},
                        "voltage_rating": {"type": "string", "required": True},
                    }
                }
            ),
        ),
    ]

    async_db_session.add_all(component_types)
    await async_db_session.commit()

    for component_type in component_types:
        await async_db_session.refresh(component_type)
        # Eagerly load attributes to prevent lazy loading in other fixtures
        _ = component_type.id
        _ = component_type.name
        _ = component_type.description
        _ = component_type.category_id

    return component_types
