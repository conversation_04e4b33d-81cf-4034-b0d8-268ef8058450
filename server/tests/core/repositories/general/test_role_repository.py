"""
Comprehensive test suite for Role Repository

Tests all RBAC repository operations including role management,
permission assignments, and database interactions.
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List

from src.core.repositories.general.role_repository import RoleRepository
from src.core.models.general.user_role import UserRole
from src.core.models.general.permission import Permission, RolePermission
from src.core.models.general.user import User
from src.core.errors.exceptions import NotFoundError, ValidationError, BusinessLogicError


class TestRoleRepository:
    """Test suite for RoleRepository"""

    @pytest.fixture
    async def role_repository(self, db_session: AsyncSession):
        """Create RoleRepository with database session"""
        return RoleRepository(db_session)

    @pytest.fixture
    async def sample_user(self, db_session: AsyncSession):
        """Create sample user in database"""
        user = User(
            name="Test User",
            email="<EMAIL>",
            password_hash="password_hash",
            is_active=True,
            is_superuser=False,
        )
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        return user

    @pytest.fixture
    async def sample_role(self, db_session: AsyncSession):
        """Create sample role in database"""
        role = UserRole(
            name="Test Role",
            description="Test role for unit tests",
            is_system_role=False,
            is_active=True,
            priority=100,
        )
        db_session.add(role)
        await db_session.commit()
        await db_session.refresh(role)
        return role

    @pytest.fixture
    async def sample_permission(self, db_session: AsyncSession):
        """Create sample permission in database"""
        permission = Permission(
            name="Read Projects",
            resource="project",
            action="read",
            description="Permission to read projects",
            is_system_permission=False,
            is_active=True,
        )
        db_session.add(permission)
        await db_session.commit()
        await db_session.refresh(permission)
        return permission

    async def test_create_role_success(self, role_repository, db_session):
        """Test successful role creation"""
        # Arrange
        role_data = UserRole(
            name="New Role",
            description="New test role",
            priority=150,
        )

        # Act
        result = await role_repository.create_role(role_data)

        # Assert
        assert result is not None
        assert result.id is not None
        assert result.name == "New Role"
        assert result.description == "New test role"
        assert result.priority == 150
        assert result.is_active is True
        assert result.is_system_role is False

        # Verify in database
        db_role = await db_session.get(UserRole, result.id)
        assert db_role is not None
        assert db_role.name == "New Role"

    async def test_create_role_duplicate_name_raises_error(self, role_repository, sample_role):
        """Test that creating role with duplicate name raises ValidationError"""
        # Arrange
        duplicate_role = UserRole(
            name=sample_role.name,  # Same name as existing role
            description="Duplicate role",
            priority=200,
        )

        # Act & Assert
        with pytest.raises(ValidationError, match="Role name already exists"):
            await role_repository.create_role(duplicate_role)

    async def test_get_role_by_id_success(self, role_repository, sample_role):
        """Test successful role retrieval by ID"""
        # Act
        result = await role_repository.get_role_by_id(sample_role.id)

        # Assert
        assert result is not None
        assert result.id == sample_role.id
        assert result.name == sample_role.name
        assert result.description == sample_role.description

    async def test_get_role_by_id_not_found(self, role_repository):
        """Test role retrieval with non-existent ID"""
        # Act
        result = await role_repository.get_role_by_id(999)

        # Assert
        assert result is None

    async def test_get_role_by_name_success(self, role_repository, sample_role):
        """Test successful role retrieval by name"""
        # Act
        result = await role_repository.get_role_by_name(sample_role.name)

        # Assert
        assert result is not None
        assert result.id == sample_role.id
        assert result.name == sample_role.name

    async def test_get_role_by_name_not_found(self, role_repository):
        """Test role retrieval with non-existent name"""
        # Act
        result = await role_repository.get_role_by_name("Non-existent Role")

        # Assert
        assert result is None

    async def test_update_role_success(self, role_repository, sample_role, db_session):
        """Test successful role update"""
        # Arrange
        sample_role.name = "Updated Role"
        sample_role.description = "Updated description"
        sample_role.priority = 250

        # Act
        result = await role_repository.update_role(sample_role)

        # Assert
        assert result is not None
        assert result.name == "Updated Role"
        assert result.description == "Updated description"
        assert result.priority == 250

        # Verify in database
        await db_session.refresh(sample_role)
        assert sample_role.name == "Updated Role"

    async def test_delete_role_success(self, role_repository, sample_role, db_session):
        """Test successful role deletion (soft delete)"""
        # Act
        result = await role_repository.delete_role(sample_role.id)

        # Assert
        assert result is True

        # Verify soft delete in database
        await db_session.refresh(sample_role)
        assert sample_role.is_active is False

    async def test_delete_role_not_found(self, role_repository):
        """Test deleting non-existent role"""
        # Act
        result = await role_repository.delete_role(999)

        # Assert
        assert result is False

    async def test_get_all_roles(self, role_repository, db_session):
        """Test getting all active roles"""
        # Arrange - Create multiple roles
        roles_data = [
            UserRole(name="Role 1", priority=100),
            UserRole(name="Role 2", priority=200),
            UserRole(name="Inactive Role", priority=300, is_active=False),
        ]
        for role in roles_data:
            db_session.add(role)
        await db_session.commit()

        # Act
        result = await role_repository.get_all_roles()

        # Assert
        assert len(result) >= 2  # At least our 2 active roles
        active_role_names = [role.name for role in result if role.is_active]
        assert "Role 1" in active_role_names
        assert "Role 2" in active_role_names
        assert "Inactive Role" not in active_role_names

    async def test_create_permission_success(self, role_repository, db_session):
        """Test successful permission creation"""
        # Arrange
        permission_data = Permission(
            name="Write Projects",
            resource="project",
            action="write",
            description="Permission to write projects",
        )

        # Act
        result = await role_repository.create_permission(permission_data)

        # Assert
        assert result is not None
        assert result.id is not None
        assert result.name == "Write Projects"
        assert result.resource == "project"
        assert result.action == "write"
        assert result.is_active is True

        # Verify in database
        db_permission = await db_session.get(Permission, result.id)
        assert db_permission is not None
        assert db_permission.name == "Write Projects"

    async def test_create_permission_duplicate_resource_action_raises_error(self, role_repository, sample_permission):
        """Test that creating permission with duplicate resource+action raises ValidationError"""
        # Arrange
        duplicate_permission = Permission(
            name="Another Read Projects",
            resource=sample_permission.resource,
            action=sample_permission.action,
        )

        # Act & Assert
        with pytest.raises(ValidationError, match="Permission already exists"):
            await role_repository.create_permission(duplicate_permission)

    async def test_get_all_permissions(self, role_repository, db_session):
        """Test getting all active permissions"""
        # Arrange - Create multiple permissions
        permissions_data = [
            Permission(name="Read Users", resource="user", action="read"),
            Permission(name="Write Users", resource="user", action="write"),
            Permission(name="Inactive Permission", resource="test", action="test", is_active=False),
        ]
        for permission in permissions_data:
            db_session.add(permission)
        await db_session.commit()

        # Act
        result = await role_repository.get_all_permissions()

        # Assert
        assert len(result) >= 2  # At least our 2 active permissions
        active_permission_names = [perm.name for perm in result if perm.is_active]
        assert "Read Users" in active_permission_names
        assert "Write Users" in active_permission_names
        assert "Inactive Permission" not in active_permission_names

    async def test_assign_permissions_to_role_success(self, role_repository, sample_role, db_session):
        """Test successful permission assignment to role"""
        # Arrange - Create permissions
        permissions = [
            Permission(name="Read", resource="test", action="read"),
            Permission(name="Write", resource="test", action="write"),
        ]
        for perm in permissions:
            db_session.add(perm)
        await db_session.commit()

        permission_ids = [perm.id for perm in permissions]

        # Act
        result = await role_repository.assign_permissions_to_role(sample_role.id, permission_ids, assigned_by_user_id=1)

        # Assert
        assert result is True

        # Verify in database
        role_permissions = await db_session.execute(
            select(RolePermission).where(RolePermission.role_id == sample_role.id)
        )
        role_perms = role_permissions.scalars().all()
        assert len(role_perms) == 2
        assert all(rp.is_active for rp in role_perms)

    async def test_assign_permissions_to_nonexistent_role_raises_error(self, role_repository):
        """Test that assigning permissions to non-existent role raises NotFoundError"""
        # Act & Assert
        with pytest.raises(NotFoundError, match="Role not found"):
            await role_repository.assign_permissions_to_role(999, [1, 2, 3])

    async def test_get_role_permissions(self, role_repository, sample_role, sample_permission, db_session):
        """Test getting permissions for a specific role"""
        # Arrange - Assign permission to role
        role_permission = RolePermission(
            role_id=sample_role.id,
            permission_id=sample_permission.id,
            granted_by_user_id=1,
            is_active=True,
        )
        db_session.add(role_permission)
        await db_session.commit()

        # Act
        result = await role_repository.get_role_permissions(sample_role.id)

        # Assert
        assert len(result) == 1
        assert f"{sample_permission.resource}.{sample_permission.action}" in result

    async def test_user_has_permission_superuser(self, role_repository, db_session):
        """Test that superuser always has permissions"""
        # Arrange - Create superuser
        superuser = User(
            name="Super User",
            email="<EMAIL>",
            password_hash="password_hash",
            is_superuser=True,
        )
        db_session.add(superuser)
        await db_session.commit()

        # Act
        result = await role_repository.user_has_permission(superuser.id, "any_resource", "any_action")

        # Assert
        assert result is True

    async def test_user_has_permission_regular_user_with_permission(
        self, role_repository, sample_user, sample_role, sample_permission, db_session
    ):
        """Test permission check for regular user with permission"""
        # Arrange - Assign role to user and permission to role
        from src.core.models.general.user_role_assignment import UserRoleAssignment

        # Create user role assignment
        assignment = UserRoleAssignment(
            user_id=sample_user.id,
            role_id=sample_role.id,
            assigned_by_user_id=sample_user.id,
            is_active=True,
        )
        db_session.add(assignment)

        # Create role permission assignment
        role_permission = RolePermission(
            role_id=sample_role.id,
            permission_id=sample_permission.id,
            granted_by_user_id=sample_user.id,
            is_active=True,
        )
        db_session.add(role_permission)
        await db_session.commit()

        # Act
        result = await role_repository.user_has_permission(
            sample_user.id, sample_permission.resource, sample_permission.action
        )

        # Assert
        assert result is True

    async def test_user_has_permission_regular_user_without_permission(self, role_repository, sample_user):
        """Test permission check for regular user without permission"""
        # Act
        result = await role_repository.user_has_permission(sample_user.id, "nonexistent_resource", "nonexistent_action")

        # Assert
        assert result is False

    async def test_get_user_permissions(self, role_repository, sample_user, sample_role, db_session):
        """Test getting all permissions for a user"""
        # Arrange - Create permissions and assign them
        permissions = [
            Permission(name="Read Projects", resource="project", action="read"),
            Permission(name="Write Projects", resource="project", action="write"),
        ]
        for perm in permissions:
            db_session.add(perm)
        await db_session.commit()

        # Assign role to user
        from src.core.models.general.user_role_assignment import UserRoleAssignment

        assignment = UserRoleAssignment(
            name=f"User Permissions Test Assignment {sample_user.id}-{sample_role.id}",
            user_id=sample_user.id,
            role_id=sample_role.id,
            assigned_by_user_id=sample_user.id,
            is_active=True,
        )
        db_session.add(assignment)

        # Assign permissions to role
        for perm in permissions:
            role_permission = RolePermission(
                role_id=sample_role.id,
                permission_id=perm.id,
                granted_by_user_id=sample_user.id,
                is_active=True,
            )
            db_session.add(role_permission)
        await db_session.commit()

        # Act
        result = await role_repository.get_user_permissions(sample_user.id)

        # Assert
        assert len(result) == 2
        assert "project.read" in result
        assert "project.write" in result

    async def test_assign_role_to_user_success(self, role_repository, sample_user, sample_role, db_session):
        """Test successful role assignment to user"""
        # Arrange
        expires_at = datetime.utcnow() + timedelta(days=30)

        # Act
        result = await role_repository.assign_role_to_user(
            user_id=sample_user.id,
            role_id=sample_role.id,
            expires_at=expires_at,
            assigned_by_user_id=1,
        )

        # Assert
        assert result is True

        # Verify in database
        from src.core.models.general.user_role_assignment import UserRoleAssignment

        assignment = await db_session.execute(
            select(UserRoleAssignment).where(
                UserRoleAssignment.user_id == sample_user.id, UserRoleAssignment.role_id == sample_role.id
            )
        )
        assignment_obj = assignment.scalar_one_or_none()
        assert assignment_obj is not None
        assert assignment_obj.is_active is True
        assert assignment_obj.expires_at == expires_at

    async def test_remove_role_from_user_success(self, role_repository, sample_user, sample_role, db_session):
        """Test successful role removal from user"""
        # Arrange - First assign role to user
        from src.core.models.general.user_role_assignment import UserRoleAssignment

        assignment = UserRoleAssignment(
            name=f"Test Assignment {sample_user.id}-{sample_role.id}",
            user_id=sample_user.id,
            role_id=sample_role.id,
            assigned_by_user_id=sample_user.id,
            is_active=True,
        )
        db_session.add(assignment)
        await db_session.commit()

        # Act
        result = await role_repository.remove_role_from_user(sample_user.id, sample_role.id)

        # Assert
        assert result is True

        # Verify in database
        await db_session.refresh(assignment)
        assert assignment.is_active is False

    async def test_get_user_active_roles(self, role_repository, sample_user, sample_role, db_session):
        """Test getting active roles for a user"""
        # Arrange - Assign roles to user
        from src.core.models.general.user_role_assignment import UserRoleAssignment

        # Create additional role
        role2 = UserRole(name="Role 2", priority=200)
        db_session.add(role2)
        await db_session.commit()

        # Active assignment
        assignment1 = UserRoleAssignment(
            name=f"Active Assignment {sample_user.id}-{sample_role.id}",
            user_id=sample_user.id,
            role_id=sample_role.id,
            assigned_by_user_id=sample_user.id,
            is_active=True,
        )
        # Inactive assignment
        assignment2 = UserRoleAssignment(
            name=f"Inactive Assignment {sample_user.id}-{role2.id}",
            user_id=sample_user.id,
            role_id=role2.id,
            assigned_by_user_id=sample_user.id,
            is_active=False,
        )
        db_session.add_all([assignment1, assignment2])
        await db_session.commit()

        # Act
        result = await role_repository.get_user_active_roles(sample_user.id)

        # Assert
        assert len(result) == 1
        assert result[0].id == sample_role.id
        assert result[0].name == sample_role.name

    async def test_get_all_permission_strings(self, role_repository, db_session):
        """Test getting all permission strings"""
        # Arrange - Create permissions
        permissions = [
            Permission(name="Read", resource="project", action="read"),
            Permission(name="Write", resource="project", action="write"),
            Permission(name="Delete", resource="user", action="delete"),
        ]
        for perm in permissions:
            db_session.add(perm)
        await db_session.commit()

        # Act
        result = await role_repository.get_all_permission_strings()

        # Assert
        assert "project.read" in result
        assert "project.write" in result
        assert "user.delete" in result

    async def test_role_hierarchy_permissions(self, role_repository, db_session):
        """Test role hierarchy and permission inheritance"""
        # Arrange - Create role hierarchy
        parent_role = UserRole(name="Parent Role", priority=100)
        child_role = UserRole(name="Child Role", parent_role_id=None, priority=200)

        db_session.add_all([parent_role, child_role])
        await db_session.commit()

        # Set parent relationship
        child_role.parent_role_id = parent_role.id
        await db_session.commit()

        # Create permissions
        parent_permission = Permission(name="Parent Perm", resource="parent", action="read")
        child_permission = Permission(name="Child Perm", resource="child", action="read")

        db_session.add_all([parent_permission, child_permission])
        await db_session.commit()

        # Assign permissions to roles
        parent_role_perm = RolePermission(
            role_id=parent_role.id,
            permission_id=parent_permission.id,
            granted_by_user_id=1,
        )
        child_role_perm = RolePermission(
            role_id=child_role.id,
            permission_id=child_permission.id,
            granted_by_user_id=1,
        )
        db_session.add_all([parent_role_perm, child_role_perm])
        await db_session.commit()

        # Act - Get child role permissions (should include inherited ones)
        result = await role_repository.get_role_permissions(child_role.id)

        # Assert - This tests the business logic of permission inheritance
        assert len(result) >= 1  # At least child permission
        assert "child.read" in result
        # Depending on implementation, parent permissions might be inherited


class TestRoleRepositoryPerformance:
    """Performance tests for RoleRepository"""

    @pytest.mark.performance
    async def test_permission_check_performance(self, role_repository):
        """Test performance of permission checking under load"""
        # Performance test for permission checking
        import time

        start_time = time.time()

        # Simulate multiple permission checks
        tasks = []
        for i in range(100):
            # This would need real user/permission data in a performance environment
            pass

        end_time = time.time()
        execution_time = end_time - start_time

        # Assert reasonable performance (adjust threshold as needed)
        assert execution_time < 1.0, f"Permission checks took too long: {execution_time}s"

    @pytest.mark.performance
    async def test_bulk_role_assignment_performance(self, role_repository):
        """Test performance of bulk role assignments"""
        # Performance test for bulk operations
        pass


class TestRoleRepositoryIntegration:
    """Integration tests for RoleRepository"""

    @pytest.mark.integration
    async def test_concurrent_permission_checks(self, role_repository):
        """Test concurrent permission checks don't cause database deadlocks"""
        import asyncio

        async def check_permission(user_id: int):
            return await role_repository.user_has_permission(user_id, "test", "read")

        # Create multiple concurrent permission checks
        tasks = [check_permission(1) for _ in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Verify no exceptions occurred
        assert all(not isinstance(result, Exception) for result in results)

    @pytest.mark.integration
    async def test_transaction_rollback_on_error(self, role_repository, db_session):
        """Test that database transactions are properly rolled back on errors"""
        # This would test transaction handling in error scenarios
        pass
