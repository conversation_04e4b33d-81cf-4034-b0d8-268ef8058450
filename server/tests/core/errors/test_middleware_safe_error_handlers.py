"""Test suite for middleware-safe error handling system.

Comprehensive tests for middleware-safe error handlers that prevent ExceptionGroup errors
in Starlette middleware async contexts.

This module tests the new middleware-safe decorators and exception types.
"""

import asyncio
import os
import sys
import logging
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from contextlib import contextmanager
from typing import Any, Callable

import pytest
from fastapi import Request, HTTPException
from starlette.responses import JSONResponse

# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..")
sys.path.insert(0, server_path)

from src.core.errors.unified_error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>rrorHandlingResult,
    UnifiedErrorHandler,
    unified_error_handler,
    handle_security_errors_safe,
)
from src.core.errors.exceptions import (
    BaseApplicationException,
    MiddlewareSafeException,
    SecurityMiddlewareException,
    DatabaseMiddlewareException,
    APIMiddlewareException,
    NotFoundError,
    DataValidation<PERSON>rror,
    <PERSON>valid<PERSON>n<PERSON><PERSON><PERSON>r,
    Service<PERSON>rror,
    DatabaseError,
    SecurityError,
)

pytestmark = [pytest.mark.unit]


class TestMiddlewareSafeExceptions:
    """Test suite for middleware-safe exception types."""

    def test_middleware_safe_exception_creation(self):
        """Test MiddlewareSafeException creation with all parameters."""
        exc = MiddlewareSafeException(
            message="Test error",
            status_code=400,
            detail="Detailed error message",
            error_code="TEST_ERROR",
            context={"key": "value"},
        )

        assert exc.message == "Test error"
        assert exc.status_code == 400
        assert exc.detail == "Detailed error message"
        assert exc.code == "TEST_ERROR"
        assert exc.category == "MiddlewareError"
        assert exc.metadata["key"] == "value"

    def test_middleware_safe_exception_defaults(self):
        """Test MiddlewareSafeException with default values."""
        exc = MiddlewareSafeException("Test error")

        assert exc.message == "Test error"
        assert exc.status_code == 500
        assert exc.detail == "Test error"
        assert exc.code == "MIDDLEWARE_ERROR"
        assert exc.category == "MiddlewareError"
        assert exc.metadata == {}

    def test_security_middleware_exception_creation(self):
        """Test SecurityMiddlewareException creation."""
        exc = SecurityMiddlewareException(
            message="Authentication failed",
            status_code=401,
            detail="Invalid credentials",
            security_operation="authentication",
            context={"user_id": "123"},
        )

        assert exc.message == "Authentication failed"
        assert exc.status_code == 401
        assert exc.detail == "Invalid credentials"
        assert exc.code == "SECURITY_AUTHENTICATION_ERROR"
        assert exc.metadata["security_operation"] == "authentication"
        assert exc.metadata["user_id"] == "123"

    def test_security_middleware_exception_defaults(self):
        """Test SecurityMiddlewareException with default values."""
        exc = SecurityMiddlewareException("Security error")

        assert exc.message == "Security error"
        assert exc.status_code == 403
        assert exc.code == "SECURITY_SECURITY_CHECK_ERROR"
        assert exc.metadata["security_operation"] == "security_check"

    def test_database_middleware_exception_creation(self):
        """Test DatabaseMiddlewareException creation."""
        exc = DatabaseMiddlewareException(
            message="Database connection failed",
            status_code=503,
            detail="Connection timeout",
            database_operation="connection",
            context={"timeout": 30},
        )

        assert exc.message == "Database connection failed"
        assert exc.status_code == 503
        assert exc.detail == "Connection timeout"
        assert exc.code == "DATABASE_CONNECTION_ERROR"
        assert exc.metadata["database_operation"] == "connection"
        assert exc.metadata["timeout"] == 30

    def test_api_middleware_exception_creation(self):
        """Test APIMiddlewareException creation."""
        exc = APIMiddlewareException(
            message="API rate limit exceeded",
            status_code=429,
            detail="Too many requests",
            api_operation="rate_limiting",
            context={"limit": 100},
        )

        assert exc.message == "API rate limit exceeded"
        assert exc.status_code == 429
        assert exc.detail == "Too many requests"
        assert exc.code == "API_RATE_LIMITING_ERROR"
        assert exc.metadata["api_operation"] == "rate_limiting"
        assert exc.metadata["limit"] == 100


class TestMiddlewareSafeErrorHandlers:
    """Test suite for middleware-safe error handler decorators."""

    @pytest.fixture
    def mock_unified_error_handler(self):
        """Mock unified error handler for testing."""
        with patch("src.core.errors.unified_error_handler.unified_error_handler") as mock:
            mock_result = Mock(spec=ErrorHandlingResult)
            mock_result.http_status_code = 500
            mock_result.error_response = Mock()
            mock_result.error_response.detail = "Internal server error"
            mock.handle_exception.return_value = mock_result
            yield mock

    def test_handle_security_errors_safe_async_success(self, mock_unified_error_handler):
        """Test handle_security_errors_safe decorator with successful async operation."""

        @handle_security_errors_safe("test_operation")
        async def test_async_function():
            return "success"

        # Test successful execution
        result = asyncio.run(test_async_function())
        assert result == "success"

    def test_handle_security_errors_safe_sync_success(self, mock_unified_error_handler):
        """Test handle_security_errors_safe decorator with successful sync operation."""

        @handle_security_errors_safe("test_operation")
        def test_sync_function():
            return "success"

        # Test successful execution
        result = test_sync_function()
        assert result == "success"

    def test_handle_security_errors_safe_async_middleware_safe_exception_passthrough(self, mock_unified_error_handler):
        """Test that middleware-safe exceptions are passed through unchanged."""

        @handle_security_errors_safe("test_operation")
        async def test_async_function():
            raise SecurityMiddlewareException("Already middleware safe")

        # Test that SecurityMiddlewareException is passed through
        with pytest.raises(SecurityMiddlewareException) as exc_info:
            asyncio.run(test_async_function())

        assert str(exc_info.value) == "Already middleware safe"

    def test_handle_security_errors_safe_async_data_validation_error_passthrough(self, mock_unified_error_handler):
        """Test that DataValidationError is passed through unchanged."""

        @handle_security_errors_safe("test_operation")
        async def test_async_function():
            raise DataValidationError("Validation failed")

        # Test that DataValidationError is passed through
        with pytest.raises(DataValidationError) as exc_info:
            asyncio.run(test_async_function())

        assert "Input validation failed" in str(exc_info.value)

    def test_handle_security_errors_safe_async_http_exception_conversion(self, mock_unified_error_handler):
        """Test that HTTPException is converted to SecurityMiddlewareException."""

        @handle_security_errors_safe("test_operation")
        async def test_async_function():
            raise HTTPException(status_code=401, detail="Unauthorized")

        # Test that HTTPException is converted to SecurityMiddlewareException
        with pytest.raises(SecurityMiddlewareException) as exc_info:
            asyncio.run(test_async_function())

        assert exc_info.value.status_code == 401
        assert "Unauthorized" in exc_info.value.detail
        assert exc_info.value.metadata["security_operation"] == "test_operation"

    def test_handle_security_errors_safe_async_generic_exception_handling(self, mock_unified_error_handler):
        """Test that generic exceptions are converted to SecurityMiddlewareException."""

        @handle_security_errors_safe("test_operation")
        async def test_async_function():
            raise ValueError("Generic error")

        # Test that generic exception is converted to SecurityMiddlewareException
        with pytest.raises(SecurityMiddlewareException) as exc_info:
            asyncio.run(test_async_function())

        assert exc_info.value.status_code == 500  # From mock
        assert "test_operation" in exc_info.value.message
        assert exc_info.value.metadata["security_operation"] == "test_operation"
        assert exc_info.value.metadata["function"] == "test_async_function"
        assert exc_info.value.metadata["error_type"] == "ValueError"

    def test_handle_security_errors_safe_sync_generic_exception_handling(self, mock_unified_error_handler):
        """Test that generic exceptions are converted to SecurityMiddlewareException in sync context."""

        @handle_security_errors_safe("test_operation")
        def test_sync_function():
            raise ValueError("Generic error")

        # Test that generic exception is converted to SecurityMiddlewareException
        with pytest.raises(SecurityMiddlewareException) as exc_info:
            test_sync_function()

        assert exc_info.value.status_code == 500  # From mock
        assert "test_operation" in exc_info.value.message
        assert exc_info.value.metadata["security_operation"] == "test_operation"
        assert exc_info.value.metadata["function"] == "test_sync_function"
        assert exc_info.value.metadata["error_type"] == "ValueError"

    def test_handle_security_errors_safe_logging(self, mock_unified_error_handler):
        """Test that errors are properly handled and converted to SecurityMiddlewareException."""

        @handle_security_errors_safe("test_operation")
        def test_function():
            raise ValueError("Test error")

        # Test that the exception is properly converted
        with pytest.raises(SecurityMiddlewareException) as exc_info:
            test_function()

        # Verify the exception contains the expected information
        assert exc_info.value.metadata["security_operation"] == "test_operation"
        assert exc_info.value.metadata["function"] == "test_function"
        assert exc_info.value.metadata["error_type"] == "ValueError"

    def test_handle_security_errors_safe_unified_error_handler_called(self, mock_unified_error_handler):
        """Test that unified error handler is called with correct parameters."""

        @handle_security_errors_safe("test_operation")
        def test_function():
            raise ValueError("Test error")

        with pytest.raises(SecurityMiddlewareException):
            test_function()

        # Verify unified error handler was called
        mock_unified_error_handler.handle_exception.assert_called_once()
        call_args = mock_unified_error_handler.handle_exception.call_args

        # Check exception type
        assert isinstance(call_args[0][0], ValueError)

        # Check error context
        assert call_args[0][1] == ErrorContext.SECURITY

        # Check additional context
        additional_context = call_args[1]["additional_context"]
        assert additional_context["security_operation"] == "test_operation"
        assert additional_context["function"] == "test_function"


class TestMiddlewareSafeExceptionGroupPrevention:
    """Test suite to verify ExceptionGroup prevention in async contexts."""

    async def test_no_exception_group_in_task_group(self):
        """Test that middleware-safe exceptions don't cause ExceptionGroup errors."""

        async def middleware_function():
            """Simulate middleware function that raises middleware-safe exception."""
            raise SecurityMiddlewareException("Security error")

        async def task_group_simulation():
            """Simulate anyio task group context like in Starlette middleware."""
            try:
                # This simulates what happens in Starlette middleware
                await middleware_function()
            except SecurityMiddlewareException:
                # This should be caught cleanly without ExceptionGroup
                return "caught_middleware_safe_exception"
            except Exception as e:
                # This would be an ExceptionGroup if our fix doesn't work
                return f"unexpected_exception: {type(e).__name__}"

        result = await task_group_simulation()
        assert result == "caught_middleware_safe_exception"

    async def test_http_exception_would_cause_exception_group(self):
        """Test that HTTPException would cause issues (for comparison)."""

        async def problematic_middleware_function():
            """Simulate problematic middleware function that raises HTTPException."""
            raise HTTPException(status_code=500, detail="Server error")

        async def task_group_simulation():
            """Simulate anyio task group context."""
            try:
                await problematic_middleware_function()
            except HTTPException:
                return "caught_http_exception"
            except Exception as e:
                return f"other_exception: {type(e).__name__}"

        result = await task_group_simulation()
        # HTTPException should be caught normally in this simple case
        # The real issue occurs in complex middleware stacks with anyio.create_task_group()
        assert result == "caught_http_exception"
