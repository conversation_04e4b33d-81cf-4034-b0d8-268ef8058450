import pytest
from unittest.mock import patch, AsyncMock
from src.core.services.general.project_member_service import ProjectMemberService
from src.core.schemas.general.project_member_schemas import (
    ProjectMemberCreateSchema,
    ProjectMemberUpdateSchema,
)
from src.core.errors.exceptions import (
    ProjectNotFoundError,
    UserNotFoundError,
    DuplicateEntryError,
    NotFoundError,
)


class TestProjectMemberService:
    """Unit tests for the ProjectMemberService class."""

    async def test_add_member_to_project_success(
        self,
        project_member_service: ProjectMemberService,
        test_project,
        test_user,
        test_user_role,
    ):
        """Test successfully adding a member to a project."""
        member_data = ProjectMemberCreateSchema(
            name="Test Member",
            user_id=test_user.id,
            role_id=test_user_role.id,
            expires_at=None,
        )

        # Create a mock project member with all required fields
        from unittest.mock import Mock
        from datetime import datetime

        mock_project_member = Mock()
        mock_project_member.id = 1
        mock_project_member.name = "Test Member"
        mock_project_member.user_id = test_user.id
        mock_project_member.project_id = test_project.id
        mock_project_member.role_id = test_user_role.id
        mock_project_member.is_active = True
        mock_project_member.expires_at = None
        mock_project_member.user = test_user
        mock_project_member.role = test_user_role
        mock_project_member.created_at = datetime.now()
        mock_project_member.updated_at = datetime.now()

        with (
            patch.object(
                project_member_service.project_repo,
                "get_by_id",
                new_callable=AsyncMock,
                return_value=test_project,
            ),
            patch.object(project_member_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=test_user),
            patch.object(
                project_member_service.project_member_repo,
                "get_member_by_user_and_project",
                new_callable=AsyncMock,
                return_value=None,
            ),
            patch.object(
                project_member_service.project_member_repo,
                "create",
                new_callable=AsyncMock,
                return_value=mock_project_member,
            ),
            patch.object(
                project_member_service.project_member_repo,
                "get_member_with_details",
                new_callable=AsyncMock,
                return_value=mock_project_member,
            ),
            patch.object(
                project_member_service.project_member_repo.db_session,
                "flush",
                new_callable=AsyncMock,
            ),
            patch.object(
                project_member_service.project_member_repo.db_session,
                "refresh",
                new_callable=AsyncMock,
            ),
        ):
            created_member = await project_member_service.add_member_to_project(test_project.id, member_data)
            assert created_member.user_id == test_user.id
            assert created_member.project_id == test_project.id
            assert created_member.role_id == test_user_role.id

    async def test_add_member_to_project_project_not_found(
        self, project_member_service: ProjectMemberService, test_user, test_user_role
    ):
        """Test adding a member to a non-existent project."""
        member_data = ProjectMemberCreateSchema(
            name="Test Member",
            user_id=test_user.id,
            role_id=test_user_role.id,
            expires_at=None,
        )

        with patch.object(project_member_service.project_repo, "get_by_id", new_callable=AsyncMock, return_value=None):
            with pytest.raises(ProjectNotFoundError):
                await project_member_service.add_member_to_project(999, member_data)

    async def test_add_member_to_project_user_not_found(
        self, project_member_service: ProjectMemberService, test_project, test_user_role
    ):
        """Test adding a non-existent user to a project."""
        member_data = ProjectMemberCreateSchema(
            name="Test Member", user_id=999, role_id=test_user_role.id, expires_at=None
        )

        with (
            patch.object(
                project_member_service.project_repo,
                "get_by_id",
                new_callable=AsyncMock,
                return_value=test_project,
            ),
            patch.object(project_member_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=None),
        ):
            with pytest.raises(UserNotFoundError):
                await project_member_service.add_member_to_project(test_project.id, member_data)

    async def test_add_member_to_project_duplicate_entry(
        self,
        project_member_service: ProjectMemberService,
        test_project,
        test_user,
        test_project_member,
    ):
        """Test adding a user who is already a member of the project."""
        member_data = ProjectMemberCreateSchema(
            name="Test Member",
            user_id=test_user.id,
            role_id=test_project_member.role_id,
            expires_at=None,
        )

        with (
            patch.object(
                project_member_service.project_repo,
                "get_by_id",
                new_callable=AsyncMock,
                return_value=test_project,
            ),
            patch.object(project_member_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=test_user),
            patch.object(
                project_member_service.project_member_repo,
                "get_member_by_user_and_project",
                new_callable=AsyncMock,
                return_value=test_project_member,
            ),
        ):
            with pytest.raises(DuplicateEntryError):
                await project_member_service.add_member_to_project(test_project.id, member_data)

    async def test_remove_member_from_project_success(
        self,
        project_member_service: ProjectMemberService,
        test_project,
        test_user,
        test_project_member,
    ):
        """Test successfully removing a member from a project."""
        with (
            patch.object(
                project_member_service.project_member_repo,
                "get_member_by_user_and_project",
                new_callable=AsyncMock,
                return_value=test_project_member,
            ),
            patch.object(
                project_member_service.project_member_repo, "delete", new_callable=AsyncMock, return_value=None
            ) as mock_delete,
            patch.object(
                project_member_service.project_member_repo.db_session,
                "flush",
                new_callable=AsyncMock,
            ),
        ):
            await project_member_service.remove_member_from_project(test_project.id, test_user.id)
            mock_delete.assert_called_once_with(test_project_member.id)

    async def test_remove_member_from_project_not_found(
        self, project_member_service: ProjectMemberService, test_project, test_user
    ):
        """Test removing a non-existent member from a project."""
        with patch.object(
            project_member_service.project_member_repo,
            "get_member_by_user_and_project",
            new_callable=AsyncMock,
            return_value=None,
        ):
            with pytest.raises(NotFoundError):
                await project_member_service.remove_member_from_project(test_project.id, test_user.id)

    async def test_update_project_member_success(
        self,
        project_member_service: ProjectMemberService,
        test_project_member,
        async_test_user_role,
    ):
        """Test successfully updating a project member's role."""
        update_data = ProjectMemberUpdateSchema(role_id=async_test_user_role.id, is_active=True, expires_at=None)

        with (
            patch.object(
                project_member_service.project_member_repo,
                "get_by_id",
                new_callable=AsyncMock,
                return_value=test_project_member,
            ),
            patch.object(
                project_member_service.project_member_repo,
                "update",
                new_callable=AsyncMock,
                return_value=test_project_member,
            ),
            patch.object(
                project_member_service.project_member_repo,
                "get_member_with_details",
                new_callable=AsyncMock,
                return_value=test_project_member,
            ),
            patch.object(
                project_member_service.project_member_repo.db_session,
                "flush",
                new_callable=AsyncMock,
            ),
            patch.object(
                project_member_service.project_member_repo.db_session,
                "refresh",
                new_callable=AsyncMock,
            ),
        ):
            result = await project_member_service.update_project_member(test_project_member.id, update_data)
            assert result.role_id == async_test_user_role.id

    async def test_update_project_member_not_found(self, project_member_service: ProjectMemberService, test_user_role):
        """Test updating a non-existent project member."""
        update_data = ProjectMemberUpdateSchema(role_id=test_user_role.id, is_active=True, expires_at=None)

        with patch.object(
            project_member_service.project_member_repo, "get_by_id", new_callable=AsyncMock, return_value=None
        ):
            with pytest.raises(NotFoundError):
                await project_member_service.update_project_member(999, update_data)

    async def test_list_project_members_success(
        self,
        project_member_service: ProjectMemberService,
        test_project,
        test_project_member,
    ):
        """Test successfully listing all members of a project."""
        with (
            patch.object(
                project_member_service.project_repo,
                "get_by_id",
                new_callable=AsyncMock,
                return_value=test_project,
            ),
            patch.object(
                project_member_service.project_member_repo,
                "list_members_by_project",
                new_callable=AsyncMock,
                return_value=[test_project_member],
            ),
        ):
            result = await project_member_service.list_project_members(test_project.id)
            assert len(result) == 1

    async def test_list_project_members_project_not_found(self, project_member_service: ProjectMemberService):
        """Test listing the members of a non-existent project."""
        with patch.object(project_member_service.project_repo, "get_by_id", new_callable=AsyncMock, return_value=None):
            with pytest.raises(ProjectNotFoundError):
                await project_member_service.list_project_members(999)
