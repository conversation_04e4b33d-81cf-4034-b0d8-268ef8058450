"""Unit tests for SynchronizationService conflict resolution methods.

This module contains comprehensive unit tests for the conflict resolution
functionality in the SynchronizationService class, focusing on the
_resolve_conflict method and its various strategies.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timedelta
from typing import Dict, Any

from src.core.services.general.synchronization_service import SynchronizationService
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.errors.exceptions import SynchronizationError


class TestSynchronizationServiceConflictResolution:
    """Test suite for SynchronizationService conflict resolution methods."""

    @pytest.fixture
    def mock_connection_manager(self) -> MagicMock:
        """Create a mock DynamicConnectionManager."""
        return MagicMock(spec=DynamicConnectionManager)

    @pytest.fixture
    def mock_project_repository(self) -> AsyncMock:
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def sync_service(
        self, mock_connection_manager: MagicMock, mock_project_repository: AsyncMock
    ) -> SynchronizationService:
        """Create a SynchronizationService instance with mocked dependencies."""
        return SynchronizationService(mock_connection_manager, mock_project_repository)

    @pytest.fixture
    def local_change_recent(self) -> Dict[str, Any]:
        """Create a local change record with recent timestamp."""
        return {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "timestamp": datetime(2024, 1, 1, 12, 0, 0),
            "new_values": {"name": "Local Project Name"},
            "source": "local",
        }

    @pytest.fixture
    def central_change_older(self) -> Dict[str, Any]:
        """Create a central change record with older timestamp."""
        return {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "timestamp": datetime(2024, 1, 1, 10, 0, 0),
            "new_values": {"name": "Central Project Name"},
            "source": "central",
        }

    @pytest.fixture
    def central_change_recent(self) -> Dict[str, Any]:
        """Create a central change record with recent timestamp."""
        return {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "timestamp": datetime(2024, 1, 1, 14, 0, 0),
            "new_values": {"name": "Central Project Name New"},
            "source": "central",
        }

    def test_resolve_conflict_last_write_wins_local_newer(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_older: Dict[str, Any],
    ) -> None:
        """Test last-write-wins when local change is newer."""
        result = sync_service._resolve_conflict(
            conflict_type="field_conflict",
            local_change=local_change_recent,
            central_change=central_change_older,
        )

        assert result == local_change_recent
        assert result["source"] == "local"

    def test_resolve_conflict_last_write_wins_central_newer(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_recent: Dict[str, Any],
    ) -> None:
        """Test last-write-wins when central change is newer."""
        result = sync_service._resolve_conflict(
            conflict_type="field_conflict",
            local_change=local_change_recent,
            central_change=central_change_recent,
        )

        assert result == central_change_recent
        assert result["source"] == "central"

    def test_resolve_conflict_double_delete(self, sync_service: SynchronizationService) -> None:
        """Test resolution of double delete conflicts."""
        local_delete = {
            "entity_type": "component",
            "entity_id": 456,
            "operation": "delete",
            "timestamp": datetime(2024, 1, 1, 12, 0, 0),
            "source": "local",
        }

        central_delete = {
            "entity_type": "component",
            "entity_id": 456,
            "operation": "delete",
            "timestamp": datetime(2024, 1, 1, 10, 0, 0),
            "source": "central",
        }

        result = sync_service._resolve_conflict(
            conflict_type="double_delete",
            local_change=local_delete,
            central_change=central_delete,
        )

        # Local delete is more recent, should win
        assert result == local_delete

    def test_resolve_conflict_delete_vs_modify(self, sync_service: SynchronizationService) -> None:
        """Test resolution of delete vs modify conflicts."""
        local_delete = {
            "entity_type": "component",
            "entity_id": 789,
            "operation": "delete",
            "timestamp": datetime(2024, 1, 1, 14, 0, 0),
            "source": "local",
        }

        central_modify = {
            "entity_type": "component",
            "entity_id": 789,
            "operation": "update",
            "timestamp": datetime(2024, 1, 1, 12, 0, 0),
            "new_values": {"name": "Modified Component"},
            "source": "central",
        }

        result = sync_service._resolve_conflict(
            conflict_type="delete_vs_modify",
            local_change=local_delete,
            central_change=central_modify,
        )

        # Local delete is more recent, should win
        assert result == local_delete

    def test_resolve_conflict_modify_vs_delete(self, sync_service: SynchronizationService) -> None:
        """Test resolution of modify vs delete conflicts."""
        local_modify = {
            "entity_type": "component",
            "entity_id": 101,
            "operation": "update",
            "timestamp": datetime(2024, 1, 1, 10, 0, 0),
            "new_values": {"name": "Modified Component"},
            "source": "local",
        }

        central_delete = {
            "entity_type": "component",
            "entity_id": 101,
            "operation": "delete",
            "timestamp": datetime(2024, 1, 1, 14, 0, 0),
            "source": "central",
        }

        result = sync_service._resolve_conflict(
            conflict_type="modify_vs_delete",
            local_change=local_modify,
            central_change=central_delete,
        )

        # Central delete is more recent, should win
        assert result == central_delete

    def test_resolve_conflict_double_create(self, sync_service: SynchronizationService) -> None:
        """Test resolution of double create conflicts."""
        local_create = {
            "entity_type": "user",
            "entity_id": 202,
            "operation": "create",
            "timestamp": datetime(2024, 1, 1, 15, 0, 0),
            "new_values": {"name": "Local User"},
            "source": "local",
        }

        central_create = {
            "entity_type": "user",
            "entity_id": 202,
            "operation": "create",
            "timestamp": datetime(2024, 1, 1, 13, 0, 0),
            "new_values": {"name": "Central User"},
            "source": "central",
        }

        result = sync_service._resolve_conflict(
            conflict_type="double_create",
            local_change=local_create,
            central_change=central_create,
        )

        # Local create is more recent, should win
        assert result == local_create

    def test_resolve_conflict_local_wins_strategy(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_recent: Dict[str, Any],
    ) -> None:
        """Test local_wins conflict resolution strategy."""
        config = {"conflict_resolution": "local_wins"}

        result = sync_service._resolve_conflict(
            conflict_type="field_conflict",
            local_change=local_change_recent,
            central_change=central_change_recent,
            sync_config=config,
        )

        assert result == local_change_recent

    def test_resolve_conflict_central_wins_strategy(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_older: Dict[str, Any],
    ) -> None:
        """Test central_wins conflict resolution strategy."""
        config = {"conflict_resolution": "central_wins"}

        result = sync_service._resolve_conflict(
            conflict_type="field_conflict",
            local_change=local_change_recent,
            central_change=central_change_older,
            sync_config=config,
        )

        assert result == central_change_older

    def test_resolve_conflict_manual_strategy(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_recent: Dict[str, Any],
    ) -> None:
        """Test manual conflict resolution strategy."""
        config = {"conflict_resolution": "manual"}

        with patch("src.core.services.general.synchronization_service.logger") as mock_logger:
            result = sync_service._resolve_conflict(
                conflict_type="field_conflict",
                local_change=local_change_recent,
                central_change=central_change_recent,
                sync_config=config,
            )

            # Should default to local change for manual resolution
            assert result == local_change_recent

            # Should log warning about manual resolution required
            mock_logger.warning.assert_called()
            warning_call = mock_logger.warning.call_args[0][0]
            assert "Manual conflict resolution required" in warning_call

    def test_resolve_conflict_invalid_strategy(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_recent: Dict[str, Any],
    ) -> None:
        """Test error handling for invalid conflict resolution strategy."""
        config = {"conflict_resolution": "invalid_strategy"}

        with pytest.raises(SynchronizationError) as exc_info:
            sync_service._resolve_conflict(
                conflict_type="field_conflict",
                local_change=local_change_recent,
                central_change=central_change_recent,
                sync_config=config,
            )

        assert "Unknown conflict resolution strategy" in str(exc_info.value)

    def test_resolve_conflict_missing_local_change(
        self,
        sync_service: SynchronizationService,
        central_change_recent: Dict[str, Any],
    ) -> None:
        """Test error handling when local change is missing."""
        with pytest.raises(SynchronizationError) as exc_info:
            sync_service._resolve_conflict(
                conflict_type="field_conflict",
                local_change=None,
                central_change=central_change_recent,
            )

        assert "missing local or central change record" in str(exc_info.value)

    def test_resolve_conflict_missing_central_change(
        self, sync_service: SynchronizationService, local_change_recent: Dict[str, Any]
    ) -> None:
        """Test error handling when central change is missing."""
        with pytest.raises(SynchronizationError) as exc_info:
            sync_service._resolve_conflict(
                conflict_type="field_conflict",
                local_change=local_change_recent,
                central_change=None,
            )

        assert "missing local or central change record" in str(exc_info.value)

    def test_compare_timestamps_missing_local_timestamp(
        self,
        sync_service: SynchronizationService,
        central_change_recent: Dict[str, Any],
    ) -> None:
        """Test timestamp comparison when local timestamp is missing."""
        local_change_no_timestamp = {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "source": "local",
        }

        result = sync_service._compare_timestamps_and_select(local_change_no_timestamp, central_change_recent)

        # Should default to central when local timestamp missing
        assert result == central_change_recent

    def test_compare_timestamps_missing_central_timestamp(
        self, sync_service: SynchronizationService, local_change_recent: Dict[str, Any]
    ) -> None:
        """Test timestamp comparison when central timestamp is missing."""
        central_change_no_timestamp = {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "source": "central",
        }

        result = sync_service._compare_timestamps_and_select(local_change_recent, central_change_no_timestamp)

        # Should default to local when central timestamp missing
        assert result == local_change_recent

    def test_compare_timestamps_string_format(self, sync_service: SynchronizationService) -> None:
        """Test timestamp comparison with ISO string format timestamps."""
        local_change_string_timestamp = {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "timestamp": "2024-01-01T15:00:00",
            "source": "local",
        }

        central_change_string_timestamp = {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "timestamp": "2024-01-01T12:00:00Z",
            "source": "central",
        }

        result = sync_service._compare_timestamps_and_select(
            local_change_string_timestamp, central_change_string_timestamp
        )

        # Local has more recent timestamp, should win
        assert result == local_change_string_timestamp

    def test_compare_timestamps_equal_timestamps(self, sync_service: SynchronizationService) -> None:
        """Test timestamp comparison with equal timestamps."""
        timestamp = datetime(2024, 1, 1, 12, 0, 0)

        local_change = {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "timestamp": timestamp,
            "source": "local",
        }

        central_change = {
            "entity_type": "project",
            "entity_id": 123,
            "operation": "update",
            "timestamp": timestamp,
            "source": "central",
        }

        result = sync_service._compare_timestamps_and_select(local_change, central_change)

        # With equal timestamps, local should win (>= comparison)
        assert result == local_change

    def test_resolve_conflict_unknown_conflict_type(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_older: Dict[str, Any],
    ) -> None:
        """Test resolution of unknown conflict types."""
        with patch("src.core.services.general.synchronization_service.logger") as mock_logger:
            result = sync_service._resolve_conflict(
                conflict_type="unknown_conflict",
                local_change=local_change_recent,
                central_change=central_change_older,
            )

            # Should default to timestamp comparison
            assert result == local_change_recent

            # Should log warning about unknown conflict type
            mock_logger.warning.assert_called()
            warning_call = mock_logger.warning.call_args[0][0]
            assert "Unknown conflict type unknown_conflict" in warning_call

    def test_resolve_conflict_with_custom_config(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_recent: Dict[str, Any],
    ) -> None:
        """Test conflict resolution with custom configuration override."""
        # Service has default config "last_write_wins", but we override with "local_wins"
        custom_config = {"conflict_resolution": "local_wins"}

        result = sync_service._resolve_conflict(
            conflict_type="field_conflict",
            local_change=local_change_recent,
            central_change=central_change_recent,
            sync_config=custom_config,
        )

        # Should use custom config, not service default
        assert result == local_change_recent

    def test_resolve_conflict_error_handling(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_recent: Dict[str, Any],
    ) -> None:
        """Test error handling during conflict resolution."""
        # Mock the _resolve_conflict_last_write_wins to raise an exception
        with patch.object(
            sync_service,
            "_resolve_conflict_last_write_wins",
            side_effect=Exception("Timestamp parsing error"),
        ):
            with pytest.raises(SynchronizationError) as exc_info:
                sync_service._resolve_conflict(
                    conflict_type="field_conflict",
                    local_change=local_change_recent,
                    central_change=central_change_recent,
                )

            assert "Conflict resolution failed" in str(exc_info.value)
            assert "Timestamp parsing error" in str(exc_info.value)

    def test_resolve_conflict_logging(
        self,
        sync_service: SynchronizationService,
        local_change_recent: Dict[str, Any],
        central_change_older: Dict[str, Any],
    ) -> None:
        """Test that conflict resolution includes proper logging."""
        with patch("src.core.services.general.synchronization_service.logger") as mock_logger:
            result = sync_service._resolve_conflict(
                conflict_type="field_conflict",
                local_change=local_change_recent,
                central_change=central_change_older,
            )

            # Verify debug logging for conflict resolution strategy
            debug_calls = mock_logger.debug.call_args_list
            assert len(debug_calls) >= 1

            # Check that entity ID and strategy are logged
            debug_message = debug_calls[0][0][0]
            assert "field_conflict" in debug_message
            assert "last_write_wins" in debug_message
            assert str(local_change_recent["entity_id"]) in debug_message
