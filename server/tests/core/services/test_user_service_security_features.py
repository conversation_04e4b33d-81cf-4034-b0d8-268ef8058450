"""Unit tests for UserService security features: account lockout, email verification, and password reset."""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime, timedelta, timezone

from src.core.services.general.user_service import UserService
from src.core.schemas.general.user_schemas import LoginRequestSchema
from src.core.security.password_handler import PasswordHandler
from src.core.errors.exceptions import InvalidInputError, NotFoundError
from src.core.models.general.user import User


class TestUserServiceSecurityFeatures:
    """Unit tests for security features in UserService."""

    async def test_account_lockout_on_failed_attempts(self, user_service: UserService):
        """Test account lockout after 5 failed login attempts."""
        password_hash = PasswordHandler.hash_password("correct_password")
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": password_hash,
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 4,  # Already 4 failed attempts
                "locked_until": None,
                "last_failed_login": None,
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": None,
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )
        
        # The test will mock the refresh method to simulate incrementing attempts
        
        login_data = LoginRequestSchema(username="<EMAIL>", password="wrong_password")

        with (
            patch.object(user_service.user_repo, "get_by_email", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "increment_failed_login_attempts", new_callable=AsyncMock),
            patch.object(user_service.user_repo, "lock_account", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "refresh", new_callable=AsyncMock) as mock_refresh,
            patch.object(user_service.user_repo.db_session, "commit", new_callable=AsyncMock),
            patch(
                "src.core.security.password_handler.PasswordHandler.verify_password",
                return_value=False,
            ),
        ):
            # Mock refresh to update the user object with incremented attempts
            def mock_refresh_side_effect(obj):
                if obj is user:
                    user.failed_login_attempts = 5
            
            mock_refresh.side_effect = mock_refresh_side_effect
            
            with pytest.raises(InvalidInputError, match="Account has been locked due to too many failed login attempts"):
                await user_service.authenticate_user(login_data)
            
            # Verify that lock_account was called
            user_service.user_repo.lock_account.assert_called_once_with(user.id, lock_duration_minutes=30)

    async def test_locked_account_cannot_login(self, user_service: UserService):
        """Test that locked accounts cannot login even with correct password."""
        password = "correct_password"
        password_hash = PasswordHandler.hash_password(password)
        locked_until = datetime.now(timezone.utc) + timedelta(minutes=10)  # Locked for 10 more minutes
        
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": password_hash,
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 5,
                "locked_until": locked_until,
                "last_failed_login": datetime.now(timezone.utc) - timedelta(minutes=5),
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": None,
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )
        
        login_data = LoginRequestSchema(username="<EMAIL>", password=password)

        with patch.object(user_service.user_repo, "get_by_email", new_callable=AsyncMock, return_value=user):
            with pytest.raises(InvalidInputError, match="Account is temporarily locked"):
                await user_service.authenticate_user(login_data)

    async def test_failed_attempts_reset_on_successful_login(self, user_service: UserService):
        """Test that failed login attempts are reset on successful authentication."""
        password = "correct_password"
        password_hash = PasswordHandler.hash_password(password)
        
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": password_hash,
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 3,  # Has some failed attempts
                "locked_until": None,
                "last_failed_login": datetime.now(timezone.utc) - timedelta(minutes=5),
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": None,
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )
        
        login_data = LoginRequestSchema(username="<EMAIL>", password=password)

        with (
            patch.object(user_service.user_repo, "get_by_email", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "reset_failed_login_attempts", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "refresh", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "commit", new_callable=AsyncMock),
            patch(
                "src.core.security.password_handler.PasswordHandler.verify_password",
                return_value=True,
            ),
            patch(
                "src.core.security.password_handler.PasswordHandler.needs_rehash",
                return_value=False,
            ),
        ):
            authenticated_user = await user_service.authenticate_user(login_data)
            
            assert authenticated_user.email == user.email
            # Verify that failed attempts were reset
            user_service.user_repo.reset_failed_login_attempts.assert_called_once_with(user.id)

    async def test_send_verification_email_success(self, user_service: UserService):
        """Test sending verification email to unverified user."""
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "hash",
                "is_active": False,
                "is_superuser": False,
                "failed_login_attempts": 0,
                "locked_until": None,
                "last_failed_login": None,
                "is_email_verified": False,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": None,
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        with (
            patch.object(user_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "set_email_verification_token", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "commit", new_callable=AsyncMock),
            patch.object(user_service.email_service, "generate_verification_token", return_value="test_token"),
            patch.object(user_service.email_service, "get_verification_expiry", return_value=datetime.now(timezone.utc) + timedelta(hours=24)),
            patch.object(user_service.email_service, "generate_verification_url", return_value="http://example.com/verify/test_token"),
            patch.object(user_service.email_service, "send_verification_email", new_callable=AsyncMock, return_value=True),
        ):
            result = await user_service.send_verification_email(1)
            
            assert result is True
            user_service.user_repo.set_email_verification_token.assert_called_once()
            user_service.email_service.send_verification_email.assert_called_once()

    async def test_send_verification_email_already_verified(self, user_service: UserService):
        """Test that sending verification email fails for already verified users."""
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "hash",
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 0,
                "locked_until": None,
                "last_failed_login": None,
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": datetime.now(timezone.utc),
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        with patch.object(user_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=user):
            with pytest.raises(InvalidInputError, match="Email is already verified"):
                await user_service.send_verification_email(1)

    async def test_verify_email_success(self, user_service: UserService):
        """Test successful email verification."""
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "hash",
                "is_active": False,
                "is_superuser": False,
                "failed_login_attempts": 0,
                "locked_until": None,
                "last_failed_login": None,
                "is_email_verified": False,
                "email_verification_token": "valid_token",
                "email_verification_expires": datetime.now(timezone.utc) + timedelta(hours=1),
                "email_verified_at": None,
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        with (
            patch.object(user_service.user_repo, "get_by_verification_token", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "verify_email", new_callable=AsyncMock),
            patch.object(user_service.user_repo, "activate_user", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "commit", new_callable=AsyncMock),
        ):
            result = await user_service.verify_email("valid_token")
            
            assert result is True
            user_service.user_repo.verify_email.assert_called_once_with(user.id)
            user_service.user_repo.activate_user.assert_called_once_with(user.id)

    async def test_verify_email_invalid_token(self, user_service: UserService):
        """Test email verification with invalid token."""
        with patch.object(user_service.user_repo, "get_by_verification_token", new_callable=AsyncMock, return_value=None):
            with pytest.raises(InvalidInputError, match="Invalid or expired verification token"):
                await user_service.verify_email("invalid_token")

    async def test_request_password_reset_success(self, user_service: UserService):
        """Test successful password reset request."""
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "hash",
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 0,
                "locked_until": None,
                "last_failed_login": None,
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": datetime.now(timezone.utc),
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        with (
            patch.object(user_service.user_repo, "get_by_email", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "set_password_reset_token", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "commit", new_callable=AsyncMock),
            patch.object(user_service.email_service, "generate_verification_token", return_value="reset_token"),
            patch.object(user_service.email_service, "get_reset_expiry", return_value=datetime.now(timezone.utc) + timedelta(hours=1)),
            patch.object(user_service.email_service, "generate_verification_url", return_value="http://example.com/reset/reset_token"),
            patch.object(user_service.email_service, "send_password_reset_email", new_callable=AsyncMock, return_value=True),
        ):
            result = await user_service.request_password_reset("<EMAIL>")
            
            assert result is True
            user_service.user_repo.set_password_reset_token.assert_called_once()
            user_service.email_service.send_password_reset_email.assert_called_once()

    async def test_request_password_reset_nonexistent_user(self, user_service: UserService):
        """Test password reset request for nonexistent user (should still return True for security)."""
        with patch.object(user_service.user_repo, "get_by_email", new_callable=AsyncMock, return_value=None):
            result = await user_service.request_password_reset("<EMAIL>")
            
            # Should return True to prevent email enumeration
            assert result is True

    async def test_reset_password_success(self, user_service: UserService):
        """Test successful password reset."""
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "old_hash",
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 2,
                "locked_until": None,
                "last_failed_login": None,
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": datetime.now(timezone.utc),
                "password_reset_token": "valid_reset_token",
                "password_reset_expires": datetime.now(timezone.utc) + timedelta(hours=1),
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        new_password = "New_Strong_Password_456!"

        with (
            patch.object(user_service.user_repo, "get_by_reset_token", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "update_password", new_callable=AsyncMock),
            patch.object(user_service.user_repo, "clear_password_reset_token", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "commit", new_callable=AsyncMock),
            patch("src.core.security.password_handler.PasswordHandler.is_password_valid", return_value=True),
            patch("src.core.security.password_handler.PasswordHandler.hash_password", return_value="new_hash"),
        ):
            result = await user_service.reset_password("valid_reset_token", new_password)
            
            assert result is True
            user_service.user_repo.update_password.assert_called_once_with(user.id, "new_hash")
            user_service.user_repo.clear_password_reset_token.assert_called_once_with(user.id)

    async def test_reset_password_invalid_token(self, user_service: UserService):
        """Test password reset with invalid token."""
        with patch.object(user_service.user_repo, "get_by_reset_token", new_callable=AsyncMock, return_value=None):
            with pytest.raises(InvalidInputError, match="Invalid or expired password reset token"):
                await user_service.reset_password("invalid_token", "New_Password_123!")

    async def test_reset_password_unlocks_account(self, user_service: UserService):
        """Test that password reset unlocks a locked account."""
        locked_until = datetime.now(timezone.utc) + timedelta(minutes=10)
        
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "old_hash",
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 5,
                "locked_until": locked_until,  # Account is locked
                "last_failed_login": datetime.now(timezone.utc),
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": datetime.now(timezone.utc),
                "password_reset_token": "valid_reset_token",
                "password_reset_expires": datetime.now(timezone.utc) + timedelta(hours=1),
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        new_password = "New_Strong_Password_456!"

        with (
            patch.object(user_service.user_repo, "get_by_reset_token", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "update_password", new_callable=AsyncMock),
            patch.object(user_service.user_repo, "clear_password_reset_token", new_callable=AsyncMock),
            patch.object(user_service.user_repo, "unlock_account", new_callable=AsyncMock),
            patch.object(user_service.user_repo.db_session, "commit", new_callable=AsyncMock),
            patch("src.core.security.password_handler.PasswordHandler.is_password_valid", return_value=True),
            patch("src.core.security.password_handler.PasswordHandler.hash_password", return_value="new_hash"),
        ):
            result = await user_service.reset_password("valid_reset_token", new_password)
            
            assert result is True
            user_service.user_repo.unlock_account.assert_called_once_with(user.id)

    async def test_unlock_user_account_success(self, user_service: UserService):
        """Test successful user account unlock."""
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "hash",
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 5,
                "locked_until": datetime.now(timezone.utc) + timedelta(minutes=10),
                "last_failed_login": datetime.now(timezone.utc),
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": datetime.now(timezone.utc),
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        with (
            patch.object(user_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=user),
            patch.object(user_service.user_repo, "unlock_account", new_callable=AsyncMock, return_value=True),
            patch.object(user_service.user_repo.db_session, "commit", new_callable=AsyncMock),
        ):
            result = await user_service.unlock_user_account(1)
            
            assert result is True
            user_service.user_repo.unlock_account.assert_called_once_with(1)

    async def test_get_user_lockout_status(self, user_service: UserService):
        """Test getting user lockout status information."""
        locked_until = datetime.now(timezone.utc) + timedelta(minutes=10)
        last_failed = datetime.now(timezone.utc) - timedelta(minutes=5)
        
        user = User(
            **{
                "id": 1,
                "name": "testuser",
                "email": "<EMAIL>",
                "password_hash": "hash",
                "is_active": True,
                "is_superuser": False,
                "failed_login_attempts": 3,
                "locked_until": locked_until,
                "last_failed_login": last_failed,
                "is_email_verified": True,
                "email_verification_token": None,
                "email_verification_expires": None,
                "email_verified_at": datetime.now(timezone.utc),
                "password_reset_token": None,
                "password_reset_expires": None,
                "created_at": "2023-01-01T00:00:00",
                "updated_at": "2023-01-01T00:00:00",
            }
        )

        with patch.object(user_service.user_repo, "get_by_id", new_callable=AsyncMock, return_value=user):
            status = await user_service.get_user_lockout_status(1)
            
            assert status["is_locked"] is True  # Will be True based on the is_locked property
            assert status["failed_attempts"] == 3
            assert status["locked_until"] == locked_until
            assert status["last_failed_login"] == last_failed