"""Test suite for Task and TaskAssignment migration.

This module provides comprehensive testing for the task management migration,
ensuring proper table creation, constraints, and relationships.
"""

import pytest
from sqlalchemy import inspect, text
from sqlalchemy.exc import IntegrityError

from src.core.models.general.task import Task, TaskAssignment
from src.core.models.general.project import Project
from src.core.models.general.user import User
from src.core.enums import TaskPriority, TaskStatus


@pytest.mark.database
class TestTaskMigration:
    """Test suite for task migration functionality."""

    def test_task_table_exists(self, db_session):
        """Test that the tasks table was created by migration."""
        inspector = inspect(db_session.bind)
        tables = inspector.get_table_names()

        assert "tasks" in tables, "Tasks table should exist after migration"

    def test_task_assignment_table_exists(self, db_session):
        """Test that the task_assignments table was created by migration."""
        inspector = inspect(db_session.bind)
        tables = inspector.get_table_names()

        assert "task_assignments" in tables, "Task assignments table should exist after migration"

    def test_task_table_columns(self, db_session):
        """Test that the tasks table has all required columns."""
        inspector = inspect(db_session.bind)
        columns = inspector.get_columns("tasks")
        column_names = [col["name"] for col in columns]

        required_columns = [
            "id",
            "task_id",
            "project_id",
            "description",
            "due_date",
            "priority",
            "status",
            "name",  # From CommonColumns (used as title)
            "notes",  # From CommonColumns
            "created_at",  # From CommonColumns
            "updated_at",  # From CommonColumns
            "is_deleted",  # From SoftDeleteColumns
            "deleted_at",  # From SoftDeleteColumns
            "deleted_by_user_id",  # From SoftDeleteColumns
        ]

        for column in required_columns:
            assert column in column_names, f"Column '{column}' should exist in tasks table"

    def test_task_assignment_table_columns(self, db_session):
        """Test that the task_assignments table has all required columns."""
        inspector = inspect(db_session.bind)
        columns = inspector.get_columns("task_assignments")
        column_names = [col["name"] for col in columns]

        required_columns = [
            "id",
            "task_id",
            "user_id",
            "assigned_at",
            "assigned_by_user_id",
            "is_active",
            "name",  # From CommonColumns
            "notes",  # From CommonColumns
            "created_at",  # From CommonColumns
            "updated_at",  # From CommonColumns
            "is_deleted",  # From SoftDeleteColumns
            "deleted_at",  # From SoftDeleteColumns
            "deleted_by_user_id",  # From SoftDeleteColumns
        ]

        for column in required_columns:
            assert column in column_names, f"Column '{column}' should exist in task_assignments table"

    def test_task_table_indexes(self, db_session):
        """Test that the tasks table has proper indexes."""
        inspector = inspect(db_session.bind)
        indexes = inspector.get_indexes("tasks")
        index_columns = []

        for index in indexes:
            index_columns.extend(index["column_names"])

        # Check for important indexes
        assert "task_id" in index_columns, "task_id should be indexed"
        assert "project_id" in index_columns, "project_id should be indexed"
        assert "priority" in index_columns, "priority should be indexed"
        assert "status" in index_columns, "status should be indexed"

    def test_task_assignment_table_indexes(self, db_session):
        """Test that the task_assignments table has proper indexes."""
        inspector = inspect(db_session.bind)
        indexes = inspector.get_indexes("task_assignments")
        index_columns = []

        for index in indexes:
            index_columns.extend(index["column_names"])

        # Check for important indexes
        assert "task_id" in index_columns, "task_id should be indexed"
        assert "user_id" in index_columns, "user_id should be indexed"
        assert "is_active" in index_columns, "is_active should be indexed"

    def test_task_foreign_key_constraints(self, db_session):
        """Test that the tasks table has proper foreign key constraints."""
        inspector = inspect(db_session.bind)
        foreign_keys = inspector.get_foreign_keys("tasks")

        # Check project_id foreign key
        project_fk_found = False
        for fk in foreign_keys:
            if "project_id" in fk["constrained_columns"]:
                assert fk["referred_table"] == "projects"
                assert "id" in fk["referred_columns"]
                project_fk_found = True

        assert project_fk_found, "project_id foreign key constraint should exist"

    def test_task_assignment_foreign_key_constraints(self, db_session):
        """Test that the task_assignments table has proper foreign key constraints."""
        inspector = inspect(db_session.bind)
        foreign_keys = inspector.get_foreign_keys("task_assignments")

        # Check task_id foreign key
        task_fk_found = False
        user_fk_found = False
        assigned_by_fk_found = False

        for fk in foreign_keys:
            if "task_id" in fk["constrained_columns"]:
                assert fk["referred_table"] == "tasks"
                assert "id" in fk["referred_columns"]
                task_fk_found = True
            elif "user_id" in fk["constrained_columns"]:
                assert fk["referred_table"] == "users"
                assert "id" in fk["referred_columns"]
                user_fk_found = True
            elif "assigned_by_user_id" in fk["constrained_columns"]:
                assert fk["referred_table"] == "users"
                assert "id" in fk["referred_columns"]
                assigned_by_fk_found = True

        assert task_fk_found, "task_id foreign key constraint should exist"
        assert user_fk_found, "user_id foreign key constraint should exist"
        assert assigned_by_fk_found, "assigned_by_user_id foreign key constraint should exist"

    def test_task_unique_constraints(self, db_session):
        """Test that the tasks table has proper unique constraints."""
        inspector = inspect(db_session.bind)
        unique_constraints = inspector.get_unique_constraints("tasks")

        # Check task_id unique constraint
        task_id_unique_found = False
        for constraint in unique_constraints:
            if "task_id" in constraint["column_names"]:
                task_id_unique_found = True

        assert task_id_unique_found, "task_id unique constraint should exist"

    def test_task_assignment_unique_constraints(self, db_session):
        """Test that the task_assignments table has proper unique constraints."""
        inspector = inspect(db_session.bind)
        unique_constraints = inspector.get_unique_constraints("task_assignments")

        # Check task_id + user_id unique constraint
        task_user_unique_found = False
        for constraint in unique_constraints:
            if "task_id" in constraint["column_names"] and "user_id" in constraint["column_names"]:
                task_user_unique_found = True

        assert task_user_unique_found, "task_id + user_id unique constraint should exist"

    def test_task_creation_after_migration(self, db_session):
        """Test that tasks can be created after migration."""
        # Create a test project first
        project = Project(
            name="Test Project",
            project_number="TEST-001",
            description="Test project for migration",
            status="active",
        )
        db_session.add(project)
        db_session.flush()

        # Create a test task
        task = Task(
            project_id=project.id,
            title="Test Task",
            description="Test task after migration",
            priority=TaskPriority.MEDIUM,
            status=TaskStatus.NOT_STARTED,
        )

        db_session.add(task)
        db_session.flush()

        assert task.id is not None
        assert task.task_id is not None
        assert task.project_id == project.id

    def test_task_assignment_creation_after_migration(self, db_session):
        """Test that task assignments can be created after migration."""
        # Create test data
        project = Project(
            name="Test Project",
            project_number="TEST-002",
            description="Test project for assignment migration",
            status="active",
        )
        user = User(
            name="Test User",
            email="<EMAIL>",
            is_active=True,
        )
        task = Task(
            project_id=project.id,
            title="Test Task for Assignment",
            priority=TaskPriority.HIGH,
            status=TaskStatus.NOT_STARTED,
        )

        db_session.add_all([project, user])
        db_session.flush()

        task.project_id = project.id
        db_session.add(task)
        db_session.flush()

        # Create task assignment
        assignment = TaskAssignment(
            task_id=task.id,
            user_id=user.id,
            name=f"Assignment for {task.title}",
        )

        db_session.add(assignment)
        db_session.flush()

        assert assignment.id is not None
        assert assignment.task_id == task.id
        assert assignment.user_id == user.id
        assert assignment.is_active is True

    def test_task_assignment_unique_constraint_enforcement(self, db_session):
        """Test that unique constraints are properly enforced after migration."""
        # Create test data
        project = Project(
            name="Test Project",
            project_number="TEST-003",
            description="Test project for constraint testing",
            status="active",
        )
        user = User(
            name="Test User",
            email="<EMAIL>",
            is_active=True,
        )
        task = Task(
            project_id=project.id,
            title="Test Task for Constraint",
            priority=TaskPriority.LOW,
            status=TaskStatus.NOT_STARTED,
        )

        db_session.add_all([project, user])
        db_session.flush()

        task.project_id = project.id
        db_session.add(task)
        db_session.flush()

        # Create first assignment
        assignment1 = TaskAssignment(
            task_id=task.id,
            user_id=user.id,
            name=f"Assignment 1 for {task.title}",
        )
        db_session.add(assignment1)
        db_session.flush()

        # Try to create duplicate assignment
        assignment2 = TaskAssignment(
            task_id=task.id,
            user_id=user.id,  # Same task and user
            name=f"Assignment 2 for {task.title}",
        )
        db_session.add(assignment2)

        with pytest.raises(IntegrityError):
            db_session.flush()

    def test_task_relationships_after_migration(self, db_session):
        """Test that relationships work properly after migration."""
        # Create test data
        project = Project(
            name="Test Project",
            project_number="TEST-004",
            description="Test project for relationships",
            status="active",
        )
        user = User(
            name="Test User",
            email="<EMAIL>",
            is_active=True,
        )

        db_session.add_all([project, user])
        db_session.flush()

        task = Task(
            project_id=project.id,
            title="Test Task for Relationships",
            priority=TaskPriority.CRITICAL,
            status=TaskStatus.IN_PROGRESS,
        )

        db_session.add(task)
        db_session.flush()

        assignment = TaskAssignment(
            task_id=task.id,
            user_id=user.id,
            name=f"Assignment for {task.title}",
        )

        db_session.add(assignment)
        db_session.flush()

        # Test relationships
        assert task.project == project
        assert assignment.task == task
        assert assignment.user == user
        assert task in project.tasks
        assert assignment in task.assignments
        assert assignment in user.task_assignments
