"""Unit tests for DynamicConnectionManager."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession, AsyncEngine

from src.core.database.connection_manager import (
    DynamicConnectionManager,
    initialize_connection_manager,
    shutdown_connection_manager,
    get_central_db_session,
    get_project_repository_dependency,
    get_contextual_db_session,
)
from src.core.errors.exceptions import DatabaseError, ProjectNotFoundError
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.models.general.project import Project


class TestDynamicConnectionManager:
    """Test cases for DynamicConnectionManager."""

    def test_init(self) -> None:
        """Test DynamicConnectionManager initialization."""
        manager = DynamicConnectionManager()
        assert manager._central_engine is None
        assert manager._central_session_factory is None
        assert manager._local_engines == {}
        assert manager._local_session_factories == {}

    @pytest.mark.asyncio
    async def test_initialize_success(self) -> None:
        """Test successful initialization with valid DATABASE_URL."""
        manager = DynamicConnectionManager()

        with patch("src.core.database.connection_manager.settings") as mock_settings:
            mock_settings.DATABASE_URL = "postgresql+asyncpg://test:test@localhost/test"
            mock_settings.DB_ECHO = False

            with patch("src.core.database.engine._create_and_test_async_engine") as mock_create_engine:
                mock_engine = MagicMock(spec=AsyncEngine)
                mock_create_engine.return_value = mock_engine

                with patch("src.core.database.connection_manager.async_sessionmaker") as mock_sessionmaker:
                    mock_session_factory = MagicMock()
                    mock_sessionmaker.return_value = mock_session_factory

                    await manager.initialize()

                    # Verify engine creation
                    mock_create_engine.assert_called_once_with(
                        "postgresql+asyncpg://test:test@localhost/test",
                        echo=False,
                    )

                    # Verify session factory creation
                    mock_sessionmaker.assert_called_once_with(
                        bind=mock_engine,
                        class_=AsyncSession,
                        expire_on_commit=False,
                    )

                    assert manager._central_engine is mock_engine
                    assert manager._central_session_factory is mock_session_factory

    @pytest.mark.asyncio
    async def test_initialize_no_database_url(self) -> None:
        """Test initialization failure when DATABASE_URL is not set."""
        manager = DynamicConnectionManager()

        with patch("src.core.database.connection_manager.settings") as mock_settings:
            mock_settings.DATABASE_URL = None

            with pytest.raises(DatabaseError) as exc_info:
                await manager.initialize()

            assert "DATABASE_URL is not set" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_session_central_database_no_project_id(self) -> None:
        """Test get_session defaults to central database when no project_id is provided."""
        manager = DynamicConnectionManager()

        # Mock central session factory
        mock_session = AsyncMock(spec=AsyncSession)
        mock_session_factory = MagicMock()
        mock_session_factory.return_value.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_factory.return_value.__aexit__ = AsyncMock(return_value=None)
        manager._central_session_factory = mock_session_factory

        mock_project_repo = MagicMock(spec=ProjectRepository)

        # Test the async generator
        async_gen = manager.get_session(mock_project_repo, None)
        session = await async_gen.__anext__()

        assert session is mock_session
        mock_project_repo.get_by_id.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_session_project_with_local_database(self) -> None:
        """Test get_session with project that has local database URL."""
        manager = DynamicConnectionManager()

        # Mock project with local database
        mock_project = MagicMock(spec=Project)
        mock_project.database_url = "postgresql+asyncpg://local:local@localhost/local_db"

        mock_project_repo = AsyncMock(spec=ProjectRepository)
        mock_project_repo.get_by_id.return_value = mock_project

        # Mock local session creation
        mock_local_session = AsyncMock(spec=AsyncSession)
        mock_local_session_factory = MagicMock()
        mock_local_session_factory.return_value.__aenter__ = AsyncMock(return_value=mock_local_session)
        mock_local_session_factory.return_value.__aexit__ = AsyncMock(return_value=None)

        with patch.object(manager, "_get_local_session_factory") as mock_get_local_factory:
            mock_get_local_factory.return_value = mock_local_session_factory

            async_gen = manager.get_session(mock_project_repo, 123)
            session = await async_gen.__anext__()

            assert session is mock_local_session
            mock_project_repo.get_by_id.assert_called_once_with(123)
            mock_get_local_factory.assert_called_once_with(mock_project.database_url)

    @pytest.mark.asyncio
    async def test_get_session_project_without_local_database(self) -> None:
        """Test get_session with project that uses central database."""
        manager = DynamicConnectionManager()

        # Mock project without local database
        mock_project = MagicMock(spec=Project)
        mock_project.database_url = None

        mock_project_repo = AsyncMock(spec=ProjectRepository)
        mock_project_repo.get_by_id.return_value = mock_project

        # Mock central session
        mock_central_session = AsyncMock(spec=AsyncSession)
        mock_central_session_factory = MagicMock()
        mock_central_session_factory.return_value.__aenter__ = AsyncMock(return_value=mock_central_session)
        mock_central_session_factory.return_value.__aexit__ = AsyncMock(return_value=None)
        manager._central_session_factory = mock_central_session_factory

        async_gen = manager.get_session(mock_project_repo, 123)
        session = await async_gen.__anext__()

        assert session is mock_central_session
        mock_project_repo.get_by_id.assert_called_once_with(123)

    @pytest.mark.asyncio
    async def test_get_session_project_not_found(self) -> None:
        """Test get_session when project is not found, defaults to central database."""
        manager = DynamicConnectionManager()

        mock_project_repo = AsyncMock(spec=ProjectRepository)
        mock_project_repo.get_by_id.side_effect = ProjectNotFoundError("Project not found")

        # Mock central session
        mock_central_session = AsyncMock(spec=AsyncSession)
        mock_central_session_factory = MagicMock()
        mock_central_session_factory.return_value.__aenter__ = AsyncMock(return_value=mock_central_session)
        mock_central_session_factory.return_value.__aexit__ = AsyncMock(return_value=None)
        manager._central_session_factory = mock_central_session_factory

        async_gen = manager.get_session(mock_project_repo, 999)
        session = await async_gen.__anext__()

        assert session is mock_central_session
        mock_project_repo.get_by_id.assert_called_once_with(999)

    @pytest.mark.asyncio
    async def test_get_session_no_valid_factory(self) -> None:
        """Test get_session raises error when no valid session factory is available."""
        manager = DynamicConnectionManager()
        manager._central_session_factory = None

        mock_project_repo = MagicMock(spec=ProjectRepository)

        with pytest.raises(DatabaseError) as exc_info:
            async_gen = manager.get_session(mock_project_repo, None)
            await async_gen.__anext__()

        assert "Could not determine a valid session factory" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_local_session_factory_new_database(self) -> None:
        """Test _get_local_session_factory creates new factory for new database."""
        manager = DynamicConnectionManager()
        db_url = "postgresql+asyncpg://local:local@localhost/new_db"

        with patch("src.core.database.engine._create_and_test_async_engine") as mock_create_engine:
            mock_engine = MagicMock(spec=AsyncEngine)
            mock_create_engine.return_value = mock_engine

            with patch("src.core.database.connection_manager.async_sessionmaker") as mock_sessionmaker:
                mock_session_factory = MagicMock()
                mock_sessionmaker.return_value = mock_session_factory

                result = await manager._get_local_session_factory(db_url)

                # Verify engine creation
                mock_create_engine.assert_called_once_with(db_url)

                # Verify session factory creation
                mock_sessionmaker.assert_called_once_with(
                    bind=mock_engine,
                    class_=AsyncSession,
                    expire_on_commit=False,
                )

                assert result is mock_session_factory
                assert manager._local_engines[db_url] is mock_engine
                assert manager._local_session_factories[db_url] is mock_session_factory

    @pytest.mark.asyncio
    async def test_get_local_session_factory_existing_database(self) -> None:
        """Test _get_local_session_factory returns existing factory for cached database."""
        manager = DynamicConnectionManager()
        db_url = "postgresql+asyncpg://local:local@localhost/existing_db"

        # Pre-populate the cache
        existing_factory = MagicMock()
        manager._local_session_factories[db_url] = existing_factory

        result = await manager._get_local_session_factory(db_url)

        assert result is existing_factory

    @pytest.mark.asyncio
    async def test_shutdown(self) -> None:
        """Test shutdown disposes of all engines."""
        manager = DynamicConnectionManager()

        # Mock central engine
        mock_central_engine = AsyncMock(spec=AsyncEngine)
        manager._central_engine = mock_central_engine

        # Mock local engines
        mock_local_engine1 = AsyncMock(spec=AsyncEngine)
        mock_local_engine2 = AsyncMock(spec=AsyncEngine)
        manager._local_engines = {
            "url1": mock_local_engine1,
            "url2": mock_local_engine2,
        }
        manager._local_session_factories = {"url1": MagicMock(), "url2": MagicMock()}

        await manager.shutdown()

        # Verify central engine disposal
        mock_central_engine.dispose.assert_called_once()

        # Verify local engines disposal
        mock_local_engine1.dispose.assert_called_once()
        mock_local_engine2.dispose.assert_called_once()

        # Verify cleanup
        assert manager._local_engines == {}
        assert manager._local_session_factories == {}


class TestDependencyFunctions:
    """Test cases for dependency injection functions."""

    @pytest.mark.asyncio
    async def test_initialize_connection_manager(self) -> None:
        """Test global connection manager initialization."""
        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            mock_manager.initialize = AsyncMock()

            await initialize_connection_manager()

            mock_manager.initialize.assert_called_once()

    @pytest.mark.asyncio
    async def test_shutdown_connection_manager(self) -> None:
        """Test global connection manager shutdown."""
        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            mock_manager.shutdown = AsyncMock()

            await shutdown_connection_manager()

            mock_manager.shutdown.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_central_db_session_success(self) -> None:
        """Test get_central_db_session with initialized factory."""
        mock_session = AsyncMock(spec=AsyncSession)
        mock_session_factory = MagicMock()
        mock_session_factory.return_value.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_factory.return_value.__aexit__ = AsyncMock(return_value=None)

        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            mock_manager._central_session_factory = mock_session_factory

            async_gen = get_central_db_session()
            session = await async_gen.__anext__()

            assert session is mock_session

    @pytest.mark.asyncio
    async def test_get_central_db_session_not_initialized(self) -> None:
        """Test get_central_db_session raises error when not initialized."""
        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            mock_manager._central_session_factory = None

            with pytest.raises(DatabaseError) as exc_info:
                async_gen = get_central_db_session()
                await async_gen.__anext__()

            assert "Central session factory is not initialized" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_project_repository_dependency(self) -> None:
        """Test get_project_repository_dependency creates ProjectRepository."""
        mock_session = MagicMock(spec=AsyncSession)

        result = await get_project_repository_dependency(mock_session)

        assert isinstance(result, ProjectRepository)
        assert result.db_session is mock_session

    @pytest.mark.asyncio
    async def test_get_contextual_db_session(self) -> None:
        """Test get_contextual_db_session delegates to connection manager."""
        mock_session = AsyncMock(spec=AsyncSession)
        mock_temp_session = AsyncMock(spec=AsyncSession)
        mock_project_repo = MagicMock(spec=ProjectRepository)

        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            # Mock the central session factory for creating temp session
            mock_manager._central_session_factory.return_value.__aenter__ = AsyncMock(return_value=mock_temp_session)
            mock_manager._central_session_factory.return_value.__aexit__ = AsyncMock(return_value=None)

            # Mock the get_session method
            mock_manager.get_session.return_value.__aiter__.return_value = [mock_session]

            # Mock ProjectRepository creation
            with patch("src.core.database.connection_manager.ProjectRepository") as mock_project_repo_class:
                mock_project_repo_class.return_value = mock_project_repo

                async_gen = get_contextual_db_session(project_id=123)
                session = await async_gen.__anext__()

                assert session is mock_session
                mock_manager.get_session.assert_called_once_with(mock_project_repo, 123)
                mock_project_repo_class.assert_called_once_with(mock_temp_session)
