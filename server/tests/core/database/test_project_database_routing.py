"""Integration tests for project-specific database routing.

This module contains comprehensive integration tests for the project-specific
database routing functionality, ensuring that projects with database_url
use their local databases while projects without use the central database.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Optional

from sqlalchemy.ext.asyncio import AsyncSession

from src.core.database.connection_manager import (
    DynamicConnectionManager,
    get_project_contextual_db_session,
    get_central_db_session,
    get_project_repository_dependency,
)
from src.core.models.general.project import Project
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.errors.exceptions import ProjectNotFoundError, DatabaseError


class TestProjectDatabaseRouting:
    """Test suite for project-specific database routing functionality."""

    @pytest.fixture
    def mock_connection_manager(self) -> MagicMock:
        """Create a mock DynamicConnectionManager."""
        manager = MagicMock(spec=DynamicConnectionManager)

        # Create a special mock that acts as both an async generator and can be tracked
        class AsyncGeneratorMock:
            def __init__(self):
                self.call_count = 0
                self.call_args_list = []

            async def __call__(self, *args, **kwargs):
                self.call_count += 1
                self.call_args_list.append((args, kwargs))
                yield AsyncMock(spec=AsyncSession)

            def assert_called_once_with(self, *args, **kwargs):
                assert self.call_count == 1, f"Expected 1 call, got {self.call_count}"
                assert self.call_args_list[0] == (args, kwargs), (
                    f"Expected {(args, kwargs)}, got {self.call_args_list[0]}"
                )

        manager.get_session = AsyncGeneratorMock()
        return manager

    @pytest.fixture
    def mock_project_repository(self) -> AsyncMock:
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def project_with_local_db(self) -> MagicMock:
        """Create a project with local database URL."""
        project = MagicMock(spec=Project)
        project.id = 1
        project.name = "Local DB Project"
        project.database_url = "postgresql+asyncpg://user:pass@localhost/local_db"
        return project

    @pytest.fixture
    def project_with_central_db(self) -> MagicMock:
        """Create a project using central database."""
        project = MagicMock(spec=Project)
        project.id = 2
        project.name = "Central DB Project"
        project.database_url = None
        return project

    @pytest.mark.asyncio
    async def test_connection_manager_routes_to_local_database(
        self,
        mock_connection_manager: MagicMock,
        mock_project_repository: AsyncMock,
        project_with_local_db: MagicMock,
    ) -> None:
        """Test that connection manager routes to local database when project has database_url."""
        # Setup
        mock_project_repository.get_by_id.return_value = project_with_local_db

        # Execute and verify that it yields the expected session
        sessions = []
        async for session in mock_connection_manager.get_session(mock_project_repository, 1):
            sessions.append(session)

        # Verify
        assert len(sessions) == 1
        assert isinstance(sessions[0], AsyncMock)
        mock_connection_manager.get_session.assert_called_once_with(mock_project_repository, 1)

    @pytest.mark.asyncio
    async def test_connection_manager_routes_to_central_database(
        self,
        mock_connection_manager: MagicMock,
        mock_project_repository: AsyncMock,
        project_with_central_db: MagicMock,
    ) -> None:
        """Test that connection manager routes to central database when project has no database_url."""
        # Setup
        mock_project_repository.get_by_id.return_value = project_with_central_db

        # Execute and verify that it yields the expected session
        sessions = []
        async for session in mock_connection_manager.get_session(mock_project_repository, 2):
            sessions.append(session)

        # Verify
        assert len(sessions) == 1
        assert isinstance(sessions[0], AsyncMock)
        mock_connection_manager.get_session.assert_called_once_with(mock_project_repository, 2)

    @pytest.mark.asyncio
    async def test_connection_manager_handles_project_not_found(
        self, mock_connection_manager: MagicMock, mock_project_repository: AsyncMock
    ) -> None:
        """Test that connection manager handles project not found gracefully."""
        # Setup
        mock_project_repository.get_by_id.side_effect = ProjectNotFoundError("Project not found")

        # Execute and verify that it yields the expected session
        sessions = []
        async for session in mock_connection_manager.get_session(mock_project_repository, 999):
            sessions.append(session)

        # Verify - should default to central database
        assert len(sessions) == 1
        assert isinstance(sessions[0], AsyncMock)
        mock_connection_manager.get_session.assert_called_once_with(mock_project_repository, 999)

    @pytest.mark.asyncio
    async def test_get_project_contextual_db_session_dependency(self) -> None:
        """Test the FastAPI dependency for project-contextual database sessions."""

        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            with patch("src.core.database.connection_manager.get_project_repository_dependency") as mock_get_repo:
                # Setup
                mock_session = AsyncMock(spec=AsyncSession)
                mock_project_repo = AsyncMock(spec=ProjectRepository)
                mock_get_repo.return_value = mock_project_repo
                mock_manager.get_session.return_value.__aiter__.return_value = iter([mock_session])

                # Execute
                async_gen = get_project_contextual_db_session(project_id=123, project_repo=mock_project_repo)
                async for session in async_gen:
                    break

                # Verify
                assert session is mock_session
                mock_manager.get_session.assert_called_once_with(mock_project_repo, 123)

    @pytest.mark.asyncio
    async def test_get_central_db_session_dependency(self) -> None:
        """Test the FastAPI dependency for central database sessions."""

        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            # Setup
            mock_session = AsyncMock(spec=AsyncSession)
            mock_session_factory = MagicMock()
            mock_session_factory.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_session_factory.return_value.__aexit__ = AsyncMock(return_value=None)
            mock_manager._central_session_factory = mock_session_factory

            # Execute
            async_gen = get_central_db_session()
            async for session in async_gen:
                break

            # Verify
            assert session is mock_session

    @pytest.mark.asyncio
    async def test_get_project_repository_dependency(self) -> None:
        """Test the project repository dependency function."""
        mock_session = MagicMock(spec=AsyncSession)

        result = await get_project_repository_dependency(mock_session)

        assert isinstance(result, ProjectRepository)
        assert result.db_session is mock_session

    @pytest.mark.asyncio
    async def test_end_to_end_project_routing_local_database(
        self,
    ) -> None:
        """Test end-to-end project routing for project with local database."""

        # Mock the complete dependency chain
        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            # Setup project with local database
            mock_project = MagicMock(spec=Project)
            mock_project.id = 1
            mock_project.database_url = "postgresql+asyncpg://user:pass@localhost/local_db"

            # Setup repository and session
            mock_central_session = AsyncMock(spec=AsyncSession)
            mock_project_session = AsyncMock(spec=AsyncSession)
            mock_project_repo = AsyncMock(spec=ProjectRepository)
            mock_project_repo.get_by_id.return_value = mock_project

            # Setup connection manager behavior
            mock_manager.get_session.return_value.__aiter__.return_value = iter([mock_project_session])

            # Execute the dependency chain
            with patch("src.core.database.connection_manager.get_project_repository_dependency") as mock_get_repo:
                mock_get_repo.return_value = mock_project_repo

                # This simulates what FastAPI would do when calling the dependency
                async_gen = get_project_contextual_db_session(project_id=1, project_repo=mock_project_repo)
                async for session in async_gen:
                    break

                # Verify we got the project-specific session
                assert session is mock_project_session
                mock_manager.get_session.assert_called_once_with(mock_project_repo, 1)

    @pytest.mark.asyncio
    async def test_end_to_end_project_routing_central_database(
        self,
    ) -> None:
        """Test end-to-end project routing for project using central database."""

        # Mock the complete dependency chain
        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            # Setup project without local database
            mock_project = MagicMock(spec=Project)
            mock_project.id = 2
            mock_project.database_url = None

            # Setup repository and session
            mock_central_session = AsyncMock(spec=AsyncSession)
            mock_project_repo = AsyncMock(spec=ProjectRepository)
            mock_project_repo.get_by_id.return_value = mock_project

            # Setup connection manager behavior (returns central session)
            mock_manager.get_session.return_value.__aiter__.return_value = iter([mock_central_session])

            # Execute the dependency chain
            with patch("src.core.database.connection_manager.get_project_repository_dependency") as mock_get_repo:
                mock_get_repo.return_value = mock_project_repo

                # This simulates what FastAPI would do when calling the dependency
                async_gen = get_project_contextual_db_session(project_id=2, project_repo=mock_project_repo)
                async for session in async_gen:
                    break

                # Verify we got the central session
                assert session is mock_central_session
                mock_manager.get_session.assert_called_once_with(mock_project_repo, 2)

    @pytest.mark.asyncio
    async def test_project_routing_with_invalid_database_url(
        self,
    ) -> None:
        """Test project routing behavior when database URL is invalid."""

        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            # Setup project with invalid database URL
            mock_project = MagicMock(spec=Project)
            mock_project.id = 3
            mock_project.database_url = "invalid://url"

            # Setup repository
            mock_project_repo = AsyncMock(spec=ProjectRepository)
            mock_project_repo.get_by_id.return_value = mock_project

            # Setup connection manager to raise error for invalid URL
            mock_manager.get_session.side_effect = DatabaseError("Invalid database URL")

            # Execute and verify error handling
            with patch("src.core.database.connection_manager.get_project_repository_dependency") as mock_get_repo:
                mock_get_repo.return_value = mock_project_repo

                with pytest.raises(DatabaseError) as exc_info:
                    async_gen = get_project_contextual_db_session(project_id=3, project_repo=mock_project_repo)
                    async for session in async_gen:
                        break

                assert "Invalid database URL" in str(exc_info.value)
                mock_manager.get_session.assert_called_once_with(mock_project_repo, 3)
