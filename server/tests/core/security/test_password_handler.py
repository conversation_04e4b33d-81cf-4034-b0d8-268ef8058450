"""Unit tests for the PasswordHandler class."""

import pytest
from src.core.security.password_handler import PasswordHandler


class TestPasswordHandler:
    """Unit tests for the PasswordHandler class."""

    def test_hash_password_argon2(self):
        """Test that password hashing returns a string and is not the same as the password."""
        password = "a_strong_password_123!"
        password_hash = PasswordHandler.hash_password(password)
        assert isinstance(password_hash, str)
        assert password_hash != password

    def test_verify_password_correct(self):
        """Test that password verification succeeds with the correct password."""
        password = "a_strong_password_123!"
        password_hash = PasswordHandler.hash_password(password)
        assert PasswordHandler.verify_password(password, password_hash) is True

    def test_verify_password_incorrect(self):
        """Test that password verification fails with an incorrect password."""
        password = "a_strong_password_123!"
        wrong_password = "a_wrong_password_456!"
        password_hash = PasswordHandler.hash_password(password)
        assert PasswordHandler.verify_password(wrong_password, password_hash) is False

    def test_needs_rehash(self):
        """Test that a newly created hash does not need rehashing."""
        password = "a_strong_password_123!"
        password_hash = PasswordHandler.hash_password(password)
        assert PasswordHandler.needs_rehash(password_hash) is False

    def test_validate_password_strength_valid(self):
        """Test password strength validation with a valid password."""
        password = "A_Strong_Password_123!"
        validations = PasswordHandler.validate_password_strength(password)
        assert all(validations.values())

    def test_validate_password_strength_invalid_length(self):
        """Test password strength validation with a password that is too short."""
        password = "a_b_C1!"
        validations = PasswordHandler.validate_password_strength(password)
        assert not validations["length"]

    def test_validate_password_strength_missing_uppercase(self):
        """Test password strength validation with a password missing an uppercase letter."""
        password = "a_strong_password_123!"
        PasswordHandler.REQUIRE_UPPERCASE = True
        validations = PasswordHandler.validate_password_strength(password.lower())
        assert not validations["uppercase"]

    def test_validate_password_strength_missing_lowercase(self):
        """Test password strength validation with a password missing a lowercase letter."""
        password = "A_STRONG_PASSWORD_123!"
        PasswordHandler.REQUIRE_LOWERCASE = True
        validations = PasswordHandler.validate_password_strength(password.upper())
        assert not validations["lowercase"]

    def test_validate_password_strength_missing_digit(self):
        """Test password strength validation with a password missing a digit."""
        password = "a_strong_password_!"
        PasswordHandler.REQUIRE_DIGITS = True
        validations = PasswordHandler.validate_password_strength(password)
        assert not validations["digits"]

    def test_validate_password_strength_missing_special(self):
        """Test password strength validation with a password missing a special character."""
        password = "a_strong_password_123"
        PasswordHandler.REQUIRE_SPECIAL = True
        validations = PasswordHandler.validate_password_strength(password)
        assert not validations["special"]

    def test_is_password_valid(self):
        """Test the is_password_valid method with a valid password."""
        password = "A_Strong_Password_123!"
        assert PasswordHandler.is_password_valid(password) is True

    def test_is_password_invalid(self):
        """Test the is_password_valid method with an invalid password."""
        password = "short"
        assert PasswordHandler.is_password_valid(password) is False
