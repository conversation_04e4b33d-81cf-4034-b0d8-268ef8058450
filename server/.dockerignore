# Docker ignore file for the server application

# Ignore Git files
.git
.gitignore

# Ignore Docker files
.dockerignore
Dockerfile

# Ignore virtual environment
.venv
venv
env

# Ignore Python cache files
__pycache__/
*.pyc
*.pyo
*.pyd

# Ignore IDE and editor files
.vscode/
.idea/
*.swp
*.swo

# Ignore OS-specific files
.DS_Store
Thumbs.db

# Ignore test files and reports
tests/
test_*.py
*_test.py
htmlcov/
.pytest_cache/
coverage.xml
test-reports/

# Ignore local data and logs
data/
logs/
test_logs/
*.db
*.log

# Ignore poetry files
poetry.lock
poetry.toml

# Ignore build artifacts
dist/
build/
*.egg-info/

# Ignore local settings
.env