# MyPy Configuration for Ultimate Electrical Designer
# Optimized for module-level validation with SQLAlchemy workarounds

[mypy]
# Basic configuration
python_version = 3.13
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True

# Import handling
ignore_missing_imports = True
follow_imports = silent
show_error_codes = True

# Error reporting
show_error_context = True
show_column_numbers = True
pretty = True

# Strictness settings (balanced for current state)
strict_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True

# Performance optimizations
cache_dir = .mypy_cache
incremental = True

# Module-specific configurations to work around SQLAlchemy issues

# Critical modules - must be error-free
[mypy-src.core.utils.performance_optimizer]
strict = True
disallow_any_generics = True

[mypy-src.core.utils.memory_manager]
strict = True
disallow_any_generics = True

[mypy-src.core.utils.json_validation]
strict = True
disallow_any_generics = True

[mypy-src.core.utils.file_io_utils]
strict = True
disallow_any_generics = True

[mypy-src.config.settings]
strict = True
disallow_any_generics = True

# Security modules - high priority
[mypy-src.core.security.*]
strict = True
warn_return_any = True

# SQLAlchemy-affected modules - relaxed settings to avoid internal errors
[mypy-src.core.models.*]
ignore_errors = True
# Note: SQLAlchemy Mapped[] annotations cause MyPy internal errors

[mypy-src.core.repositories.*]
ignore_errors = True
# Note: Depends on models, affected by SQLAlchemy issue

[mypy-src.core.services.*]
ignore_errors = True
# Note: Depends on repositories, affected by SQLAlchemy issue

[mypy-src.api.*]
ignore_errors = True
# Note: Depends on services, affected by SQLAlchemy issue

# Utility modules - standard validation
[mypy-src.core.utils.*]
warn_return_any = True
disallow_untyped_defs = True

# Configuration modules - strict validation
[mypy-src.config.*]
strict = True
disallow_any_generics = True

# Error handling modules - strict validation
[mypy-src.core.errors.*]
strict = True
warn_return_any = True

# Monitoring modules - strict validation
[mypy-src.core.monitoring.*]
strict = True
warn_return_any = True

# Third-party library configurations
[mypy-sqlalchemy.*]
ignore_missing_imports = True

[mypy-alembic.*]
ignore_missing_imports = True

[mypy-fastapi.*]
ignore_missing_imports = True

[mypy-pydantic.*]
ignore_missing_imports = True

[mypy-uvicorn.*]
ignore_missing_imports = True

[mypy-pytest.*]
ignore_missing_imports = True

[mypy-argon2-cffi.*]
ignore_missing_imports = True

[mypy-jose.*]
ignore_missing_imports = True

[mypy-bcrypt.*]
ignore_missing_imports = True

[mypy-python_multipart.*]
ignore_missing_imports = True

# Development and testing
[mypy-tests.*]
ignore_errors = True
disallow_untyped_defs = False

# Documentation generation
[mypy-docs.*]
ignore_errors = True
