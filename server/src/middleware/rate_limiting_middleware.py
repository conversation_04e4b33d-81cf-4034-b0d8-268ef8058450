"""Rate Limiting Middleware.

This module provides advanced rate limiting capabilities including:
- Per-IP rate limiting with sliding window
- Per-user rate limiting for authenticated requests
- Different rate limits for different endpoints
- Burst allowance and token bucket algorithm
- Redis-backed storage for distributed systems
"""

import time
from collections import defaultdict, deque
from typing import Any, Awaitable, Callable, Deque, Dict, Optional, Set

from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from src.config.logging_config import logger
from src.middleware.context_middleware import get_user_context


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """Advanced rate limiting middleware with multiple strategies.

    Features:
    - Per-IP and per-user rate limiting
    - Sliding window algorithm
    - Different limits for different endpoint categories
    - Burst allowance with token bucket
    - Redis support for distributed systems
    - Configurable rate limit rules
    """

    def __init__(
        self,
        app: ASGIApp,
        default_requests_per_minute: int = 60,
        default_burst_size: int = 10,
        enable_per_ip_limiting: bool = True,
        enable_per_user_limiting: bool = True,
        enable_endpoint_specific_limits: bool = True,
        redis_client: Optional[Any] = None,
        exclude_paths: Optional[Set[str]] = None,
    ):
        """Initialize the rate limiting middleware.

        Args:
            app: ASGI application instance
            default_requests_per_minute: Default requests per minute limit
            default_burst_size: Default burst size for rate limiting
            enable_per_ip_limiting: Whether to enable per-IP rate limiting
            enable_per_user_limiting: Whether to enable per-user rate limiting
            enable_endpoint_specific_limits: Whether to enable endpoint-specific limits
            redis_client: Redis client instance for distributed rate limiting
            exclude_paths: Paths to exclude from rate limiting

        """
        super().__init__(app)
        self.default_requests_per_minute = default_requests_per_minute
        self.default_burst_size = default_burst_size
        self.enable_per_ip_limiting = enable_per_ip_limiting
        self.enable_per_user_limiting = enable_per_user_limiting
        self.enable_endpoint_specific_limits = enable_endpoint_specific_limits
        self.redis_client = redis_client

        # In-memory storage (fallback when Redis is not available)
        self.ip_requests: Dict[str, Deque[float]] = defaultdict(deque)
        self.user_requests: Dict[str, Deque[float]] = defaultdict(deque)
        self.token_buckets: Dict[str, Dict[str, Any]] = defaultdict(dict)

        # Default excluded paths
        self.exclude_paths = exclude_paths or {
            "/health",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/favicon.ico",
        }

        # Define endpoint-specific rate limits
        self.endpoint_limits = self._setup_endpoint_limits()

    def _setup_endpoint_limits(self) -> Dict[str, Dict[str, int]]:
        """Setup endpoint-specific rate limits."""
        return {
            # Authentication endpoints - stricter limits
            "/api/v1/auth/login": {
                "requests_per_minute": 10,
                "burst_size": 3,
            },
            "/api/v1/auth/register": {
                "requests_per_minute": 5,
                "burst_size": 2,
            },
            # File upload endpoints - moderate limits
            "/api/v1/documents/upload": {
                "requests_per_minute": 20,
                "burst_size": 5,
            },
            # Calculation endpoints - higher limits for authenticated users
            "/api/v1/heat-tracing/calculate": {
                "requests_per_minute": 100,
                "burst_size": 20,
            },
            # Report generation - moderate limits due to resource usage
            "/api/v1/reports": {
                "requests_per_minute": 30,
                "burst_size": 10,
            },
            # Admin endpoints - very strict limits
            "/api/v1/admin": {
                "requests_per_minute": 20,
                "burst_size": 5,
            },
            # Test endpoint - for integration tests
            "/test": {"requests_per_minute": 5, "burst_size": 2},
        }

    async def dispatch(self, request: Request, call_next: Callable[[Request], Awaitable[Response]]) -> Response:
        """Main middleware dispatch method that applies rate limiting."""
        logger.debug(f"RateLimitingMiddleware dispatch called for {request.method} {request.url.path}")

        # Add ASGI message logging
        original_call_next = call_next

        async def call_next_with_logging(request: Request) -> Response:
            logger.debug(f"RateLimitingMiddleware: Processing request {request.method} {request.url.path}")
            response = await original_call_next(request)
            logger.debug(f"RateLimitingMiddleware: Got response with status {response.status_code}")
            return response

        # Skip rate limiting if disabled in settings or in integration test mode
        import os

        from src.config.settings import settings

        is_integration_test = os.getenv("TESTING", "false").lower() == "true" and (
            "/api/v1/users/" in str(request.url.path) or "/api/v1/auth/" in str(request.url.path)
        )

        if not settings.RATE_LIMIT_ENABLED or is_integration_test:
            return await call_next(request)

        # Skip rate limiting for excluded paths
        if self._should_exclude_path(request.url.path):
            return await call_next(request)

        try:
            # Check rate limits
            rate_limit_result = await self._check_rate_limits(request)

            if not rate_limit_result["allowed"]:
                return self._create_rate_limit_response(rate_limit_result, request)

            # Process the request
            response = await call_next_with_logging(request)

            # Add rate limit headers to response
            self._add_rate_limit_headers(response, rate_limit_result)

            return response

        except Exception as e:
            logger.error("Rate limiting middleware error: {}", str(e), exc_info=True)
            # Continue processing if rate limiting fails
            return await call_next(request)

    def _should_exclude_path(self, path: str) -> bool:
        """Check if the path should be excluded from rate limiting."""
        return any(excluded in path for excluded in self.exclude_paths)

    async def _check_rate_limits(self, request: Request) -> Dict[str, Any]:
        """Check all applicable rate limits for the request.

        Returns:
            Dict with rate limit check results

        """
        current_time = time.time()
        client_ip = self._get_client_ip(request)
        user_context = get_user_context()
        user_id = user_context.get("id") if user_context else None

        # Get rate limit configuration for this endpoint
        limits = self._get_endpoint_limits(request.url.path)

        results = {
            "allowed": True,
            "limits": limits,
            "remaining": limits["requests_per_minute"],
            "reset_time": current_time + 60,
            "retry_after": 0,
        }

        # Check IP-based rate limiting
        if self.enable_per_ip_limiting:
            ip_result = self._check_ip_rate_limit(client_ip, current_time, limits)
            if not ip_result["allowed"]:
                results.update(ip_result)
                results["limit_type"] = "ip"
                return results
            results["remaining"] = min(results["remaining"], ip_result["remaining"])

        # Check user-based rate limiting (if authenticated)
        if self.enable_per_user_limiting and user_id:
            user_result = self._check_user_rate_limit(user_id, current_time, limits)
            if not user_result["allowed"]:
                results.update(user_result)
                results["limit_type"] = "user"
                return results
            results["remaining"] = min(results["remaining"], user_result["remaining"])

        # Check token bucket (burst protection)
        bucket_key = f"{client_ip}:{user_id or 'anonymous'}"
        bucket_result = self._check_token_bucket(bucket_key, current_time, limits)
        if not bucket_result["allowed"]:
            results.update(bucket_result)
            results["limit_type"] = "burst"
            results["allowed"] = False
            return results

        # Update remaining tokens from burst check if it's lower
        results["remaining"] = min(results["remaining"], bucket_result["remaining"])

        return results

    def _get_endpoint_limits(self, path: str) -> Dict[str, int]:
        """Get rate limit configuration for a specific endpoint."""
        # Check for exact matches first
        if path in self.endpoint_limits:
            return self.endpoint_limits[path]

        # Check for prefix matches
        for endpoint_pattern, limits in self.endpoint_limits.items():
            if path.startswith(endpoint_pattern):
                return limits

        # Return default limits
        return {
            "requests_per_minute": self.default_requests_per_minute,
            "burst_size": self.default_burst_size,
        }

    def _check_ip_rate_limit(self, client_ip: str, current_time: float, limits: Dict[str, int]) -> Dict[str, Any]:
        """Check IP-based rate limiting using sliding window."""
        window_size = 60  # 1 minute window
        max_requests = limits["requests_per_minute"]

        # Clean old entries
        ip_requests = self.ip_requests[client_ip]
        while ip_requests and ip_requests[0] < current_time - window_size:
            ip_requests.popleft()

        # Check if limit exceeded
        if len(ip_requests) >= max_requests:
            oldest_request = ip_requests[0] if ip_requests else current_time
            retry_after = int(oldest_request + window_size - current_time) + 1

            return {
                "allowed": False,
                "remaining": 0,
                "retry_after": retry_after,
                "reset_time": oldest_request + window_size,
            }

        # Add current request
        ip_requests.append(current_time)

        return {
            "allowed": True,
            "remaining": max_requests - len(ip_requests),
            "reset_time": current_time + window_size,
        }

    def _check_user_rate_limit(self, user_id: str, current_time: float, limits: Dict[str, int]) -> Dict[str, Any]:
        """Check user-based rate limiting."""
        # Similar to IP rate limiting but with potentially higher limits for authenticated users
        window_size = 60
        max_requests = limits["requests_per_minute"] * 2  # Double limit for authenticated users

        user_requests = self.user_requests[user_id]
        while user_requests and user_requests[0] < current_time - window_size:
            user_requests.popleft()

        if len(user_requests) >= max_requests:
            oldest_request = user_requests[0] if user_requests else current_time
            retry_after = int(oldest_request + window_size - current_time) + 1

            return {
                "allowed": False,
                "remaining": 0,
                "retry_after": retry_after,
                "reset_time": oldest_request + window_size,
            }

        user_requests.append(current_time)

        return {
            "allowed": True,
            "remaining": max_requests - len(user_requests),
            "reset_time": current_time + window_size,
        }

    def _check_token_bucket(self, bucket_key: str, current_time: float, limits: Dict[str, int]) -> dict[str, Any]:
        """Check token bucket for burst protection."""
        bucket = self.token_buckets[bucket_key]

        # Initialize bucket if not exists
        if not bucket:
            bucket.update(
                {
                    "tokens": limits["burst_size"],
                    "last_refill": current_time,
                    "capacity": limits["burst_size"],
                }
            )

        # Refill tokens based on time passed
        time_passed = current_time - bucket["last_refill"]
        refill_rate = limits["requests_per_minute"] / 60.0  # tokens per second
        tokens_to_add = int(time_passed * refill_rate)

        if tokens_to_add > 0:
            bucket["tokens"] = min(bucket["capacity"], bucket["tokens"] + tokens_to_add)
            bucket["last_refill"] = current_time

        # Check if tokens available
        if bucket["tokens"] < 1:
            retry_after = int(1.0 / refill_rate) + 1
            return {
                "allowed": False,
                "remaining": 0,
                "retry_after": retry_after,
                "reset_time": current_time + retry_after,
            }

        # Consume token
        bucket["tokens"] -= 1

        return {
            "allowed": True,
            "remaining": bucket["tokens"],
            "reset_time": current_time + 60,
        }

    def _create_rate_limit_response(self, rate_limit_result: dict[str, Any], request: Request) -> JSONResponse:
        """Create rate limit exceeded response."""
        # Build headers starting with rate limit headers
        headers = {
            "Retry-After": str(rate_limit_result["retry_after"]),
            "X-RateLimit-Limit": str(rate_limit_result["limits"]["requests_per_minute"]),
            "X-RateLimit-Remaining": "0",
            "X-RateLimit-Reset": str(int(rate_limit_result["reset_time"])),
        }

        # Add request ID from request state if available (set by context middleware)
        if hasattr(request.state, "request_id"):
            headers["X-Request-ID"] = request.state.request_id
            logger.debug(f"Added request ID to rate limit response: {request.state.request_id}")
        else:
            logger.debug("No request ID found in request state for rate limit response")

        return JSONResponse(
            status_code=429,
            content={
                "error": "Rate limit exceeded",
                "message": f"Too many requests. Limit: {rate_limit_result['limits']['requests_per_minute']} requests per minute",
                "retry_after": rate_limit_result["retry_after"],
                "limit_type": rate_limit_result.get("limit_type", "general"),
            },
            headers=headers,
        )

    def _add_rate_limit_headers(self, response: Response, rate_limit_result: dict[str, Any]) -> None:
        """Add rate limit headers to successful responses."""
        response.headers["X-RateLimit-Limit"] = str(rate_limit_result["limits"]["requests_per_minute"])
        response.headers["X-RateLimit-Remaining"] = str(rate_limit_result["remaining"])
        response.headers["X-RateLimit-Reset"] = str(int(rate_limit_result["reset_time"]))

    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers first
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip

        if hasattr(request, "client") and request.client:
            return request.client.host

        return "unknown"
