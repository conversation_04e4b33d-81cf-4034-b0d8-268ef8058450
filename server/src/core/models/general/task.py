"""Task and TaskAssignment Database Models.

SQLAlchemy models for task management within projects.
It includes models for tasks and their assignments to users.

Key models:
- Task: Represents individual tasks within a project with priority, status, and due dates
- TaskAssignment: Association table linking tasks to assigned users
"""

import datetime
from typing import TYPE_CHECKING, List, Optional
import uuid

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.dialects.postgresql import UUID

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns, EnumType
from src.core.enums import TaskPriority, TaskStatus
from src.core.utils.datetime_utils import utcnow_naive

if TYPE_CHECKING:
    from .project import Project
    from .user import User


class Task(CommonColumns, SoftDeleteColumns, Base):
    """Task model representing individual tasks within a project.

    This model stores task information including title, description, priority,
    status, due dates, and relationships to projects and assigned users.
    Tasks support assignment to multiple users through the TaskAssignment association table.

    Attributes:
        task_id: Unique UUID identifier for the task
        project_id: Foreign key to the associated project
        title: Task title (separate from CommonColumns.name which is excluded)
        description: Detailed task description
        due_date: Optional due date for the task
        priority: Task priority level (Low, Medium, High, Critical)
        status: Current task status (Not Started, In Progress, etc.)
        project: Related project entity
        assignments: List of task assignments to users
    """

    __tablename__ = "tasks"

    # Override the name field from CommonColumns to use title value
    @declared_attr
    def name(cls) -> Mapped[str]:
        """Override name to use title value for backward compatibility."""
        return mapped_column(String(255), nullable=False)

    # Unique task identifier (UUID)
    task_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the task",
    )

    # Project relationship
    project_id: Mapped[int] = mapped_column(
        ForeignKey("projects.id"), nullable=False, index=True, comment="Foreign key to the associated project"
    )

    # Task details - title field is separate from name in CommonColumns
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="Task title")

    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="Detailed task description")

    # Due date
    due_date: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True, comment="Optional due date for the task"
    )

    # Priority and status
    priority: Mapped[TaskPriority] = mapped_column(
        EnumType(TaskPriority), nullable=False, default=TaskPriority.MEDIUM, index=True, comment="Task priority level"
    )

    status: Mapped[TaskStatus] = mapped_column(
        EnumType(TaskStatus), nullable=False, default=TaskStatus.NOT_STARTED, index=True, comment="Current task status"
    )

    # Relationships
    project: Mapped["Project"] = relationship("Project", back_populates="tasks", foreign_keys=[project_id])

    assignments: Mapped[List["TaskAssignment"]] = relationship(
        "TaskAssignment", back_populates="task", cascade="all, delete-orphan"
    )

    __table_args__ = (UniqueConstraint("task_id", name="uq_task_task_id"),)

    def __init__(self, **kwargs):
        """Initialize Task and set name to title for backward compatibility."""
        # Set name to title if title is provided
        if "title" in kwargs and "name" not in kwargs:
            kwargs["name"] = kwargs["title"]
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        return f"<Task(task_id={self.task_id}, title='{self.title}', status={self.status.value if self.status else 'N/A'})>"


class TaskAssignment(CommonColumns, SoftDeleteColumns, Base):
    """Association model linking Tasks and Users for task assignments.

    This model represents the many-to-many relationship between tasks and users,
    allowing multiple users to be assigned to a single task and tracking
    assignment-specific information like assignment date and role.

    Attributes:
        task_id: Foreign key to the assigned task
        user_id: Foreign key to the assigned user
        assigned_at: Timestamp when the assignment was made
        assigned_by_user_id: ID of the user who made the assignment
        is_active: Whether the assignment is currently active
        task: Related task entity
        user: Related user entity
        assigned_by_user: User who made the assignment
    """

    __tablename__ = "task_assignments"

    # Task and User relationships
    task_id: Mapped[int] = mapped_column(
        ForeignKey("tasks.id"), nullable=False, index=True, comment="Foreign key to the assigned task"
    )

    user_id: Mapped[int] = mapped_column(
        ForeignKey("users.id"), nullable=False, index=True, comment="Foreign key to the assigned user"
    )

    # Assignment metadata
    assigned_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_naive, nullable=False, comment="Timestamp when the assignment was made"
    )

    assigned_by_user_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id"), nullable=True, comment="ID of the user who made the assignment"
    )

    is_active: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False, index=True, comment="Whether the assignment is currently active"
    )

    # Relationships
    task: Mapped["Task"] = relationship("Task", back_populates="assignments", foreign_keys=[task_id])

    user: Mapped["User"] = relationship("User", back_populates="task_assignments", foreign_keys=[user_id])

    assigned_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[assigned_by_user_id])

    __table_args__ = (UniqueConstraint("task_id", "user_id", name="uq_task_assignment_task_user"),)

    def __repr__(self) -> str:
        return f"<TaskAssignment(task_id={self.task_id}, user_id={self.user_id}, is_active={self.is_active})>"
