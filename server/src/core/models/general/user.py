"""User and User Preference Database Models.

SQLAlchemy models for user accounts and their preferences.
It includes models for user authentication, authorization, and application settings.

Key models:
- User: Represents user accounts with authentication and authorization details
- UserPreference: Stores user-specific application preferences and settings
"""

import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON>olean, ForeignKey, String, UniqueConstraint, Index, func, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns
from src.core.utils.json_validation import FlexibleJSON

if TYPE_CHECKING:
    from .activity_log import ActivityLog, AuditTrail
    from .project import ProjectMember
    from .synchronization_log import SynchronizationConflict, SynchronizationLog
    from .task import TaskAssignment
    from .user_role import UserRoleAssignment


class User(CommonColumns, SoftDeleteColumns, Base):
    """Model representing a user account.

    Users have authentication details, authorization roles,
    and preferences for the application.

    Properties:
        email: Unique email address for the user
        password_hash: Hashed password for authentication
        is_active: Indicates if the user account is active
        is_superuser: Indicates if the user has superuser privileges
        preferences: User-specific application preferences
        role_assignments: List of roles assigned to the user
        project_memberships: List of projects the user is a member of
        activity_logs: Audit trail of user activities
        audit_trails: Audit trails for changes made by the user
        synchronization_logs: Logs of synchronization activities performed by the user
        resolved_sync_conflicts: List of synchronization conflicts resolved by the user

    Methods:
        role: Returns the user's primary role based on assigned roles and their priorities
        __repr__: String representation of the User instance
    """

    __tablename__ = "users"

    email: Mapped[str] = mapped_column(String, nullable=False)
    password_hash: Mapped[Optional[str]] = mapped_column(nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_superuser: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    last_login: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Account lockout fields
    failed_login_attempts: Mapped[int] = mapped_column(default=0, nullable=False)
    locked_until: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    last_failed_login: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Email verification fields
    is_email_verified: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    email_verification_token: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    email_verification_expires: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    email_verified_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Password reset fields
    password_reset_token: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    password_reset_expires: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Preference relationships
    preferences: Mapped[Optional["UserPreference"]] = relationship(
        "UserPreference",
        back_populates="user",
        cascade="all, delete-orphan",
        uselist=False,
        foreign_keys="[UserPreference.user_id]",
    )

    # RBAC relationships
    role_assignments: Mapped[List["UserRoleAssignment"]] = relationship(
        "UserRoleAssignment",
        back_populates="user",
        cascade="all, delete-orphan",
        foreign_keys="[UserRoleAssignment.user_id]",
    )

    # Project relationships
    project_memberships: Mapped[List["ProjectMember"]] = relationship(
        "ProjectMember",
        back_populates="user",
        foreign_keys="[ProjectMember.user_id]",
    )

    # Audit trail relationships
    activity_logs: Mapped[List["ActivityLog"]] = relationship(
        "ActivityLog",
        back_populates="user",
        foreign_keys="[ActivityLog.user_id]",
    )

    audit_trails: Mapped[List["AuditTrail"]] = relationship(
        "AuditTrail",
        back_populates="user",
        foreign_keys="[AuditTrail.user_id]",
    )

    # Synchronization relationships
    synchronization_logs: Mapped[List["SynchronizationLog"]] = relationship(
        "SynchronizationLog",
        back_populates="user",
        foreign_keys="[SynchronizationLog.user_id]",
    )

    resolved_sync_conflicts: Mapped[List["SynchronizationConflict"]] = relationship(
        "SynchronizationConflict",
        back_populates="resolved_by_user",
        foreign_keys="[SynchronizationConflict.resolved_by_user_id]",
    )

    # Task relationships
    task_assignments: Mapped[List["TaskAssignment"]] = relationship(
        "TaskAssignment",
        back_populates="user",
        foreign_keys="[TaskAssignment.user_id]",
    )

    __table_args__ = (
        UniqueConstraint("name", name="uq_user_name"),
        UniqueConstraint("email", name="uq_user_email"),
        # Case-insensitive index for email lookups
        Index("ix_user_email_lower", func.lower(email)),
    )

    @property
    def role(self) -> Optional[str]:
        """Get the user's primary role name.

        Returns the role with the highest priority if multiple roles are assigned.
        """
        try:
            if not self.role_assignments:
                return None

            # Get active role assignments sorted by priority (highest first)
            active_assignments = [
                assignment for assignment in self.role_assignments if assignment.is_active and not assignment.is_deleted
            ]

            if not active_assignments:
                return None

            # Sort by role priority (highest first)
            active_assignments.sort(key=lambda x: x.role.priority if x.role else 0, reverse=True)
            return active_assignments[0].role.name if active_assignments[0].role else None
        except Exception:
            # If we can't access role_assignments (e.g., outside async context),
            # return None gracefully
            return None

    @property
    def is_locked(self) -> bool:
        """Check if the account is currently locked."""
        if self.locked_until is None:
            return False
        
        from src.core.utils.datetime_utils import utcnow_aware
        return utcnow_aware() < self.locked_until

    @property
    def is_verification_token_valid(self) -> bool:
        """Check if the email verification token is valid and not expired."""
        if not self.email_verification_token or not self.email_verification_expires:
            return False
        
        from src.core.utils.datetime_utils import utcnow_aware
        return utcnow_aware() < self.email_verification_expires

    @property
    def is_password_reset_token_valid(self) -> bool:
        """Check if the password reset token is valid and not expired."""
        if not self.password_reset_token or not self.password_reset_expires:
            return False
        
        from src.core.utils.datetime_utils import utcnow_aware
        return utcnow_aware() < self.password_reset_expires

    def __repr__(self) -> str:
        return f"<User(id={self.id}, name='{self.name}', email='{self.email}')>"


class UserPreference(CommonColumns, SoftDeleteColumns, Base):
    """Model representing user preferences and application settings.

    User preferences include UI theme, default temperature settings,
    preferred manufacturers, and other application-specific settings.

    Attributes:
        user_id: Foreign key to the User this preference belongs to
        ui_theme: Preferred UI theme for the user (e.g., 'light', 'dark')
        user: Relationship to the User this preference belongs to

    Methods:
        __repr__: String representation of the UserPreference instance
    """

    __tablename__ = "user_basic_preferences"

    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False, unique=True)

    ui_theme: Mapped[str] = mapped_column(default="light", nullable=False)

    # Relationships
    user: Mapped["User"] = relationship(back_populates="preferences", foreign_keys=[user_id])

    def __repr__(self) -> str:
        return f"<UserPreference(id={self.id}, user_id={self.user_id}, ui_theme='{self.ui_theme}')>"
