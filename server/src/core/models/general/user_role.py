"""User Role and Role Assignment Database Models.

SQLAlchemy models for role-based access control (RBAC) system.
It includes models for user roles, permissions, and role assignments.

Key models:
- UserRole: Represents roles that can be assigned to users
- UserRoleAssignment: Links users to roles with additional metadata
"""

import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns
from src.core.utils.datetime_utils import utcnow_aware, utcnow_naive

if TYPE_CHECKING:
    from .permission import RolePermission
    from .project import ProjectMember
    from .user import User


class UserRole(CommonColumns, SoftDeleteColumns, Base):
    """Model representing a user role for RBAC system.

    Roles define sets of permissions that can be assigned to users.
    This supports a flexible role-based access control system.

    Attributes:
        description: Optional description of the role.
        is_system_role: Indicates if this is a system-defined role.
        is_active: Indicates if the role is currently active.
        permissions: JSON string representing the permissions granted by this role.
        parent_role_id: Optional foreign key to another UserRole for hierarchy.
        priority: Integer indicating the priority of this role for resolution.
        parent_role: Self-referential relationship to support role hierarchy.
        child_roles: List of child roles that inherit from this role.
        role_assignments: List of UserRoleAssignment linking users to this role.
        project_assignments: List of ProjectMember linking projects to this role.
    """

    __tablename__ = "user_roles"

    # Role properties
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_system_role: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Permission scope
    permissions: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON string of permissions

    # Role hierarchy support
    parent_role_id: Mapped[Optional[int]] = mapped_column(ForeignKey("user_roles.id"), nullable=True)

    # Priority for role resolution (higher number = higher priority)
    priority: Mapped[int] = mapped_column(default=0, nullable=False)

    # Self-referential relationship for role hierarchy
    parent_role: Mapped[Optional["UserRole"]] = relationship(
        "UserRole",
        remote_side="UserRole.id",
        back_populates="child_roles",
        foreign_keys=[parent_role_id],
    )

    child_roles: Mapped[List["UserRole"]] = relationship(
        "UserRole",
        back_populates="parent_role",
        foreign_keys=[parent_role_id],
    )

    # Relationships
    role_assignments: Mapped[List["UserRoleAssignment"]] = relationship(
        "UserRoleAssignment",
        back_populates="role",
        cascade="all, delete-orphan",
    )

    project_assignments: Mapped[List["ProjectMember"]] = relationship(
        "ProjectMember",
        back_populates="role",
    )

    role_permissions: Mapped[List["RolePermission"]] = relationship(
        "RolePermission",
        back_populates="role",
        cascade="all, delete-orphan",
    )

    __table_args__ = (UniqueConstraint("name", name="uq_user_role_name"),)

    def __repr__(self) -> str:
        return f"<UserRole(id={self.id}, name='{self.name}', is_active={self.is_active})>"


class UserRoleAssignment(SoftDeleteColumns, Base):
    """Model representing the assignment of a role to a user.

    This junction table links users to roles with additional metadata
    such as assignment dates, expiration, and assignment context.

    Attributes:
        id: Primary key for the role assignment
        user_id: Foreign key to the User being assigned the role.
        role_id: Foreign key to the UserRole being assigned.
        assigned_by_user_id: Optional foreign key to the User who assigned the role.
        assigned_at: Timestamp when the role was assigned.
        expires_at: Optional expiration date for the role assignment.
        is_active: Indicates if the role assignment is currently active.
        assignment_context: Optional context or reason for the assignment.
        notes: Optional notes about the role assignment
        created_at: Timestamp when the assignment was created
        updated_at: Timestamp when the assignment was last updated
        user: Relationship to the User being assigned the role.
        role: Relationship to the UserRole being assigned.
        assigned_by_user: Relationship to the User who assigned the role.
        is_expired: Property to check if the role assignment has expired.
    """

    __tablename__ = "user_role_assignments"

    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    # Assignment name
    name: Mapped[str] = mapped_column(String(100), nullable=False)

    # Foreign keys
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    role_id: Mapped[int] = mapped_column(ForeignKey("user_roles.id"), nullable=False)

    # Assignment metadata
    assigned_by_user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), nullable=True)
    assigned_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utcnow_naive, nullable=False)

    # Optional expiration
    expires_at: Mapped[Optional[datetime.datetime]] = mapped_column(DateTime, nullable=True)

    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Context or reason for assignment
    assignment_context: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Common fields
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utcnow_naive, nullable=False)
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_naive, onupdate=utcnow_naive, nullable=False
    )

    # Relationships
    user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="role_assignments",
    )

    role: Mapped["UserRole"] = relationship(
        "UserRole",
        foreign_keys=[role_id],
        back_populates="role_assignments",
    )

    assigned_by_user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[assigned_by_user_id],
    )

    __table_args__ = (UniqueConstraint("user_id", "role_id", name="uq_user_role_assignment"),)

    def __repr__(self) -> str:
        return (
            f"<UserRoleAssignment(id={self.id}, user_id={self.user_id}, "
            f"role_id={self.role_id}, is_active={self.is_active})>"
        )

    @property
    def is_expired(self) -> bool:
        """Check if the role assignment has expired."""
        if self.expires_at is None:
            return False
        return utcnow_naive() > self.expires_at
