"""System Configuration Database Models.

SQLAlchemy models for electrical system configuration management with
IEEE/IEC standards compliance and user preference management.

Key models:
- SystemConfiguration: Global and project-specific electrical system settings
- ElectricalStandard: IEEE/IEC/EN electrical standards definitions
- UserPreferences: User-specific configuration preferences
- ConfigurationTemplate: Reusable configuration templates
"""

import datetime
from typing import TYPE_CHECKING, List, Optional
import uuid

from sqlalchemy import Boolean, DateTime, ForeignKey, String, Text, UniqueConstraint, JSON, Float
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns, EnumType
from src.core.enums.common_enums import TemperatureUnit, VoltageClass, FrequencyType
from src.core.enums.standards_enums import ElectricalStandardType, ComplianceLevel
from src.core.utils.datetime_utils import utcnow_naive

if TYPE_CHECKING:
    from .project import Project
    from .user import User


class SystemConfiguration(CommonColumns, SoftDeleteColumns, Base):
    """System configuration model for electrical system settings.

    Manages both global system configuration and project-specific overrides
    for electrical design parameters, calculation methods, and compliance settings.

    Attributes:
        config_id: Unique UUID identifier for the configuration
        project_id: Optional foreign key for project-specific configurations (null for global)
        scope: Configuration scope (global, project, user)
        category: Configuration category (calculation, display, standards, etc.)
        voltage_system: Default voltage system configuration
        frequency_system: Default frequency configuration (50Hz/60Hz)
        temperature_unit: Default temperature unit (Celsius/Fahrenheit)
        calculation_precision: Number of decimal places for calculations
        rounding_method: Rounding method for calculations
        safety_factors: JSON configuration of safety factors by equipment type
        environmental_conditions: JSON of default environmental parameters
        standards_compliance: JSON array of applicable electrical standards
        validation_rules: JSON configuration of validation rules
        is_active: Whether this configuration is currently active
        project: Related project entity (if project-specific)
    """

    __tablename__ = "system_configurations"

    # Unique configuration identifier (UUID)
    config_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the configuration",
    )

    # Project relationship (optional - null for global configurations)
    project_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("projects.id"),
        nullable=True,
        index=True,
        comment="Optional foreign key for project-specific configurations",
    )

    # Configuration scope and category
    scope: Mapped[str] = mapped_column(
        String(50), nullable=False, index=True, default="global", comment="Configuration scope (global, project, user)"
    )

    category: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        index=True,
        comment="Configuration category (calculation, display, standards, etc.)",
    )

    # Electrical system defaults
    voltage_system: Mapped[Optional[str]] = mapped_column(
        String(50), nullable=True, comment="Default voltage system configuration"
    )

    frequency_system: Mapped[Optional[FrequencyType]] = mapped_column(
        EnumType(FrequencyType), nullable=True, comment="Default frequency configuration (50Hz/60Hz)"
    )

    temperature_unit: Mapped[Optional[TemperatureUnit]] = mapped_column(
        EnumType(TemperatureUnit), nullable=True, default=TemperatureUnit.CELSIUS, comment="Default temperature unit"
    )

    # Calculation settings
    calculation_precision: Mapped[int] = mapped_column(
        nullable=False, default=2, comment="Number of decimal places for calculations"
    )

    rounding_method: Mapped[str] = mapped_column(
        String(20), nullable=False, default="round_half_up", comment="Rounding method for calculations"
    )

    # Configuration data (JSON)
    safety_factors: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of safety factors by equipment type"
    )

    environmental_conditions: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON of default environmental parameters"
    )

    standards_compliance: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON array of applicable electrical standards"
    )

    validation_rules: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of validation rules"
    )

    # Status
    is_active: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False, index=True, comment="Whether this configuration is currently active"
    )

    # Relationships
    project: Mapped[Optional["Project"]] = relationship("Project", foreign_keys=[project_id])

    __table_args__ = (
        UniqueConstraint("config_id", name="uq_system_config_id"),
        UniqueConstraint("project_id", "category", "scope", name="uq_project_config_category"),
    )

    def __repr__(self) -> str:
        scope_info = f"project={self.project_id}" if self.project_id else "global"
        return f"<SystemConfiguration(config_id={self.config_id}, category='{self.category}', scope={scope_info})>"


class ElectricalStandard(CommonColumns, SoftDeleteColumns, Base):
    """Electrical standards model for IEEE/IEC/EN standards definitions.

    Manages electrical engineering standards compliance requirements,
    validation rules, and calculation methods for different standards.

    Attributes:
        standard_id: Unique UUID identifier for the standard
        standard_type: Type of electrical standard (IEC, IEEE, EN, etc.)
        standard_number: Official standard number (e.g., "IEC 60364")
        title: Official standard title
        version: Standard version/revision
        publication_date: Standard publication date
        compliance_level: Required compliance level (mandatory, recommended, optional)
        applicable_regions: JSON array of applicable geographic regions
        scope_description: Description of standard scope and applicability
        calculation_methods: JSON configuration of calculation methods
        validation_rules: JSON configuration of validation rules
        parameter_limits: JSON configuration of parameter limits and ranges
        is_active: Whether this standard is currently active for compliance
    """

    __tablename__ = "electrical_standards"

    # Unique standard identifier (UUID)
    standard_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the standard",
    )

    # Standard identification
    standard_type: Mapped[ElectricalStandardType] = mapped_column(
        EnumType(ElectricalStandardType), nullable=False, index=True, comment="Type of electrical standard"
    )

    standard_number: Mapped[str] = mapped_column(
        String(50), nullable=False, index=True, comment="Official standard number (e.g., 'IEC 60364')"
    )

    title: Mapped[str] = mapped_column(String(500), nullable=False, comment="Official standard title")

    version: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, comment="Standard version/revision")

    publication_date: Mapped[Optional[datetime.date]] = mapped_column(
        nullable=True, comment="Standard publication date"
    )

    # Compliance requirements
    compliance_level: Mapped[ComplianceLevel] = mapped_column(
        EnumType(ComplianceLevel), nullable=False, index=True, comment="Required compliance level"
    )

    applicable_regions: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON array of applicable geographic regions"
    )

    scope_description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="Description of standard scope and applicability"
    )

    # Technical specifications
    calculation_methods: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of calculation methods"
    )

    validation_rules: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of validation rules"
    )

    parameter_limits: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of parameter limits and ranges"
    )

    # Status
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        index=True,
        comment="Whether this standard is currently active for compliance",
    )

    __table_args__ = (
        UniqueConstraint("standard_id", name="uq_electrical_standard_id"),
        UniqueConstraint("standard_number", "version", name="uq_standard_number_version"),
    )

    def __repr__(self) -> str:
        version_info = f" v{self.version}" if self.version else ""
        return f"<ElectricalStandard(standard_number='{self.standard_number}{version_info}', type={self.standard_type.value if self.standard_type else 'N/A'})>"


class UserPreferences(CommonColumns, SoftDeleteColumns, Base):
    """User preferences model for personalized system configuration.

    Stores user-specific preferences for electrical design interface,
    calculation defaults, display settings, and workflow preferences.

    Attributes:
        preference_id: Unique UUID identifier for the preference set
        user_id: Foreign key to the user
        category: Preference category (ui, calculations, workflow, etc.)
        default_units: JSON configuration of preferred units
        calculation_preferences: JSON of calculation-specific preferences
        ui_preferences: JSON of user interface preferences
        notification_settings: JSON of notification preferences
        workflow_preferences: JSON of workflow and automation preferences
        is_active: Whether this preference set is currently active
        user: Related user entity
    """

    __tablename__ = "user_preferences"

    # Unique preference identifier (UUID)
    preference_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the preference set",
    )

    # User relationship
    user_id: Mapped[int] = mapped_column(
        ForeignKey("users.id"), nullable=False, index=True, comment="Foreign key to the user"
    )

    # Preference category
    category: Mapped[str] = mapped_column(
        String(100), nullable=False, index=True, comment="Preference category (ui, calculations, workflow, etc.)"
    )

    # Preference configurations (JSON)
    default_units: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of preferred units"
    )

    calculation_preferences: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON of calculation-specific preferences"
    )

    ui_preferences: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON of user interface preferences"
    )

    notification_settings: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON of notification preferences"
    )

    workflow_preferences: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON of workflow and automation preferences"
    )

    # Status
    is_active: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False, index=True, comment="Whether this preference set is currently active"
    )

    # Relationships
    user: Mapped["User"] = relationship("User", foreign_keys=[user_id])

    __table_args__ = (
        UniqueConstraint("preference_id", name="uq_user_preference_id"),
        UniqueConstraint("user_id", "category", name="uq_user_preference_category"),
    )

    def __repr__(self) -> str:
        return (
            f"<UserPreferences(preference_id={self.preference_id}, user_id={self.user_id}, category='{self.category}')>"
        )


class ConfigurationTemplate(CommonColumns, SoftDeleteColumns, Base):
    """Configuration template model for reusable system configurations.

    Provides predefined configuration templates for different electrical
    system types, regions, and standards compliance requirements.

    Attributes:
        template_id: Unique UUID identifier for the template
        template_type: Type of configuration template
        region: Target geographic region
        standards_set: JSON array of applicable standards
        default_values: JSON configuration of default parameter values
        validation_rules: JSON configuration of validation rules
        description: Template description and usage notes
        is_public: Whether template is available to all users
        created_by_user_id: User who created the template
        created_by_user: User who created the template
    """

    __tablename__ = "configuration_templates"

    # Unique template identifier (UUID)
    template_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the template",
    )

    # Template details
    template_type: Mapped[str] = mapped_column(
        String(100), nullable=False, index=True, comment="Type of configuration template"
    )

    region: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True, index=True, comment="Target geographic region"
    )

    standards_set: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON array of applicable standards"
    )

    default_values: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of default parameter values"
    )

    validation_rules: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True, comment="JSON configuration of validation rules"
    )

    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, comment="Template description and usage notes"
    )

    # Template sharing
    is_public: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False, index=True, comment="Whether template is available to all users"
    )

    created_by_user_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id"), nullable=True, index=True, comment="User who created the template"
    )

    # Relationships
    created_by_user: Mapped[Optional["User"]] = relationship("User", foreign_keys=[created_by_user_id])

    __table_args__ = (UniqueConstraint("template_id", name="uq_config_template_id"),)

    def __repr__(self) -> str:
        return f"<ConfigurationTemplate(template_id={self.template_id}, type='{self.template_type}', region='{self.region}')>"
