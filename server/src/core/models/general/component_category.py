"""Component Category Database Model.

the ComponentCategory model for electrical component category management
in the Ultimate Electrical Designer application. It provides comprehensive data
storage for component categories with hierarchical organization support.

Key Features:
- Hierarchical category organization with parent-child relationships
- Professional electrical design standards compliance
- Soft delete functionality with audit trails
- Comprehensive indexing for performance optimization
- Integration with ComponentType for relational data integrity
"""

import datetime
from typing import TYPE_CHECKING, List, Optional, Tuple

if TYPE_CHECKING:
    from src.core.models.general.component_type import ComponentType

from sqlalchemy import ForeignKey, Index, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns


class ComponentCategory(CommonColumns, SoftDeleteColumns, Base):
    """Component category model for electrical component organization.

    This model stores comprehensive information about component categories
    used for organizing electrical components in professional design workflows.
    It supports hierarchical organization and provides the foundation for
    component classification and filtering.

    Attributes:
        name: Category name (unique within parent level)
        description: Detailed category description
        parent_category_id: Optional parent category for hierarchical organization
        is_active: Whether category is active in the system
        component_types: Related component types in this category
        child_categories: Child categories for hierarchical organization
        parent_category: Parent category reference

    """

    __tablename__ = "component_categories"

    # Basic Information
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True, comment="Detailed category description")

    # Hierarchical Organization
    parent_category_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("component_categories.id"),
        nullable=True,
        index=True,
        comment="Parent category ID for hierarchical organization",
    )

    # Status
    is_active: Mapped[bool] = mapped_column(
        default=True,
        nullable=False,
        index=True,
        comment="Whether category is active in the system",
    )

    # Relationships
    component_types: Mapped[List["ComponentType"]] = relationship(
        "ComponentType",
        back_populates="category",
        cascade="all, delete-orphan",
        lazy="select",
    )

    child_categories: Mapped[List["ComponentCategory"]] = relationship(
        "ComponentCategory",
        back_populates="parent_category",
        cascade="all, delete-orphan",
        lazy="select",
    )

    parent_category: Mapped[Optional["ComponentCategory"]] = relationship(
        "ComponentCategory",
        back_populates="child_categories",
        remote_side="ComponentCategory.id",
        lazy="select",
    )

    # Indexes for performance optimization
    __table_args__ = (
        Index("idx_component_category_name_active", "name", "is_active"),
        Index("idx_component_category_parent_active", "parent_category_id", "is_active"),
        UniqueConstraint(
            "name",
            "parent_category_id",
            "is_deleted",
            name="uq_component_category_name_parent",
        ),
    )

    def __init__(self, **kwargs):
        """Initialize ComponentCategory with validation.

        Args:
            **kwargs: Category attributes

        Raises:
            ValueError: If category data is invalid

        """
        super().__init__(**kwargs)

    def __repr__(self) -> str:
        """String representation of ComponentCategory."""
        return f"<ComponentCategory(id={self.id}, name='{self.name}', active={self.is_active})>"

    def __str__(self) -> str:
        """Human-readable string representation."""
        return self.name

    @property
    def full_path(self) -> str:
        """Get the full hierarchical path of the category.

        Returns:
            str: Full path from root to this category (e.g., "Power Distribution > Switchboards")

        """
        if self.parent_category:
            return f"{self.parent_category.full_path} > {self.name}"
        return self.name

    @property
    def level(self) -> int:
        """Get the hierarchical level of the category.

        Returns:
            int: Level in hierarchy (0 for root categories)

        """
        if self.parent_category:
            return self.parent_category.level + 1
        return 0

    @property
    def is_root_category(self) -> bool:
        """Check if this is a root category.

        Returns:
            bool: True if this is a root category (no parent)

        """
        return self.parent_category_id is None

    @property
    def has_children(self) -> bool:
        """Check if this category has child categories.

        Returns:
            bool: True if this category has child categories

        """
        return len(self.child_categories) > 0

    @property
    def component_count(self) -> int:
        """Get the number of component types in this category.

        Returns:
            int: Number of active component types in this category

        """
        return len([ct for ct in self.component_types if ct.is_active and not ct.is_deleted])

    def get_all_descendant_categories(self) -> List["ComponentCategory"]:
        """Get all descendant categories recursively.

        Returns:
            List[ComponentCategory]: All descendant categories

        """
        descendants = []
        for child in self.child_categories:
            if not child.is_deleted:
                descendants.append(child)
                descendants.extend(child.get_all_descendant_categories())
        return descendants

    def get_all_component_types(self, include_descendants: bool = False) -> List["ComponentType"]:
        """Get all component types in this category.

        Args:
            include_descendants: Whether to include component types from descendant categories

        Returns:
            List[ComponentType]: Component types in this category (and descendants if requested)

        """
        component_types = [ct for ct in self.component_types if ct.is_active and not ct.is_deleted]

        if include_descendants:
            for descendant in self.get_all_descendant_categories():
                component_types.extend(descendant.get_all_component_types(include_descendants=False))

        return component_types

    def can_delete(self) -> Tuple[bool, Optional[str]]:
        """Check if this category can be safely deleted.

        Returns:
            Tuple[bool, Optional[str]]: (can_delete, reason_if_not)

        """
        # Check for active component types
        active_types = [ct for ct in self.component_types if ct.is_active and not ct.is_deleted]
        if active_types:
            return False, f"Category has {len(active_types)} active component types"

        # Check for active child categories
        active_children = [cc for cc in self.child_categories if cc.is_active and not cc.is_deleted]
        if active_children:
            return False, f"Category has {len(active_children)} active child categories"

        return True, None

    def soft_delete(self, deleted_by_user_id: Optional[int] = None) -> bool:
        """Soft delete the category with dependency checking.

        Args:
            deleted_by_user_id: ID of user performing deletion

        Returns:
            bool: True if deletion was successful

        Raises:
            ValueError: If category cannot be deleted due to dependencies

        """
        can_delete, reason = self.can_delete()
        if not can_delete:
            raise ValueError(f"Cannot delete category: {reason}")

        from src.core.utils.datetime_utils import utcnow_naive

        self.is_deleted = True
        self.deleted_at = utcnow_naive()
        self.deleted_by_user_id = deleted_by_user_id
        self.is_active = False

        return True
