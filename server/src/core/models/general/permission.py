"""Permission and Role-Permission Database Models.

SQLAlchemy models for fine-grained permission system within the RBAC framework.
It includes models for permissions and role-permission relationships.

Key models:
- Permission: Represents specific permissions that can be granted
- RolePermission: Links roles to permissions with additional metadata
"""

import datetime
from typing import TYPE_CHECKING, List, Optional

from sqlalchemy import <PERSON>ole<PERSON>, DateTime, ForeignKey, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns
from src.core.utils.datetime_utils import utcnow_naive

if TYPE_CHECKING:
    from .user import User
    from .user_role import UserRole


class Permission(CommonColumns, SoftDeleteColumns, Base):
    """Model representing a specific permission in the system.

    Permissions define granular access rights that can be assigned to roles.
    This supports fine-grained authorization control.

    Attributes:
        resource: The resource this permission applies to (e.g., 'project', 'component')
        action: The action this permission allows (e.g., 'read', 'write', 'delete')
        description: Optional description of what this permission grants
        is_system_permission: Indicates if this is a system-defined permission
        is_active: Indicates if the permission is currently active
        role_permissions: List of RolePermission linking roles to this permission
    """

    __tablename__ = "permissions"

    # Permission scope
    resource: Mapped[str] = mapped_column(String(100), nullable=False)
    action: Mapped[str] = mapped_column(String(50), nullable=False)

    # Permission properties
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_system_permission: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Relationships
    role_permissions: Mapped[List["RolePermission"]] = relationship(
        "RolePermission",
        back_populates="permission",
        cascade="all, delete-orphan",
    )

    __table_args__ = (
        UniqueConstraint("name", name="uq_permission_name"),
        UniqueConstraint("resource", "action", name="uq_permission_resource_action"),
    )

    def __repr__(self) -> str:
        return f"<Permission(id={self.id}, name='{self.name}', resource='{self.resource}', action='{self.action}')>"


class RolePermission(SoftDeleteColumns, Base):
    """Model representing the assignment of a permission to a role.

    This junction table links roles to permissions with additional metadata
    such as grant conditions and assignment context.

    Attributes:
        id: Primary key for the role permission assignment
        role_id: Foreign key to the UserRole being granted the permission
        permission_id: Foreign key to the Permission being granted
        granted_by_user_id: Optional foreign key to the User who granted the permission
        granted_at: Timestamp when the permission was granted
        is_active: Indicates if the permission grant is currently active
        grant_context: Optional context or reason for granting the permission
        notes: Optional notes about the permission assignment
        created_at: Timestamp when the permission assignment was created
        updated_at: Timestamp when the permission assignment was last updated
        role: Relationship to the UserRole being granted the permission
        permission: Relationship to the Permission being granted
        granted_by_user: Relationship to the User who granted the permission
    """

    __tablename__ = "role_permissions"

    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    # Foreign keys
    role_id: Mapped[int] = mapped_column(ForeignKey("user_roles.id"), nullable=False)
    permission_id: Mapped[int] = mapped_column(ForeignKey("permissions.id"), nullable=False)

    # Grant metadata
    granted_by_user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), nullable=True)
    granted_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utcnow_naive, nullable=False)

    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)

    # Context or reason for grant
    grant_context: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # Common fields
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime.datetime] = mapped_column(DateTime, default=utcnow_naive, nullable=False)
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_naive, onupdate=utcnow_naive, nullable=False
    )

    # Relationships
    role: Mapped["UserRole"] = relationship(
        "UserRole",
        foreign_keys=[role_id],
        back_populates="role_permissions",
    )

    permission: Mapped["Permission"] = relationship(
        "Permission",
        foreign_keys=[permission_id],
        back_populates="role_permissions",
    )

    granted_by_user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[granted_by_user_id],
    )

    __table_args__ = (UniqueConstraint("role_id", "permission_id", name="uq_role_permission"),)

    def __repr__(self) -> str:
        return (
            f"<RolePermission(id={self.id}, role_id={self.role_id}, "
            f"permission_id={self.permission_id}, is_active={self.is_active})>"
        )
