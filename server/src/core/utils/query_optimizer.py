"""Database Query Optimizer for Component Management System.

This module provides database query optimization capabilities including:
- Query plan analysis and optimization
- Index usage monitoring
- Connection pooling optimization
- Query batching and caching
- Performance metrics collection
"""

import logging
import time
from contextlib import contextmanager
from dataclasses import dataclass
from functools import wraps
from typing import Any, Dict, List, Optional, Tuple, Union

from sqlalchemy import func, select, text
from sqlalchemy.engine import Engine
from sqlalchemy.orm import Session
from sqlalchemy.pool import QueuePool

from src.config.logging_config import logger
from src.core.utils.advanced_cache_manager import cache_manager, cached


@dataclass
class QueryMetrics:
    """Metrics for query performance analysis."""

    query_hash: str
    execution_time: float
    rows_examined: int
    rows_returned: int
    index_usage: Dict[str, Any]
    query_plan: Optional[str] = None
    timestamp: float = 0.0


class QueryOptimizer:
    """Database query optimizer with performance monitoring."""

    def __init__(self, engine: Engine):
        """Initialize the query optimizer.

        Args:
            engine: SQLAlchemy engine instance

        """
        self.engine = engine
        self.query_metrics: List[QueryMetrics] = []
        self.slow_query_threshold = 1.0  # seconds

    def analyze_query_plan(self, query: str, params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Analyze query execution plan.

        Args:
            query: SQL query string
            params: Query parameters

        Returns:
            Dict with query plan analysis

        """
        try:
            with self.engine.connect() as conn:
                # Get query plan (PostgreSQL specific)
                explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"

                if params:
                    result = conn.execute(text(explain_query), params)
                else:
                    result = conn.execute(text(explain_query))

                row = result.fetchone()
                if row is not None:
                    plan_data = row[0]
                else:
                    return {"error": "No query plan data returned"}

                # Extract key metrics
                plan_info = {
                    "total_cost": self._extract_cost(plan_data),
                    "execution_time": self._extract_execution_time(plan_data),
                    "index_usage": self._extract_index_usage(plan_data),
                    "sequential_scans": self._count_sequential_scans(plan_data),
                    "sort_operations": self._count_sort_operations(plan_data),
                    "join_operations": self._count_join_operations(plan_data),
                    "raw_plan": plan_data,
                }

                return plan_info

        except Exception as e:
            logger.error(f"Error analyzing query plan: {e}")
            return {"error": str(e)}

    def optimize_query_with_hints(self, query: str) -> str:
        """Add optimization hints to query.

        Args:
            query: Original SQL query

        Returns:
            Optimized query with hints

        """
        # Add common optimization hints
        optimizations = []

        # Force index usage for component searches
        if "components" in query.lower() and "where" in query.lower():
            if "manufacturer" in query.lower():
                optimizations.append("/*+ INDEX(components idx_components_manufacturer) */")
            if "specifications" in query.lower():
                optimizations.append("/*+ INDEX(components idx_components_specifications_gin) */")

        # Add join hints for complex queries
        if "join" in query.lower():
            optimizations.append("/*+ USE_HASH_JOIN */")

        # Combine hints with query
        if optimizations:
            hint_string = " ".join(optimizations)
            return f"{hint_string}\n{query}"

        return query

    def batch_queries(self, queries: List[Tuple[str, Optional[Dict[str, Any]]]]) -> List[Any]:
        """Execute multiple queries in a batch for better performance.

        Args:
            queries: List of (query, params) tuples

        Returns:
            List of query results

        """
        results = []

        try:
            with self.engine.connect() as conn:
                # Use transaction for batch
                with conn.begin():
                    for query, params in queries:
                        if params:
                            result = conn.execute(text(query), params)
                        else:
                            result = conn.execute(text(query))

                        results.append(result.fetchall())

        except Exception as e:
            logger.error(f"Error in batch query execution: {e}")
            raise

        return results

    def get_slow_queries(self, limit: int = 10) -> List[QueryMetrics]:
        """Get slowest queries from metrics.

        Args:
            limit: Maximum number of queries to return

        Returns:
            List of slow query metrics

        """
        slow_queries = [metric for metric in self.query_metrics if metric.execution_time > self.slow_query_threshold]

        # Sort by execution time (descending)
        slow_queries.sort(key=lambda x: x.execution_time, reverse=True)

        return slow_queries[:limit]

    def get_index_usage_stats(self) -> Dict[str, Any]:
        """Get index usage statistics.

        Returns:
            Dict with index usage statistics

        """
        try:
            with self.engine.connect() as conn:
                # PostgreSQL specific query for index usage
                index_stats_query = """
                SELECT 
                    schemaname,
                    tablename,
                    indexname,
                    idx_tup_read,
                    idx_tup_fetch,
                    idx_scan
                FROM pg_stat_user_indexes
                WHERE schemaname = 'public'
                ORDER BY idx_scan DESC;
                """

                result = conn.execute(text(index_stats_query))
                index_stats = result.fetchall()

                # Format results
                stats = {
                    "total_indexes": len(index_stats),
                    "indexes": [
                        {
                            "schema": row[0],
                            "table": row[1],
                            "index": row[2],
                            "tuples_read": row[3],
                            "tuples_fetched": row[4],
                            "scans": row[5],
                        }
                        for row in index_stats
                    ],
                }

                return stats

        except Exception as e:
            logger.error(f"Error getting index usage stats: {e}")
            return {"error": str(e)}

    def suggest_indexes(self, table_name: str) -> List[str]:
        """Suggest indexes based on query patterns.

        Args:
            table_name: Name of the table to analyze

        Returns:
            List of suggested index creation statements

        """
        suggestions = []

        # Common patterns for components table
        if table_name == "components":
            suggestions.extend(
                [
                    "CREATE INDEX CONCURRENTLY idx_components_manufacturer_model ON components(manufacturer, model_number);",
                    "CREATE INDEX CONCURRENTLY idx_components_category_type ON components(category, component_type);",
                    "CREATE INDEX CONCURRENTLY idx_components_price_range ON components(unit_price) WHERE unit_price IS NOT NULL;",
                    "CREATE INDEX CONCURRENTLY idx_components_active_deleted ON components(is_active, is_deleted);",
                    "CREATE INDEX CONCURRENTLY idx_components_specifications_gin ON components USING GIN(specifications);",
                    "CREATE INDEX CONCURRENTLY idx_components_search_text ON components USING GIN(to_tsvector('english', name || ' ' || description));",
                ]
            )

        return suggestions

    def _extract_cost(self, plan_data: List[Dict[str, Any]]) -> float:
        """Extract total cost from query plan."""
        try:
            return float(plan_data[0]["Plan"]["Total Cost"])
        except (KeyError, IndexError):
            return 0.0

    def _extract_execution_time(self, plan_data: List[Dict[str, Any]]) -> float:
        """Extract execution time from query plan."""
        try:
            return float(plan_data[0]["Execution Time"])
        except (KeyError, IndexError):
            return 0.0

    def _extract_index_usage(self, plan_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Extract index usage information from query plan."""
        index_info: Dict[str, Any] = {"indexes_used": [], "sequential_scans": 0}

        def traverse_plan(node: Any) -> None:
            if isinstance(node, dict):
                if node.get("Node Type") == "Index Scan":
                    index_info["indexes_used"].append(node.get("Index Name", "unknown"))
                elif node.get("Node Type") == "Seq Scan":
                    index_info["sequential_scans"] += 1

                # Traverse child plans
                for key, value in node.items():
                    if key == "Plans" and isinstance(value, list):
                        for child_plan in value:
                            traverse_plan(child_plan)
                    elif isinstance(value, (dict, list)):
                        traverse_plan(value)
            elif isinstance(node, list):
                for item in node:
                    traverse_plan(item)

        try:
            traverse_plan(plan_data[0]["Plan"])
        except (KeyError, IndexError):
            pass

        return index_info

    def _count_sequential_scans(self, plan_data: List[Dict[str, Any]]) -> int:
        """Count sequential scans in query plan."""
        count = 0

        def count_scans(node: Any) -> None:
            nonlocal count
            if isinstance(node, dict):
                if node.get("Node Type") == "Seq Scan":
                    count += 1
                for value in node.values():
                    if isinstance(value, (dict, list)):
                        count_scans(value)
            elif isinstance(node, list):
                for item in node:
                    count_scans(item)

        try:
            count_scans(plan_data[0]["Plan"])
        except (KeyError, IndexError):
            pass

        return count

    def _count_sort_operations(self, plan_data: List[Dict[str, Any]]) -> int:
        """Count sort operations in query plan."""
        count = 0

        def count_sorts(node: Any) -> None:
            nonlocal count
            if isinstance(node, dict):
                if "Sort" in node.get("Node Type", ""):
                    count += 1
                for value in node.values():
                    if isinstance(value, (dict, list)):
                        count_sorts(value)
            elif isinstance(node, list):
                for item in node:
                    count_sorts(item)

        try:
            count_sorts(plan_data[0]["Plan"])
        except (KeyError, IndexError):
            pass

        return count

    def _count_join_operations(self, plan_data: List[Dict[str, Any]]) -> int:
        """Count join operations in query plan."""
        count = 0

        def count_joins(node: Any) -> None:
            nonlocal count
            if isinstance(node, dict):
                if "Join" in node.get("Node Type", ""):
                    count += 1
                for value in node.values():
                    if isinstance(value, (dict, list)):
                        count_joins(value)
            elif isinstance(node, list):
                for item in node:
                    count_joins(item)

        try:
            count_joins(plan_data[0]["Plan"])
        except (KeyError, IndexError):
            pass

        return count


def monitor_query_performance(func_name: Optional[str] = None) -> Any:
    """Decorator to monitor query performance.

    Args:
        func_name: Name for the monitored function

    Returns:
        Decorated function

    """

    def decorator(func: Any) -> Any:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            start_time = time.time()

            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time

                # Log slow queries
                if execution_time > 1.0:  # 1 second threshold
                    logger.warning(f"Slow query detected in {func_name or func.__name__}: {execution_time:.3f}s")

                return result

            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"Query error in {func_name or func.__name__} after {execution_time:.3f}s: {e}")
                raise

        return wrapper

    return decorator


@contextmanager
def optimized_session(engine: Engine) -> Any:
    """Context manager for optimized database sessions.

    Args:
        engine: SQLAlchemy engine

    Yields:
        Optimized database session

    """
    session = Session(engine)

    try:
        # Get database engine type for database-specific optimizations
        database_type = str(engine.url).split("://")[0].lower()

        # Set session-level optimizations based on database type
        if database_type == "postgresql":
            session.execute(text("SET work_mem = '256MB'"))
            session.execute(text("SET random_page_cost = 1.1"))
            session.execute(text("SET effective_cache_size = '1GB'"))
            logger.debug("PostgreSQL-specific query optimizations applied")
        else:
            logger.debug(f"Generic database session - no specific optimizations applied for {database_type}")

        yield session
        session.commit()

    except Exception as e:
        session.rollback()
        logger.error(f"Session error: {e}")
        raise
    finally:
        session.close()


# Global query optimizer instance
query_optimizer: Optional[QueryOptimizer] = None


def initialize_query_optimizer(engine: Engine) -> None:
    """Initialize the global query optimizer.

    Args:
        engine: SQLAlchemy engine instance

    """
    global query_optimizer
    query_optimizer = QueryOptimizer(engine)
    logger.info("Query optimizer initialized")
