"""Performance optimization utilities for large dataset operations.

This module provides utilities for optimizing performance when working with
large datasets, including memory management, caching strategies, and
batch processing optimizations.
"""

from functools import wraps
from typing import Any, Callable, Dict, List, Optional, TypeVar, cast

from src.config.logging_config import logger

T = TypeVar("T")


# Lazy import to avoid circular dependency
def _get_monitor_utility_performance() -> Any:
    """Lazy import of monitor_utility_performance to avoid circular imports."""
    from src.core.monitoring.unified_performance_monitor import (
        monitor_utility_performance,
    )

    return monitor_utility_performance


import time
import tracemalloc
from collections.abc import Callable, Generator, Iterator
from contextlib import contextmanager
from functools import wraps
from typing import Any, TypeVar

import psutil


class PerformanceMonitor:
    """Performance monitoring utility for tracking execution metrics.

    Provides context managers and decorators for monitoring execution time,
    memory usage, and other performance metrics during operations.
    """

    def __init__(self) -> None:
        """Initialize the performance monitor."""
        self.metrics: Dict[str, Any] = {}
        self.start_time: Optional[float] = None
        self.start_memory: Optional[float] = None

    @contextmanager
    def monitor_execution(self, operation_name: str) -> Generator[Dict[str, Any], None, None]:
        """Context manager for monitoring execution metrics.

        Args:
            operation_name: Name of the operation being monitored

        Yields:
            Dict[str, Any]: Real-time metrics dictionary

        Example:
            with monitor.monitor_execution("bulk_calculation") as metrics:
                # Perform operation
                result = perform_calculation()
                # metrics dict is updated in real-time

        """
        logger.debug(f"Starting performance monitoring for: {operation_name}")

        # Initialize metrics
        metrics = {
            "operation_name": operation_name,
            "start_time": time.time(),
            "start_memory_mb": self._get_memory_usage_mb(),
            "peak_memory_mb": 0.0,
            "execution_time_ms": 0.0,
            "cpu_usage_percent": 0.0,
        }

        # Start memory tracing
        tracemalloc.start()
        process = psutil.Process()
        initial_cpu_times = process.cpu_times()

        try:
            yield metrics
        finally:
            # Calculate final metrics
            end_time = time.time()
            final_cpu_times = process.cpu_times()
            current_memory, peak_memory = tracemalloc.get_traced_memory()
            tracemalloc.stop()

            # Calculate final metrics
            end_memory_mb = self._get_memory_usage_mb()

            # Update metrics
            start_time = cast(float, metrics["start_time"])
            start_memory_mb = cast(float, metrics["start_memory_mb"])
            metrics.update(
                {
                    "execution_time_ms": (end_time - start_time) * 1000,
                    "end_memory_mb": end_memory_mb,
                    "peak_memory_mb": peak_memory / 1024 / 1024,
                    "memory_delta_mb": end_memory_mb - start_memory_mb,
                    "cpu_time_delta": (
                        (final_cpu_times.user + final_cpu_times.system)
                        - (initial_cpu_times.user + initial_cpu_times.system)
                    ),
                }
            )

            logger.debug(f"Performance monitoring completed for: {operation_name}")
            logger.debug(f"Execution time: {metrics['execution_time_ms']:.2f}ms")
            logger.debug(f"Peak memory: {metrics['peak_memory_mb']:.2f}MB")

    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            return float(process.memory_info().rss / 1024 / 1024)
        except Exception as e:
            logger.warning(f"Failed to get memory usage: {e}")
            return 0.0


def performance_monitor(operation_name: Optional[str] = None) -> Any:
    """Decorator for monitoring function performance.

    Args:
        operation_name: Optional name for the operation (defaults to function name)

    Returns:
        Decorated function with performance monitoring

    Example:
        @performance_monitor("complex_calculation")
        def calculate_something(data):
            return process_data(data)

    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            op_name = operation_name or func.__name__
            monitor = PerformanceMonitor()

            with monitor.monitor_execution(op_name) as metrics:
                result = func(*args, **kwargs)

                # Log performance summary
                logger.debug(
                    f"Performance summary for {op_name}: "
                    f"{metrics['execution_time_ms']:.2f}ms, "
                    f"peak memory: {metrics['peak_memory_mb']:.2f}MB"
                )

                return result

        return wrapper

    return decorator


class BatchProcessor:
    """Utility for processing large datasets in batches to optimize memory usage.

    Provides methods for splitting large datasets into manageable chunks
    and processing them efficiently with memory management.
    """

    def __init__(self, batch_size: int = 100, max_memory_mb: float = 500.0):
        """Initialize the batch processor.

        Args:
            batch_size: Number of items to process per batch
            max_memory_mb: Maximum memory usage threshold in MB

        """
        self.batch_size = batch_size
        self.max_memory_mb = max_memory_mb
        self.monitor = PerformanceMonitor()

    def process_in_batches(
        self,
        items: List[T],
        processor_func: Callable[[List[T]], Any],
        progress_callback: Callable[[int, int], None] | None = None,
    ) -> List[Any]:
        """Process a large list of items in batches.

        Args:
            items: List of items to process
            processor_func: Function to process each batch
            progress_callback: Optional callback for progress updates

        Returns:
            List[Any]: Combined results from all batches

        Raises:
            MemoryError: If memory usage exceeds threshold

        """
        logger.info(f"Processing {len(items)} items in batches of {self.batch_size}")

        results = []
        total_batches = (len(items) + self.batch_size - 1) // self.batch_size

        for i in range(0, len(items), self.batch_size):
            batch_num = i // self.batch_size + 1
            batch = items[i : i + self.batch_size]

            logger.debug(f"Processing batch {batch_num}/{total_batches} ({len(batch)} items)")

            # Check memory usage
            current_memory = self.monitor._get_memory_usage_mb()
            if current_memory > self.max_memory_mb:
                logger.warning(f"Memory usage ({current_memory:.2f}MB) exceeds threshold ({self.max_memory_mb}MB)")
                # Force garbage collection
                import gc

                gc.collect()

                # Check again after cleanup
                current_memory = self.monitor._get_memory_usage_mb()
                if current_memory > self.max_memory_mb:
                    raise MemoryError(f"Memory usage ({current_memory:.2f}MB) exceeds threshold")

            # Process batch
            with self.monitor.monitor_execution(f"batch_{batch_num}") as metrics:
                batch_result = processor_func(batch)
                results.append(batch_result)

            # Update progress
            if progress_callback:
                progress_callback(batch_num, total_batches)

        logger.info(f"Completed processing {total_batches} batches")
        return results

    def stream_process(
        self,
        items: Iterator[T],
        processor_func: Callable[[T], Any],
        buffer_size: Optional[int] = None,
    ) -> Generator[Any, None, None]:
        """Stream process items one by one with buffering.

        Args:
            items: Iterator of items to process
            processor_func: Function to process each item
            buffer_size: Optional buffer size (defaults to batch_size)

        Yields:
            Any: Processed results

        Example:
            for result in processor.stream_process(large_dataset, process_item):
                handle_result(result)

        """
        buffer_size = buffer_size or self.batch_size
        buffer = []

        for item in items:
            buffer.append(item)

            if len(buffer) >= buffer_size:
                # Process buffer
                for buffered_item in buffer:
                    yield processor_func(buffered_item)

                # Clear buffer and check memory
                buffer.clear()
                current_memory = self.monitor._get_memory_usage_mb()
                if current_memory > self.max_memory_mb:
                    import gc

                    gc.collect()

        # Process remaining items in buffer
        for item in buffer:
            yield processor_func(item)


class CacheManager:
    """Simple in-memory cache manager with size limits and TTL support.

    Provides caching functionality to optimize repeated calculations
    and reduce database queries for large dataset operations.
    """

    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        """Initialize the cache manager.

        Args:
            max_size: Maximum number of items to cache
            default_ttl: Default time-to-live in seconds

        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, Any] = {}
        self.access_times: dict[str, float] = {}
        self.expiry_times: dict[str, float] = {}

    def get(self, key: str) -> Any | None:
        """Get item from cache.

        Args:
            key: Cache key

        Returns:
            Any | None: Cached value or None if not found/expired

        """
        if key not in self.cache:
            return None

        # Check if expired
        if key in self.expiry_times and time.time() > self.expiry_times[key]:
            self.delete(key)
            return None

        # Update access time
        self.access_times[key] = time.time()
        return self.cache[key]

    def set(self, key: str, value: Any, ttl: int | None = None) -> None:
        """Set item in cache.

        Args:
            key: Cache key
            value: Value to cache
            ttl: Time-to-live in seconds (optional)

        """
        # Evict if at capacity
        if len(self.cache) >= self.max_size and key not in self.cache:
            self._evict_lru()

        self.cache[key] = value
        self.access_times[key] = time.time()

        if ttl is not None:
            self.expiry_times[key] = time.time() + ttl
        elif self.default_ttl > 0:
            self.expiry_times[key] = time.time() + self.default_ttl

    def delete(self, key: str) -> None:
        """Delete item from cache."""
        self.cache.pop(key, None)
        self.access_times.pop(key, None)
        self.expiry_times.pop(key, None)

    def clear(self) -> None:
        """Clear all cached items."""
        self.cache.clear()
        self.access_times.clear()
        self.expiry_times.clear()

    def _evict_lru(self) -> None:
        """Evict least recently used item."""
        if not self.access_times:
            return

        lru_key = min(self.access_times, key=lambda k: self.access_times[k])
        self.delete(lru_key)

    def get_stats(self) -> dict[str, Any]:
        """Get cache statistics."""
        return {
            "size": len(self.cache),
            "max_size": self.max_size,
            "hit_rate": getattr(self, "_hit_count", 0) / max(getattr(self, "_total_requests", 1), 1),
            "memory_usage_mb": self._estimate_memory_usage(),
        }

    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage of cached items."""
        try:
            import sys

            total_size = sum(sys.getsizeof(v) for v in self.cache.values())
            return total_size / 1024 / 1024
        except Exception:
            return 0.0
