"""Project Member schemas.

This module provides Pydantic schemas for managing project members,
including adding, updating, and viewing memberships.
"""

from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel, Field
from src.core.schemas.base_schemas import (
    BaseSchema,
    PaginatedResponseSchema,
    TimestampMixin,
)
from src.core.schemas.general.user_role_schemas import UserRoleResponse
from src.core.schemas.general.user_schemas import UserReadSchema


class ProjectMemberBaseSchema(BaseSchema):
    """Base schema for project members."""

    name: str = Field(..., description="Name/title for this membership")
    user_id: int = Field(..., description="User ID of the member")
    project_id: int = Field(..., description="Project ID")
    role_id: int = Field(..., description="Role ID of the member in the project")
    is_active: bool = Field(True, description="Whether the membership is active")
    expires_at: Optional[datetime] = Field(None, description="When the membership expires")


class ProjectMemberCreateSchema(BaseModel):
    """Schema for adding a member to a project."""

    name: Optional[str] = Field(None, description="Name/title for this membership (auto-generated if not provided)")
    user_id: int = Field(..., description="User ID to add to the project")
    role_id: int = Field(..., description="Role ID for the user in the project")
    expires_at: Optional[datetime] = Field(None, description="When the membership expires")


class ProjectMemberUpdateSchema(BaseModel):
    """Schema for updating a project member."""

    role_id: Optional[int] = Field(None, description="New role ID for the member")
    is_active: Optional[bool] = Field(None, description="New status for the membership")
    expires_at: Optional[datetime] = Field(None, description="New expiration date")


class ProjectMemberReadSchema(ProjectMemberBaseSchema, TimestampMixin):
    """Schema for reading project members."""

    id: int = Field(..., description="Unique identifier for the membership")
    user: UserReadSchema = Field(..., description="User details")
    role: UserRoleResponse = Field(..., description="Role details")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class ProjectMemberListResponseSchema(PaginatedResponseSchema[ProjectMemberReadSchema]):
    """Paginated response schema for project members."""

    pass


__all__ = [
    "ProjectMemberBaseSchema",
    "ProjectMemberCreateSchema",
    "ProjectMemberUpdateSchema",
    "ProjectMemberReadSchema",
    "ProjectMemberListResponseSchema",
    "ProjectMemberUpdateSchema",
    "ProjectMemberReadSchema",
    "ProjectMemberListResponseSchema",
]
