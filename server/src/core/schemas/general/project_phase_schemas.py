"""Project Phase and Milestone schemas.

This module provides Pydantic schemas for project phase and milestone operations,
supporting IEEE/IEC electrical project lifecycle management.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional
import uuid

from pydantic import BaseModel, Field, field_validator, ValidationInfo
from src.core.enums.project_management_enums import ProjectPhaseType, MilestoneStatus, ProjectTemplateType
from src.core.schemas.base_schemas import (
    BaseSchema,
    PaginatedResponseSchema,
    TimestampMixin,
)


class ProjectPhaseBaseSchema(BaseSchema):
    """Base schema for project phases."""

    name: str = Field(..., min_length=1, max_length=255, description="Phase name")
    phase_type: ProjectPhaseType = Field(..., description="Type of project phase")
    start_date: datetime = Field(..., description="Phase start date")
    end_date: Optional[datetime] = Field(None, description="Phase end date")
    progress_percentage: int = Field(0, ge=0, le=100, description="Completion percentage")
    is_active: bool = Field(False, description="Whether phase is currently active")
    prerequisites: Optional[List[str]] = Field([], description="List of prerequisite phase IDs")
    deliverables: Optional[List[Dict[str, Any]]] = Field([], description="Expected deliverables")
    notes: Optional[str] = Field(None, max_length=2000, description="Phase notes")

    @field_validator("end_date")
    @classmethod
    def validate_end_date(cls, v: Optional[datetime], info: ValidationInfo) -> Optional[datetime]:
        """Validate end date is after start date."""
        if v is not None and hasattr(info, "data") and "start_date" in info.data:
            if v <= info.data["start_date"]:
                raise ValueError("End date must be after start date")
        return v

    @field_validator("prerequisites")
    @classmethod
    def validate_prerequisites(cls, v: Optional[List[str]]) -> List[str]:
        """Validate prerequisites format."""
        if v is None:
            return []
        for prereq in v:
            try:
                uuid.UUID(prereq)
            except ValueError:
                raise ValueError(f"Invalid prerequisite ID format: {prereq}")
        return v


class ProjectPhaseCreateSchema(ProjectPhaseBaseSchema):
    """Schema for creating a project phase."""

    project_id: int = Field(..., description="Project ID")


class ProjectPhaseUpdateSchema(BaseModel):
    """Schema for updating a project phase."""

    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Phase name")
    start_date: Optional[datetime] = Field(None, description="Phase start date")
    end_date: Optional[datetime] = Field(None, description="Phase end date")
    progress_percentage: Optional[int] = Field(None, ge=0, le=100, description="Completion percentage")
    is_active: Optional[bool] = Field(None, description="Whether phase is currently active")
    prerequisites: Optional[List[str]] = Field(None, description="List of prerequisite phase IDs")
    deliverables: Optional[List[Dict[str, Any]]] = Field(None, description="Expected deliverables")
    notes: Optional[str] = Field(None, max_length=2000, description="Phase notes")


class ProjectPhaseReadSchema(ProjectPhaseBaseSchema, TimestampMixin):
    """Schema for reading project phase data."""

    id: int = Field(..., description="Phase database ID")
    phase_id: str = Field(..., description="Phase UUID")
    project_id: int = Field(..., description="Project ID")


class ProjectMilestoneBaseSchema(BaseSchema):
    """Base schema for project milestones."""

    name: str = Field(..., min_length=1, max_length=255, description="Milestone name")
    title: str = Field(..., min_length=1, max_length=255, description="Milestone title")
    description: Optional[str] = Field(None, max_length=2000, description="Milestone description")
    due_date: datetime = Field(..., description="Milestone due date")
    completion_date: Optional[datetime] = Field(None, description="Actual completion date")
    status: MilestoneStatus = Field(MilestoneStatus.NOT_STARTED, description="Milestone status")
    assigned_user_id: Optional[int] = Field(None, description="Assigned user ID")
    dependencies: Optional[List[str]] = Field([], description="Dependent milestone IDs")
    completion_criteria: Optional[List[Dict[str, Any]]] = Field([], description="Completion criteria")

    @field_validator("completion_date")
    @classmethod
    def validate_completion_date(cls, v: Optional[datetime], info: ValidationInfo) -> Optional[datetime]:
        """Validate completion date logic."""
        if v is not None and hasattr(info, "data") and "due_date" in info.data:
            # Allow completion after due date (overdue completion)
            pass
        return v


class ProjectMilestoneCreateSchema(ProjectMilestoneBaseSchema):
    """Schema for creating a project milestone."""

    phase_id: int = Field(..., description="Phase ID")


class ProjectMilestoneUpdateSchema(BaseModel):
    """Schema for updating a project milestone."""

    title: Optional[str] = Field(None, min_length=1, max_length=255, description="Milestone title")
    description: Optional[str] = Field(None, max_length=2000, description="Milestone description")
    due_date: Optional[datetime] = Field(None, description="Milestone due date")
    completion_date: Optional[datetime] = Field(None, description="Actual completion date")
    status: Optional[MilestoneStatus] = Field(None, description="Milestone status")
    assigned_user_id: Optional[int] = Field(None, description="Assigned user ID")
    dependencies: Optional[List[str]] = Field(None, description="Dependent milestone IDs")
    completion_criteria: Optional[List[Dict[str, Any]]] = Field(None, description="Completion criteria")


class ProjectMilestoneReadSchema(ProjectMilestoneBaseSchema, TimestampMixin):
    """Schema for reading project milestone data."""

    id: int = Field(..., description="Milestone database ID")
    milestone_id: str = Field(..., description="Milestone UUID")
    phase_id: int = Field(..., description="Phase ID")


class ProjectTemplateBaseSchema(BaseSchema):
    """Base schema for project templates."""

    name: str = Field(..., min_length=1, max_length=255, description="Template name")
    template_type: ProjectTemplateType = Field(..., description="Template type")
    category: str = Field(..., min_length=1, max_length=100, description="Template category")
    description: Optional[str] = Field(None, max_length=2000, description="Template description")
    phases_config: Optional[List[Dict[str, Any]]] = Field([], description="Phase configuration")
    milestones_config: Optional[List[Dict[str, Any]]] = Field([], description="Milestone configuration")
    default_settings: Optional[Dict[str, Any]] = Field({}, description="Default project settings")
    compliance_standards: Optional[List[str]] = Field([], description="Applicable standards")
    is_public: bool = Field(False, description="Whether template is public")

    @field_validator("compliance_standards")
    @classmethod
    def validate_standards(cls, v: Optional[List[str]]) -> List[str]:
        """Validate compliance standards format."""
        if v is None:
            return []
        # Basic validation - could be enhanced with actual standard validation
        for standard in v:
            if not standard.strip():
                raise ValueError("Standard names cannot be empty")
        return v


class ProjectTemplateCreateSchema(ProjectTemplateBaseSchema):
    """Schema for creating a project template."""

    created_by_user_id: Optional[int] = Field(None, description="Creator user ID")


class ProjectTemplateUpdateSchema(BaseModel):
    """Schema for updating a project template."""

    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Template name")
    description: Optional[str] = Field(None, max_length=2000, description="Template description")
    phases_config: Optional[List[Dict[str, Any]]] = Field(None, description="Phase configuration")
    milestones_config: Optional[List[Dict[str, Any]]] = Field(None, description="Milestone configuration")
    default_settings: Optional[Dict[str, Any]] = Field(None, description="Default project settings")
    compliance_standards: Optional[List[str]] = Field(None, description="Applicable standards")
    is_public: Optional[bool] = Field(None, description="Whether template is public")


class ProjectTemplateReadSchema(ProjectTemplateBaseSchema, TimestampMixin):
    """Schema for reading project template data."""

    id: int = Field(..., description="Template database ID")
    template_id: str = Field(..., description="Template UUID")
    created_by_user_id: Optional[int] = Field(None, description="Creator user ID")


# Navigation-specific schemas for the frontend organism
class ProjectNavigationPhaseSchema(BaseModel):
    """Simplified phase schema for navigation display."""

    id: int = Field(..., description="Phase ID")
    phase_id: str = Field(..., description="Phase UUID")
    name: str = Field(..., description="Phase name")
    phase_type: ProjectPhaseType = Field(..., description="Phase type")
    progress_percentage: int = Field(..., description="Progress percentage")
    is_active: bool = Field(..., description="Is currently active")
    start_date: datetime = Field(..., description="Start date")
    end_date: Optional[datetime] = Field(None, description="End date")


class ProjectNavigationMilestoneSchema(BaseModel):
    """Simplified milestone schema for navigation display."""

    id: int = Field(..., description="Milestone ID")
    milestone_id: str = Field(..., description="Milestone UUID")
    title: str = Field(..., description="Milestone title")
    status: MilestoneStatus = Field(..., description="Status")
    due_date: datetime = Field(..., description="Due date")
    completion_date: Optional[datetime] = Field(None, description="Completion date")
    assigned_user_id: Optional[int] = Field(None, description="Assigned user")


class ProjectNavigationSchema(BaseModel):
    """Complete project navigation data schema."""

    project: Dict[str, Any] = Field(..., description="Project summary data")
    phases: List[ProjectNavigationPhaseSchema] = Field([], description="Project phases")
    active_phase: Optional[ProjectNavigationPhaseSchema] = Field(None, description="Current active phase")
    upcoming_milestones: List[ProjectNavigationMilestoneSchema] = Field([], description="Upcoming milestones")
    recent_activity: List[Dict[str, Any]] = Field([], description="Recent project activity")
    collaboration_status: Dict[str, Any] = Field({}, description="Real-time collaboration status")


# Response schemas
class ProjectPhaseListResponseSchema(PaginatedResponseSchema[ProjectPhaseReadSchema]):
    """Response schema for project phase list."""

    pass


class ProjectMilestoneListResponseSchema(PaginatedResponseSchema[ProjectMilestoneReadSchema]):
    """Response schema for project milestone list."""

    pass


class ProjectTemplateListResponseSchema(PaginatedResponseSchema[ProjectTemplateReadSchema]):
    """Response schema for project template list."""

    pass
