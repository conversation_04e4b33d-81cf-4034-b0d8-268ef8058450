"""Component Category Schemas.

This module provides comprehensive Pydantic schemas for component category management
operations, supporting CRUD operations, validation, and serialization for
electrical component category organization.

Key Features:
- Comprehensive field validation with custom validators
- Hierarchical category validation and organization
- Input sanitization and data transformation
- Output formatting and serialization rules
- Schema versioning support and backward compatibility
- Custom validation rules for electrical component standards
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator, model_validator
from src.core.schemas.base_schemas import BaseSchema, TimestampMixin


class ComponentCategoryBaseSchema(BaseSchema):
    """Base schema for component category operations."""

    name: str = Field(..., min_length=1, max_length=100, description="Category name")
    description: Optional[str] = Field(None, max_length=1000, description="Detailed category description")
    parent_category_id: Optional[int] = Field(
        None, ge=1, description="Parent category ID for hierarchical organization"
    )
    is_active: bool = Field(True, description="Whether category is active in the system")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate and sanitize category name."""
        if not v or not v.strip():
            raise ValueError("Category name cannot be empty")

        # Sanitize the name
        sanitized = v.strip()

        # Check for invalid characters
        invalid_chars = ["<", ">", '"', "'", "&", "\n", "\r", "\t"]
        for char in invalid_chars:
            if char in sanitized:
                raise ValueError(f"Category name cannot contain '{char}'")

        return sanitized

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize description."""
        if v is None:
            return v

        # Sanitize the description
        sanitized = v.strip()
        if not sanitized:
            return None

        # Check for invalid characters
        invalid_chars = ["<", ">", '"', "'", "&"]
        for char in invalid_chars:
            if char in sanitized:
                raise ValueError(f"Description cannot contain '{char}'")

        return sanitized


class ComponentCategoryCreateSchema(ComponentCategoryBaseSchema):
    """Schema for creating component categories."""

    @model_validator(mode="after")
    def validate_category_creation(self) -> "ComponentCategoryCreateSchema":
        """Validate category creation data."""
        # Additional validation can be added here
        return self


class ComponentCategoryUpdateSchema(BaseModel):
    """Schema for updating component categories with partial validation."""

    name: Optional[str] = Field(None, min_length=1, max_length=100, description="Category name")
    description: Optional[str] = Field(None, max_length=1000, description="Detailed category description")
    parent_category_id: Optional[int] = Field(
        None, ge=1, description="Parent category ID for hierarchical organization"
    )
    is_active: Optional[bool] = Field(None, description="Whether category is active in the system")

    # Apply same validators as base schema
    @field_validator("name")
    @classmethod
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize category name."""
        if v is None:
            return v
        return ComponentCategoryBaseSchema.validate_name(v)

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize description."""
        return ComponentCategoryBaseSchema.validate_description(v)


class ComponentCategoryReadSchema(ComponentCategoryBaseSchema, TimestampMixin):
    """Schema for reading component categories."""

    id: int = Field(..., description="Category ID")
    full_path: str = Field(..., description="Full hierarchical path")
    level: int = Field(..., description="Hierarchical level")
    is_root_category: bool = Field(..., description="Whether this is a root category")
    has_children: bool = Field(..., description="Whether category has child categories")
    component_count: int = Field(..., description="Number of component types in category")

    class Config:
        """Pydantic configuration for ComponentCategorySummarySchema."""

        from_attributes = True


class ComponentCategorySummarySchema(BaseModel):
    """Schema for component category summary information."""

    id: int = Field(..., description="Category ID")
    name: str = Field(..., description="Category name")
    description: Optional[str] = Field(None, description="Category description")
    parent_category_id: Optional[int] = Field(None, description="Parent category ID")
    is_active: bool = Field(..., description="Whether category is active")
    component_count: int = Field(..., description="Number of component types")
    child_count: int = Field(..., description="Number of child categories")

    class Config:
        """Pydantic configuration for ComponentCategoryReadSchema."""

        from_attributes = True


class ComponentCategoryTreeNodeSchema(BaseModel):
    """Schema for hierarchical category tree representation."""

    id: int = Field(..., description="Category ID")
    name: str = Field(..., description="Category name")
    description: Optional[str] = Field(None, description="Category description")
    is_active: bool = Field(..., description="Whether category is active")
    level: int = Field(..., description="Hierarchical level")
    component_count: int = Field(..., description="Number of component types")
    children: List["ComponentCategoryTreeNodeSchema"] = Field(default_factory=list, description="Child categories")

    class Config:
        """Pydantic configuration for ComponentCategoryTreeNodeSchema."""

        from_attributes = True


class ComponentCategoryListResponseSchema(BaseModel):
    """Schema for paginated component category list responses."""

    categories: List[ComponentCategorySummarySchema] = Field(..., description="List of component categories")
    total_count: int = Field(..., description="Total number of categories")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there are more pages")
    has_previous: bool = Field(..., description="Whether there are previous pages")


class ComponentCategoryTreeResponseSchema(BaseModel):
    """Schema for hierarchical category tree responses."""

    tree: List[ComponentCategoryTreeNodeSchema] = Field(..., description="Hierarchical category tree")
    total_categories: int = Field(..., description="Total number of categories")
    max_depth: int = Field(..., description="Maximum tree depth")


class ComponentCategorySearchSchema(BaseModel):
    """Schema for component category search parameters."""

    search_term: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="Search term for name or description",
    )
    parent_category_id: Optional[int] = Field(None, ge=1, description="Filter by parent category")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    include_children: bool = Field(False, description="Include child categories in results")
    min_component_count: Optional[int] = Field(None, ge=0, description="Minimum number of component types")
    max_component_count: Optional[int] = Field(None, ge=0, description="Maximum number of component types")


class ComponentCategoryBulkCreateSchema(BaseModel):
    """Schema for bulk component category creation."""

    categories: List[ComponentCategoryCreateSchema] = Field(
        ..., min_length=1, max_length=100, description="List of categories to create"
    )
    validate_hierarchy: bool = Field(True, description="Whether to validate hierarchical relationships")
    skip_duplicates: bool = Field(False, description="Whether to skip duplicate categories")


class ComponentCategoryBulkUpdateSchema(BaseModel):
    """Schema for bulk component category updates."""

    category_updates: List[Dict[str, Any]] = Field(
        ...,
        min_length=1,
        max_length=100,
        description="List of category updates with ID and fields",
    )
    validate_hierarchy: bool = Field(True, description="Whether to validate hierarchical relationships")


class ComponentCategoryValidationResultSchema(BaseModel):
    """Schema for category validation results."""

    is_valid: bool = Field(..., description="Whether validation passed")
    errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    category_id: Optional[int] = Field(None, description="Category ID if applicable")


class ComponentCategoryStatsSchema(BaseModel):
    """Schema for component category statistics."""

    total_categories: int = Field(..., description="Total number of categories")
    active_categories: int = Field(..., description="Number of active categories")
    root_categories: int = Field(..., description="Number of root categories")
    max_depth: int = Field(..., description="Maximum hierarchy depth")
    avg_component_types_per_category: float = Field(..., description="Average component types per category")
    categories_with_no_types: int = Field(..., description="Categories with no component types")


# Update forward references
ComponentCategoryTreeNodeSchema.model_rebuild()


__all__ = [
    "ComponentCategoryBaseSchema",
    "ComponentCategoryCreateSchema",
    "ComponentCategoryUpdateSchema",
    "ComponentCategoryReadSchema",
    "ComponentCategorySummarySchema",
    "ComponentCategoryTreeNodeSchema",
    "ComponentCategoryListResponseSchema",
    "ComponentCategoryTreeResponseSchema",
    "ComponentCategorySearchSchema",
    "ComponentCategoryBulkCreateSchema",
    "ComponentCategoryBulkUpdateSchema",
    "ComponentCategoryValidationResultSchema",
    "ComponentCategoryStatsSchema",
]
