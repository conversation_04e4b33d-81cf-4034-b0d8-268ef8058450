"""Custom Exception Classes.

custom exception classes used throughout the Ultimate Electrical
Designer backend application. It provides a hierarchy of exceptions for different
error types including validation errors, calculation errors, database errors,
and business logic errors.

All exceptions inherit from BaseApplicationException and include structured error
codes, messages, and context information for proper error handling and logging.
"""

from typing import Any, Dict, Optional, Union


class BaseApplicationException(Exception):
    """Base exception for all application-specific errors."""

    def __init__(
        self,
        code: str,
        detail: str,
        category: str = "ServerError",
        status_code: int = 500,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the base application exception.

        Args:
            code: Error code identifier
            detail: Error detail message
            category: Error category
            status_code: HTTP status code
            metadata: Additional error metadata

        """
        super().__init__(detail)
        self.code = code
        self.detail = detail
        self.category = category
        self.status_code = status_code  # Useful for API/HTTP context, even in desktop
        self.metadata = metadata or {}


# Domain-specific exceptions
class NotFoundError(BaseApplicationException):
    """Exception raised when a requested resource is not found.

    This is a base class for all not-found related errors in the application.
    """

    def __init__(
        self,
        code: str,
        detail: str,
        category: str = "NotFound",
        status_code: int = 404,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the not found error.

        Args:
            code: Error code identifier
            detail: Error detail message
            category: Error category
            status_code: HTTP status code
            metadata: Additional error metadata

        """
        super().__init__(code, detail, category, status_code, metadata)


class ProjectNotFoundError(NotFoundError):
    """Exception raised when a project with the specified ID is not found."""

    def __init__(self, project_id: str):
        """Initialize the project not found error.

        Args:
            project_id: ID of the project that was not found

        """
        super().__init__(
            code="PROJECT_NOT_FOUND",
            detail=f"Project with ID '{project_id}' not found.",
            category="ClientError",
            status_code=404,
            metadata={"project_id": project_id},
        )


class UserNotFoundError(NotFoundError):
    """Exception raised when a user with the specified ID is not found."""

    def __init__(self, user_id: int):
        """Initialize the user not found error.

        Args:
            user_id: ID of the user that was not found

        """
        super().__init__(
            code="USER_NOT_FOUND",
            detail=f"User with ID '{user_id}' not found.",
            category="ClientError",
            status_code=404,
            metadata={"user_id": user_id},
        )


class DataValidationError(BaseApplicationException):
    """Exception raised when data validation fails.

    This exception is typically used for Pydantic validation errors
    and other data validation failures.
    """

    def __init__(self, details: Dict[str, Any]):
        """Initialize the data validation error.

        Args:
            details: Validation error details dictionary

        """  # For Pydantic validation errors
        super().__init__(
            code="DATA_VALIDATION_FAILED",
            detail="Input validation failed.",
            category="Validation",
            status_code=422,  # Use 422 for validation errors (Unprocessable Entity)
            metadata={"validation_errors": details},
        )


class InvalidInputError(BaseApplicationException):
    """Exception raised for invalid user inputs."""

    def __init__(self, message: str):
        """Initialize the invalid input error.

        Args:
            message: Error message describing the invalid input

        """
        super().__init__(
            code="INVALID_INPUT",
            detail=message,
            category="Validation",
            status_code=400,
            metadata={"input_error": message},
        )


class ServiceError(BaseApplicationException):
    """Exception raised for service layer errors."""

    def __init__(self, message: str):
        """Initialize the service error.

        Args:
            message: Error message describing the service error

        """
        super().__init__(
            code="SERVICE_ERROR",
            detail=message,
            category="ServiceError",
            status_code=500,
            metadata={"service_error": message},
        )


class DuplicateEntryError(BaseApplicationException):
    """Exception raised when a duplicate entry is detected."""

    def __init__(self, message: str, original_exception: Optional[Exception] = None):
        """Initialize the duplicate entry error.

        Args:
            message: Error message describing the duplicate entry
            original_exception: Original exception that caused the duplicate entry error

        """
        super().__init__(
            code="DUPLICATE_ENTRY",
            detail=message,
            category="ClientError",
            status_code=409,
            metadata={"original_exception": (str(original_exception) if original_exception else None)},
        )
        self.original_exception = original_exception


class DatabaseError(BaseApplicationException):
    """Exception raised for database operation errors."""

    def __init__(self, reason: str, original_exception: Optional[Exception] = None):
        """Initialize the database error.

        Args:
            reason: Reason for the database error
            original_exception: Original exception that caused the database error

        """
        super().__init__(
            code="DB_OPERATION_FAILED",
            detail=f"Database error: {reason}",
            category="DatabaseError",
            status_code=500,
            metadata={
                "reason": reason,
                "original_exception": (str(original_exception) if original_exception else None),
            },
        )
        self.original_exception = original_exception


class ComponentNotFoundError(NotFoundError):
    """Exception raised when a component is not found."""

    def __init__(self, component_id_or_name: str):
        """Initialize the component not found error.

        Args:
            component_id_or_name: ID or name of the component that was not found

        """
        super().__init__(
            code="COMPONENT_NOT_FOUND",
            detail=f"Component '{component_id_or_name}' not found.",
            category="ClientError",
            status_code=404,
            metadata={"component_identifier": component_id_or_name},
        )


class CalculationError(BaseApplicationException):
    """Raised when a calculation cannot be performed or yields invalid results."""

    def __init__(self, details: str):
        """Initialize the calculation error.

        Args:
            details: Details about the calculation error

        """
        super().__init__(
            code="CALCULATION_ERROR",
            detail=f"Calculation error: {details}.",
            category="Calculation",
            status_code=422,  # Unprocessable Entity
            metadata={"reason": details},
        )


class StandardComplianceError(CalculationError):
    """Raised when a design or calculation violates a specific engineering standard."""

    # Inherit from CalculationError or BaseApplicationException
    def __init__(self, details: str):
        """Initialize the standard compliance error.

        Args:
            details: Details about the standard compliance violation

        """
        super().__init__(details)


class BusinessLogicError(BaseApplicationException):
    """Exception for business logic violations."""

    def __init__(
        self,
        code: str = "BUSINESS_LOGIC_ERROR",
        detail: Optional[str] = None,
    ):
        """Initialize the business logic error.

        Args:
            code: Error code identifier
            detail: Error detail message

        """
        if detail is None:
            detail = "Business logic error"

        super().__init__(
            code=code,
            detail=detail,
            category="BusinessLogic",
            status_code=422,
            metadata={"business_logic_error": detail},
        )


class UtilityError(BaseApplicationException):
    """Exception for utility operation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "UTILITY_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the utility error.

        Args:
            message: Error message
            error_code: Error code identifier
            context: Additional error context

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="UtilityError",
            status_code=500,
            metadata=context or {},
        )


class MiddlewareError(BaseApplicationException):
    """Exception for middleware operation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "MIDDLEWARE_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the middleware error.

        Args:
            message: Error message
            error_code: Error code identifier
            context: Additional error context

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="MiddlewareError",
            status_code=500,
            metadata=context or {},
        )


class APIError(BaseApplicationException):
    """Exception for API operation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "API_ERROR",
        context: Optional[Dict[str, Any]] = None,
        status_code: int = 500,
    ):
        """Initialize the API error.

        Args:
            message: Error message
            error_code: Error code identifier
            context: Additional error context
            status_code: HTTP status code

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="APIError",
            status_code=status_code,
            metadata=context or {},
        )


class SecurityError(BaseApplicationException):
    """Exception for security-related errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "SECURITY_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the security error.

        Args:
            message: Error message
            error_code: Error code identifier
            context: Additional error context

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="SecurityError",
            status_code=403,
            metadata=context or {},
        )


class UnauthorizedError(BaseApplicationException):
    """Exception for unauthorized access errors."""

    def __init__(
        self,
        message: str = "Unauthorized access",
        error_code: str = "UNAUTHORIZED",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the unauthorized error.

        Args:
            message: Error message
            error_code: Error code identifier
            context: Additional error context

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="AuthError",
            status_code=401,
            metadata=context or {},
        )


class PermissionDeniedError(BaseApplicationException):
    """Exception for permission denied errors."""

    def __init__(
        self,
        message: str = "Permission denied",
        error_code: str = "PERMISSION_DENIED",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the permission denied error.

        Args:
            message: Error message
            error_code: Error code identifier
            context: Additional error context

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="AuthError",
            status_code=403,
            metadata=context or {},
        )


class ValidationError(BaseApplicationException):
    """Exception for validation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "VALIDATION_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the validation error.

        Args:
            message: Error message
            error_code: Error code identifier
            context: Additional error context

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="ValidationError",
            status_code=400,
            metadata=context or {},
        )


class SyncError(BaseApplicationException):
    """Exception raised when database synchronization fails."""

    def __init__(
        self,
        message: str,
        error_code: str = "SYNC_ERROR",
        sync_details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the sync error.

        Args:
            message: Error message
            error_code: Error code identifier
            sync_details: Additional sync error details

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="SyncError",
            status_code=500,
            metadata=sync_details or {},
        )


class SynchronizationError(BaseApplicationException):
    """Exception raised for synchronization operation errors."""

    def __init__(
        self,
        message: str,
        error_code: str = "SYNCHRONIZATION_ERROR",
        sync_details: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the synchronization error.

        Args:
            message: Error message
            error_code: Error code identifier
            sync_details: Additional synchronization error details

        """
        super().__init__(
            code=error_code,
            detail=message,
            category="SynchronizationError",
            status_code=500,
            metadata=sync_details or {},
        )


# Middleware-Safe Exception Types
class MiddlewareSafeException(BaseApplicationException):
    """Base exception that can be safely raised in middleware context.

    This exception type is designed to be raised within Starlette middleware
    without causing ExceptionGroup errors in anyio TaskGroup contexts.
    """

    def __init__(
        self,
        message: str,
        status_code: int = 500,
        detail: Optional[str] = None,
        error_code: str = "MIDDLEWARE_ERROR",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize middleware-safe exception.

        Args:
            message: Error message
            status_code: HTTP status code
            detail: Detailed error description
            error_code: Error code identifier
            context: Additional error context

        """
        super().__init__(
            code=error_code,
            detail=detail or message,
            category="MiddlewareError",
            status_code=status_code,
            metadata=context or {},
        )
        self.message = message


class SecurityMiddlewareException(MiddlewareSafeException):
    """Security-related middleware exception."""

    def __init__(
        self,
        message: str,
        status_code: int = 403,
        detail: Optional[str] = None,
        security_operation: str = "security_check",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize security middleware exception.

        Args:
            message: Error message
            status_code: HTTP status code (default 403 Forbidden)
            detail: Detailed error description
            security_operation: Name of the security operation that failed
            context: Additional error context

        """
        error_code = f"SECURITY_{security_operation.upper()}_ERROR"
        context = context or {}
        context["security_operation"] = security_operation

        super().__init__(
            message=message,
            status_code=status_code,
            detail=detail,
            error_code=error_code,
            context=context,
        )


class DatabaseMiddlewareException(MiddlewareSafeException):
    """Database-related middleware exception."""

    def __init__(
        self,
        message: str,
        status_code: int = 500,
        detail: Optional[str] = None,
        database_operation: str = "database_operation",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize database middleware exception.

        Args:
            message: Error message
            status_code: HTTP status code (default 500 Internal Server Error)
            detail: Detailed error description
            database_operation: Name of the database operation that failed
            context: Additional error context

        """
        error_code = f"DATABASE_{database_operation.upper()}_ERROR"
        context = context or {}
        context["database_operation"] = database_operation

        super().__init__(
            message=message,
            status_code=status_code,
            detail=detail,
            error_code=error_code,
            context=context,
        )


class APIMiddlewareException(MiddlewareSafeException):
    """API-related middleware exception."""

    def __init__(
        self,
        message: str,
        status_code: int = 500,
        detail: Optional[str] = None,
        api_operation: str = "api_operation",
        context: Optional[Dict[str, Any]] = None,
    ):
        """Initialize API middleware exception.

        Args:
            message: Error message
            status_code: HTTP status code (default 500 Internal Server Error)
            detail: Detailed error description
            api_operation: Name of the API operation that failed
            context: Additional error context

        """
        error_code = f"API_{api_operation.upper()}_ERROR"
        context = context or {}
        context["api_operation"] = api_operation

        super().__init__(
            message=message,
            status_code=status_code,
            detail=detail,
            error_code=error_code,
            context=context,
        )
