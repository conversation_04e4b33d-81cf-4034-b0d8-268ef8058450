"""Legacy Data Format Migration Validation System.

This module provides comprehensive validation for migrating legacy electrical data formats
to modern formats with data integrity checking, format conversion, and validation rules.
"""

import asyncio
import json
import csv
import defusedxml.ElementTree as ET
from xml.etree.ElementTree import Element, SubElement  # nosec B405 - For type annotations only, not parsing
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
import re
import io
import logging

from src.config.logging_config import logger
from src.core.validation.data_format_validator import (
    MultiFormatDataValidator,
    DataFormat,
    ValidationLevel,
    FormatValidationResult,
)
from src.core.validation.json_schema_validator import AdvancedJsonSchemaValidator
from src.core.validation.advanced_validators import AdvancedElectricalValidator


class MigrationStatus(Enum):
    """Status of legacy data migration."""

    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    VALIDATION_ERROR = "validation_error"
    PARTIAL_SUCCESS = "partial_success"


class LegacyFormatType(Enum):
    """Types of legacy data formats supported."""

    OLD_CSV = "old_csv"
    PIPE_DELIMITED = "pipe_delimited"
    TAB_DELIMITED = "tab_delimited"
    XML_V1 = "xml_v1"
    JSON_V1 = "json_v1"
    EXCEL_OLD = "excel_old"
    PROPRIETARY = "proprietary"
    DATABASE_EXPORT = "database_export"


@dataclass
class MigrationValidationResult:
    """Result of legacy migration validation."""

    source_format: LegacyFormatType
    target_format: DataFormat
    migration_status: MigrationStatus
    total_records: int
    migrated_records: int
    failed_records: int
    validation_errors: List[Dict[str, Any]]
    data_quality_score: float  # 0.0 to 1.0
    warnings: List[str]
    recommendations: List[str]
    processing_time_ms: float
    source_checksum: str
    target_checksum: str
    field_mapping: Dict[str, str]
    data_loss: List[str]
    metadata: Dict[str, Any]


@dataclass
class FieldMapping:
    """Mapping between legacy and modern field names."""

    legacy_field: str
    modern_field: str
    data_type: str
    transformation_rule: Optional[str] = None
    default_value: Optional[Any] = None
    validation_rule: Optional[str] = None


class LegacyMigrationValidator:
    """Comprehensive legacy data migration validation system."""

    def __init__(self) -> None:
        self.format_validator = MultiFormatDataValidator()
        self.json_validator = AdvancedJsonSchemaValidator()
        self.electrical_validator = AdvancedElectricalValidator()
        self.field_mappings: Dict[LegacyFormatType, List[FieldMapping]] = {}
        self.transformation_rules: Dict[str, Any] = {}
        self.migration_history: List[MigrationValidationResult] = []

        self._initialize_field_mappings()
        self._initialize_transformation_rules()

    def _initialize_field_mappings(self) -> None:
        """Initialize field mappings for legacy formats."""
        # Old CSV format mappings
        self.field_mappings[LegacyFormatType.OLD_CSV] = [
            FieldMapping("VOLT", "voltage_rating", "float"),
            FieldMapping("CURR", "current_rating", "float"),
            FieldMapping("POW", "power_rating", "float"),
            FieldMapping("FREQ", "frequency_rating", "float"),
            FieldMapping("TYPE", "component_type", "string"),
            FieldMapping("CAT", "category", "string"),
            FieldMapping("DESC", "description", "string"),
            FieldMapping("MFG", "manufacturer", "string"),
            FieldMapping("MODEL", "model_number", "string"),
            FieldMapping("IP", "ip_rating", "string", transformation_rule="add_ip_prefix"),
        ]

        # Pipe delimited format mappings
        self.field_mappings[LegacyFormatType.PIPE_DELIMITED] = [
            FieldMapping("V_RATING", "voltage_rating", "float"),
            FieldMapping("I_RATING", "current_rating", "float"),
            FieldMapping("P_RATING", "power_rating", "float"),
            FieldMapping("F_RATING", "frequency_rating", "float"),
            FieldMapping("ENV_CLASS", "environmental_rating", "string"),
            FieldMapping("TEMP_MIN", "min_operating_temp", "float"),
            FieldMapping("TEMP_MAX", "max_operating_temp", "float"),
            FieldMapping(
                "STANDARDS",
                "compliance_standards",
                "list",
                transformation_rule="split_standards",
            ),
        ]

        # XML v1 format mappings
        self.field_mappings[LegacyFormatType.XML_V1] = [
            FieldMapping("voltage", "voltage_rating", "float"),
            FieldMapping("current", "current_rating", "float"),
            FieldMapping("power", "power_rating", "float"),
            FieldMapping("frequency", "frequency_rating", "float"),
            FieldMapping("componentType", "component_type", "string"),
            FieldMapping("category", "category", "string"),
            FieldMapping("ipCode", "ip_rating", "string"),
            FieldMapping(
                "temperatureRange",
                "temperature_range",
                "dict",
                transformation_rule="parse_temp_range",
            ),
        ]

        # JSON v1 format mappings
        self.field_mappings[LegacyFormatType.JSON_V1] = [
            FieldMapping("v", "voltage_rating", "float"),
            FieldMapping("i", "current_rating", "float"),
            FieldMapping("p", "power_rating", "float"),
            FieldMapping("f", "frequency_rating", "float"),
            FieldMapping("t", "component_type", "string"),
            FieldMapping("c", "category", "string"),
            FieldMapping("desc", "description", "string"),
            FieldMapping("ip", "ip_rating", "string"),
            FieldMapping("standards", "compliance_standards", "list"),
        ]

    def _initialize_transformation_rules(self) -> None:
        """Initialize data transformation rules."""
        self.transformation_rules = {
            "add_ip_prefix": lambda x: f"IP{x}" if not str(x).startswith("IP") else str(x),
            "split_standards": lambda x: [s.strip() for s in str(x).split(",")] if x else [],
            "parse_temp_range": lambda x: {
                "min": float(str(x).split("-")[0].strip()) if str(x).split("-")[0].strip() else -40,
                "max": float(str(x).split("-")[1].strip())
                if len(str(x).split("-")) > 1 and str(x).split("-")[1].strip()
                else 85,
            }
            if x and "-" in str(x) and str(x).strip()
            else {"min": -40, "max": 85},
            "convert_voltage_unit": lambda x: float(x) * 1000 if str(x).endswith("kV") else float(x),
            "convert_current_unit": lambda x: float(x) * 1000 if str(x).endswith("kA") else float(x),
            "convert_power_unit": lambda x: float(x) * 1000 if str(x).endswith("kW") else float(x),
        }

    async def validate_legacy_migration(
        self,
        source_data: Union[str, bytes, Path],
        source_format: LegacyFormatType,
        target_format: DataFormat = DataFormat.JSON,
        validation_level: ValidationLevel = ValidationLevel.COMPLETE,
    ) -> MigrationValidationResult:
        """Validate legacy data migration with comprehensive checks."""
        start_time = datetime.utcnow()

        try:
            # Detect source format if not specified
            if source_format == LegacyFormatType.PROPRIETARY:
                source_format = await self._detect_legacy_format(source_data)

            # Parse legacy data
            parsed_data = await self._parse_legacy_data(source_data, source_format)

            # Validate source data integrity
            source_validation = await self._validate_source_data(parsed_data, source_format, validation_level)

            # Transform to modern format
            transformed_data = await self._transform_to_modern(parsed_data, source_format, target_format)

            # Validate transformed data
            target_validation = await self.format_validator.validate_data_format(
                transformed_data, target_format, validation_level
            )

            # Calculate data quality metrics
            data_quality_score = await self._calculate_migration_quality(source_validation, target_validation)

            # Generate checksums
            source_checksum = await self._generate_checksum(parsed_data)
            target_checksum = await self._generate_checksum(transformed_data)

            # Identify data loss
            data_loss = await self._identify_data_loss(parsed_data, transformed_data, source_format, target_format)

            # Generate field mapping
            field_mapping = await self._generate_field_mapping(source_format)

            # Determine migration status
            migration_status = self._determine_migration_status(source_validation, target_validation)

            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            result = MigrationValidationResult(
                source_format=source_format,
                target_format=target_format,
                migration_status=migration_status,
                total_records=len(parsed_data) if isinstance(parsed_data, list) else 1,
                migrated_records=target_validation.record_count,
                failed_records=len(target_validation.errors),
                validation_errors=[
                    {"error": str(e), "severity": e.severity, "path": e.path} for e in target_validation.errors
                ]
                + source_validation.get("errors", []),
                data_quality_score=data_quality_score,
                warnings=target_validation.warnings,
                recommendations=await self._generate_migration_recommendations(
                    source_validation, target_validation, data_loss
                ),
                processing_time_ms=processing_time,
                source_checksum=source_checksum,
                target_checksum=target_checksum,
                field_mapping=field_mapping,
                data_loss=data_loss,
                metadata={
                    "source_format_version": "legacy",
                    "target_format_version": "modern",
                    "transformation_rules_applied": list(self.transformation_rules.keys()),
                    "validation_level": validation_level.value,
                },
            )

            # Store in history
            self.migration_history.append(result)

            return result

        except Exception as e:
            logger.error(f"Error in legacy migration validation: {e}")
            return MigrationValidationResult(
                source_format=source_format,
                target_format=target_format,
                migration_status=MigrationStatus.FAILED,
                total_records=0,
                migrated_records=0,
                failed_records=0,
                validation_errors=[{"error": str(e), "severity": "error", "path": "migration"}],
                data_quality_score=0.0,
                warnings=[f"Migration failed: {str(e)}"],
                recommendations=["Review source data format and content"],
                processing_time_ms=(datetime.utcnow() - start_time).total_seconds() * 1000,
                source_checksum="",
                target_checksum="",
                field_mapping={},
                data_loss=["migration_failed"],
                metadata={"error": str(e)},
            )

    async def _detect_legacy_format(self, source_data: Union[str, bytes, Path]) -> LegacyFormatType:
        """Automatically detect legacy data format."""
        try:
            if isinstance(source_data, Path):
                content = source_data.read_text(encoding="utf-8")
            elif isinstance(source_data, bytes):
                content = source_data.decode("utf-8")
            else:
                content = str(source_data)

            content = content.strip()

            # Check for pipe delimited
            if "|" in content and content.count("|") > 2:
                return LegacyFormatType.PIPE_DELIMITED

            # Check for tab delimited
            if "\t" in content and content.count("\t") > 2:
                return LegacyFormatType.TAB_DELIMITED

            # Check for old CSV
            if content.startswith("VOLT,") or content.startswith("V,CURR,"):
                return LegacyFormatType.OLD_CSV

            # Check for XML v1
            if content.startswith("<?xml") and "component" in content.lower():
                return LegacyFormatType.XML_V1

            # Check for JSON v1
            if content.strip().startswith("{") and '"v"' in content:
                return LegacyFormatType.JSON_V1

            # Default to proprietary
            return LegacyFormatType.PROPRIETARY

        except Exception:
            return LegacyFormatType.PROPRIETARY

    async def _parse_legacy_data(
        self, source_data: Union[str, bytes, Path], source_format: LegacyFormatType
    ) -> List[Dict[str, Any]]:
        """Parse legacy data based on format type."""
        try:
            if isinstance(source_data, Path):
                content = source_data.read_text(encoding="utf-8")
            elif isinstance(source_data, bytes):
                content = source_data.decode("utf-8")
            else:
                content = str(source_data)

            if source_format == LegacyFormatType.OLD_CSV:
                return list(csv.DictReader(io.StringIO(content)))

            elif source_format == LegacyFormatType.PIPE_DELIMITED:
                lines = content.strip().split("\n")
                if not lines:
                    return []

                headers = [h.strip() for h in lines[0].split("|")]
                data = []
                for line in lines[1:]:
                    values = [v.strip() for v in line.split("|")]
                    if len(values) == len(headers):
                        data.append(dict(zip(headers, values)))
                return data

            elif source_format == LegacyFormatType.TAB_DELIMITED:
                lines = content.strip().split("\n")
                if not lines:
                    return []

                headers = [h.strip() for h in lines[0].split("\t")]
                data = []
                for line in lines[1:]:
                    values = [v.strip() for v in line.split("\t")]
                    if len(values) == len(headers):
                        data.append(dict(zip(headers, values)))
                return data

            elif source_format == LegacyFormatType.XML_V1:
                root = ET.fromstring(content)
                return self._xml_to_dict_list(root)

            elif source_format == LegacyFormatType.JSON_V1:
                parsed = json.loads(content)
                return parsed if isinstance(parsed, list) else [parsed]

            else:
                # Try generic CSV
                try:
                    return list(csv.DictReader(io.StringIO(content)))
                except:
                    return [{"raw_data": content}]

        except Exception as e:
            logger.error(f"Error parsing legacy data: {e}")
            return []

    def _xml_to_dict_list(self, root: Element) -> List[Dict[str, Any]]:
        """Convert XML to list of dictionaries."""
        data = []

        for child in root:
            record = {}
            for subchild in child:
                record[subchild.tag] = subchild.text or ""
            if record:
                data.append(record)

        return data

    async def _validate_source_data(
        self,
        parsed_data: List[Dict[str, Any]],
        source_format: LegacyFormatType,
        validation_level: ValidationLevel,
    ) -> Dict[str, Any]:
        """Validate source legacy data integrity."""
        errors: List[Union[str, Dict[str, Any]]] = []
        warnings = []

        if not parsed_data:
            warnings.append("No data records found in source (only headers present)")
            return {"errors": errors, "warnings": warnings}

        # Check for required fields
        field_mappings = self.field_mappings.get(source_format, [])
        required_fields = [fm.legacy_field for fm in field_mappings if fm.legacy_field in parsed_data[0]]

        for i, record in enumerate(parsed_data):
            missing_fields = [f for f in required_fields if f not in record or not record[f]]
            if missing_fields:
                warnings.append(f"Record {i + 1} missing fields: {missing_fields}")

        # Validate electrical parameters
        for i, record in enumerate(parsed_data):
            electrical_errors = await self._validate_electrical_parameters(record, i + 1)
            errors.extend(electrical_errors)

        return {"errors": errors, "warnings": warnings}

    async def _validate_electrical_parameters(self, record: Dict[str, Any], record_num: int) -> List[Dict[str, Any]]:
        """Validate electrical parameters in legacy data."""
        errors = []

        # Check for negative values
        for field, value in record.items():
            field_lower = field.lower()
            # Use more precise matching to avoid false positives
            electrical_patterns = [
                "volt",
                "voltage",
                "v_rating",
                "curr",
                "current",
                "i_rating",
                "amp",
                "amperage",
                "pow",
                "power",
                "p_rating",
                "watt",
                "watts",
            ]
            # Check if field name exactly matches or starts with electrical terms
            is_electrical = (
                field_lower in electrical_patterns
                or any(field_lower.startswith(pattern) for pattern in electrical_patterns)
                or field_lower in ["v", "i", "p"]  # Single letter fields only if exact match
            )
            if is_electrical:
                try:
                    # Clean the value by removing common unit suffixes
                    clean_value = str(value).replace("k", "000")
                    # Remove common electrical units
                    for unit in ["V", "A", "W", "Hz", "kV", "kA", "kW", "mV", "mA", "mW"]:
                        clean_value = clean_value.replace(unit, "")
                    clean_value = clean_value.strip()

                    if clean_value:  # Only validate if there's a numeric value left
                        num_value = float(clean_value)
                        if num_value < 0:
                            errors.append(
                                {
                                    "error": f"Negative {field} value: {value}",
                                    "severity": "error",
                                    "record": record_num,
                                    "field": field,
                                }
                            )
                except ValueError:
                    # Only flag as error if it's clearly not a valid number with units
                    if not any(unit in str(value) for unit in ["V", "A", "W", "Hz", "kV", "kA", "kW"]):
                        errors.append(
                            {
                                "error": f"Invalid numeric value for {field}: {value}",
                                "severity": "warning",
                                "record": record_num,
                                "field": field,
                            }
                        )

        return errors

    async def _transform_to_modern(
        self,
        parsed_data: List[Dict[str, Any]],
        source_format: LegacyFormatType,
        target_format: DataFormat,
    ) -> str:
        """Transform legacy data to modern format."""
        transformed_records = []

        for record in parsed_data:
            transformed_record = {}

            # Apply field mappings
            field_mappings = self.field_mappings.get(source_format, [])
            for mapping in field_mappings:
                if mapping.legacy_field in record:
                    value = record[mapping.legacy_field]

                    # Apply transformation rules
                    if mapping.transformation_rule and mapping.transformation_rule in self.transformation_rules:
                        value = self.transformation_rules[mapping.transformation_rule](value)

                    # Convert data type
                    if mapping.data_type == "float":
                        try:
                            str_value = str(value).replace("k", "000").strip()
                            value = float(str_value) if str_value else 0.0
                        except (ValueError, TypeError):
                            value = 0.0
                    elif mapping.data_type == "int":
                        try:
                            str_value = str(value).replace("k", "000").strip()
                            value = int(float(str_value)) if str_value else 0
                        except (ValueError, TypeError):
                            value = 0
                    elif mapping.data_type == "list":
                        if not isinstance(value, list):
                            value = [value] if value else []

                    transformed_record[mapping.modern_field] = value

            # Add default values for missing fields
            for mapping in field_mappings:
                if mapping.modern_field not in transformed_record and mapping.default_value is not None:
                    transformed_record[mapping.modern_field] = mapping.default_value

            transformed_records.append(transformed_record)

        # Convert to target format
        if target_format == DataFormat.JSON:
            return json.dumps(transformed_records, indent=2)
        elif target_format == DataFormat.CSV:
            if transformed_records:
                output = io.StringIO()
                writer = csv.DictWriter(output, fieldnames=transformed_records[0].keys())
                writer.writeheader()
                writer.writerows(transformed_records)
                return output.getvalue()
            return ""
        else:
            return json.dumps(transformed_records, indent=2)

    async def _calculate_migration_quality(
        self,
        source_validation: Dict[str, Any],
        target_validation: FormatValidationResult,
    ) -> float:
        """Calculate overall migration quality score."""
        source_errors = len(source_validation.get("errors", []))
        target_errors = len(target_validation.errors)

        # Base score
        score = 1.0

        # Penalty for errors
        score -= source_errors * 0.1
        score -= target_errors * 0.2

        # Bonus for successful validation
        if target_validation.is_valid:
            score += 0.1

        return max(0.0, min(1.0, score))

    async def _generate_checksum(self, data: Any) -> str:
        """Generate checksum for data integrity."""
        import hashlib

        data_str = json.dumps(data, sort_keys=True)
        return hashlib.sha256(data_str.encode()).hexdigest()

    async def _identify_data_loss(
        self,
        source_data: List[Dict[str, Any]],
        transformed_data: str,
        source_format: LegacyFormatType,
        target_format: DataFormat,
    ) -> List[str]:
        """Identify potential data loss during migration."""
        data_loss = []

        # Parse transformed data
        try:
            if target_format == DataFormat.JSON:
                target_data = json.loads(transformed_data)
            else:
                target_data = []
        except:
            target_data = []

        # Check record count
        if len(target_data) != len(source_data):
            data_loss.append(f"Record count mismatch: {len(source_data)} → {len(target_data)}")

        # Check field mappings
        field_mappings = self.field_mappings.get(source_format, [])
        source_fields = set(source_data[0].keys()) if source_data else set()
        target_fields = set(target_data[0].keys()) if target_data else set()

        unmigrated_fields = source_fields - {fm.legacy_field for fm in field_mappings}
        if unmigrated_fields:
            data_loss.append(f"Unmigrated fields: {list(unmigrated_fields)}")

        return data_loss

    async def _generate_field_mapping(self, source_format: LegacyFormatType) -> Dict[str, str]:
        """Generate field mapping for the migration."""
        field_mappings = self.field_mappings.get(source_format, [])
        mapping = {fm.legacy_field: fm.modern_field for fm in field_mappings}
        # Also include modern field names for easier lookup
        for fm in field_mappings:
            mapping[fm.modern_field] = fm.legacy_field
        return mapping

    def _determine_migration_status(
        self,
        source_validation: Dict[str, Any],
        target_validation: FormatValidationResult,
    ) -> MigrationStatus:
        """Determine overall migration status."""
        if target_validation.is_valid and len(source_validation.get("errors", [])) == 0:
            return MigrationStatus.COMPLETED
        elif target_validation.is_valid:
            return MigrationStatus.PARTIAL_SUCCESS
        elif len(target_validation.errors) > 0:
            return MigrationStatus.VALIDATION_ERROR
        else:
            return MigrationStatus.FAILED

    async def _generate_migration_recommendations(
        self,
        source_validation: Dict[str, Any],
        target_validation: FormatValidationResult,
        data_loss: List[str],
    ) -> List[str]:
        """Generate recommendations for migration improvements."""
        recommendations = []

        if source_validation.get("errors"):
            recommendations.append("Fix source data integrity issues before migration")

        if target_validation.errors:
            recommendations.append("Review target format validation rules")

        if data_loss:
            recommendations.append("Review field mappings to minimize data loss")
            recommendations.append("Consider adding custom transformation rules")

        recommendations.append("Perform test migration with sample data")
        recommendations.append("Validate migrated data against business rules")

        return recommendations

    def get_migration_history(self) -> List[MigrationValidationResult]:
        """Get migration history."""
        return self.migration_history

    def clear_migration_history(self) -> None:
        """Clear migration history."""
        self.migration_history.clear()

    def add_custom_field_mapping(
        self,
        source_format: LegacyFormatType,
        legacy_field: str,
        modern_field: str,
        data_type: str,
        transformation_rule: Optional[str] = None,
        default_value: Optional[Any] = None,
    ) -> None:
        """Add custom field mapping for legacy migration."""
        if source_format not in self.field_mappings:
            self.field_mappings[source_format] = []

        self.field_mappings[source_format].append(
            FieldMapping(
                legacy_field=legacy_field,
                modern_field=modern_field,
                data_type=data_type,
                transformation_rule=transformation_rule,
                default_value=default_value,
            )
        )

    def add_custom_transformation_rule(self, rule_name: str, rule_function: Callable[..., Any]) -> None:
        """Add custom transformation rule."""
        self.transformation_rules[rule_name] = rule_function


# Global validator instance
legacy_migration_validator: LegacyMigrationValidator = LegacyMigrationValidator()
