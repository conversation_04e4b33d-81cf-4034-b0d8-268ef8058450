"""Validation module.

This module provides comprehensive validation capabilities for electrical design
including schema validation, constraint checking, compatibility analysis,
and cross-entity validation.

To avoid circular dependencies, use lazy imports and factory patterns.
"""

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .advanced_validators import (
        AdvancedElectricalValidator,
        EnhancedTemperatureValidator,
    )
    from .compatibility_matrix import CompatibilityMatrix
    from .constraint_validator import ComplexConstraintValidator
    from .json_schema_validator import AdvancedJsonSchemaValidator
    from .standards_validator import StandardsValidator


# Use lazy loading to prevent circular imports
def get_advanced_electrical_validator() -> "AdvancedElectricalValidator":
    """Get AdvancedElectricalValidator instance with lazy loading."""
    from .advanced_validators import advanced_electrical_validator

    return advanced_electrical_validator


def get_enhanced_temperature_validator() -> "EnhancedTemperatureValidator":
    """Get EnhancedTemperatureValidator instance with lazy loading."""
    from .advanced_validators import enhanced_temperature_validator

    return enhanced_temperature_validator


def get_compatibility_matrix() -> "CompatibilityMatrix":
    """Get CompatibilityMatrix instance with lazy loading."""
    from .compatibility_matrix import CompatibilityMatrix

    return CompatibilityMatrix()


def get_constraint_validator() -> "ComplexConstraintValidator":
    """Get ComplexConstraintValidator instance with lazy loading."""
    from .constraint_validator import complex_constraint_validator

    return complex_constraint_validator


def get_json_schema_validator() -> "AdvancedJsonSchemaValidator":
    """Get AdvancedJsonSchemaValidator instance with lazy loading."""
    from .json_schema_validator import json_schema_validator

    return json_schema_validator


def get_standards_validator() -> "StandardsValidator":
    """Get StandardsValidator instance with lazy loading."""
    from .standards_validator import StandardsValidator

    return StandardsValidator()


# Public API
__all__ = [
    "get_advanced_electrical_validator",
    "get_enhanced_temperature_validator",
    "get_compatibility_matrix",
    "get_constraint_validator",
    "get_json_schema_validator",
    "get_standards_validator",
]
