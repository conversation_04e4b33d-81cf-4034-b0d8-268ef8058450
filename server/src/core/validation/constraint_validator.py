"""Complex Constraint Validation System.

This module provides sophisticated constraint validation across multiple entities,
including cross-entity business rules, temporal constraints, and advanced data integrity checks.
"""

import asyncio
import re
from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

import numpy as np

from src.config.logging_config import logger
from src.core.validation.json_schema_validator import AdvancedJsonSchemaValidator


class ConstraintType(Enum):
    """Types of complex constraints."""

    CROSS_ENTITY = "cross_entity"
    TEMPORAL = "temporal"
    BUSINESS_RULE = "business_rule"
    DATA_INTEGRITY = "data_integrity"
    REFERENTIAL = "referential"
    NUMERICAL = "numerical"
    CONDITIONAL = "conditional"
    AGGREGATE = "aggregate"


class ConstraintSeverity(Enum):
    """Severity levels for constraint violations."""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    WARNING = "warning"


@dataclass
class ConstraintViolation:
    """Detailed constraint violation information."""

    constraint_type: ConstraintType
    severity: ConstraintSeverity
    message: str
    entities: List[str]
    violated_rule: str
    expected_value: Any
    actual_value: Any
    suggestion: str
    metadata: Dict[str, Any]
    timestamp: datetime

    # Legacy properties for backward compatibility
    @property
    def constraint_id(self) -> str:
        """Get constraint ID from constraint type."""
        return self.constraint_type.value

    @property
    def entity_path(self) -> str:
        """Get entity path from entities list."""
        return ".".join(self.entities)

    @property
    def suggested_fix(self) -> str:
        """Get suggested fix from suggestion."""
        return self.suggestion


@dataclass
class ConstraintValidationResult:
    """Complete constraint validation result."""

    is_valid: bool
    violations: List[ConstraintViolation]
    warnings: List[str]
    validation_score: float  # 0.0 to 1.0
    processed_constraints: int
    passed_constraints: int
    failed_constraints: int
    validation_time_ms: float


class ConstraintRule:
    """Definition of a complex constraint rule."""

    def __init__(
        self,
        name: str,
        constraint_type: ConstraintType,
        severity: ConstraintSeverity,
        condition: str,
        validation_function: Callable[..., Any],
        entities: List[str],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.name = name
        self.constraint_type = constraint_type
        self.severity = severity
        self.condition = condition
        self.validation_function = validation_function
        self.entities = entities
        self.metadata = metadata or {}


class ComplexConstraintValidator:
    """Advanced constraint validation system for multiple entities."""

    def __init__(self) -> None:
        self.json_validator = AdvancedJsonSchemaValidator()
        self.constraints: Dict[str, ConstraintRule] = {}
        self.cache: Dict[str, ConstraintValidationResult] = {}
        self.validation_functions: Dict[str, Callable[..., Any]] = {}

        self._initialize_constraints()
        self._register_validation_functions()

    def _initialize_constraints(self) -> None:
        """Initialize complex electrical constraints."""
        # Cross-entity power balance
        self.add_constraint(
            ConstraintRule(
                name="power_balance",
                constraint_type=ConstraintType.CROSS_ENTITY,
                severity=ConstraintSeverity.CRITICAL,
                condition="sum(component_powers) <= project_capacity",
                validation_function=self._validate_power_balance,
                entities=["project", "components"],
                metadata={"tolerance": 0.1, "safety_factor": 1.25},
            )
        )

        # Voltage compatibility across components
        self.add_constraint(
            ConstraintRule(
                name="voltage_compatibility",
                constraint_type=ConstraintType.CROSS_ENTITY,
                severity=ConstraintSeverity.HIGH,
                condition="all component voltages compatible with system voltage",
                validation_function=self._validate_voltage_compatibility,
                entities=["project", "components"],
                metadata={"tolerance": 0.05},
            )
        )

        # Current loading balance
        self.add_constraint(
            ConstraintRule(
                name="current_balance",
                constraint_type=ConstraintType.AGGREGATE,
                severity=ConstraintSeverity.HIGH,
                condition="load_current <= component_current_sum",
                validation_function=self._validate_current_balance,
                entities=["project", "components"],
                metadata={"balance_factor": 0.8},
            )
        )

        # Temperature derating consistency
        self.add_constraint(
            ConstraintRule(
                name="temperature_derating",
                constraint_type=ConstraintType.BUSINESS_RULE,
                severity=ConstraintSeverity.MEDIUM,
                condition="temperature_derating_factors_consistent",
                validation_function=self._validate_temperature_derating,
                entities=["project", "components"],
                metadata={"max_temp": 40, "derating_per_degree": 0.005},
            )
        )

        # Standards compliance across system
        self.add_constraint(
            ConstraintRule(
                name="standards_consistency",
                constraint_type=ConstraintType.REFERENTIAL,
                severity=ConstraintSeverity.HIGH,
                condition="all components meet project standards",
                validation_function=self._validate_standards_consistency,
                entities=["project", "components"],
                metadata={"required_overlap": 0.8},
            )
        )

        # Protection coordination
        self.add_constraint(
            ConstraintRule(
                name="protection_coordination",
                constraint_type=ConstraintType.BUSINESS_RULE,
                severity=ConstraintSeverity.CRITICAL,
                condition="protection_devices_properly_coordinated",
                validation_function=self._validate_protection_coordination,
                entities=["components"],
                metadata={"coordination_margin": 0.2},
            )
        )

        # Cable sizing adequacy
        self.add_constraint(
            ConstraintRule(
                name="cable_ampacity",
                constraint_type=ConstraintType.NUMERICAL,
                severity=ConstraintSeverity.CRITICAL,
                condition="cable_ampacity >= load_current * safety_factor",
                validation_function=self._validate_cable_ampacity,
                entities=["cables", "loads"],
                metadata={"safety_factor": 1.25, "ambient_temp": 30},
            )
        )

        # Load balance across phases
        self.add_constraint(
            ConstraintRule(
                name="load_balance",
                constraint_type=ConstraintType.AGGREGATE,
                severity=ConstraintSeverity.MEDIUM,
                condition="phase_imbalance <= max_imbalance",
                validation_function=self._validate_load_balance,
                entities=["loads"],
                metadata={"max_imbalance": 0.1},
            )
        )

        # Short circuit rating adequacy
        self.add_constraint(
            ConstraintRule(
                name="short_circuit_rating",
                constraint_type=ConstraintType.NUMERICAL,
                severity=ConstraintSeverity.CRITICAL,
                condition="component_sccr >= calculated_fault_current",
                validation_function=self._validate_short_circuit_rating,
                entities=["components", "system"],
                metadata={"safety_margin": 1.2},
            )
        )

        # Grounding system integrity
        self.add_constraint(
            ConstraintRule(
                name="grounding_integrity",
                constraint_type=ConstraintType.REFERENTIAL,
                severity=ConstraintSeverity.HIGH,
                condition="grounding_impedance <= max_allowed",
                validation_function=self._validate_grounding_integrity,
                entities=["system"],
                metadata={"max_impedance": 25},
            )
        )

    def _register_validation_functions(self) -> None:
        """Register complex validation functions."""
        self.validation_functions.update(
            {
                "power_balance": self._validate_power_balance,
                "voltage_compatibility": self._validate_voltage_compatibility,
                "current_balance": self._validate_current_balance,
                "temperature_derating": self._validate_temperature_derating,
                "standards_consistency": self._validate_standards_consistency,
                "protection_coordination": self._validate_protection_coordination,
                "cable_ampacity": self._validate_cable_ampacity,
                "load_balance": self._validate_load_balance,
                "short_circuit_rating": self._validate_short_circuit_rating,
                "grounding_integrity": self._validate_grounding_integrity,
                "temporal_sequence": self._validate_temporal_sequence,
                "data_integrity": self._validate_data_integrity,
                "referential_consistency": self._validate_referential_consistency,
                "conditional_logic": self._validate_conditional_logic,
                "aggregate_calculations": self._validate_aggregate_calculations,
            }
        )

    def add_constraint(self, constraint: ConstraintRule) -> None:
        """Add a new constraint rule."""
        self.constraints[constraint.name] = constraint

    async def validate_constraints(
        self,
        entities: Dict[str, List[Dict[str, Any]]],
        constraint_types: Optional[List[ConstraintType]] = None,
        entity_types: Optional[List[str]] = None,
    ) -> ConstraintValidationResult:
        """Validate complex constraints across multiple entities."""
        start_time = datetime.utcnow()

        try:
            violations = []
            warnings = []
            processed_constraints = 0
            passed_constraints = 0
            failed_constraints = 0

            # Filter constraints by type and entity
            active_constraints = self._filter_constraints(constraint_types, entity_types)

            for constraint_name, constraint in active_constraints.items():
                processed_constraints += 1

                try:
                    # Extract relevant entities for this constraint
                    relevant_entities = self._extract_relevant_entities(entities, constraint.entities)

                    # Execute validation
                    is_valid, violation_data = await constraint.validation_function(
                        relevant_entities, constraint.metadata
                    )

                    if not is_valid:
                        failed_constraints += 1
                        violations.append(
                            ConstraintViolation(
                                constraint_type=constraint.constraint_type,
                                severity=constraint.severity,
                                message=violation_data.get(
                                    "message",
                                    f"Constraint '{constraint.name}' violated",
                                ),
                                entities=list(relevant_entities.keys()),
                                violated_rule=constraint.condition,
                                expected_value=violation_data.get("expected"),
                                actual_value=violation_data.get("actual"),
                                suggestion=violation_data.get("suggestion", "Review constraint requirements"),
                                metadata=violation_data.get("metadata", {}),
                                timestamp=datetime.utcnow(),
                            )
                        )
                    else:
                        passed_constraints += 1

                except Exception as e:
                    warnings.append(f"Error validating constraint '{constraint_name}': {str(e)}")
                    failed_constraints += 1

            # Calculate validation score
            validation_score = passed_constraints / processed_constraints if processed_constraints > 0 else 1.0

            # Cache results
            cache_key = self._generate_cache_key(entities, constraint_types, entity_types)
            result = ConstraintValidationResult(
                is_valid=len(violations) == 0,
                violations=violations,
                warnings=warnings,
                validation_score=validation_score,
                processed_constraints=processed_constraints,
                passed_constraints=passed_constraints,
                failed_constraints=failed_constraints,
                validation_time_ms=(datetime.utcnow() - start_time).total_seconds() * 1000,
            )

            self.cache[cache_key] = result
            return result

        except Exception as e:
            logger.error(f"Error in constraint validation: {e}")
            return ConstraintValidationResult(
                is_valid=False,
                violations=[
                    ConstraintViolation(
                        constraint_type=ConstraintType.BUSINESS_RULE,
                        severity=ConstraintSeverity.CRITICAL,
                        message=f"Validation system error: {str(e)}",
                        entities=list(entities.keys()),
                        violated_rule="system_availability",
                        expected_value="successful validation",
                        actual_value="validation_error",
                        suggestion="Contact system administrator",
                        metadata={"error": str(e)},
                        timestamp=datetime.utcnow(),
                    )
                ],
                warnings=[],
                validation_score=0.0,
                processed_constraints=0,
                passed_constraints=0,
                failed_constraints=1,
                validation_time_ms=(datetime.utcnow() - start_time).total_seconds() * 1000,
            )

    async def _validate_power_balance(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate power balance across components."""
        project = entities.get("project", [{}])[0]
        components = entities.get("components", [])

        project_capacity = project.get("total_power", 0)
        component_powers = [comp.get("power_rating", 0) for comp in components]
        total_component_power = sum(component_powers)

        safety_factor = metadata.get("safety_factor", 1.25)
        max_allowed = project_capacity / safety_factor

        if total_component_power > max_allowed:
            return False, {
                "message": f"Total component power ({total_component_power}W) exceeds project capacity ({project_capacity}W)",
                "expected": f"<= {max_allowed}W",
                "actual": f"{total_component_power}W",
                "suggestion": "Reduce component power ratings or increase project capacity",
                "metadata": {
                    "project_capacity": project_capacity,
                    "component_power_sum": total_component_power,
                    "safety_factor": safety_factor,
                },
            }

        return True, {}

    async def _validate_voltage_compatibility(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate voltage compatibility across components."""
        project = entities.get("project", [{}])[0]
        components = entities.get("components", [])

        system_voltage = project.get("system_voltage", 0)
        tolerance = metadata.get("tolerance", 0.05)

        incompatible_components = []
        for component in components:
            component_voltage = component.get("voltage_rating", 0)
            if component_voltage > 0:
                deviation = abs(system_voltage - component_voltage) / system_voltage
                if deviation > tolerance:
                    incompatible_components.append(
                        {
                            "component": component.get("id", "unknown"),
                            "component_voltage": component_voltage,
                            "system_voltage": system_voltage,
                            "deviation": deviation,
                        }
                    )

        if incompatible_components:
            return False, {
                "message": f"{len(incompatible_components)} components have voltage incompatibility",
                "expected": f"within {tolerance * 100}% of {system_voltage}V",
                "actual": incompatible_components,
                "suggestion": "Select components with compatible voltage ratings",
                "metadata": {
                    "system_voltage": system_voltage,
                    "tolerance": tolerance,
                    "incompatible_count": len(incompatible_components),
                },
            }

        return True, {}

    async def _validate_current_balance(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate current balance across components."""
        project = entities.get("project", [{}])[0]
        components = entities.get("components", [])

        load_current = project.get("total_load_current", 0)
        component_currents = [comp.get("rated_current", 0) for comp in components]
        total_component_current = sum(component_currents)

        balance_factor = metadata.get("balance_factor", 0.8)
        required_current = load_current / balance_factor

        if total_component_current < required_current:
            return False, {
                "message": f"Total current capacity ({total_component_current}A) insufficient for load ({load_current}A)",
                "expected": f">= {required_current}A",
                "actual": f"{total_component_current}A",
                "suggestion": "Increase component current ratings or add parallel components",
                "metadata": {
                    "load_current": load_current,
                    "total_component_current": total_component_current,
                    "balance_factor": balance_factor,
                    "required_current": required_current,
                },
            }

        return True, {}

    async def _validate_temperature_derating(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate temperature derating consistency."""
        project = entities.get("project", [{}])[0]
        components = entities.get("components", [])

        ambient_temp = project.get("environmental_conditions", {}).get("ambient_temperature", 25)
        max_temp = metadata.get("max_temp", 40)
        derating_per_degree = metadata.get("derating_per_degree", 0.005)

        excessive_derating_components = []
        for component in components:
            component_max_temp = component.get("environmental_rating", {}).get("temperature_range", {}).get("max", 40)
            if component_max_temp < ambient_temp:
                derating = (ambient_temp - component_max_temp) * derating_per_degree
                excessive_derating_components.append(
                    {
                        "component": component.get("id", "unknown"),
                        "component_max_temp": component_max_temp,
                        "ambient_temp": ambient_temp,
                        "derating_factor": derating,
                    }
                )

        if excessive_derating_components:
            return False, {
                "message": f"{len(excessive_derating_components)} components exceed temperature limits",
                "expected": f"component max temp >= {ambient_temp}°C",
                "actual": excessive_derating_components,
                "suggestion": "Select components with higher temperature ratings or provide cooling",
                "metadata": {
                    "ambient_temperature": ambient_temp,
                    "excessive_components": len(excessive_derating_components),
                },
            }

        return True, {}

    async def _validate_standards_consistency(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate standards consistency across components."""
        project = entities.get("project", [{}])[0]
        components = entities.get("components", [])

        project_standards = set(project.get("applicable_standards", []))
        required_overlap = metadata.get("required_overlap", 0.8)

        non_compliant_components = []
        for component in components:
            component_standards = set(component.get("compliance_standards", []))
            overlap = (
                len(project_standards.intersection(component_standards)) / len(project_standards)
                if project_standards
                else 1.0
            )

            if overlap < required_overlap:
                non_compliant_components.append(
                    {
                        "component": component.get("id", "unknown"),
                        "overlap": overlap,
                        "missing_standards": list(project_standards - component_standards),
                    }
                )

        if non_compliant_components:
            return False, {
                "message": f"{len(non_compliant_components)} components don't meet standards requirements",
                "expected": f"{required_overlap * 100}% overlap with project standards",
                "actual": non_compliant_components,
                "suggestion": "Ensure all components comply with required standards",
                "metadata": {
                    "project_standards": list(project_standards),
                    "required_overlap": required_overlap,
                    "non_compliant_count": len(non_compliant_components),
                },
            }

        return True, {}

    async def _validate_protection_coordination(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate protection device coordination."""
        # Simplified implementation - would be more complex in reality
        components = entities.get("components", [])

        protection_devices = [comp for comp in components if comp.get("type") in ["breaker", "fuse", "relay"]]

        if len(protection_devices) < 2:
            return True, {}  # No coordination needed

        # Check for basic coordination (simplified)
        coordination_margin = metadata.get("coordination_margin", 0.2)

        # This is a placeholder for actual coordination logic
        return True, {}  # Assume valid for this implementation

    async def _validate_cable_ampacity(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate cable ampacity adequacy."""
        cables = entities.get("cables", [])
        loads = entities.get("loads", [])

        if not cables or not loads:
            return True, {}  # No validation needed

        safety_factor = metadata.get("safety_factor", 1.25)
        ambient_temp = metadata.get("ambient_temp", 30)

        inadequate_cables = []
        for cable in cables:
            cable_ampacity = cable.get("ampacity", 0)
            connected_loads = [load for load in loads if load.get("cable_id") == cable.get("id")]
            total_load_current = sum(load.get("current", 0) for load in connected_loads)

            required_ampacity = total_load_current * safety_factor

            if cable_ampacity < required_ampacity:
                inadequate_cables.append(
                    {
                        "cable": cable.get("id", "unknown"),
                        "cable_ampacity": cable_ampacity,
                        "required_ampacity": required_ampacity,
                        "load_current": total_load_current,
                    }
                )

        if inadequate_cables:
            return False, {
                "message": f"{len(inadequate_cables)} cables have inadequate ampacity",
                "expected": f"cable ampacity >= load * {safety_factor}",
                "actual": inadequate_cables,
                "suggestion": "Increase cable sizes or reduce loads",
                "metadata": {
                    "safety_factor": safety_factor,
                    "ambient_temperature": ambient_temp,
                    "inadequate_count": len(inadequate_cables),
                },
            }

        return True, {}

    async def _validate_load_balance(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate load balance across phases."""
        loads = entities.get("loads", [])

        if not loads:
            return True, {}

        max_imbalance = metadata.get("max_imbalance", 0.1)

        # Group loads by phase
        phase_loads = defaultdict(list)
        for load in loads:
            phase = load.get("phase", "A")
            phase_loads[phase].append(load.get("power", 0))

        if len(phase_loads) < 3:
            return True, {}  # Not a 3-phase system

        phase_powers = [sum(phase_loads[phase]) for phase in ["A", "B", "C"]]
        max_power = max(phase_powers)
        min_power = min(phase_powers)

        if max_power > 0:
            imbalance = (max_power - min_power) / max_power
            if imbalance > max_imbalance:
                return False, {
                    "message": f"Phase imbalance {imbalance:.2%} exceeds limit {max_imbalance:.2%}",
                    "expected": f"imbalance <= {max_imbalance:.2%}",
                    "actual": f"{imbalance:.2%}",
                    "suggestion": "Redistribute loads across phases",
                    "metadata": {
                        "phase_powers": dict(zip(["A", "B", "C"], phase_powers)),
                        "imbalance": imbalance,
                    },
                }

        return True, {}

    async def _validate_short_circuit_rating(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate short circuit current rating."""
        components = entities.get("components", [])

        safety_margin = metadata.get("safety_margin", 1.2)

        # This is a simplified implementation
        # In reality, would calculate fault currents
        underrated_components = []
        for component in components:
            sccr = component.get("short_circuit_current_rating", 0)
            # Placeholder calculation
            if sccr < 1000 and sccr > 0:  # Simplified threshold
                underrated_components.append(
                    {
                        "component": component.get("id", "unknown"),
                        "sccr": sccr,
                        "required": 1000,
                    }
                )

        if underrated_components:
            return False, {
                "message": f"{len(underrated_components)} components have inadequate short circuit rating",
                "expected": f"SCCR >= calculated fault current * {safety_margin}",
                "actual": underrated_components,
                "suggestion": "Verify short circuit analysis and select appropriately rated components",
                "metadata": {
                    "safety_margin": safety_margin,
                    "underrated_count": len(underrated_components),
                },
            }

        return True, {}

    async def _validate_grounding_integrity(
        self, entities: Dict[str, List[Dict[str, Any]]], metadata: Dict[str, Any]
    ) -> Tuple[bool, Dict[str, Any]]:
        """Validate grounding system integrity."""
        system = entities.get("system", [{}])[0]

        max_impedance = metadata.get("max_impedance", 25)
        grounding_impedance = system.get("grounding_impedance", 0)

        if grounding_impedance > max_impedance:
            return False, {
                "message": f"Grounding impedance {grounding_impedance}Ω exceeds maximum {max_impedance}Ω",
                "expected": f"<= {max_impedance}Ω",
                "actual": f"{grounding_impedance}Ω",
                "suggestion": "Improve grounding system design or add grounding electrodes",
                "metadata": {
                    "grounding_impedance": grounding_impedance,
                    "max_impedance": max_impedance,
                },
            }

        return True, {}

    def _filter_constraints(
        self,
        constraint_types: Optional[List[ConstraintType]],
        entity_types: Optional[List[str]],
    ) -> Dict[str, ConstraintRule]:
        """Filter constraints by type and entity."""
        if not constraint_types and not entity_types:
            return self.constraints

        filtered = {}
        for name, constraint in self.constraints.items():
            type_match = not constraint_types or constraint.constraint_type in constraint_types
            entity_match = not entity_types or any(entity in constraint.entities for entity in entity_types)

            if type_match and entity_match:
                filtered[name] = constraint

        return filtered

    def _extract_relevant_entities(
        self,
        all_entities: Dict[str, List[Dict[str, Any]]],
        required_entities: List[str],
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Extract only relevant entities for constraint validation."""
        relevant = {}
        for entity_type in required_entities:
            if entity_type in all_entities:
                relevant[entity_type] = all_entities[entity_type]
        return relevant

    def _generate_cache_key(
        self,
        entities: Dict[str, List[Dict[str, Any]]],
        constraint_types: Optional[List[ConstraintType]],
        entity_types: Optional[List[str]],
    ) -> str:
        """Generate cache key for validation results."""
        import hashlib
        import json

        key_data = {
            "entities": {k: len(v) for k, v in entities.items()},
            "constraint_types": [ct.value for ct in constraint_types] if constraint_types else None,
            "entity_types": entity_types,
        }

        return hashlib.sha256(json.dumps(key_data, sort_keys=True).encode()).hexdigest()

    def clear_cache(self) -> None:
        """Clear validation cache."""
        self.cache.clear()
        logger.info("Constraint validation cache cleared")

    def get_constraint_summary(self) -> Dict[str, Any]:
        """Get summary of loaded constraints."""
        return {
            "total_constraints": len(self.constraints),
            "by_type": {
                ct.value: len([c for c in self.constraints.values() if c.constraint_type == ct])
                for ct in ConstraintType
            },
            "by_severity": {
                cs.value: len([c for c in self.constraints.values() if c.severity == cs]) for cs in ConstraintSeverity
            },
            "supported_entities": list(
                set(entity for constraint in self.constraints.values() for entity in constraint.entities)
            ),
        }

    def _validate_temporal_sequence(self, value: Any, constraints: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate temporal sequence of events or operations."""
        if not isinstance(value, dict):
            return False, "Temporal sequence must be provided as a dictionary"

        required_keys = ["start_time", "end_time", "sequence"]
        missing_keys = [key for key in required_keys if key not in value]
        if missing_keys:
            return False, f"Missing required temporal sequence keys: {missing_keys}"

        start_time = value.get("start_time")
        end_time = value.get("end_time")
        sequence = value.get("sequence", [])

        if start_time and end_time and start_time >= end_time:
            return False, "Start time must be before end time"

        if sequence and len(sequence) > 1:
            for i in range(1, len(sequence)):
                if sequence[i - 1].get("timestamp", 0) > sequence[i].get("timestamp", 0):
                    return (
                        False,
                        f"Temporal sequence violation at step {i}: events out of order",
                    )

        max_duration = constraints.get("max_duration_hours", 24)
        if start_time and end_time:
            duration = (end_time - start_time).total_seconds() / 3600
            if duration > max_duration:
                return (
                    False,
                    f"Sequence duration {duration:.1f}h exceeds maximum {max_duration}h",
                )

        return True, ""

    def _validate_data_integrity(self, value: Any, constraints: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate data integrity constraints."""
        if not isinstance(value, dict):
            return False, "Data integrity validation requires a dictionary input"

        # Check for null/empty values in required fields
        required_fields = constraints.get("required_fields", [])
        for field in required_fields:
            if field not in value or value[field] is None or value[field] == "":
                return False, f"Required field '{field}' is null or empty"

        # Check data consistency
        consistency_rules = constraints.get("consistency_rules", {})
        for rule_config in consistency_rules.values():
            field1 = rule_config.get("field1")
            field2 = rule_config.get("field2")
            operator = rule_config.get("operator", "==")

            if field1 in value and field2 in value:
                val1, val2 = value[field1], value[field2]
                try:
                    if operator == "==" and val1 != val2:
                        return (
                            False,
                            f"Data consistency violation: {field1} must equal {field2}",
                        )
                    elif operator == "<" and val1 >= val2:
                        return (
                            False,
                            f"Data consistency violation: {field1} must be less than {field2}",
                        )
                    elif operator == ">" and val1 <= val2:
                        return (
                            False,
                            f"Data consistency violation: {field1} must be greater than {field2}",
                        )
                except (TypeError, ValueError):
                    return (
                        False,
                        f"Cannot compare {field1} and {field2}: incompatible types",
                    )

        return True, ""

    def _validate_referential_consistency(self, value: Any, constraints: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate referential consistency between entities."""
        if not isinstance(value, dict):
            return (
                False,
                "Referential consistency validation requires a dictionary input",
            )

        # Check foreign key references
        foreign_keys = constraints.get("foreign_keys", {})
        for fk_field, reference_config in foreign_keys.items():
            if fk_field in value:
                fk_value = value[fk_field]
                reference_table = reference_config.get("table")
                reference_field = reference_config.get("field", "id")

                # For validation purposes, assume references are valid if non-null
                # In a real implementation, this would check against actual referenced data
                if fk_value is None:
                    if reference_config.get("nullable", False):
                        continue
                    else:
                        return (
                            False,
                            f"Non-nullable foreign key '{fk_field}' cannot be null",
                        )

        # Check entity relationship constraints
        relationships = constraints.get("relationships", [])
        for relationship in relationships:
            parent_field = relationship.get("parent_field")
            child_field = relationship.get("child_field")
            relationship_type = relationship.get("type", "one_to_many")

            if parent_field in value and child_field in value:
                parent_val = value[parent_field]
                child_val = value[child_field]

                if relationship_type == "one_to_one" and isinstance(child_val, list) and len(child_val) > 1:
                    return (
                        False,
                        f"One-to-one relationship violated: {child_field} has multiple values",
                    )

        return True, ""

    def _validate_conditional_logic(self, value: Any, constraints: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate conditional logic constraints."""
        if not isinstance(value, dict):
            return False, "Conditional logic validation requires a dictionary input"

        # Evaluate conditional rules
        conditions = constraints.get("conditions", [])
        for condition in conditions:
            condition_field = condition.get("field")
            condition_operator = condition.get("operator", "==")
            condition_value = condition.get("value")
            then_rules = condition.get("then", [])
            else_rules = condition.get("else", [])

            if condition_field not in value:
                continue

            field_value = value[condition_field]
            condition_met = False

            try:
                if condition_operator == "==" and field_value == condition_value:
                    condition_met = True
                elif condition_operator == "!=" and field_value != condition_value:
                    condition_met = True
                elif condition_operator == ">" and field_value > condition_value:
                    condition_met = True
                elif condition_operator == "<" and field_value < condition_value:
                    condition_met = True
                elif condition_operator == "in" and field_value in condition_value:
                    condition_met = True
            except (TypeError, ValueError):
                return False, f"Cannot evaluate condition for field '{condition_field}'"

            # Apply appropriate rules based on condition result
            rules_to_apply = then_rules if condition_met else else_rules
            for rule in rules_to_apply:
                rule_field = rule.get("field")
                rule_operator = rule.get("operator", "required")
                rule_value = rule.get("value")

                if rule_operator == "required" and (rule_field not in value or value[rule_field] is None):
                    return (
                        False,
                        f"Conditional requirement violated: '{rule_field}' is required when {condition_field} {condition_operator} {condition_value}",
                    )
                elif rule_operator == "forbidden" and rule_field in value and value[rule_field] is not None:
                    return (
                        False,
                        f"Conditional constraint violated: '{rule_field}' is forbidden when {condition_field} {condition_operator} {condition_value}",
                    )

        return True, ""

    def _validate_aggregate_calculations(self, value: Any, constraints: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate aggregate calculation constraints."""
        if not isinstance(value, (dict, list)):
            return (
                False,
                "Aggregate calculations validation requires a dictionary or list input",
            )

        # Handle list of records for aggregation
        if isinstance(value, list):
            records = value
        else:
            records = [value]

        aggregations = constraints.get("aggregations", {})
        for agg_name, agg_config in aggregations.items():
            field = agg_config.get("field")
            operation = agg_config.get("operation", "sum")
            expected_value = agg_config.get("expected_value")
            tolerance = agg_config.get("tolerance", 0.01)

            if not field:
                continue

            # Collect values for aggregation
            values = []
            for record in records:
                if isinstance(record, dict) and field in record:
                    try:
                        val = float(record[field])
                        values.append(val)
                    except (TypeError, ValueError):
                        continue

            if not values:
                continue

            # Calculate aggregate value
            try:
                if operation == "sum":
                    calculated = sum(values)
                elif operation == "avg":
                    calculated = sum(values) / len(values)
                elif operation == "min":
                    calculated = min(values)
                elif operation == "max":
                    calculated = max(values)
                elif operation == "count":
                    calculated = len(values)
                else:
                    return False, f"Unsupported aggregation operation: {operation}"

                # Validate against expected value if provided
                if expected_value is not None:
                    if abs(calculated - expected_value) > tolerance:
                        return (
                            False,
                            f"Aggregate calculation '{agg_name}' failed: expected {expected_value}, got {calculated:.3f}",
                        )

                # Check min/max constraints
                min_value = agg_config.get("min_value")
                max_value = agg_config.get("max_value")

                if min_value is not None and calculated < min_value:
                    return (
                        False,
                        f"Aggregate '{agg_name}' value {calculated:.3f} below minimum {min_value}",
                    )
                if max_value is not None and calculated > max_value:
                    return (
                        False,
                        f"Aggregate '{agg_name}' value {calculated:.3f} above maximum {max_value}",
                    )

            except (TypeError, ValueError, ZeroDivisionError) as e:
                return False, f"Error calculating aggregate '{agg_name}': {str(e)}"

        return True, ""

    def add_custom_constraint(
        self,
        name: str,
        constraint_type: ConstraintType,
        severity: ConstraintSeverity,
        condition: str,
        validation_function: Callable[..., Any],
        entities: List[str],
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Add custom constraint rule."""
        constraint = ConstraintRule(
            name=name,
            constraint_type=constraint_type,
            severity=severity,
            condition=condition,
            validation_function=validation_function,
            entities=entities,
            metadata=metadata,
        )
        self.add_constraint(constraint)

    async def validate_constraints_async(self, data: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Async constraint validation method required by parallel processor."""
        context = parameters.get("context", {})
        result = await self.validate_constraints(data, context)

        return {
            "violations": [
                {
                    "constraint_id": violation.constraint_id,
                    "severity": violation.severity.value,
                    "message": violation.message,
                    "entity_path": violation.entity_path,
                    "suggested_fix": violation.suggested_fix,
                    "metadata": violation.metadata,
                }
                for violation in result.violations
            ],
            "warnings": result.warnings,
            "passed_constraints": result.passed_constraints,
            "failed_constraints": result.failed_constraints,
        }


# Global validator instance
complex_constraint_validator = ComplexConstraintValidator()
