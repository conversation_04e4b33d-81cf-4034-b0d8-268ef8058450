"""Enumeration types related to mechanical elements and
their properties that are relevant for integrated electrical design and
project management within the Ultimate Electrical Designer application.
It includes classifications for piping materials, thermal insulation,
tank types, structural supports, and valve types, facilitating cross-disciplinary
data consistency and contextual understanding for electrical installations.
"""

from enum import Enum


class PipeMaterialType(Enum):
    """Common materials used for piping."""

    CARBON_STEEL = "Carbon Steel"
    STAINLESS_STEEL = "Stainless Steel"
    COPPER = "Copper"
    PVC = "Polyvinyl Chloride (PVC)"
    HDPE = "High-Density Polyethylene (HDPE)"
    FRP = "Fiber Reinforced Plastic (FRP)"  # Fiberglass
    GALVANIZED_STEEL = "Galvanized Steel"
    DUCTILE_IRON = "Ductile Iron"


class ThermalInsulationType(Enum):
    """Common materials used for thermal insulation of pipes and equipment."""

    MINERAL_WOOL = "Mineral Wool"
    FIBERGLASS = "Fiberglass"  # Renamed from Glass Wool for common usage
    CALCIUM_SILICATE = "Calcium Silicate"
    FOAMED_GLASS = "Foamed Glass"
    POLYURETHANE_FOAM = "Polyurethane Foam"
    ELASTOMERIC_FOAM = "Elastomeric Foam"
    AEROGEL = "Aerogel"  # High performance insulation


class TankType(Enum):
    """Common geometries or classifications for tanks."""

    VERTICAL_CYLINDRICAL = "Vertical Cylindrical Tank"
    HORIZONTAL_CYLINDRICAL = "Horizontal Cylindrical Tank"
    SPHERICAL = "Spherical Tank"
    RECTANGULAR = "Rectangular Tank"
    CONE_BOTTOM = "Cone Bottom Tank"
    DISHED_HEAD = "Dished Head Tank"
    OPEN_TOP = "Open Top Tank"
    FLOATING_ROOF = "Floating Roof Tank"  # For large oil tanks


class SupportType(Enum):
    """Types of structural supports for equipment or piping."""

    CONCRETE_SLAB = "Concrete Slab"
    SKID = "Skid"  # Pre-fabricated modular support
    PIPE_RACK = "Pipe Rack"
    STRUCTURAL_STEEL_FRAME = "Structural Steel Frame"
    LEGS = "Legs"  # For vessels, smaller equipment
    HANGERS = "Hangers"  # For pipes from overhead structures
    SADDLE_SUPPORT = "Saddle Support"  # For horizontal vessels/pipes
    SPRING_SUPPORT = "Spring Support"  # For piping accommodating thermal expansion
    VIBRATION_ISOLATOR = "Vibration Isolator"  # For pumps, compressors


class TankAccessoryType(Enum):
    """Common accessories or fittings found on tanks."""

    MANHOLE = "Manhole"
    HANDHOLE = "Handhole"
    NOZZLE = "Nozzle"
    VENT = "Vent"
    GAUGE = "Gauge (Level, Pressure, Temp)"
    RELIEF_VALVE = "Relief Valve"
    AGITATOR = "Agitator / Mixer"
    LADDER = "Ladder"
    PLATFORM = "Platform"
    HEATING_COIL = "Heating Coil"  # For internal heating


class PipeSchedule(Enum):
    """Standard pipe schedules defining wall thickness."""

    SCH_10 = "SCH 10"
    SCH_20 = "SCH 20"
    SCH_30 = "SCH 30"
    SCH_40 = "SCH 40"
    SCH_60 = "SCH 60"
    SCH_80 = "SCH 80"
    SCH_100 = "SCH 100"
    SCH_120 = "SCH 120"
    SCH_140 = "SCH 140"
    SCH_160 = "SCH 160"
    STD = "Standard Weight"  # Equivalent to SCH 40 for certain sizes
    XS = "Extra Strong"  # Equivalent to SCH 80 for certain sizes
    XXS = "Double Extra Strong"


class ValveType(Enum):
    """Common types of valves encountered in piping systems."""

    BALL_VALVE = "Ball Valve"
    GATE_VALVE = "Gate Valve"
    GLOBE_VALVE = "Globe Valve"
    CHECK_VALVE = "Check Valve"
    BUTTERFLY_VALVE = "Butterfly Valve"
    PLUG_VALVE = "Plug Valve"
    DIAPHRAGM_VALVE = "Diaphragm Valve"
    PINCH_VALVE = "Pinch Valve"
    PRESSURE_RELIEF_VALVE = "Pressure Relief Valve (PRV)"
    CONTROL_VALVE = "Control Valve"  # For modulating flow
    SOLENOID_VALVE = "Solenoid Valve"  # Electrically actuated


class SoilType(Enum):
    """Common classifications of soil."""

    CLAY = "Clay"
    SAND = "Sand"
    SILT = "Silt"
    GRAVEL = "Gravel"
    LOAM = "Loam"
    BEDROCK = "Bedrock"
    FILLED_GROUND = "Filled Ground"  # Man-made fill
