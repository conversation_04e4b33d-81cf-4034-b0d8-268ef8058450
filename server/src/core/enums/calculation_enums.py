"""Enumeration types for various engineering calculations
performed by the Ultimate Electrical Designer application.
It classifies the types of analyses (e.g., heat loss, cable sizing, short circuit),
defines calculation statuses, and outlines optimization objectives.
These enums are fundamental for managing the computational workflows and
interpreting the results of the design process.
"""

from enum import Enum


class CalculationType(Enum):
    """Defines the types of engineering calculations performed by the application.
    These are the *functions* or *analyses* that the system can perform.
    """

    HEAT_LOSS_CALCULATION = "Heat Loss Calculation"
    HEAT_TRACING_DESIGN = "Heat Tracing Design Calculation"  # More specific than just "Heat Tracing"
    CABLE_SIZING_CALCULATION = "Cable Sizing Calculation"
    VOLTAGE_DROP_CALCULATION = "Voltage Drop Calculation"
    SHORT_CIRCUIT_CALCULATION = "Short Circuit Calculation"
    LOAD_FLOW_ANALYSIS = "Load Flow Analysis"
    POWER_REQUIREMENT_ESTIMATION = "Power Requirement Estimation"
    EARTHING_DESIGN_CALCULATION = "Earthing System Design Calculation"  # New, critical for electrical
    LIGHTING_CALCULATION = "Lighting Calculation"
    POWER_FACTOR_CORRECTION_CALCULATION = "Power Factor Correction Calculation"
    MOTOR_STARTING_ANALYSIS = "Motor Starting Analysis"
    PROTECTION_COORDINATION_STUDY = "Protection Coordination Study"
    ARC_FLASH_STUDY = "Arc Flash Study"  # New, very important safety calculation
    THERMAL_PERFORMANCE_ANALYSIS = "Thermal Performance Analysis"  # For enclosures, equipment
    OPTIMIZATION = "Optimization Calculation"  # More generic optimization
    STANDARDS_COMPLIANCE_CHECK = "Standards Compliance Check"  # Not a calculation, but a validation type


class CalculationStatus(Enum):
    """Current status of a calculation run."""

    PENDING = "Pending"
    RUNNING = "Running"
    COMPLETED = "Completed"
    FAILED = "Failed"
    ABORTED = "Aborted"  # New: user stopped it
    QUEUED = "Queued"  # New: waiting to run


class OptimizationObjective(Enum):
    """Objectives for optimization calculations."""

    MINIMIZE_COST = "Minimize Cost"
    MAXIMIZE_EFFICIENCY = "Maximize Efficiency"
    MINIMIZE_WEIGHT = "Minimize Weight"
    MINIMIZE_SPACE = "Minimize Space"
    BALANCED_PERFORMANCE_COST = "Balanced Performance/Cost"
    MINIMIZE_ENERGY_CONSUMPTION = "Minimize Energy Consumption"
    MAXIMIZE_RELIABILITY = "Maximize Reliability"
