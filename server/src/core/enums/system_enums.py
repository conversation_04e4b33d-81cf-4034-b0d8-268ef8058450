"""Enumeration types for internal system operations,
error handling, monitoring, and data validation within the
Ultimate Electrical Designer application. It covers classifications for
error severity and context, monitoring metrics, and detailed validation
statuses and types. These enums are vital for maintaining system stability,
diagnosing issues, and ensuring data integrity and design quality.
"""

from enum import Enum


class ErrorSeverity(Enum):
    """Severity levels for errors encountered in the system."""

    CRITICAL = "Critical"  # System failure, data corruption, major security breach
    HIGH = "High"  # Major feature broken, significant data inaccuracy
    MEDIUM = "Medium"  # Minor feature broken, usability issue
    LOW = "Low"  # Cosmetic, minor inconvenience
    INFO = "Info"  # Informational, not an error but notable event


class ErrorContext(Enum):
    """Context or module where an error occurred."""

    API = "API"
    SERVICE = "Service Layer"
    REPOSITORY = "Repository"
    CALCULATION = "Calculation"
    MIDDLEWARE = "Middleware"
    SECURITY = "Security"
    DATABASE = "Database"
    CALCULATION_ENGINE = "Calculation Engine"
    DATA_IMPORT_EXPORT = "Data Import/Export"
    REPORT_GENERATION = "Report Generation"
    AUTHENTICATION = "Authentication"
    AUTHORIZATION = "Authorization"
    CONFIGURATION = "Configuration"
    EXTERNAL_INTEGRATION = "External Integration"
    BACKGROUND_JOB = "Background Job"
    VALIDATION = "Validation"


class MonitoringContext(Enum):
    """Contexts for system monitoring metrics."""

    OPERATION = "Operation Performance"
    FUNCTION = "Function Performance"
    SERVICE = "Service Method Performance"
    UTILITY = "Utility Performance"
    API = "API Performance"
    DATABASE = "Database Performance"
    REPOSITORY = "Repository Performance"
    CALCULATION = "Calculation Performance"
    BATCH = "Batch Processing Performance"
    MIDDLEWARE = "Middleware Performance"
    CPU_USAGE = "CPU Usage"
    MEMORY_USAGE = "Memory Usage"
    DISK_USAGE = "Disk Usage"
    NETWORK_THROUGHPUT = "Network Throughput"
    CALCULATION_RESOURCE_USAGE = "Calculation Resource Usage"
    USER_ACTIVITY = "User Activity"
    DATA_STORAGE = "Data Storage"
    SERVICE_LATENCY = "Service Latency"


class MetricType(Enum):
    """Types of performance or usage metrics."""

    EXECUTION_TIME = "Execution Time"
    MEMORY_USAGE = "Memory Usage"
    CPU_USAGE = "CPU Usage"
    REQUEST_COUNT = "Request Count"
    ERROR_RATE = "Error Rate"
    DISK_IO = "Disk I/O"
    NETWORK_LATENCY = "Network Latency"
    CONCURRENT_USERS = "Concurrent Users"
    DATA_VOLUME = "Data Volume"
    THROUGHPUT = "Throughput"


class ValidationSeverity(Enum):
    """Severity levels for data or design validation results."""

    ERROR = "Error"  # Blocks action, must be fixed
    WARNING = "Warning"  # Suggests a potential issue, allows continuation but advises review
    INFO = "Info"  # Informational message, best practice suggestion
    CRITICAL_ERROR = "Critical Error"  # For very severe, potentially data-corrupting issues


class ValidationType(Enum):
    """Types of validation checks performed."""

    REQUIRED_FIELD = "Required Field Check"
    DATA_TYPE = "Data Type Validation"
    RANGE_CHECK = "Range Check"
    FORMAT_CHECK = "Format Check"
    UNIQUENESS = "Uniqueness Validation"
    CONSISTENCY = "Consistency Check (Cross-Field)"
    REFERENCE_INTEGRITY = "Reference Integrity Check"
    BUSINESS_RULE = "Business Rule Validation"
    STANDARDS_COMPLIANCE = "Standards Compliance Validation"
    DESIGN_RULE = "Design Rule Validation"
    UNIT_CONSISTENCY = "Unit Consistency"  # E.g., if inputs use different units
    CIRCUIT_BREAKER_SELECTION = "Circuit Breaker Selection Validation"  # Specific to electrical
    CABLE_SIZING_VALIDATION = "Cable Sizing Validation"  # Specific to electrical
    HARMONIC_DISTORTION = "Harmonic Distortion Validation"  # Specific electrical check


class ValidationResult(Enum):
    """Outcome of a validation check."""

    PASSED = "Passed"
    FAILED = "Failed"
    WARNING = "Warning"
    BLOCKED = "Blocked"  # Implies the operation cannot proceed due to failure
    SKIPPED = "Skipped"  # If validation was not performed for a reason


class SecurityLevel(Enum):
    """Levels of security access or classification."""

    BASIC = "Basic"
    STANDARD = "Standard"
    STRICT = "Strict"
    CUSTOM = "Custom"
    PUBLIC = "Public"
    INTERNAL = "Internal"
    CONFIDENTIAL = "Confidential"
    SENSITIVE = "Sensitive"
    TOP_SECRET = "Top Secret"  # Security classification level - not a password  # nosec B105


class SyncStatus(Enum):
    """Status of synchronization operations."""

    PENDING = "Pending"
    IN_PROGRESS = "In Progress"
    COMPLETED = "Completed"
    FAILED = "Failed"
    CANCELLED = "Cancelled"
    RETRY_REQUIRED = "Retry Required"


class SyncDirection(Enum):
    """Direction of synchronization operations."""

    LOCAL_TO_CENTRAL = "Local to Central"
    CENTRAL_TO_LOCAL = "Central to Local"
    BIDIRECTIONAL = "Bidirectional"


class SyncOperation(Enum):
    """Types of synchronization operations."""

    FULL_SYNC = "Full Sync"
    INCREMENTAL_SYNC = "Incremental Sync"
    MANUAL_SYNC = "Manual Sync"
    SCHEDULED_SYNC = "Scheduled Sync"
    CONFLICT_RESOLUTION = "Conflict Resolution"
