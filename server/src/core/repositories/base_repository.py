"""Base Repository.

This module provides a generic base repository class for database operations,
providing common CRUD methods and error handling.
"""

from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession

from src.config.logging_config import logger
from src.core.errors.exceptions import NotFoundError
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.base import Base
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.utils.pagination_utils import (
    PaginationParams,
    PaginationResult,
    SortParams,
    paginate_query_async,
)
from src.core.utils.query_utils import QueryBuilder

ModelType = TypeVar("ModelType", bound=Base)


class BaseRepository(Generic[ModelType]):
    """Base repository class for asynchronous database operations."""

    def __init__(self, db_session: AsyncSession, model: Type[ModelType]):
        """Initialize the BaseRepository."""
        self.db_session = db_session
        self.model = model

    @handle_repository_errors("session_health")
    @monitor_repository_performance("session_health")
    async def is_session_healthy(self) -> bool:
        """Check if the database session is healthy and usable."""
        if self.db_session is None or not self.db_session.is_active:
            return False

        from sqlalchemy import text

        await self.db_session.execute(text("SELECT 1"))
        return True

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    async def get_by_id(self, item_id: int) -> Optional[ModelType]:
        """Get item by ID."""
        query = select(self.model).where(self.model.id == item_id)
        if hasattr(self.model, "is_deleted"):
            query = query.where(self.model.is_deleted == False)

        result = await self.db_session.execute(query)
        return result.scalars().one_or_none()

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """Get all items with optional pagination."""
        query = select(self.model).offset(skip).limit(limit)
        result = await self.db_session.execute(query)
        return list(result.scalars().all())

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    async def create(self, data: Dict[str, Any]) -> ModelType:
        """Create a new item."""
        item = self.model(**data)
        self.db_session.add(item)
        await self.db_session.flush()
        return item

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    async def update(self, item_id: int, data: Dict[str, Any]) -> ModelType:
        """Update an existing item by ID."""
        item = await self.get_by_id(item_id)
        if not item:
            raise NotFoundError(detail=f"{self.model.__name__} with ID '{item_id}' not found.")

        for key, value in data.items():
            if hasattr(item, key):
                setattr(item, key, value)

        await self.db_session.flush()
        return item

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    async def delete(self, item_id: int) -> bool:
        """Delete an item by ID."""
        item = await self.get_by_id(item_id)
        if not item:
            raise NotFoundError(detail=f"{self.model.__name__} with ID '{item_id}' not found.")

        self.db_session.delete(item)
        await self.db_session.flush()
        return True

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    async def get_paginated(
        self,
        pagination_params: PaginationParams,
        sort_params: Optional[SortParams] = None,
        filters: Optional[Dict[str, Any]] = None,
    ) -> PaginationResult:
        """Get paginated results with optional filters and sorting."""
        query = select(self.model)
        if filters:
            builder = QueryBuilder(self.db_session, self.model, query=query)
            for field, value in filters.items():
                if hasattr(self.model, field) and value is not None:
                    builder.filter_by_field(field, value)
            query = builder.build()

        return await paginate_query_async(self.db_session, query, self.model, pagination_params, sort_params)

    @handle_repository_errors("base")
    @monitor_repository_performance("base")
    async def search_paginated(
        self,
        search_term: str,
        searchable_fields: List[str],
        pagination_params: PaginationParams,
        sort_params: Optional[SortParams] = None,
        additional_filters: Optional[Dict[str, Any]] = None,
    ) -> PaginationResult:
        """Search entities with pagination."""
        builder = QueryBuilder(self.db_session, self.model)

        if search_term and searchable_fields:
            builder.filter_by_text_search(search_term, searchable_fields)

        if additional_filters:
            for field, value in additional_filters.items():
                if hasattr(self.model, field) and value is not None:
                    builder.filter_by_field(field, value)

        query = builder.build()

        return await paginate_query_async(self.db_session, query, self.model, pagination_params, sort_params)
