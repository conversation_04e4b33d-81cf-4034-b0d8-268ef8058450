"""Project Member Repository.

This module provides the data access layer for the ProjectMember entity,
handling all database operations related to project memberships.
"""

from typing import List, Optional

from sqlalchemy import and_, select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.project import ProjectMember
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository


class ProjectMemberRepository(BaseRepository[ProjectMember]):
    """Repository for ProjectMember entity data access operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the ProjectMember repository."""
        super().__init__(db_session, ProjectMember)
        logger.debug("ProjectMemberRepository initialized")

    @handle_repository_errors("project_member")
    @monitor_repository_performance("project_member")
    async def get_member_by_user_and_project(self, user_id: int, project_id: int) -> Optional[ProjectMember]:
        """Get a project member by user ID and project ID."""
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.user_id == user_id,
                    self.model.project_id == project_id,
                    self.model.is_deleted == False,
                )
            )
            .options(selectinload(self.model.user), selectinload(self.model.role))
        )
        result = await self.db_session.execute(stmt)
        return result.scalars().first()

    @handle_repository_errors("project_member")
    @monitor_repository_performance("project_member")
    async def list_members_by_project(self, project_id: int, skip: int = 0, limit: int = 100) -> List[ProjectMember]:
        """List all members for a given project."""
        stmt = (
            select(self.model)
            .where(and_(self.model.project_id == project_id, self.model.is_deleted == False))
            .offset(skip)
            .limit(limit)
            .options(selectinload(self.model.user), selectinload(self.model.role))
        )
        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("project_member")
    @monitor_repository_performance("project_member")
    async def list_projects_by_user(self, user_id: int, skip: int = 0, limit: int = 100) -> List[ProjectMember]:
        """List all project memberships for a given user."""
        stmt = (
            select(self.model)
            .where(and_(self.model.user_id == user_id, self.model.is_deleted == False))
            .offset(skip)
            .limit(limit)
            .options(selectinload(self.model.project), selectinload(self.model.role))
        )
        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("project_member")
    @monitor_repository_performance("project_member")
    async def get_member_with_details(self, member_id: int) -> Optional[ProjectMember]:
        """Get a single project member with user and role details."""
        from sqlalchemy.orm import selectinload
        from src.core.models.general.user import User
        from src.core.models.general.user_role import UserRole, UserRoleAssignment

        stmt = (
            select(self.model)
            .where(and_(self.model.id == member_id, self.model.is_deleted == False))
            .options(
                selectinload(self.model.user).selectinload(User.role_assignments).selectinload(UserRoleAssignment.role),
                selectinload(self.model.role),
            )
        )
        result = await self.db_session.execute(stmt)
        return result.scalars().first()
