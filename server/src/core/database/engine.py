"""Database Engine Management.

This module handles the creation and configuration of SQLAlchemy engines
for the application's database connections.
"""

from typing import Any, Dict, Optional, cast

from sqlalchemy import Engine, text
from sqlalchemy import create_engine as sqlalchemy_create_engine
from sqlalchemy.engine import Connection
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncEngine, create_async_engine
from sqlalchemy.pool import StaticPool

try:
    from src.config.logging_config import logger
    from src.config.settings import settings
except ImportError:
    from src.config.logging_config import logger
    from src.config.settings import settings
try:
    from src.core.errors.exceptions import DatabaseError
except ImportError:
    from src.core.errors.exceptions import DatabaseError

from src.core.errors.unified_error_handler import handle_database_errors

# Global engine instance
_engine: Optional[Engine] = None
_async_engine: Optional[AsyncEngine] = None


@handle_database_errors("engine_creation")
def create_engine(database_url: Optional[str] = None, echo: bool = False, force_recreate: bool = False) -> Engine:
    """Create and configure a SQLAlchemy synchronous engine.
    This is primarily for use by CLI commands and migration scripts.
    """
    global _engine
    if _engine and not force_recreate:
        return _engine

    db_url_to_use = database_url or settings.DATABASE_URL
    if not db_url_to_use:
        raise DatabaseError(reason="DATABASE_URL is not configured.")

    logger.info(f"Creating synchronous database engine for: {db_url_to_use}")

    try:
        engine_kwargs: Dict[str, Any] = {
            "echo": echo,
            "future": True,
            "pool_size": 10,
            "max_overflow": 20,
            "pool_pre_ping": True,
        }

        engine = sqlalchemy_create_engine(db_url_to_use, **engine_kwargs)
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))

        _engine = engine
        logger.info(f"Successfully created synchronous engine for: {_get_db_type_from_url(engine.url)}")
        return _engine
    except Exception as e:
        logger.error(f"Failed to create synchronous engine for {db_url_to_use}: {e}")
        raise DatabaseError(
            reason=f"Failed to connect to synchronous database: {e}",
            original_exception=e,
        )


def _get_db_type_from_url(url: Any) -> str:
    """Extract database type from URL for logging purposes."""
    url_str = str(url)
    if url_str.startswith("mssql") or url_str.startswith("sqlserver"):
        return "SQL Server"
    if url_str.startswith("postgresql"):
        return "PostgreSQL"
    if url_str.startswith("mysql"):
        return "MySQL"
    return "Unknown"


@handle_database_errors("async_engine_creation")
async def initialize_database_engine() -> AsyncEngine:
    """Initializes the async database engine."""
    global _async_engine
    if _async_engine:
        return _async_engine

    db_url = settings.DATABASE_URL
    if not db_url:
        raise DatabaseError(reason="DATABASE_URL is not configured.")

    try:
        logger.info(f"Attempting to connect to database: {db_url}")
        _async_engine = await _create_and_test_async_engine(db_url, echo=settings.DB_ECHO)
        logger.info("Successfully connected to the database.")
        return _async_engine
    except DatabaseError as e:
        logger.error(f"Database connection failed: {e}")
        raise  # Fail fast in all environments


async def _create_and_test_async_engine(database_url: str, echo: bool = False) -> AsyncEngine:
    """Create, configure, and test a SQLAlchemy async engine."""
    if "postgresql" in database_url and "asyncpg" not in database_url:
        database_url = database_url.replace("postgresql://", "postgresql+asyncpg://")

    engine_kwargs: Dict[str, Any] = {
        "echo": echo,
        "future": True,
        "pool_size": 30,
        "max_overflow": 50,
        "pool_pre_ping": True,
    }

    engine = create_async_engine(database_url, **engine_kwargs)

    try:
        async with engine.connect() as conn:
            await conn.run_sync(lambda sync_conn: sync_conn.execute(text("SELECT 1")))
        logger.info(f"Async engine created successfully for: {_get_db_type_from_url(engine.url)}")
        return engine
    except Exception as e:
        logger.error(f"Failed to create async engine for {database_url}: {e}")
        await engine.dispose()
        raise DatabaseError(reason=f"Failed to connect to async database: {e}", original_exception=e)


@handle_database_errors("engine_retrieval")
def get_engine() -> Engine:
    """Get the current engine instance.

    Returns:
        Current Engine instance

    Raises:
        RuntimeError: If no engine has been created yet

    """
    global _engine
    if _engine is None:
        raise RuntimeError("Database engine not initialized. Call create_engine() first.")
    return _engine


@handle_database_errors("async_engine_retrieval")
def get_async_engine() -> AsyncEngine:
    """Get the current async engine instance."""
    global _async_engine
    if _async_engine is None:
        raise RuntimeError("Async database engine not initialized. Call initialize_database_engine() first.")
    return _async_engine


async def close_async_engine() -> None:
    """Dispose of the async engine."""
    global _async_engine
    if _async_engine:
        await _async_engine.dispose()
        _async_engine = None
        logger.info("Async database engine disposed.")


def close_engine() -> None:
    """Close the current engine and reset the global instance.

    This function handles disposal errors gracefully to ensure the engine
    is always reset to None, even if disposal fails.
    """
    global _engine
    if _engine is not None:
        try:
            _engine.dispose()
            logger.info("Database engine closed and disposed")
        except Exception as e:
            # Log disposal error but don't raise - we still want to reset the engine
            logger.warning(f"Error during engine disposal: {e}")
        finally:
            # Always reset the engine to None, even if disposal failed
            _engine = None
