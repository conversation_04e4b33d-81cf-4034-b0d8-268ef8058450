"""Database Layer - Public Interface.

This module provides the public interface for the database layer,
exposing the key components needed by other layers.
"""

from .engine import close_engine, create_engine, get_engine
from .initialization import (
    create_initial_migration,
    initialize_database,
    run_alembic_migrations,
)
from .session import (
    create_tables,
    drop_tables,
    get_db,
    get_db_session,
    get_session_factory,
)

__all__ = [
    "create_engine",
    "get_engine",
    "close_engine",
    "get_session_factory",
    "get_db_session",
    "get_db",
    "create_tables",
    "drop_tables",
    "initialize_database",
    "run_alembic_migrations",
    "create_initial_migration",
]
