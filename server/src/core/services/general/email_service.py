"""Email Service.

This module provides email sending capabilities for user notifications,
including email verification, password reset, and other communication.
"""

import secrets
from datetime import datetime, timedelta
from typing import Optional
from urllib.parse import urljoin

from src.config.logging_config import logger
from src.config.settings import settings
from src.core.errors.exceptions import BaseApplicationException
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.utils.datetime_utils import utcnow_aware


class EmailService:
    """Service for sending email notifications and managing email verification."""

    def __init__(self):
        """Initialize the email service."""
        logger.debug("EmailService initialized")

    def generate_verification_token(self) -> str:
        """Generate a secure random token for email verification."""
        return secrets.token_urlsafe(32)

    def generate_verification_url(self, token: str, base_url: Optional[str] = None, path: str = "/auth/verify-email") -> str:
        """Generate verification URL for email verification or password reset."""
        if not base_url:
            base_url = getattr(settings, 'FRONTEND_URL', 'http://localhost:3000')
        
        verification_path = f"{path}?token={token}"
        return urljoin(base_url, verification_path)

    @handle_service_errors("send_verification_email")
    @monitor_service_performance("send_verification_email")
    async def send_verification_email(self, email: str, name: str, verification_url: str) -> bool:
        """Send email verification email to user."""
        logger.info(f"Sending verification email to: {email}")
        
        # For now, we'll log the email content instead of actually sending
        # In production, this would integrate with an email service like SendGrid, AWS SES, etc.
        
        email_subject = "Verify Your Email Address - Ultimate Electrical Designer"
        email_body = f"""
        Hello {name},

        Thank you for registering with Ultimate Electrical Designer!

        To complete your registration and verify your email address, please click the link below:
        
        {verification_url}

        This verification link will expire in 24 hours for security reasons.

        If you did not create an account with us, please ignore this email.

        Best regards,
        The Ultimate Electrical Designer Team
        """
        
        # Log email content (replace with actual email sending in production)
        logger.info(f"Email verification content for {email}:")
        logger.info(f"Subject: {email_subject}")
        logger.info(f"Body: {email_body}")
        logger.info(f"Verification URL: {verification_url}")
        
        # Simulate successful email sending
        logger.info(f"Verification email sent successfully to: {email}")
        return True

    @handle_service_errors("send_password_reset_email")
    @monitor_service_performance("send_password_reset_email")
    async def send_password_reset_email(self, email: str, name: str, reset_url: str) -> bool:
        """Send password reset email to user."""
        logger.info(f"Sending password reset email to: {email}")
        
        email_subject = "Password Reset Request - Ultimate Electrical Designer"
        email_body = f"""
        Hello {name},

        We received a request to reset your password for your Ultimate Electrical Designer account.

        To reset your password, please click the link below:
        
        {reset_url}

        This password reset link will expire in 1 hour for security reasons.

        If you did not request a password reset, please ignore this email. Your password will remain unchanged.

        Best regards,
        The Ultimate Electrical Designer Team
        """
        
        # Log email content (replace with actual email sending in production)
        logger.info(f"Password reset email content for {email}:")
        logger.info(f"Subject: {email_subject}")
        logger.info(f"Body: {email_body}")
        logger.info(f"Reset URL: {reset_url}")
        
        # Simulate successful email sending
        logger.info(f"Password reset email sent successfully to: {email}")
        return True

    def get_verification_expiry(self, hours: int = 24) -> datetime:
        """Get expiry datetime for verification tokens."""
        return utcnow_aware() + timedelta(hours=hours)

    def get_reset_expiry(self, hours: int = 1) -> datetime:
        """Get expiry datetime for password reset tokens."""
        return utcnow_aware() + timedelta(hours=hours)