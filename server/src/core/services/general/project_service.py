"""Project Service Layer.

This module contains the business logic for Project entity operations including:
- Project creation, retrieval, updating, and deletion
- Business validation and rules enforcement
- Transaction management and orchestration
- Error handling and logging
"""

from typing import Any, Dict, List, Optional, Union

from src.config.logging_config import logger
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.enums import ProjectStatus
from src.core.errors.exceptions import (
    DatabaseError,
    DataValidationError,
    DuplicateEntryError,
    ProjectNotFoundError,
)
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.models.general.project import Project
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.schemas.base_schemas import PaginationSchema
from src.core.schemas.general.project_schemas import (
    ProjectCreateSchema,
    ProjectListResponseSchema,
    ProjectReadSchema,
    ProjectSummarySchema,
    ProjectUpdateSchema,
)
from src.core.utils import (
    PaginationParams,
    SortParams,
    create_pagination_response,
    sanitize_text,
    slugify,
)
from src.core.utils.datetime_utils import utcnow_naive


class ProjectService:
    """Service class for Project entity business logic."""

    def __init__(
        self,
        project_repository: ProjectRepository,
        connection_manager: Optional[DynamicConnectionManager] = None,
    ):
        """Initialize the Project service."""
        self.project_repository = project_repository
        self.connection_manager = connection_manager
        logger.debug(f"ProjectService initialized with repository: {type(project_repository).__name__}")

    @handle_service_errors("create_project")
    @monitor_service_performance("create_project")
    async def create_project(self, project_data: ProjectCreateSchema) -> ProjectReadSchema:
        """Create a new project with business validation."""
        logger.info(f"Attempting to create new project: '{project_data.name}'")

        await self._validate_project_creation(project_data)
        await self._handle_database_url_in_creation(project_data)

        project_dict = project_data.model_dump()

        if not project_dict.get("project_number"):
            import time

            project_dict["project_number"] = f"PROJ-{int(time.time())}-{project_data.name[:3].upper()}"

        if "status" in project_dict and hasattr(project_dict["status"], "value"):
            project_dict["status"] = project_dict["status"].value

        project_dict.update(
            {
                "description": sanitize_text(project_data.description or ""),
            }
        )

        project_slug = slugify(project_data.name)
        logger.debug(f"Generated slug for project '{project_data.name}': '{project_slug}'")

        new_project = await self.project_repository.create(project_dict)
        await self.project_repository.db_session.flush()
        await self.project_repository.db_session.refresh(new_project)

        logger.info(f"Project '{new_project.name}' (ID: {new_project.id}) created successfully")

        return self._convert_project_to_schema(new_project)

    def _convert_project_to_schema(self, project: Project) -> ProjectReadSchema:
        """Convert a project model to schema, avoiding async relationship loading issues."""
        project_data = {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "status": project.status,
            "client": project.client,
            "location": project.location,
            "is_offline": project.is_offline,
            "database_url": project.database_url,
            "project_number": project.project_number,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "members": [],  # Empty list to avoid async loading issues
        }
        return ProjectReadSchema.model_validate(project_data)

    @handle_service_errors("get_project_details")
    @monitor_service_performance("get_project_details")
    async def get_project_details(self, project_id: str) -> ProjectReadSchema:
        """Retrieve detailed project information."""
        logger.debug(f"Retrieving project details for ID: {project_id}")

        project = await self._get_project_by_id_or_code(project_id)

        logger.debug(f"Project found: '{project.name}' (ID: {project.id})")
        return self._convert_project_to_schema(project)

    @handle_service_errors("update_project")
    @monitor_service_performance("update_project")
    async def update_project(self, project_id: str, project_data: ProjectUpdateSchema) -> ProjectReadSchema:
        """Update an existing project with new data.

        Args:
            project_id: The ID or project number of the project to update
            project_data: The project update data containing fields to modify

        Returns:
            ProjectReadSchema: The updated project data

        Raises:
            ProjectNotFoundError: If the project doesn't exist
            DataValidationError: If the update data is invalid
            DatabaseError: If the database operation fails

        """
        logger.info(f"Attempting to update project: {project_id}")

        existing_project = await self._get_project_by_id_or_code(project_id)

        await self._validate_project_update(existing_project, project_data)
        await self._handle_database_url_in_update(existing_project, project_data)

        update_dict = project_data.model_dump(exclude_unset=True)

        if not update_dict:
            logger.debug(f"No fields to update for project {project_id}")
            return self._convert_project_to_schema(existing_project)

        # Map schema field names to model field names
        if "client_name" in update_dict:
            update_dict["client"] = update_dict.pop("client_name")

        # Remove client_contact as it's not a model field (it's for future use)
        if "client_contact" in update_dict:
            update_dict.pop("client_contact")

        if "description" in update_dict:
            update_dict["description"] = sanitize_text(update_dict["description"] or "")

        update_dict["updated_at"] = utcnow_naive()

        updated_project = await self.project_repository.update(existing_project.id, update_dict)
        await self.project_repository.db_session.flush()
        await self.project_repository.db_session.refresh(updated_project)

        logger.info(f"Project '{updated_project.name}' (ID: {updated_project.id}) updated successfully")
        return self._convert_project_to_schema(updated_project)

    @handle_service_errors("delete_project")
    @monitor_service_performance("delete_project")
    async def delete_project(self, project_id: str, deleted_by_user_id: Optional[int] = None) -> None:
        """Soft delete a project."""
        logger.info(f"Attempting to delete project: {project_id}")

        existing_project = await self._get_project_by_id_or_code(project_id)

        if existing_project.is_deleted:
            logger.warning(f"Project {project_id} is already deleted")
            raise ProjectNotFoundError(project_id=project_id)

        await self.project_repository.soft_delete_project(existing_project.id, deleted_by_user_id)

        logger.info(f"Project '{existing_project.name}' (ID: {existing_project.id}) deleted successfully")

    @handle_service_errors("get_projects_list")
    @monitor_service_performance("get_projects_list")
    async def get_projects_list(
        self,
        page: int = 1,
        per_page: int = 10,
        include_deleted: bool = False,
        pagination_params: Optional[PaginationParams] = None,
        sort_params: Optional[SortParams] = None,
        filters: Optional[Dict[str, Any]] = None,
    ) -> ProjectListResponseSchema:
        """Get paginated list of projects."""
        logger.debug(f"Retrieving projects list: page={page}, per_page={per_page}, include_deleted={include_deleted}")

        # Handle enhanced parameters if provided
        if pagination_params is None:
            pagination_params = PaginationParams(page=page, per_page=per_page)

        if filters is None:
            filters = {"is_deleted": include_deleted}

        result = await self.project_repository.get_paginated(
            pagination_params=pagination_params, sort_params=sort_params, filters=filters
        )

        # Convert projects to ProjectReadSchema
        project_schemas = [self._convert_project_to_schema(p) for p in result.items]

        # Create pagination response dict
        response_dict = create_pagination_response(project_schemas, result)

        # Convert to ProjectListResponseSchema
        return ProjectListResponseSchema.model_validate(response_dict)

    @handle_service_errors("get_projects_paginated")
    @monitor_service_performance("get_projects_paginated")
    async def get_projects_paginated(
        self,
        pagination_params: PaginationParams,
        sort_params: Optional[SortParams] = None,
        filters: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """Get paginated list of projects with enhanced search and sorting."""
        logger.debug(
            f"Retrieving paginated projects: page={pagination_params.page}, "
            f"per_page={pagination_params.per_page}, sort={sort_params}, filters={filters}"
        )

        search_term = filters.pop("search", None) if filters else None

        result = await self.project_repository.search_paginated(
            search_term=search_term,
            searchable_fields=["name", "description", "project_number"],
            pagination_params=pagination_params,
            sort_params=sort_params,
            additional_filters=filters,
        )

        project_summaries = [ProjectSummarySchema.model_validate(p) for p in result.items]

        return create_pagination_response(project_summaries, result)

    @handle_service_errors("_get_project_by_id_or_code")
    async def _get_project_by_id_or_code(self, project_id: Union[str, int]) -> Project:
        """Helper method to get project by ID or project number."""
        project: Optional[Project] = None

        # Handle integer ID (direct database lookup)
        if isinstance(project_id, int):
            project = await self.project_repository.get_by_id(project_id)
        # Handle string ID (could be numeric ID or project code)
        elif isinstance(project_id, str):
            if project_id.isdigit():
                project = await self.project_repository.get_by_id(int(project_id))

            if project is None:
                project = await self.project_repository.get_by_project_number(project_id)
                if project:
                    logger.debug(f"Project found by project number in helper method: {project_id}")

        if project is None or project.is_deleted:
            raise ProjectNotFoundError(project_id=str(project_id))

        return project

    @handle_service_errors("_validate_project_creation")
    async def _validate_project_creation(self, project_data: ProjectCreateSchema) -> None:
        """Perform business validation for project creation."""
        validation_errors = {}

        if not project_data.name or not project_data.name.strip():
            validation_errors["name"] = "Project name is required and cannot be empty"
        elif len(project_data.name.strip()) < 3:
            validation_errors["name"] = "Project name must be at least 3 characters long"
        elif len(project_data.name.strip()) > 255:
            validation_errors["name"] = "Project name cannot exceed 255 characters"

        if project_data.project_number:
            if not project_data.project_number.strip():
                validation_errors["project_number"] = "Project number cannot be empty or just whitespace"
            elif len(project_data.project_number.strip()) > 100:
                validation_errors["project_number"] = "Project number cannot exceed 100 characters"

            existing_by_number = await self.project_repository.get_by_project_number(
                project_data.project_number.strip()
            )
            if existing_by_number:
                validation_errors["project_number"] = (
                    f"Project number '{project_data.project_number}' is already in use."
                )

        existing_by_name = await self.project_repository.get_by_name(project_data.name.strip())
        if existing_by_name:
            validation_errors["name"] = f"Project name '{project_data.name}' is already in use."

        if project_data.description and len(project_data.description.strip()) > 2000:
            validation_errors["description"] = "Project description cannot exceed 2000 characters"

        if project_data.client and len(project_data.client.strip()) > 255:
            validation_errors["client"] = "Client name cannot exceed 255 characters"

        if project_data.location and len(project_data.location.strip()) > 255:
            validation_errors["location"] = "Location cannot exceed 255 characters"

        if validation_errors:
            raise DataValidationError(details={"validation_errors": validation_errors})

    @handle_service_errors("_validate_project_update")
    async def _validate_project_update(self, existing_project: Project, project_data: ProjectUpdateSchema) -> None:
        """Perform business validation for project updates."""
        validation_errors = {}

        if project_data.name is not None:
            if not project_data.name or not project_data.name.strip():
                validation_errors["name"] = "Project name cannot be empty"
            elif len(project_data.name.strip()) < 3:
                validation_errors["name"] = "Project name must be at least 3 characters"
            elif len(project_data.name.strip()) > 255:
                validation_errors["name"] = "Project name cannot exceed 255 characters"
            else:
                existing_by_name = await self.project_repository.get_by_name(project_data.name.strip())
                if existing_by_name and existing_by_name.id != existing_project.id:
                    validation_errors["name"] = f"Project name '{project_data.name}' is already in use."

        if project_data.description is not None and len(project_data.description.strip()) > 2000:
            validation_errors["description"] = "Project description cannot exceed 2000 characters"

        if project_data.client_name and len(project_data.client_name.strip()) > 255:
            validation_errors["client_name"] = "Client name cannot exceed 255 characters"

        if project_data.start_date and project_data.end_date:
            if project_data.start_date >= project_data.end_date:
                validation_errors["end_date"] = "End date must be after start date"

        if validation_errors:
            raise DataValidationError(details={"validation_errors": validation_errors})

    @handle_service_errors("_validate_database_url")
    async def _validate_database_url(self, database_url: Optional[str]) -> None:
        """Validate and test database URL connectivity if provided."""
        if not database_url:
            return

        if not self.connection_manager:
            logger.warning("No connection manager available for database URL validation")
            return

        # Validate URL format (this is already done in the schema, but double-check here)
        database_url = database_url.strip()
        if not database_url:
            return

        supported_schemes = [
            "postgresql://",
            "postgresql+asyncpg://",
        ]

        if not any(database_url.startswith(scheme) for scheme in supported_schemes):
            raise DataValidationError(
                details={
                    "validation_errors": {
                        "database_url": f"Database URL must use supported schemes: {', '.join(supported_schemes)}"
                    }
                }
            )

        # Test connectivity by attempting to create a session factory
        try:
            session_factory = self.connection_manager._get_local_session_factory(database_url)
            logger.info(f"Database URL validation successful: {database_url}")
        except Exception as e:
            logger.error(f"Database URL validation failed for {database_url}: {str(e)}")
            raise DataValidationError(
                details={"validation_errors": {"database_url": f"Cannot connect to database: {str(e)}"}}
            )

    async def _handle_database_url_in_creation(self, project_data: ProjectCreateSchema) -> None:
        """Handle database_url field processing during project creation."""
        if hasattr(project_data, "database_url") and project_data.database_url:
            await self._validate_database_url(project_data.database_url)
            logger.info(f"Project will use local database: {project_data.database_url}")
        else:
            logger.info("Project will use central database")

    async def _handle_database_url_in_update(
        self, existing_project: Project, project_data: ProjectUpdateSchema
    ) -> None:
        """Handle database_url field processing during project update."""
        if hasattr(project_data, "database_url") and project_data.database_url is not None:
            # Only validate if the URL is actually changing
            if project_data.database_url != existing_project.database_url:
                await self._validate_database_url(project_data.database_url)
                if project_data.database_url:
                    logger.info(f"Project database will be updated to: {project_data.database_url}")
                else:
                    logger.info("Project will switch to central database")
            else:
                logger.debug("Database URL unchanged during update")
