"""Project Phase Service Layer.

This module contains the business logic for Project Phase and Milestone operations including:
- Phase creation, retrieval, updating, and deletion with IEEE/IEC compliance
- Milestone tracking and dependency management
- Project template management and application
- Business validation and workflow enforcement
- Real-time collaboration support
- Progress tracking and reporting
"""

import json
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

from src.config.logging_config import logger
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.enums.project_management_enums import ProjectPhaseType, MilestoneStatus, ProjectTemplateType
from src.core.errors.exceptions import (
    DatabaseError,
    DataValidationError,
    DuplicateEntryError,
    ProjectNotFoundError,
)
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.models.general.project_phase import ProjectPhase, ProjectMilestone, ProjectTemplate
from src.core.models.general.project import Project
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.schemas.base_schemas import PaginationSchema
from src.core.utils import (
    PaginationParams,
    SortParams,
    create_pagination_response,
    sanitize_text,
    slugify,
)
from src.core.utils.datetime_utils import utcnow_naive


if TYPE_CHECKING:
    from src.core.models.general.system_configuration import SystemConfiguration


class ProjectPhaseService:
    """Service class for Project Phase and Milestone business logic."""

    def __init__(
        self,
        project_repository: ProjectRepository,
        connection_manager: Optional[DynamicConnectionManager] = None,
    ):
        """Initialize the ProjectPhaseService.

        Args:
            project_repository: Repository for project data access
            connection_manager: Optional database connection manager
        """
        self.project_repository = project_repository
        self.connection_manager = connection_manager

    @handle_service_errors("project_phase_creation")
    @monitor_service_performance("project_phase_service.create_phase")
    async def create_phase(
        self, project_id: int, phase_data: Dict[str, Any], user_id: Optional[int] = None
    ) -> ProjectPhase:
        """Create a new project phase with validation.

        Args:
            project_id: ID of the project
            phase_data: Phase data dictionary
            user_id: ID of user creating the phase

        Returns:
            Created ProjectPhase instance

        Raises:
            ProjectNotFoundError: If project doesn't exist
            DataValidationError: If phase data is invalid
            DuplicateEntryError: If phase type already exists for project
        """
        logger.info(f"Creating new phase for project {project_id}")

        # Validate project exists
        project = await self.project_repository.get_by_id(project_id)
        if not project:
            raise ProjectNotFoundError(f"Project with ID {project_id} not found")

        # Validate phase data
        await self._validate_phase_data(phase_data, project_id)

        # Check for duplicate phase type
        existing_phases = await self.get_project_phases(project_id)
        phase_type = ProjectPhaseType(phase_data["phase_type"])

        if any(phase.phase_type == phase_type for phase in existing_phases):
            raise DuplicateEntryError(f"Phase type '{phase_type.value}' already exists for project {project_id}")

        # Create phase
        phase = ProjectPhase(
            project_id=project_id,
            name=phase_data.get("name", phase_type.value),
            phase_type=phase_type,
            start_date=phase_data["start_date"],
            end_date=phase_data.get("end_date"),
            progress_percentage=phase_data.get("progress_percentage", 0),
            is_active=phase_data.get("is_active", False),
            prerequisites=json.dumps(phase_data.get("prerequisites", [])),
            deliverables=json.dumps(phase_data.get("deliverables", [])),
            notes=phase_data.get("notes"),
            created_at=utcnow_naive(),
            updated_at=utcnow_naive(),
        )

        saved_phase = await self.project_repository.create(phase)
        logger.info(f"Created phase {saved_phase.phase_id} for project {project_id}")

        return saved_phase

    @handle_service_errors("project_phase_retrieval")
    @monitor_service_performance("project_phase_service.get_project_phases")
    async def get_project_phases(self, project_id: int, active_only: bool = False) -> List[ProjectPhase]:
        """Get all phases for a project.

        Args:
            project_id: ID of the project
            active_only: Whether to return only active phases

        Returns:
            List of project phases
        """
        logger.debug(f"Retrieving phases for project {project_id}")

        filters = {"project_id": project_id}
        if active_only:
            filters["is_active"] = True

        phases = await self.project_repository.get_all(
            model=ProjectPhase, filters=filters, order_by=[ProjectPhase.start_date.asc()]
        )

        logger.debug(f"Retrieved {len(phases)} phases for project {project_id}")
        return phases

    @handle_service_errors("project_phase_update")
    @monitor_service_performance("project_phase_service.update_phase")
    async def update_phase(
        self, phase_id: str, phase_data: Dict[str, Any], user_id: Optional[int] = None
    ) -> ProjectPhase:
        """Update an existing project phase.

        Args:
            phase_id: UUID of the phase to update
            phase_data: Updated phase data
            user_id: ID of user updating the phase

        Returns:
            Updated ProjectPhase instance

        Raises:
            ProjectNotFoundError: If phase doesn't exist
            DataValidationError: If update data is invalid
        """
        logger.info(f"Updating phase {phase_id}")

        # Get existing phase
        phase = await self.project_repository.get_by_field(ProjectPhase, "phase_id", phase_id)
        if not phase:
            raise ProjectNotFoundError(f"Phase with ID {phase_id} not found")

        # Validate update data
        await self._validate_phase_data(phase_data, phase.project_id, is_update=True)

        # Update fields
        for field, value in phase_data.items():
            if hasattr(phase, field):
                if field in ["prerequisites", "deliverables"] and isinstance(value, (list, dict)):
                    setattr(phase, field, json.dumps(value))
                else:
                    setattr(phase, field, value)

        phase.updated_at = utcnow_naive()

        updated_phase = await self.project_repository.update(phase)
        logger.info(f"Updated phase {phase_id}")

        return updated_phase

    @handle_service_errors("project_milestone_creation")
    @monitor_service_performance("project_phase_service.create_milestone")
    async def create_milestone(
        self, phase_id: int, milestone_data: Dict[str, Any], user_id: Optional[int] = None
    ) -> ProjectMilestone:
        """Create a new project milestone.

        Args:
            phase_id: ID of the project phase
            milestone_data: Milestone data dictionary
            user_id: ID of user creating the milestone

        Returns:
            Created ProjectMilestone instance
        """
        logger.info(f"Creating new milestone for phase {phase_id}")

        # Validate phase exists
        phase = await self.project_repository.get_by_id(phase_id)
        if not phase:
            raise ProjectNotFoundError(f"Phase with ID {phase_id} not found")

        # Create milestone
        milestone = ProjectMilestone(
            phase_id=phase_id,
            name=milestone_data["title"],
            title=milestone_data["title"],
            description=milestone_data.get("description"),
            due_date=milestone_data["due_date"],
            status=MilestoneStatus(milestone_data.get("status", MilestoneStatus.NOT_STARTED)),
            assigned_user_id=milestone_data.get("assigned_user_id"),
            dependencies=json.dumps(milestone_data.get("dependencies", [])),
            completion_criteria=json.dumps(milestone_data.get("completion_criteria", [])),
            created_at=utcnow_naive(),
            updated_at=utcnow_naive(),
        )

        saved_milestone = await self.project_repository.create(milestone)
        logger.info(f"Created milestone {saved_milestone.milestone_id}")

        return saved_milestone

    @handle_service_errors("project_template_application")
    @monitor_service_performance("project_phase_service.apply_template")
    async def apply_template(
        self, project_id: int, template_id: str, user_id: Optional[int] = None
    ) -> List[ProjectPhase]:
        """Apply a project template to create phases and milestones.

        Args:
            project_id: ID of the target project
            template_id: UUID of the template to apply
            user_id: ID of user applying the template

        Returns:
            List of created project phases

        Raises:
            ProjectNotFoundError: If project or template doesn't exist
        """
        logger.info(f"Applying template {template_id} to project {project_id}")

        # Validate project exists
        project = await self.project_repository.get_by_id(project_id)
        if not project:
            raise ProjectNotFoundError(f"Project with ID {project_id} not found")

        # Get template
        template = await self.project_repository.get_by_field(ProjectTemplate, "template_id", template_id)
        if not template:
            raise ProjectNotFoundError(f"Template with ID {template_id} not found")

        created_phases = []

        if template.phases_config:
            phases_config = json.loads(template.phases_config)

            for phase_config in phases_config:
                # Calculate start date based on project start or previous phase
                start_date = self._calculate_phase_start_date(project, created_phases, phase_config)

                phase_data = {
                    **phase_config,
                    "start_date": start_date,
                    "end_date": start_date + timedelta(days=phase_config.get("duration_days", 30)),
                }

                try:
                    phase = await self.create_phase(project_id, phase_data, user_id)
                    created_phases.append(phase)

                    # Create milestones if defined
                    if "milestones" in phase_config:
                        for milestone_config in phase_config["milestones"]:
                            milestone_data = {
                                **milestone_config,
                                "due_date": start_date + timedelta(days=milestone_config.get("days_from_start", 15)),
                            }
                            await self.create_milestone(phase.id, milestone_data, user_id)

                except DuplicateEntryError:
                    # Skip if phase type already exists
                    logger.warning(f"Phase type {phase_config['phase_type']} already exists, skipping")
                    continue

        logger.info(f"Applied template {template_id}, created {len(created_phases)} phases")
        return created_phases

    async def _validate_phase_data(self, phase_data: Dict[str, Any], project_id: int, is_update: bool = False) -> None:
        """Validate phase data.

        Args:
            phase_data: Phase data to validate
            project_id: ID of the project
            is_update: Whether this is an update operation

        Raises:
            DataValidationError: If validation fails
        """
        errors = []

        # Required fields for creation
        if not is_update:
            required_fields = ["phase_type", "start_date"]
            errors.extend(
                [
                    {"loc": [field], "msg": f"Field '{field}' is required", "type": "value_error"}
                    for field in required_fields
                    if field not in phase_data
                ]
            )

        # Validate phase type
        if "phase_type" in phase_data:
            try:
                ProjectPhaseType(phase_data["phase_type"])
            except ValueError:
                errors.append(
                    {
                        "loc": ["phase_type"],
                        "msg": f"Invalid phase type: {phase_data['phase_type']}",
                        "type": "value_error",
                    }
                )

        # Validate date logic
        if "start_date" in phase_data and "end_date" in phase_data:
            if phase_data["end_date"] and phase_data["start_date"] >= phase_data["end_date"]:
                errors.append({"loc": ["end_date"], "msg": "End date must be after start date", "type": "value_error"})

        # Validate progress percentage
        if "progress_percentage" in phase_data:
            progress = phase_data["progress_percentage"]
            if not isinstance(progress, int) or progress < 0 or progress > 100:
                errors.append(
                    {
                        "loc": ["progress_percentage"],
                        "msg": "Progress percentage must be between 0 and 100",
                        "type": "value_error",
                    }
                )

        if errors:
            raise DataValidationError(details={"validation_errors": errors})

    def _calculate_phase_start_date(
        self, project: Project, previous_phases: List[ProjectPhase], phase_config: Dict[str, Any]
    ) -> datetime:
        """Calculate the start date for a phase based on project and dependencies.

        Args:
            project: The project instance
            previous_phases: List of already created phases
            phase_config: Configuration for the new phase

        Returns:
            Calculated start date
        """
        # If explicit start date provided, use it
        if "start_date" in phase_config:
            return phase_config["start_date"]

        # Default to project creation date or current date
        base_date = project.created_at or utcnow_naive()

        # If there are prerequisites, start after the latest prerequisite
        if "prerequisites" in phase_config and previous_phases:
            prereq_types = [ProjectPhaseType(pt) for pt in phase_config["prerequisites"]]
            latest_end = None

            for phase in previous_phases:
                if phase.phase_type in prereq_types:
                    phase_end = phase.end_date or (phase.start_date + timedelta(days=30))
                    if latest_end is None or phase_end > latest_end:
                        latest_end = phase_end

            if latest_end:
                base_date = latest_end

        return base_date


class SystemConfigurationService:
    """Service class for System Configuration business logic."""

    def __init__(
        self,
        project_repository: ProjectRepository,
        connection_manager: Optional[DynamicConnectionManager] = None,
    ):
        """Initialize the SystemConfigurationService.

        Args:
            project_repository: Repository for project data access
            connection_manager: Optional database connection manager
        """
        self.project_repository = project_repository
        self.connection_manager = connection_manager

    @handle_service_errors("system_config_retrieval")
    @monitor_service_performance("system_config_service.get_configuration")
    async def get_configuration(
        self, scope: str = "global", project_id: Optional[int] = None, category: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get system configuration with inheritance.

        Args:
            scope: Configuration scope (global, project, user)
            project_id: Optional project ID for project-specific config
            category: Optional category filter

        Returns:
            Configuration dictionary with merged settings
        """
        from src.core.models.general.system_configuration import SystemConfiguration

        logger.debug(f"Retrieving configuration for scope: {scope}")

        # Start with global defaults
        config = await self._get_default_configuration()

        # Get global configurations
        global_configs = await self.project_repository.get_all(
            model=SystemConfiguration, filters={"scope": "global", "is_active": True}
        )

        for cfg in global_configs:
            if not category or cfg.category == category:
                config.update(self._parse_config_values(cfg))

        # Override with project-specific configurations if requested
        if project_id:
            project_configs = await self.project_repository.get_all(
                model=SystemConfiguration, filters={"scope": "project", "project_id": project_id, "is_active": True}
            )

            for cfg in project_configs:
                if not category or cfg.category == category:
                    config.update(self._parse_config_values(cfg))

        logger.debug(f"Retrieved configuration with {len(config)} settings")
        return config

    def _parse_config_values(self, config: "SystemConfiguration") -> Dict[str, Any]:
        """Parse configuration values from a SystemConfiguration instance.

        Args:
            config: SystemConfiguration instance

        Returns:
            Dictionary of parsed configuration values
        """
        from src.core.models.general.system_configuration import SystemConfiguration

        values = {}

        # Direct field values
        if config.voltage_system:
            values["voltage_system"] = config.voltage_system
        if config.frequency_system:
            values["frequency_system"] = config.frequency_system.value
        if config.temperature_unit:
            values["temperature_unit"] = config.temperature_unit.value

        values["calculation_precision"] = config.calculation_precision
        values["rounding_method"] = config.rounding_method

        # JSON field values
        if config.safety_factors:
            values["safety_factors"] = json.loads(config.safety_factors)
        if config.environmental_conditions:
            values["environmental_conditions"] = json.loads(config.environmental_conditions)
        if config.standards_compliance:
            values["standards_compliance"] = json.loads(config.standards_compliance)
        if config.validation_rules:
            values["validation_rules"] = json.loads(config.validation_rules)

        return values

    async def _get_default_configuration(self) -> Dict[str, Any]:
        """Get default system configuration values.

        Returns:
            Dictionary of default configuration values
        """
        return {
            "voltage_system": "3-phase",
            "frequency_system": "50Hz",
            "temperature_unit": "°C",
            "calculation_precision": 2,
            "rounding_method": "round_half_up",
            "safety_factors": {"motor_starting": 1.25, "continuous_load": 1.0, "emergency_load": 1.5},
            "environmental_conditions": {
                "default_ambient_temp": 25.0,
                "default_humidity": 60.0,
                "default_altitude": 0.0,
            },
            "standards_compliance": ["IEC 60364", "IEEE C2", "EN 50110"],
            "validation_rules": {"voltage_drop_max": 5.0, "load_factor_max": 0.8, "temperature_derating": True},
        }
