"""Project Phase and Milestone API endpoints.

This module provides API endpoints for managing project phases, milestones,
and templates supporting IEEE/IEC electrical project lifecycle management.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, Path, Body
from fastapi.responses import JSONResponse

from src.config.logging_config import logger
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ProjectNotFoundError
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.database.connection_manager import get_contextual_db_session, _connection_manager
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.project_phase_schemas import (
    ProjectPhaseCreateSchema,
    ProjectPhaseReadSchema,
    ProjectPhaseUpdateSchema,
    ProjectPhaseListResponseSchema,
    ProjectMilestoneCreateSchema,
    ProjectMilestoneReadSchema,
    ProjectMilestoneUpdateSchema,
    ProjectMilestoneListResponseSchema,
    ProjectTemplateCreateSchema,
    ProjectTemplateReadSchema,
    ProjectTemplateUpdateSchema,
    ProjectTemplateListResponseSchema,
    ProjectNavigationSchema,
)
from src.core.security.enhanced_dependencies import require_authenticated_user
from src.core.services.general.project_phase_service import ProjectPhaseService


def get_project_phase_service(
    session: Any = Depends(get_contextual_db_session),
) -> ProjectPhaseService:
    """Get project phase service dependency."""
    return ProjectPhaseService(ProjectRepository(session), _connection_manager)


router = APIRouter(
    prefix="/projects",
    tags=["project-phases"],
    responses={
        404: {"model": ErrorResponseSchema, "description": "Not found"},
        422: {"model": ErrorResponseSchema, "description": "Validation error"},
        500: {"model": ErrorResponseSchema, "description": "Internal server error"},
    },
)


# Project Phase endpoints
@router.post(
    "/{project_id}/phases",
    response_model=ProjectPhaseReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new project phase",
    description="Create a new phase for a project with IEEE/IEC compliance validation",
)
@handle_api_errors("create_project_phase")
@monitor_api_performance("project_phase_api.create_phase")
async def create_project_phase(
    project_id: int = Path(..., description="Project ID", ge=1),
    phase_data: ProjectPhaseCreateSchema = Body(..., description="Phase data"),
    current_user=Depends(require_authenticated_user),
    service: ProjectPhaseService = Depends(get_project_phase_service),
) -> ProjectPhaseReadSchema:
    """Create a new project phase."""
    logger.info(f"Creating phase for project {project_id} by user {current_user.get('user_id')}")

    try:
        # Override project_id from path
        phase_dict = phase_data.model_dump()
        phase_dict["project_id"] = project_id

        created_phase = await service.create_phase(
            project_id=project_id, phase_data=phase_dict, user_id=current_user.get("user_id")
        )

        return ProjectPhaseReadSchema.model_validate(created_phase)

    except ProjectNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Project not found: {e}")
    except Exception as e:
        logger.error(f"Failed to create project phase: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create project phase")


@router.get(
    "/{project_id}/phases",
    response_model=List[ProjectPhaseReadSchema],
    summary="Get project phases",
    description="Retrieve all phases for a project with optional filtering",
)
@handle_api_errors("get_project_phases")
@monitor_api_performance("project_phase_api.get_phases")
async def get_project_phases(
    project_id: int = Path(..., description="Project ID", ge=1),
    active_only: bool = Query(False, description="Return only active phases"),
    current_user=Depends(require_authenticated_user),
    service: ProjectPhaseService = Depends(get_project_phase_service),
) -> List[ProjectPhaseReadSchema]:
    """Get all phases for a project."""
    logger.debug(f"Retrieving phases for project {project_id}")

    try:
        phases = await service.get_project_phases(project_id, active_only=active_only)
        return [ProjectPhaseReadSchema.model_validate(phase) for phase in phases]

    except Exception as e:
        logger.error(f"Failed to retrieve project phases: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve project phases"
        )


@router.put(
    "/{project_id}/phases/{phase_id}",
    response_model=ProjectPhaseReadSchema,
    summary="Update a project phase",
    description="Update an existing project phase",
)
@handle_api_errors("update_project_phase")
@monitor_api_performance("project_phase_api.update_phase")
async def update_project_phase(
    project_id: int = Path(..., description="Project ID", ge=1),
    phase_id: str = Path(..., description="Phase UUID"),
    phase_data: ProjectPhaseUpdateSchema = Body(..., description="Updated phase data"),
    current_user=Depends(require_authenticated_user),
    service: ProjectPhaseService = Depends(get_project_phase_service),
) -> ProjectPhaseReadSchema:
    """Update a project phase."""
    logger.info(f"Updating phase {phase_id} for project {project_id}")

    try:
        updated_phase = await service.update_phase(
            phase_id=phase_id, phase_data=phase_data.model_dump(exclude_unset=True), user_id=current_user.get("user_id")
        )

        return ProjectPhaseReadSchema.model_validate(updated_phase)

    except ProjectNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Phase not found: {e}")
    except Exception as e:
        logger.error(f"Failed to update project phase: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to update project phase")


# Project Milestone endpoints
@router.post(
    "/{project_id}/phases/{phase_id}/milestones",
    response_model=ProjectMilestoneReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new milestone",
    description="Create a new milestone within a project phase",
)
@handle_api_errors("create_project_milestone")
@monitor_api_performance("project_phase_api.create_milestone")
async def create_project_milestone(
    project_id: int = Path(..., description="Project ID", ge=1),
    phase_id: int = Path(..., description="Phase ID", ge=1),
    milestone_data: ProjectMilestoneCreateSchema = Body(..., description="Milestone data"),
    current_user=Depends(require_authenticated_user),
    service: ProjectPhaseService = Depends(get_project_phase_service),
) -> ProjectMilestoneReadSchema:
    """Create a new project milestone."""
    logger.info(f"Creating milestone for phase {phase_id} in project {project_id}")

    try:
        # Override phase_id from path
        milestone_dict = milestone_data.model_dump()
        milestone_dict["phase_id"] = phase_id

        created_milestone = await service.create_milestone(
            phase_id=phase_id, milestone_data=milestone_dict, user_id=current_user.get("user_id")
        )

        return ProjectMilestoneReadSchema.model_validate(created_milestone)

    except ProjectNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Phase not found: {e}")
    except Exception as e:
        logger.error(f"Failed to create milestone: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to create milestone")


# Project Template endpoints
@router.post(
    "/{project_id}/apply-template",
    response_model=List[ProjectPhaseReadSchema],
    summary="Apply project template",
    description="Apply a project template to create phases and milestones",
)
@handle_api_errors("apply_project_template")
@monitor_api_performance("project_phase_api.apply_template")
async def apply_project_template(
    project_id: int = Path(..., description="Project ID", ge=1),
    template_id: str = Body(..., embed=True, description="Template UUID"),
    current_user=Depends(require_authenticated_user),
    service: ProjectPhaseService = Depends(get_project_phase_service),
) -> List[ProjectPhaseReadSchema]:
    """Apply a project template to create phases and milestones."""
    logger.info(f"Applying template {template_id} to project {project_id}")

    try:
        created_phases = await service.apply_template(
            project_id=project_id, template_id=template_id, user_id=current_user.get("user_id")
        )

        return [ProjectPhaseReadSchema.model_validate(phase) for phase in created_phases]

    except ProjectNotFoundError as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Project or template not found: {e}")
    except Exception as e:
        logger.error(f"Failed to apply template: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to apply template")


# Project Navigation endpoint (for the frontend organism)
@router.get(
    "/{project_id}/navigation",
    response_model=ProjectNavigationSchema,
    summary="Get project navigation data",
    description="Get complete project navigation data for the ProjectNavigation organism",
)
@handle_api_errors("get_project_navigation")
@monitor_api_performance("project_phase_api.get_navigation")
async def get_project_navigation(
    project_id: int = Path(..., description="Project ID", ge=1),
    current_user=Depends(require_authenticated_user),
    service: ProjectPhaseService = Depends(get_project_phase_service),
) -> ProjectNavigationSchema:
    """Get complete project navigation data."""
    logger.debug(f"Retrieving navigation data for project {project_id}")

    try:
        # Get project phases
        phases = await service.get_project_phases(project_id)

        # Find active phase
        active_phase = next((phase for phase in phases if phase.is_active), None)

        # Get project basic info (simplified for now)
        project_info = {
            "id": project_id,
            "name": f"Project {project_id}",  # This would come from project service
            "status": "active",
            "progress": 0,  # Calculate from phases
        }

        # Calculate overall progress
        if phases:
            total_progress = sum(phase.progress_percentage for phase in phases)
            project_info["progress"] = total_progress // len(phases)

        # Get upcoming milestones (placeholder - would need milestone service)
        upcoming_milestones = []

        # Recent activity (placeholder)
        recent_activity = []

        # Collaboration status (placeholder)
        collaboration_status = {"active_users": 0, "recent_changes": 0, "sync_status": "synced"}

        return ProjectNavigationSchema(
            project=project_info,
            phases=[
                {
                    "id": phase.id,
                    "phase_id": phase.phase_id,
                    "name": phase.name,
                    "phase_type": phase.phase_type,
                    "progress_percentage": phase.progress_percentage,
                    "is_active": phase.is_active,
                    "start_date": phase.start_date,
                    "end_date": phase.end_date,
                }
                for phase in phases
            ],
            active_phase={
                "id": active_phase.id,
                "phase_id": active_phase.phase_id,
                "name": active_phase.name,
                "phase_type": active_phase.phase_type,
                "progress_percentage": active_phase.progress_percentage,
                "is_active": active_phase.is_active,
                "start_date": active_phase.start_date,
                "end_date": active_phase.end_date,
            }
            if active_phase
            else None,
            upcoming_milestones=upcoming_milestones,
            recent_activity=recent_activity,
            collaboration_status=collaboration_status,
        )

    except Exception as e:
        logger.error(f"Failed to retrieve project navigation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to retrieve project navigation data"
        )
