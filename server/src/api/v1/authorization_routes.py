"""Authorization API Routes.

This module provides RBAC (Role-Based Access Control) endpoints including
role management, permission management, and user role assignments.
"""

from typing import Dict, List

from fastapi import APIRouter, Depends, status

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.permission_schemas import (
    PermissionCreateSchema,
    PermissionReadSchema,
)
from src.core.schemas.general.user_role_schemas import (
    RoleCreateSchema,
    RoleReadSchema,
    RoleUpdateSchema,
    UserRoleAssignmentCreateSchema,
    UserRoleAssignmentReadSchema,
)
from src.core.security.enhanced_dependencies import (
    require_admin_user,
    require_authenticated_user,
)
from src.core.services.dependencies import get_authorization_service
from src.core.services.general.authorization_service import AuthorizationService


# Create router with proper tags and metadata
router = APIRouter(
    prefix="/auth",
    tags=["Authorization"],
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "model": ErrorResponseSchema,
            "description": "Authentication required",
        },
        status.HTTP_403_FORBIDDEN: {
            "model": ErrorResponseSchema,
            "description": "Insufficient permissions",
        },
    },
)


# Role Management Endpoints
@router.post(
    "/roles",
    response_model=RoleReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create Role",
    description="Create a new role with specified permissions. Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("create_role")
@monitor_api_performance("create_role")
async def create_role(
    role_data: RoleCreateSchema,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
    current_user=Depends(require_authenticated_user),
) -> RoleReadSchema:
    """Create a new role."""
    logger.info(f"Creating role: {role_data.name}")
    return await authorization_service.create_role(role_data, current_user["id"])


@router.get(
    "/roles",
    response_model=List[RoleReadSchema],
    summary="Get All Roles",
    description="Retrieve all active roles. Requires authentication.",
    dependencies=[Depends(require_authenticated_user)],
)
@handle_api_errors("get_all_roles")
@monitor_api_performance("get_all_roles")
async def get_all_roles(
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> List[RoleReadSchema]:
    """Get all active roles."""
    logger.debug("Retrieving all roles")
    return await authorization_service.get_all_roles()


@router.get(
    "/roles/{role_id}",
    response_model=RoleReadSchema,
    summary="Get Role by ID",
    description="Retrieve a specific role by ID with permissions. Requires authentication.",
    dependencies=[Depends(require_authenticated_user)],
)
@handle_api_errors("get_role_by_id")
@monitor_api_performance("get_role_by_id")
async def get_role_by_id(
    role_id: int,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> RoleReadSchema:
    """Get role by ID."""
    logger.debug(f"Retrieving role by ID: {role_id}")
    return await authorization_service.get_role_by_id(role_id)


@router.patch(
    "/roles/{role_id}",
    response_model=RoleReadSchema,
    summary="Update Role",
    description="Update role permissions and metadata. Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("update_role")
@monitor_api_performance("update_role")
async def update_role(
    role_id: int,
    role_data: RoleUpdateSchema,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
    current_user=Depends(require_authenticated_user),
) -> RoleReadSchema:
    """Update a role's permissions."""
    logger.info(f"Updating role ID: {role_id}")
    return await authorization_service.update_role(role_id, role_data, current_user["id"])


@router.put(
    "/roles/{role_id}",
    response_model=RoleReadSchema,
    summary="Update Role (Full)",
    description="Update role permissions and metadata (full update). Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("update_role_full")
@monitor_api_performance("update_role_full")
async def update_role_full(
    role_id: int,
    role_data: RoleUpdateSchema,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
    current_user=Depends(require_authenticated_user),
) -> RoleReadSchema:
    """Update a role's permissions (full update)."""
    logger.info(f"Updating role ID (full): {role_id}")
    return await authorization_service.update_role(role_id, role_data, current_user["id"])


@router.delete(
    "/roles/{role_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Role",
    description="Delete (soft delete) a role. Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("delete_role")
@monitor_api_performance("delete_role")
async def delete_role(
    role_id: int,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> None:
    """Delete a role (soft delete)."""
    logger.info(f"Deleting role ID: {role_id}")
    await authorization_service.delete_role(role_id)


# Permission Management Endpoints
@router.post(
    "/permissions",
    response_model=PermissionReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create Permission",
    description="Create a new permission. Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("create_permission")
@monitor_api_performance("create_permission")
async def create_permission(
    permission_data: PermissionCreateSchema,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
    current_user=Depends(require_authenticated_user),
) -> PermissionReadSchema:
    """Create a new permission."""
    logger.info(f"Creating permission: {permission_data.name}")
    return await authorization_service.create_permission(permission_data, current_user["id"])


@router.get(
    "/permissions",
    response_model=List[PermissionReadSchema],
    summary="Get All Permissions",
    description="Retrieve all active permissions. Requires authentication.",
    dependencies=[Depends(require_authenticated_user)],
)
@handle_api_errors("get_all_permissions")
@monitor_api_performance("get_all_permissions")
async def get_all_permissions(
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> List[PermissionReadSchema]:
    """Get all active permissions."""
    logger.debug("Retrieving all permissions")
    return await authorization_service.get_all_permissions()


@router.get(
    "/permissions/{permission_id}",
    response_model=PermissionReadSchema,
    summary="Get Permission by ID",
    description="Retrieve a specific permission by ID. Requires authentication.",
    dependencies=[Depends(require_authenticated_user)],
)
@handle_api_errors("get_permission_by_id")
@monitor_api_performance("get_permission_by_id")
async def get_permission_by_id(
    permission_id: int,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> PermissionReadSchema:
    """Get permission by ID."""
    logger.debug(f"Retrieving permission by ID: {permission_id}")
    permission = await authorization_service.get_permission_by_id(permission_id)
    return permission


# Role Permission Assignment Endpoints
@router.get(
    "/roles/{role_id}/permissions",
    response_model=List[str],
    summary="Get Role Permissions",
    description="Get all permissions for a role. Requires authentication.",
    dependencies=[Depends(require_authenticated_user)],
)
@handle_api_errors("get_role_permissions")
@monitor_api_performance("get_role_permissions")
async def get_role_permissions(
    role_id: int,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> List[str]:
    """Get permissions for a role."""
    logger.debug(f"Retrieving permissions for role {role_id}")
    permissions = await authorization_service.get_role_permissions(role_id)
    return permissions


@router.post(
    "/roles/{role_id}/permissions",
    response_model=RoleReadSchema,
    summary="Assign Permissions to Role",
    description="Assign multiple permissions to a role. Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("assign_permissions_to_role")
@monitor_api_performance("assign_permissions_to_role")
async def assign_permissions_to_role_post(
    role_id: int,
    assignment_data: Dict[str, List[int]],
    authorization_service: AuthorizationService = Depends(get_authorization_service),
    current_user=Depends(require_authenticated_user),
) -> RoleReadSchema:
    """Assign permissions to a role (POST method)."""
    permission_ids = assignment_data.get("permission_ids", [])
    logger.info(f"Assigning permissions {permission_ids} to role {role_id}")
    return await authorization_service.assign_permissions_to_role(role_id, permission_ids, current_user["id"])


@router.patch(
    "/roles/{role_id}/permissions",
    response_model=RoleReadSchema,
    summary="Assign Permissions to Role",
    description="Assign multiple permissions to a role. Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("assign_permissions_to_role")
@monitor_api_performance("assign_permissions_to_role")
async def assign_permissions_to_role(
    role_id: int,
    permission_ids: List[int],
    authorization_service: AuthorizationService = Depends(get_authorization_service),
    current_user=Depends(require_authenticated_user),
) -> RoleReadSchema:
    """Assign permissions to a role."""
    logger.info(f"Assigning permissions {permission_ids} to role {role_id}")
    return await authorization_service.assign_permissions_to_role(role_id, permission_ids, current_user["id"])


# User Role Assignment Endpoints
@router.get(
    "/users/{user_id}/roles",
    response_model=List[RoleReadSchema],
    summary="Get User Roles",
    description="Get all roles assigned to a user. Requires authentication.",
    dependencies=[Depends(require_authenticated_user)],
)
@handle_api_errors("get_user_roles")
@monitor_api_performance("get_user_roles")
async def get_user_roles(
    user_id: int,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> List[RoleReadSchema]:
    """Get all roles for a user."""
    logger.debug(f"Retrieving roles for user {user_id}")
    roles = await authorization_service.get_user_roles(user_id)
    return roles


@router.post(
    "/users/{user_id}/roles",
    response_model=UserRoleAssignmentReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Assign Role to User",
    description="Assign a role to a user. Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("assign_role_to_user")
@monitor_api_performance("assign_role_to_user")
async def assign_role_to_user(
    user_id: int,
    role_assignment_data: UserRoleAssignmentCreateSchema,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
    current_user=Depends(require_authenticated_user),
) -> UserRoleAssignmentReadSchema:
    """Assign a role to a user."""
    logger.info(f"Assigning role {role_assignment_data.role_id} to user {user_id}")
    return await authorization_service.assign_role_to_user(
        user_id=user_id,
        role_id=role_assignment_data.role_id,
        name=role_assignment_data.name,
        assigned_by_user_id=current_user["id"],
        expires_at=role_assignment_data.expires_at,
        assignment_context=role_assignment_data.assignment_context,
    )


@router.delete(
    "/users/{user_id}/roles/{role_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Remove Role from User",
    description="Remove a role from a user. Requires admin privileges.",
    dependencies=[Depends(require_admin_user)],
)
@handle_api_errors("remove_role_from_user")
@monitor_api_performance("remove_role_from_user")
async def remove_role_from_user(
    user_id: int,
    role_id: int,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> None:
    """Remove a role from a user."""
    logger.info(f"Removing role {role_id} from user {user_id}")
    await authorization_service.remove_role_from_user(user_id, role_id)


# Permission Check Endpoints
@router.get(
    "/users/{user_id}/permissions/{resource}/{action}",
    response_model=Dict[str, bool],
    summary="Check User Permission",
    description="Check if a user has permission to perform an action on a resource. Requires authentication.",
    dependencies=[Depends(require_authenticated_user)],
)
@handle_api_errors("check_user_permission")
@monitor_api_performance("check_user_permission")
async def check_user_permission(
    user_id: int,
    resource: str,
    action: str,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> Dict[str, bool]:
    """Check if a user has permission to perform an action on a resource."""
    logger.debug(f"Checking permission for user {user_id}: {resource}.{action}")
    has_permission = await authorization_service.check_user_permission(user_id, resource, action)
    return {"has_permission": has_permission}


@router.get(
    "/users/{user_id}/permissions",
    response_model=List[str],
    summary="Get User Permissions",
    description="Get all permissions for a user. Requires authentication.",
    dependencies=[Depends(require_authenticated_user)],
)
@handle_api_errors("get_user_permissions")
@monitor_api_performance("get_user_permissions")
async def get_user_permissions(
    user_id: int,
    authorization_service: AuthorizationService = Depends(get_authorization_service),
) -> List[str]:
    """Get all permissions for a user."""
    logger.debug(f"Retrieving all permissions for user {user_id}")
    permissions = await authorization_service.get_user_permissions(user_id)
    return list(permissions)
