"""Cross-entity validation API routes for advanced dependency checking.

This module provides REST API endpoints for cross-entity validation operations,
including project-component compatibility, dependency analysis, and impact assessment.
"""

from typing import Any, Dict, List, Optional
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from pydantic import BaseModel, Field

from src.config.logging_config import logger
from src.core.security.enhanced_dependencies import get_current_user
from src.core.validation.cross_entity_validator import (
    CrossEntityDependencyValidator,
    DependencyValidationResult,
    ValidationSeverity,
)

# Initialize validator
validator = CrossEntityDependencyValidator()


# Request/Response models
class ProjectComponentValidationRequest(BaseModel):
    """Request model for project-component validation."""

    project_data: Dict[str, Any] = Field(..., description="Project configuration data")
    components: List[Dict[str, Any]] = Field(..., description="List of component configurations")
    validation_rules: Optional[List[str]] = Field(None, description="Specific rules to validate")
    include_warnings: bool = Field(True, description="Include warnings in results")


class ComponentCompatibilityRequest(BaseModel):
    """Request model for component compatibility validation."""

    components: List[Dict[str, Any]] = Field(..., description="List of components to check compatibility")
    compatibility_type: str = Field("electrical", description="Type of compatibility to check")
    strict_mode: bool = Field(False, description="Use strict compatibility checking")


class DependencyImpactRequest(BaseModel):
    """Request model for dependency impact analysis."""

    changed_entity: Dict[str, Any] = Field(..., description="Entity being changed")
    affected_entities: List[Dict[str, Any]] = Field(..., description="Entities potentially affected")
    analysis_depth: int = Field(2, ge=1, le=5, description="Depth of dependency analysis")


class ValidationResponse(BaseModel):
    """Response model for validation results."""

    is_valid: bool
    results: List[Dict[str, Any]]
    summary: Dict[str, int]
    timestamp: str
    metadata: Dict[str, Any]


class CacheStatsResponse(BaseModel):
    """Response model for cache statistics."""

    cache_size: int
    graph_nodes: int
    graph_edges: int
    cache_keys: List[str]


# Create router
router = APIRouter(prefix="/projects/{project_id}/cross-validation", tags=["cross-entity-validation"])


@router.post("/project-components", response_model=ValidationResponse)
async def validate_project_components(
    project_id: int,
    request: ProjectComponentValidationRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
):
    """Validate project-component compatibility and dependencies."""
    try:
        logger.info(f"Validating project components for user {current_user.id}")

        # Perform validation
        results = await validator.validate_project_components(
            request.project_data, request.components, request.validation_rules
        )

        # Convert results to dict format
        formatted_results = []
        for result in results:
            if not request.include_warnings and result.severity == ValidationSeverity.WARNING:
                continue

            formatted_results.append(
                {
                    "is_valid": result.is_valid,
                    "severity": result.severity.value,
                    "message": result.message,
                    "affected_entities": result.affected_entities,
                    "suggested_actions": result.suggested_actions,
                    "metadata": result.metadata,
                }
            )

        # Generate summary
        summary = {
            "total_checks": len(formatted_results),
            "errors": len([r for r in formatted_results if r["severity"] == "error"]),
            "warnings": len([r for r in formatted_results if r["severity"] == "warning"]),
            "info": len([r for r in formatted_results if r["severity"] == "info"]),
            "suggestions": len([r for r in formatted_results if r["severity"] == "suggestion"]),
        }

        # Background task for analytics
        background_tasks.add_task(
            _log_validation_analytics,
            "project_components",
            current_user.id,
            len(request.components),
            summary,
        )

        return ValidationResponse(
            is_valid=all(r["is_valid"] for r in formatted_results),
            results=formatted_results,
            summary=summary,
            timestamp=datetime.utcnow().isoformat(),
            metadata={
                "project_id": project_id,
                "component_count": len(request.components),
                "user_id": str(current_user.id),
            },
        )

    except Exception as e:
        logger.error(f"Error validating project components: {e}")
        raise HTTPException(status_code=500, detail=f"Validation error: {str(e)}")


@router.post("/component-compatibility", response_model=ValidationResponse)
async def validate_component_compatibility(
    request: ComponentCompatibilityRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
):
    """Validate compatibility between components."""
    try:
        logger.info(f"Checking component compatibility for user {current_user.id}")

        if len(request.components) < 2:
            raise HTTPException(
                status_code=400,
                detail="At least 2 components required for compatibility checking",
            )

        # Perform validation
        results = await validator.validate_component_compatibility(request.components, request.compatibility_type)

        # Convert results to dict format
        formatted_results = [
            {
                "is_valid": result.is_valid,
                "severity": result.severity.value,
                "message": result.message,
                "affected_entities": result.affected_entities,
                "suggested_actions": result.suggested_actions,
                "metadata": result.metadata,
            }
            for result in results
        ]

        # Generate summary
        summary = {
            "total_checks": len(formatted_results),
            "errors": len([r for r in formatted_results if r["severity"] == "error"]),
            "warnings": len([r for r in formatted_results if r["severity"] == "warning"]),
            "info": len([r for r in formatted_results if r["severity"] == "info"]),
            "suggestions": len([r for r in formatted_results if r["severity"] == "suggestion"]),
        }

        # Background task for analytics
        background_tasks.add_task(
            _log_validation_analytics,
            "component_compatibility",
            current_user.id,
            len(request.components),
            summary,
        )

        return ValidationResponse(
            is_valid=all(r["is_valid"] for r in formatted_results),
            results=formatted_results,
            summary=summary,
            timestamp=datetime.utcnow().isoformat(),
            metadata={
                "component_count": len(request.components),
                "compatibility_type": request.compatibility_type,
                "user_id": str(current_user.id),
            },
        )

    except Exception as e:
        logger.error(f"Error validating component compatibility: {e}")
        raise HTTPException(status_code=500, detail=f"Validation error: {str(e)}")


@router.post("/dependency-impact", response_model=ValidationResponse)
async def analyze_dependency_impact(
    request: DependencyImpactRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
):
    """Analyze the impact of changes on dependent entities."""
    try:
        logger.info(f"Analyzing dependency impact for user {current_user.id}")

        # Perform impact analysis
        results = await validator.validate_dependency_impact(request.changed_entity, request.affected_entities)

        # Convert results to dict format
        formatted_results = [
            {
                "is_valid": result.is_valid,
                "severity": result.severity.value,
                "message": result.message,
                "affected_entities": result.affected_entities,
                "suggested_actions": result.suggested_actions,
                "metadata": result.metadata,
            }
            for result in results
        ]

        # Generate summary
        summary = {
            "total_checks": len(formatted_results),
            "errors": len([r for r in formatted_results if r["severity"] == "error"]),
            "warnings": len([r for r in formatted_results if r["severity"] == "warning"]),
            "info": len([r for r in formatted_results if r["severity"] == "info"]),
            "suggestions": len([r for r in formatted_results if r["severity"] == "suggestion"]),
        }

        # Background task for analytics
        background_tasks.add_task(
            _log_validation_analytics,
            "dependency_impact",
            current_user.id,
            len(request.affected_entities),
            summary,
        )

        return ValidationResponse(
            is_valid=all(r["is_valid"] for r in formatted_results),
            results=formatted_results,
            summary=summary,
            timestamp=datetime.utcnow().isoformat(),
            metadata={
                "changed_entity": request.changed_entity.get("id", "unknown"),
                "affected_count": len(request.affected_entities),
                "analysis_depth": request.analysis_depth,
                "user_id": str(current_user.id),
            },
        )

    except Exception as e:
        logger.error(f"Error analyzing dependency impact: {e}")
        raise HTTPException(status_code=500, detail=f"Impact analysis error: {str(e)}")


@router.get("/cache-stats", response_model=CacheStatsResponse)
async def get_cache_stats(current_user=Depends(get_current_user)):
    """Get cross-validation cache statistics."""
    try:
        stats = validator.get_cache_stats()
        return CacheStatsResponse(**stats)
    except Exception as e:
        logger.error(f"Error getting cache stats: {e}")
        raise HTTPException(status_code=500, detail=f"Cache stats error: {str(e)}")


@router.post("/cache/clear")
async def clear_cache(background_tasks: BackgroundTasks, current_user=Depends(get_current_user)):
    """Clear cross-validation cache."""
    try:
        validator.clear_cache()

        background_tasks.add_task(
            _log_validation_analytics,
            "cache_clear",
            current_user.id,
            0,
            {"action": "cache_cleared"},
        )

        return {
            "status": "success",
            "message": "Cross-validation cache cleared",
            "timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail=f"Cache clear error: {str(e)}")


@router.get("/rules")
async def get_validation_rules(current_user=Depends(get_current_user)):
    """Get available validation rules."""
    return {
        "project_component_rules": [
            {
                "rule_id": "voltage_compatibility",
                "description": "Component voltage rating must be compatible with project system voltage",
                "severity": "error",
            },
            {
                "rule_id": "current_capacity",
                "description": "Component current rating must meet project load requirements",
                "severity": "warning",
            },
            {
                "rule_id": "environmental_rating",
                "description": "Component environmental rating must match project location conditions",
                "severity": "error",
            },
            {
                "rule_id": "standards_compliance",
                "description": "Component must comply with project specified standards",
                "severity": "error",
            },
        ],
        "dependency_chain_rules": [
            {
                "rule_id": "cascading_impact",
                "description": "Changes to upstream components must be validated for downstream impact",
                "severity": "warning",
            },
            {
                "rule_id": "circular_dependency",
                "description": "Circular dependencies between components must be detected and resolved",
                "severity": "error",
            },
            {
                "rule_id": "resource_availability",
                "description": "Required resources must be available for all components in the chain",
                "severity": "error",
            },
        ],
    }


async def _log_validation_analytics(validation_type: str, user_id: str, entity_count: int, summary: Dict[str, Any]):
    """Log validation analytics for monitoring."""
    logger.info(
        f"Cross-validation analytics: type={validation_type}, "
        f"user={user_id}, entities={entity_count}, summary={summary}"
    )
