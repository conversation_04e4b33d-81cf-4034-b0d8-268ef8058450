"""WebSocket validation routes for real-time electrical parameter validation.

This module provides WebSocket endpoints for real-time validation of electrical
parameters, enabling immediate feedback during component configuration and
project design.
"""

import asyncio
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field

from src.config.logging_config import logger
from src.core.database.dependencies import get_project_db_session
from src.core.services.dependencies import get_project_service
from src.core.services.general.project_service import ProjectService
from src.core.validation.advanced_validators import (
    AdvancedElectricalValidator,
    EnhancedTemperatureValidator,
)

# Initialize validators
advanced_validator = AdvancedElectricalValidator()
temperature_validator = EnhancedTemperatureValidator()


def get_project_specific_project_service(
    session: Any = Depends(get_project_db_session),
) -> "ProjectService":
    """Get project service dependency with project-specific session."""
    from src.core.repositories.general.project_repository import ProjectRepository
    from src.core.services.general.project_service import ProjectService

    return ProjectService(ProjectRepository(session))


# WebSocket connection manager
class ConnectionManager:
    """Manages WebSocket connections for real-time validation."""

    def __init__(self) -> None:
        self.active_connections: List[WebSocket] = []
        self.connection_metadata: Dict[WebSocket, Dict[str, Any]] = {}

    async def connect(
        self,
        websocket: WebSocket,
        client_id: str,
        project_id: Optional[str] = None,
        project_service: Optional[ProjectService] = None,
    ):
        """Connect a new WebSocket client."""
        if project_id and project_service:
            project = await project_service.get_project_details(project_id)
            if project and project.is_offline:
                await websocket.close(
                    code=4000,
                    reason="Real-time collaboration is disabled for offline projects.",
                )
                logger.info(f"Rejected WebSocket connection for offline project {project_id}")
                return

        await websocket.accept()
        self.active_connections.append(websocket)
        self.connection_metadata[websocket] = {
            "client_id": client_id,
            "project_id": project_id,
            "connected_at": datetime.utcnow().isoformat(),
        }
        logger.info(f"WebSocket client {client_id} connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        """Disconnect a WebSocket client."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            metadata = self.connection_metadata.pop(websocket, {})
            logger.info(
                f"WebSocket client {metadata.get('client_id', 'unknown')} disconnected. Total connections: {len(self.active_connections)}"
            )

    async def send_personal_message(self, message: Dict[str, Any], websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_json(message)
            return True
        except Exception as e:
            logger.error(f"Error sending message to WebSocket: {e}")
            return False

    async def broadcast(self, message: Dict[str, Any], exclude: Optional[WebSocket] = None):
        """Broadcast a message to all connected clients except excluded one."""
        disconnected = []
        for connection in self.active_connections:
            if connection != exclude:
                try:
                    await connection.send_json(message)
                except Exception as e:
                    logger.error(f"Error broadcasting to WebSocket: {e}")
                    disconnected.append(connection)

        # Clean up disconnected connections
        for conn in disconnected:
            self.disconnect(conn)


# Global connection manager
manager = ConnectionManager()


# Validation request models
class ElectricalValidationRequest(BaseModel):
    """Request model for electrical parameter validation."""

    parameters: Dict[str, Any] = Field(..., description="Electrical parameters to validate")
    application_type: str = Field(default="industrial", description="Application context")
    region: str = Field(default="north_america", description="Regional standards")
    validation_type: str = Field(default="comprehensive", description="Type of validation to perform")


class TemperatureValidationRequest(BaseModel):
    """Request model for temperature validation."""

    min_temperature: float = Field(..., description="Minimum temperature in Celsius")
    max_temperature: float = Field(..., description="Maximum temperature in Celsius")
    application_type: str = Field(default="industrial", description="Application context")
    location_type: str = Field(default="ambient", description="Location type for limits")


class ValidationResponse(BaseModel):
    """Response model for validation results."""

    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]
    normalized_values: Dict[str, float]
    timestamp: str
    validation_type: str


# Validation service
class RealTimeValidationService:
    """Service for handling real-time validation operations."""

    async def validate_electrical_parameters(self, request: ElectricalValidationRequest) -> ValidationResponse:
        """Validate electrical parameters in real-time."""
        try:
            result = advanced_validator.validate_electrical_parameters(
                request.parameters, request.application_type, request.region
            )

            return ValidationResponse(
                is_valid=result.is_valid,
                errors=result.errors,
                warnings=result.warnings,
                suggestions=result.suggestions,
                normalized_values=result.normalized_values,
                timestamp=datetime.utcnow().isoformat(),
                validation_type="electrical",
            )

        except Exception as e:
            logger.error(f"Error validating electrical parameters: {e}")
            return ValidationResponse(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                suggestions=[],
                normalized_values={},
                timestamp=datetime.utcnow().isoformat(),
                validation_type="electrical",
            )

    async def validate_temperature_range(self, request: TemperatureValidationRequest) -> ValidationResponse:
        """Validate temperature range in real-time."""
        try:
            result = temperature_validator.validate_temperature_range(
                request.min_temperature,
                request.max_temperature,
                request.application_type,
                request.location_type,
            )

            return ValidationResponse(
                is_valid=result.is_valid,
                errors=result.errors,
                warnings=result.warnings,
                suggestions=result.suggestions,
                normalized_values=result.normalized_values,
                timestamp=datetime.utcnow().isoformat(),
                validation_type="temperature",
            )

        except Exception as e:
            logger.error(f"Error validating temperature range: {e}")
            return ValidationResponse(
                is_valid=False,
                errors=[f"Validation error: {str(e)}"],
                warnings=[],
                suggestions=[],
                normalized_values={},
                timestamp=datetime.utcnow().isoformat(),
                validation_type="temperature",
            )


# Initialize validation service
validation_service = RealTimeValidationService()


# WebSocket routes
router = APIRouter(prefix="/ws/validation", tags=["real-time-validation"])


@router.websocket("/electrical")
async def websocket_electrical_validation(
    websocket: WebSocket,
    project_id: int,
    client_id: str,
    project_service: ProjectService = Depends(get_project_specific_project_service),
):
    """WebSocket endpoint for real-time electrical parameter validation."""
    await manager.connect(websocket, client_id, project_id, project_service)

    try:
        while True:
            # Receive validation request
            data = await websocket.receive_json()

            # Parse and validate request
            try:
                request = ElectricalValidationRequest(**data)
            except Exception as e:
                await manager.send_personal_message(
                    {
                        "type": "error",
                        "message": f"Invalid request format: {str(e)}",
                        "timestamp": datetime.utcnow().isoformat(),
                    },
                    websocket,
                )
                continue

            # Perform validation
            response = await validation_service.validate_electrical_parameters(request)

            # Send response
            await manager.send_personal_message(
                {
                    "type": "electrical_validation_result",
                    "data": response.dict(),
                    "client_id": client_id,
                    "project_id": project_id,
                },
                websocket,
            )

            # Log validation activity
            logger.debug(f"Electrical validation performed for client {client_id}: {response.is_valid}")

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"Error in electrical validation WebSocket: {e}")
        await manager.send_personal_message(
            {
                "type": "error",
                "message": f"WebSocket error: {str(e)}",
                "timestamp": datetime.utcnow().isoformat(),
            },
            websocket,
        )


@router.websocket("/temperature")
async def websocket_temperature_validation(
    websocket: WebSocket,
    project_id: int,
    client_id: str,
    project_service: ProjectService = Depends(get_project_specific_project_service),
):
    """WebSocket endpoint for real-time temperature validation."""
    await manager.connect(websocket, client_id, project_id, project_service)

    try:
        while True:
            # Receive validation request
            data = await websocket.receive_json()

            # Parse and validate request
            try:
                request = TemperatureValidationRequest(**data)
            except Exception as e:
                await manager.send_personal_message(
                    {
                        "type": "error",
                        "message": f"Invalid request format: {str(e)}",
                        "timestamp": datetime.utcnow().isoformat(),
                    },
                    websocket,
                )
                continue

            # Perform validation
            response = await validation_service.validate_temperature_range(request)

            # Send response
            await manager.send_personal_message(
                {
                    "type": "temperature_validation_result",
                    "data": response.dict(),
                    "client_id": client_id,
                    "project_id": project_id,
                },
                websocket,
            )

            # Log validation activity
            logger.debug(f"Temperature validation performed for client {client_id}: {response.is_valid}")

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"Error in temperature validation WebSocket: {e}")
        await manager.send_personal_message(
            {
                "type": "error",
                "message": f"WebSocket error: {str(e)}",
                "timestamp": datetime.utcnow().isoformat(),
            },
            websocket,
        )


@router.websocket("/live")
async def websocket_live_validation(
    websocket: WebSocket,
    project_id: int,
    client_id: str,
    project_service: ProjectService = Depends(get_project_specific_project_service),
):
    """WebSocket endpoint for live streaming validation with debouncing."""
    await manager.connect(websocket, client_id, project_id, project_service)

    try:
        last_validation_time: Optional[datetime] = None
        validation_cache: Dict[str, Dict[str, Any]] = {}

        while True:
            # Receive validation request
            data = await websocket.receive_json()

            # Check for debouncing
            current_time = datetime.utcnow()
            if last_validation_time and (current_time - last_validation_time).total_seconds() < 0.1:
                continue  # Skip if too soon

            # Cache key for deduplication
            # Use a robust cache key
            cache_key = json.dumps(data, sort_keys=True)
            if cache_key in validation_cache:
                cached_result = validation_cache[cache_key]
                if (current_time - cached_result["timestamp"]).total_seconds() < 1:
                    await manager.send_personal_message(cached_result["response"], websocket)
                    continue

            # Perform validation based on type
            validation_type = data.get("validation_type", "electrical")
            response: Optional[ValidationResponse] = None

            if validation_type == "electrical":
                try:
                    electrical_request = ElectricalValidationRequest(**data)
                    response = await validation_service.validate_electrical_parameters(electrical_request)
                except Exception as e:
                    response = ValidationResponse(
                        is_valid=False,
                        errors=[str(e)],
                        warnings=[],
                        suggestions=[],
                        normalized_values={},
                        timestamp=current_time.isoformat(),
                        validation_type="error",
                    )

            elif validation_type == "temperature":
                try:
                    temperature_request = TemperatureValidationRequest(**data)
                    response = await validation_service.validate_temperature_range(temperature_request)
                except Exception as e:
                    response = ValidationResponse(
                        is_valid=False,
                        errors=[str(e)],
                        warnings=[],
                        suggestions=[],
                        normalized_values={},
                        timestamp=current_time.isoformat(),
                        validation_type="error",
                    )

            else:
                response = ValidationResponse(
                    is_valid=False,
                    errors=[f"Unknown validation type: {validation_type}"],
                    warnings=[],
                    suggestions=[],
                    normalized_values={},
                    timestamp=current_time.isoformat(),
                    validation_type="error",
                )

            # Construct the full response message
            full_response_message = {
                "type": "live_validation_result",
                "data": response.dict(),
                "client_id": client_id,
                "project_id": project_id,
            }

            # Cache the full response message
            validation_cache[cache_key] = {
                "response": full_response_message,
                "timestamp": current_time,
            }

            # Send response
            await manager.send_personal_message(full_response_message, websocket)

            last_validation_time = current_time

    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"Error in live validation WebSocket: {e}")
        await manager.send_personal_message(
            {
                "type": "error",
                "message": f"WebSocket error: {str(e)}",
                "timestamp": datetime.utcnow().isoformat(),
            },
            websocket,
        )


# Health check endpoint
@router.get("/health")
async def validation_websocket_health():
    """Health check for validation WebSocket service."""
    return {
        "status": "healthy",
        "active_connections": len(manager.active_connections),
        "timestamp": datetime.utcnow().isoformat(),
    }


# Connection info endpoint
@router.get("/connections")
async def get_active_connections():
    """Get information about active WebSocket connections."""
    connections = [
        {
            "client_id": metadata.get("client_id"),
            "project_id": metadata.get("project_id"),
            "connected_at": metadata.get("connected_at"),
        }
        for metadata in manager.connection_metadata.values()
    ]

    return {
        "total_connections": len(manager.active_connections),
        "connections": connections,
        "timestamp": datetime.utcnow().isoformat(),
    }
