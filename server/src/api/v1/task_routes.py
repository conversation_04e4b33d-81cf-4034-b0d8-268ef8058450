"""Task Management API Routes.

FastAPI routes for task management within projects, providing CRUD operations
and additional task-specific functionality like assignments and statistics.
"""

from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.task_schemas import (
    TaskAssignmentRequestSchema,
    TaskCreateSchema,
    TaskListResponseSchema,
    TaskReadSchema,
    TaskStatisticsSchema,
    TaskStatusUpdateSchema,
    TaskSummarySchema,
    TaskUnassignmentRequestSchema,
    TaskUpdateSchema,
)
from src.core.security.enhanced_dependencies import require_authenticated_user
from src.core.services.dependencies import get_task_manager_service
from src.core.services.general.task_manager_service import TaskManagerService
from src.core.utils.pagination_utils import PaginationParams, SortParams


def get_task_manager_service_dependency(
    session: Any = Depends(get_task_manager_service),
) -> TaskManagerService:
    """Get task manager service dependency."""
    return session


router = APIRouter(
    prefix="/projects/{project_id}/tasks",
    tags=["Tasks"],
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Insufficient permissions",
            "model": ErrorResponseSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Task or project not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "Internal server error",
            "model": ErrorResponseSchema,
        },
    },
)


# ============================================================================
# TASK CRUD ENDPOINTS
# ============================================================================


@router.post(
    "/",
    response_model=TaskReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create Task",
    description="Create a new task within a project with optional user assignments",
    operation_id="createTask",
)
@handle_api_errors("create_task")
@monitor_api_performance("create_task")
async def create_task(
    project_id: int,
    task_data: TaskCreateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    task_service: TaskManagerService = Depends(get_task_manager_service_dependency),
) -> TaskReadSchema:
    """Create a new task within a project."""
    logger.info(f"Creating task '{task_data.title}' in project {project_id}")

    task = await task_service.create_task_with_assignments(
        project_id=project_id,
        title=task_data.title,
        description=task_data.description,
        due_date=task_data.due_date,
        priority=task_data.priority.value if hasattr(task_data.priority, "value") else task_data.priority,
        status=task_data.status.value if hasattr(task_data.status, "value") else task_data.status,
        assigned_user_ids=task_data.assigned_user_ids,
        created_by_user_id=current_user.get("id"),
    )

    return TaskReadSchema.model_validate(task)


@router.get(
    "/",
    response_model=List[TaskReadSchema],
    summary="List Tasks",
    description="Retrieve all tasks for a specific project with optional filtering",
    operation_id="listTasks",
)
@handle_api_errors("list_tasks")
@monitor_api_performance("list_tasks")
async def list_tasks(
    project_id: int,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    status: str = Query(None, description="Filter by task status"),
    priority: str = Query(None, description="Filter by task priority"),
    assigned_user_id: int = Query(None, ge=1, description="Filter by assigned user ID"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    task_service: TaskManagerService = Depends(get_task_manager_service_dependency),
) -> List[TaskReadSchema]:
    """Retrieve all tasks for a specific project."""
    logger.debug(f"Listing tasks for project {project_id}")

    # Build filters
    filters = {}
    if status:
        filters["status"] = status
    if priority:
        filters["priority"] = priority
    if assigned_user_id:
        filters["assigned_user_id"] = assigned_user_id

    # Build pagination and sorting
    pagination_params = PaginationParams(page=page, per_page=size)
    sort_params = SortParams(sort_by=sort_by, sort_order=sort_order)

    tasks = await task_service.get_tasks_for_project(
        project_id=project_id,
        pagination_params=pagination_params,
        sort_params=sort_params,
        filters=filters,
    )

    return [TaskReadSchema.model_validate(task) for task in tasks]


@router.get(
    "/statistics",
    response_model=TaskStatisticsSchema,
    summary="Get Task Statistics",
    description="Retrieve task statistics for a project",
    operation_id="getTaskStatistics",
)
@handle_api_errors("get_task_statistics")
@monitor_api_performance("get_task_statistics")
async def get_task_statistics(
    project_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    task_service: TaskManagerService = Depends(get_task_manager_service_dependency),
) -> TaskStatisticsSchema:
    """Retrieve task statistics for a project."""
    logger.debug(f"Retrieving task statistics for project {project_id}")

    statistics = await task_service.get_task_statistics(project_id)

    return TaskStatisticsSchema.model_validate(statistics)


@router.get(
    "/{task_id}",
    response_model=TaskReadSchema,
    summary="Get Task",
    description="Retrieve a specific task by its ID",
    operation_id="getTask",
)
@handle_api_errors("get_task")
@monitor_api_performance("get_task")
async def get_task(
    project_id: int,
    task_id: str,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    task_service: TaskManagerService = Depends(get_task_manager_service_dependency),
) -> TaskReadSchema:
    """Retrieve a specific task by its ID."""
    logger.debug(f"Retrieving task {task_id} from project {project_id}")

    task = await task_service.get_task_by_id(task_id)

    # Verify task belongs to the specified project
    if task.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found in project {project_id}",
        )

    return TaskReadSchema.model_validate(task)


@router.put(
    "/{task_id}",
    response_model=TaskReadSchema,
    summary="Update Task",
    description="Update an existing task",
    operation_id="updateTask",
)
@handle_api_errors("update_task")
@monitor_api_performance("update_task")
async def update_task(
    project_id: int,
    task_id: str,
    task_data: TaskUpdateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    task_service: TaskManagerService = Depends(get_task_manager_service_dependency),
) -> TaskReadSchema:
    """Update an existing task."""
    logger.info(f"Updating task {task_id} in project {project_id}")

    # First verify task exists and belongs to project
    existing_task = await task_service.get_task_by_id(task_id)
    if existing_task.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found in project {project_id}",
        )

    task = await task_service.update_task(
        task_id=task_id,
        title=task_data.title,
        description=task_data.description,
        due_date=task_data.due_date,
        priority=task_data.priority.value
        if task_data.priority and hasattr(task_data.priority, "value")
        else task_data.priority,
        status=task_data.status.value if task_data.status and hasattr(task_data.status, "value") else task_data.status,
        updated_by_user_id=current_user.get("id"),
    )

    return TaskReadSchema.model_validate(task)


@router.delete(
    "/{task_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Task",
    description="Soft delete a task",
    operation_id="deleteTask",
)
@handle_api_errors("delete_task")
@monitor_api_performance("delete_task")
async def delete_task(
    project_id: int,
    task_id: str,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    task_service: TaskManagerService = Depends(get_task_manager_service_dependency),
) -> None:
    """Soft delete a task."""
    logger.info(f"Deleting task {task_id} from project {project_id}")

    # First verify task exists and belongs to project
    existing_task = await task_service.get_task_by_id(task_id)
    if existing_task.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found in project {project_id}",
        )

    await task_service.delete_task(task_id=task_id, deleted_by_user_id=current_user.get("id"))


# ============================================================================
# TASK ASSIGNMENT ENDPOINTS
# ============================================================================


@router.post(
    "/{task_id}/assignments",
    response_model=TaskReadSchema,
    summary="Assign Users to Task",
    description="Assign one or more users to a task",
    operation_id="assignUsersToTask",
)
@handle_api_errors("assign_users_to_task")
@monitor_api_performance("assign_users_to_task")
async def assign_users_to_task(
    project_id: int,
    task_id: str,
    assignment_data: TaskAssignmentRequestSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    task_service: TaskManagerService = Depends(get_task_manager_service_dependency),
) -> TaskReadSchema:
    """Assign users to a task."""
    logger.info(f"Assigning {len(assignment_data.user_ids)} users to task {task_id}")

    # First verify task exists and belongs to project
    existing_task = await task_service.get_task_by_id(task_id)
    if existing_task.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found in project {project_id}",
        )

    task = await task_service.assign_users_to_task(
        task_id=task_id,
        user_ids=assignment_data.user_ids,
        assigned_by_user_id=current_user.get("id"),
    )

    return TaskReadSchema.model_validate(task)


@router.delete(
    "/{task_id}/assignments/{user_id}",
    response_model=TaskReadSchema,
    summary="Unassign User from Task",
    description="Remove a user assignment from a task",
    operation_id="unassignUserFromTask",
)
@handle_api_errors("unassign_user_from_task")
@monitor_api_performance("unassign_user_from_task")
async def unassign_user_from_task(
    project_id: int,
    task_id: str,
    user_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    task_service: TaskManagerService = Depends(get_task_manager_service_dependency),
) -> TaskReadSchema:
    """Remove a user assignment from a task."""
    logger.info(f"Unassigning user {user_id} from task {task_id}")

    # First verify task exists and belongs to project
    existing_task = await task_service.get_task_by_id(task_id)
    if existing_task.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task {task_id} not found in project {project_id}",
        )

    task = await task_service.unassign_user_from_task(
        task_id=task_id,
        user_id=user_id,
        unassigned_by_user_id=current_user.get("id"),
    )

    return TaskReadSchema.model_validate(task)
