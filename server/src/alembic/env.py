"""Alembic Database Migration Environment Configuration.

This module configures the Alembic environment for database migrations in the
Ultimate Electrical Designer backend application. It handles database connection
setup, migration context configuration, and migration execution.
"""

import logging
from logging.config import fileConfig
from typing import Any, Optional

from alembic import context

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set up logger for this module
logger = logging.getLogger(__name__)

# add your model's MetaData object here
# for 'autogenerate' support
# from myapp import mymodel
# target_metadata = mymodel.Base.metadata

import os
import sys
from dotenv import load_dotenv

# Add the project root directory to the sys.path to import project modules
# This env.py is in src/alembic, so we need to go up two directories to reach the project root
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", ".."))
sys.path.insert(0, project_root)

# Load environment variables from .env file in the project root (server/)
# This ensures that the DATABASE_URL is available for migrations
load_dotenv(dotenv_path=os.path.join(project_root, ".env"))


# Import all models to register them with SQLAlchemy metadata
from src.core.models import *

# Import your Base object and settings
from src.config.settings import settings
from src.core.models.base import Base
from src.core.database.engine import _get_db_type_from_url

target_metadata = Base.metadata


def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = settings.DATABASE_URL
    logger.info(f"Running offline migrations with URL: {_get_db_type_from_url(url)}")

    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()


def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    from src.core.database.engine import create_engine

    try:
        connectable = create_engine(echo=settings.DB_ECHO)
        logger.info(f"Running online migrations with: {_get_db_type_from_url(connectable.url)}")

        with connectable.connect() as connection:
            context.configure(
                connection=connection,
                target_metadata=target_metadata,
                compare_type=True,  # Set compare_type to True as a default for type comparison
            )

            with context.begin_transaction():
                context.run_migrations()

    except Exception as e:
        logger.error(f"Failed to run online migrations: {e}")
        raise


if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
