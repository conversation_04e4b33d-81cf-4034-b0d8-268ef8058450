"""Add is_offline to Project model

Revision ID: eef4bdd715bb
Revises: 783f143cf619
Create Date: 2025-07-21 11:15:24.675458

"""

from __future__ import annotations

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "eef4bdd715bb"
down_revision: str = "783f143cf619"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "Project",
        sa.Column("is_offline", sa.<PERSON>(), nullable=False, server_default=sa.false()),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("Project", "is_offline")
    # ### end Alembic commands ###
