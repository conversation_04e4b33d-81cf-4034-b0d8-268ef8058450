"""Synchronize database schema with models

Revision ID: 272130620d46
Revises: f8e2b6c3d5a1
Create Date: 2025-07-24 22:13:10.790555

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "272130620d46"
down_revision = "f8e2b6c3d5a1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_synchronizationconflict_detected_at"), table_name="SynchronizationConflict")
    op.drop_index(op.f("ix_synchronizationconflict_entity_id"), table_name="SynchronizationConflict")
    op.drop_index(op.f("ix_synchronizationconflict_entity_type"), table_name="SynchronizationConflict")
    op.drop_index(op.f("ix_synchronizationconflict_is_resolved"), table_name="SynchronizationConflict")
    op.drop_index(op.f("ix_synchronizationconflict_severity"), table_name="SynchronizationConflict")
    op.drop_index(op.f("ix_synchronizationconflict_sync_log_id"), table_name="SynchronizationConflict")
    op.drop_table("SynchronizationConflict")
    op.drop_index(op.f("ix_synchronizationlog_operation_type"), table_name="SynchronizationLog")
    op.drop_index(op.f("ix_synchronizationlog_project_id"), table_name="SynchronizationLog")
    op.drop_index(op.f("ix_synchronizationlog_started_at"), table_name="SynchronizationLog")
    op.drop_index(op.f("ix_synchronizationlog_status"), table_name="SynchronizationLog")
    op.drop_index(op.f("ix_synchronizationlog_user_id"), table_name="SynchronizationLog")
    op.drop_table("SynchronizationLog")
    op.create_index("ix_user_email_lower", "User", [sa.literal_column("lower(email)")], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_user_email_lower", table_name="User")
    op.create_table(
        "SynchronizationLog",
        sa.Column(
            "id",
            sa.INTEGER(),
            server_default=sa.text("nextval('\"SynchronizationLog_id_seq\"'::regclass)"),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("project_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("session_id", sa.VARCHAR(length=255), autoincrement=False, nullable=True),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("operation_type", sa.VARCHAR(length=50), autoincrement=False, nullable=False),
        sa.Column("sync_direction", sa.VARCHAR(length=50), autoincrement=False, nullable=False),
        sa.Column("status", sa.VARCHAR(length=50), autoincrement=False, nullable=False),
        sa.Column("started_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("completed_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column("duration_ms", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("source_database_url", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("target_database_url", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("records_processed", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("records_created", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("records_updated", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("records_deleted", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("conflicts_detected", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("conflicts_resolved", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("error_message", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("error_details", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("sync_metadata", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("sync_config", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column(
            "throughput_records_per_second", sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True
        ),
        sa.Column("memory_usage_peak_mb", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("network_bytes_transferred", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("is_automatic", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column("is_retry", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column("is_critical", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column("retry_count", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("max_retries", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("next_retry_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column("created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(["project_id"], ["Project.id"], name="SynchronizationLog_project_id_fkey"),
        sa.ForeignKeyConstraint(["user_id"], ["User.id"], name="SynchronizationLog_user_id_fkey"),
        sa.PrimaryKeyConstraint("id", name="SynchronizationLog_pkey"),
        postgresql_ignore_search_path=False,
    )
    op.create_index(op.f("ix_synchronizationlog_user_id"), "SynchronizationLog", ["user_id"], unique=False)
    op.create_index(op.f("ix_synchronizationlog_status"), "SynchronizationLog", ["status"], unique=False)
    op.create_index(op.f("ix_synchronizationlog_started_at"), "SynchronizationLog", ["started_at"], unique=False)
    op.create_index(op.f("ix_synchronizationlog_project_id"), "SynchronizationLog", ["project_id"], unique=False)
    op.create_index(
        op.f("ix_synchronizationlog_operation_type"), "SynchronizationLog", ["operation_type"], unique=False
    )
    op.create_table(
        "SynchronizationConflict",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("sync_log_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("entity_type", sa.VARCHAR(length=100), autoincrement=False, nullable=False),
        sa.Column("entity_id", sa.VARCHAR(length=255), autoincrement=False, nullable=False),
        sa.Column("table_name", sa.VARCHAR(length=100), autoincrement=False, nullable=False),
        sa.Column("conflict_type", sa.VARCHAR(length=100), autoincrement=False, nullable=False),
        sa.Column("field_name", sa.VARCHAR(length=100), autoincrement=False, nullable=True),
        sa.Column("local_value", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("central_value", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("local_timestamp", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column("central_timestamp", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column("is_resolved", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column("resolution_strategy", sa.VARCHAR(length=100), autoincrement=False, nullable=True),
        sa.Column("resolved_value", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("resolved_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
        sa.Column("resolved_by_user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column("severity", sa.VARCHAR(length=50), autoincrement=False, nullable=False),
        sa.Column("requires_manual_intervention", sa.BOOLEAN(), autoincrement=False, nullable=False),
        sa.Column("conflict_metadata", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("resolution_notes", sa.TEXT(), autoincrement=False, nullable=True),
        sa.Column("detected_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.Column("updated_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
        sa.ForeignKeyConstraint(
            ["resolved_by_user_id"], ["User.id"], name=op.f("SynchronizationConflict_resolved_by_user_id_fkey")
        ),
        sa.ForeignKeyConstraint(
            ["sync_log_id"], ["SynchronizationLog.id"], name=op.f("SynchronizationConflict_sync_log_id_fkey")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("SynchronizationConflict_pkey")),
    )
    op.create_index(
        op.f("ix_synchronizationconflict_sync_log_id"), "SynchronizationConflict", ["sync_log_id"], unique=False
    )
    op.create_index(op.f("ix_synchronizationconflict_severity"), "SynchronizationConflict", ["severity"], unique=False)
    op.create_index(
        op.f("ix_synchronizationconflict_is_resolved"), "SynchronizationConflict", ["is_resolved"], unique=False
    )
    op.create_index(
        op.f("ix_synchronizationconflict_entity_type"), "SynchronizationConflict", ["entity_type"], unique=False
    )
    op.create_index(
        op.f("ix_synchronizationconflict_entity_id"), "SynchronizationConflict", ["entity_id"], unique=False
    )
    op.create_index(
        op.f("ix_synchronizationconflict_detected_at"), "SynchronizationConflict", ["detected_at"], unique=False
    )
    # ### end Alembic commands ###
