"""Add database_url to Project model

Revision ID: 30a40908dea4
Revises: eef4bdd715bb
Create Date: 2025-07-22 00:21:01.870555

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "30a40908dea4"
down_revision = "eef4bdd715bb"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("Project", schema=None) as batch_op:
        batch_op.add_column(sa.Column("database_url", sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("Project", schema=None) as batch_op:
        batch_op.drop_column("database_url")

    # ### end Alembic commands ###
