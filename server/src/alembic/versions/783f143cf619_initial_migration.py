"""Initial migration

Revision ID: 783f143cf619
Revises:
Create Date: 2025-07-19 14:56:25.274608

"""

from __future__ import annotations

from typing import Optional, Sequence

from alembic import op
import sqlalchemy as sa
from src.core.models.base import EnumType
from src.core.utils.json_validation import FlexibleJSON


# revision identifiers, used by Alembic.
revision: str = "783f143cf619"
down_revision: Optional[str] = None
branch_labels: Optional[Sequence[str]] = None
depends_on: Optional[Sequence[str]] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("CREATE EXTENSION IF NOT EXISTS pg_trgm")
    op.create_table(
        "User",
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("password_hash", sa.String(), nullable=True),
        sa.Column("is_active", sa.<PERSON>(), nullable=False),
        sa.Column("is_superuser", sa.<PERSON>(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("email", name="uq_user_email"),
        sa.UniqueConstraint("name", name="uq_user_name"),
    )
    op.create_table(
        "ActivityLog",
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("session_id", sa.String(length=255), nullable=True),
        sa.Column("action_type", sa.String(length=100), nullable=False),
        sa.Column("action_description", sa.Text(), nullable=False),
        sa.Column("target_type", sa.String(length=100), nullable=True),
        sa.Column("target_id", sa.Integer(), nullable=True),
        sa.Column("target_name", sa.String(length=255), nullable=True),
        sa.Column("request_method", sa.String(length=10), nullable=True),
        sa.Column("request_path", sa.String(length=500), nullable=True),
        sa.Column("request_ip", sa.String(length=45), nullable=True),
        sa.Column("user_agent", sa.String(length=500), nullable=True),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("severity", EnumType(), nullable=False),
        sa.Column("action_metadata", FlexibleJSON(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("execution_time_ms", sa.Integer(), nullable=True),
        sa.Column("category", sa.String(length=100), nullable=True),
        sa.Column("tags", sa.String(length=500), nullable=True),
        sa.Column("is_security_related", sa.Boolean(), nullable=False),
        sa.Column("is_data_change", sa.Boolean(), nullable=False),
        sa.Column("is_system_event", sa.Boolean(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "ComponentCategory",
        sa.Column(
            "description",
            sa.Text(),
            nullable=True,
            comment="Detailed category description",
        ),
        sa.Column(
            "parent_category_id",
            sa.Integer(),
            nullable=True,
            comment="Parent category ID for hierarchical organization",
        ),
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether category is active in the system",
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_category_id"],
            ["ComponentCategory.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "name",
            "parent_category_id",
            "is_deleted",
            name="uq_component_category_name_parent",
        ),
    )
    with op.batch_alter_table("ComponentCategory", schema=None) as batch_op:
        batch_op.create_index("idx_component_category_name_active", ["name", "is_active"], unique=False)
        batch_op.create_index(
            "idx_component_category_parent_active",
            ["parent_category_id", "is_active"],
            unique=False,
        )
        batch_op.create_index(batch_op.f("ix_ComponentCategory_is_active"), ["is_active"], unique=False)
        batch_op.create_index(
            batch_op.f("ix_ComponentCategory_parent_category_id"),
            ["parent_category_id"],
            unique=False,
        )

    op.create_table(
        "Project",
        sa.Column("project_number", sa.String(), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("client", sa.String(), nullable=True),
        sa.Column("location", sa.String(), nullable=True),
        sa.Column("language", sa.String(), nullable=True),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("currency", sa.String(), nullable=True),
        sa.Column("wind_speed_ms", sa.Float(), nullable=True),
        sa.Column("installation_environment", EnumType(), nullable=True),
        sa.Column("available_voltages_json", FlexibleJSON(), nullable=True),
        sa.Column("default_voltage", sa.String(), nullable=True),
        sa.Column("default_frequency", sa.String(), nullable=True),
        sa.Column("default_temperature_unit", sa.String(), nullable=True),
        sa.Column("default_min_ambient_temp", sa.Float(), nullable=True),
        sa.Column("default_max_ambient_temp", sa.Float(), nullable=True),
        sa.Column("default_desired_maintenance_temp", sa.Float(), nullable=True),
        sa.Column("default_safety_margin_percent", sa.Float(), nullable=False),
        sa.Column("preferred_power_cable_manufacturers_json", FlexibleJSON(), nullable=True),
        sa.Column("preferred_control_cable_manufacturers_json", FlexibleJSON(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", name="uq_project_name"),
        sa.UniqueConstraint("project_number", name="uq_project_number"),
    )
    op.create_table(
        "UserPreference",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("ui_theme", sa.String(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id"),
    )
    op.create_table(
        "UserRole",
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("is_system_role", sa.Boolean(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("permissions", sa.Text(), nullable=True),
        sa.Column("parent_role_id", sa.Integer(), nullable=True),
        sa.Column("priority", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["parent_role_id"],
            ["UserRole.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", name="uq_user_role_name"),
    )
    op.create_table(
        "AuditTrail",
        sa.Column("activity_log_id", sa.Integer(), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("changed_at", sa.DateTime(), nullable=False),
        sa.Column("table_name", sa.String(length=100), nullable=False),
        sa.Column("record_id", sa.Integer(), nullable=False),
        sa.Column("operation", sa.String(length=20), nullable=False),
        sa.Column("field_name", sa.String(length=100), nullable=True),
        sa.Column("old_value", sa.Text(), nullable=True),
        sa.Column("new_value", sa.Text(), nullable=True),
        sa.Column("change_reason", sa.String(length=255), nullable=True),
        sa.Column("change_context", FlexibleJSON(), nullable=True),
        sa.Column("is_sensitive", sa.Boolean(), nullable=False),
        sa.Column("is_system_change", sa.Boolean(), nullable=False),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["activity_log_id"],
            ["ActivityLog.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "ComponentType",
        sa.Column(
            "description",
            sa.Text(),
            nullable=True,
            comment="Detailed component type description",
        ),
        sa.Column(
            "category_id",
            sa.Integer(),
            nullable=False,
            comment="Component category ID for organization",
        ),
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether component type is active in the system",
        ),
        sa.Column(
            "specifications_template",
            FlexibleJSON(),
            nullable=True,
            comment="JSON template for component specifications",
        ),
        sa.Column(
            "metadata_json",
            FlexibleJSON(),
            nullable=True,
            comment="Additional metadata for component type",
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["category_id"],
            ["ComponentCategory.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "category_id", "is_deleted", name="uq_component_type_name_category"),
    )
    with op.batch_alter_table("ComponentType", schema=None) as batch_op:
        batch_op.create_index(
            "idx_component_type_category_active",
            ["category_id", "is_active"],
            unique=False,
        )
        batch_op.create_index("idx_component_type_name_active", ["name", "is_active"], unique=False)
        batch_op.create_index("idx_component_type_name_category", ["name", "category_id"], unique=False)
        batch_op.create_index(batch_op.f("ix_ComponentType_category_id"), ["category_id"], unique=False)
        batch_op.create_index(batch_op.f("ix_ComponentType_is_active"), ["is_active"], unique=False)

    op.create_table(
        "ProjectMember",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("role_id", sa.Integer(), nullable=False),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("assigned_at", sa.DateTime(), nullable=False),
        sa.Column("expires_at", sa.DateTime(), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["UserRole.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "project_id", "role_id", name="uq_project_user_role"),
    )
    op.create_table(
        "UserRoleAssignment",
        sa.Column("user_id", sa.Integer(), nullable=False),
        sa.Column("role_id", sa.Integer(), nullable=False),
        sa.Column("assigned_by_user_id", sa.Integer(), nullable=True),
        sa.Column("assigned_at", sa.DateTime(), nullable=False),
        sa.Column("expires_at", sa.DateTime(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=False),
        sa.Column("assignment_context", sa.String(length=255), nullable=True),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["assigned_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["UserRole.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_id", "role_id", name="uq_user_role_assignment"),
    )
    op.create_table(
        "Component",
        sa.Column(
            "manufacturer",
            sa.String(length=100),
            nullable=False,
            comment="Component manufacturer name",
        ),
        sa.Column(
            "model_number",
            sa.String(length=100),
            nullable=False,
            comment="Manufacturer's model/part number",
        ),
        sa.Column(
            "description",
            sa.Text(),
            nullable=True,
            comment="Detailed component description",
        ),
        sa.Column(
            "component_type_id",
            sa.Integer(),
            nullable=False,
            comment="Component type ID (relational approach)",
        ),
        sa.Column(
            "category_id",
            sa.Integer(),
            nullable=False,
            comment="Component category ID (relational approach)",
        ),
        sa.Column(
            "specifications",
            FlexibleJSON(),
            nullable=True,
            comment="Electrical specifications in JSON format",
        ),
        sa.Column(
            "unit_price",
            sa.Numeric(precision=10, scale=2),
            nullable=True,
            comment="Component unit price",
        ),
        sa.Column(
            "currency",
            sa.String(length=3),
            nullable=False,
            comment="Price currency code (ISO 4217)",
        ),
        sa.Column(
            "supplier",
            sa.String(length=100),
            nullable=True,
            comment="Primary supplier name",
        ),
        sa.Column(
            "part_number",
            sa.String(length=100),
            nullable=True,
            comment="Supplier part number",
        ),
        sa.Column(
            "weight_kg",
            sa.Numeric(precision=8, scale=3),
            nullable=True,
            comment="Component weight in kilograms",
        ),
        sa.Column(
            "dimensions_json",
            FlexibleJSON(),
            nullable=True,
            comment="Physical dimensions (L x W x H) in JSON format",
        ),
        sa.Column(
            "is_active",
            sa.Boolean(),
            nullable=False,
            comment="Whether component is active in catalog",
        ),
        sa.Column(
            "is_preferred",
            sa.Boolean(),
            nullable=False,
            comment="Whether component is marked as preferred",
        ),
        sa.Column(
            "stock_status",
            sa.String(length=20),
            nullable=False,
            comment="Current stock availability status",
        ),
        sa.Column(
            "version",
            sa.String(length=20),
            nullable=False,
            comment="Component data version for change tracking",
        ),
        sa.Column(
            "metadata_json",
            FlexibleJSON(),
            nullable=True,
            comment="Additional metadata for component",
        ),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["category_id"],
            ["ComponentCategory.id"],
        ),
        sa.ForeignKeyConstraint(
            ["component_type_id"],
            ["ComponentType.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "manufacturer",
            "model_number",
            "is_deleted",
            name="uq_component_manufacturer_model",
        ),
    )
    with op.batch_alter_table("Component", schema=None) as batch_op:
        batch_op.create_index(
            "idx_component_active_preferred",
            ["is_active", "is_preferred"],
            unique=False,
        )
        batch_op.create_index(
            "idx_component_dimensions_gin",
            ["dimensions_json"],
            unique=False,
            postgresql_using="gin",
            postgresql_ops={"dimensions_json": "gin_trgm_ops"},
        )
        batch_op.create_index(
            "idx_component_manufacturer_supplier",
            ["manufacturer", "supplier"],
            unique=False,
        )
        batch_op.create_index(
            "idx_component_metadata_gin",
            ["metadata_json"],
            unique=False,
            postgresql_using="gin",
            postgresql_ops={"metadata_json": "gin_trgm_ops"},
        )
        batch_op.create_index(
            "idx_component_search",
            ["manufacturer", "model_number", "name"],
            unique=False,
        )
        batch_op.create_index(
            "idx_component_specifications_gin",
            ["specifications"],
            unique=False,
            postgresql_using="gin",
            postgresql_ops={"specifications": "gin_trgm_ops"},
        )
        batch_op.create_index("idx_component_stock_active", ["stock_status", "is_active"], unique=False)
        batch_op.create_index(
            "idx_component_type_category",
            ["component_type_id", "category_id"],
            unique=False,
        )
        batch_op.create_index(batch_op.f("ix_Component_category_id"), ["category_id"], unique=False)
        batch_op.create_index(
            batch_op.f("ix_Component_component_type_id"),
            ["component_type_id"],
            unique=False,
        )
        batch_op.create_index(batch_op.f("ix_Component_is_active"), ["is_active"], unique=False)
        batch_op.create_index(batch_op.f("ix_Component_is_preferred"), ["is_preferred"], unique=False)
        batch_op.create_index(batch_op.f("ix_Component_manufacturer"), ["manufacturer"], unique=False)
        batch_op.create_index(batch_op.f("ix_Component_model_number"), ["model_number"], unique=False)
        batch_op.create_index(batch_op.f("ix_Component_part_number"), ["part_number"], unique=False)
        batch_op.create_index(batch_op.f("ix_Component_stock_status"), ["stock_status"], unique=False)
        batch_op.create_index(batch_op.f("ix_Component_supplier"), ["supplier"], unique=False)

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table("Component", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_Component_supplier"))
        batch_op.drop_index(batch_op.f("ix_Component_stock_status"))
        batch_op.drop_index(batch_op.f("ix_Component_part_number"))
        batch_op.drop_index(batch_op.f("ix_Component_model_number"))
        batch_op.drop_index(batch_op.f("ix_Component_manufacturer"))
        batch_op.drop_index(batch_op.f("ix_Component_is_preferred"))
        batch_op.drop_index(batch_op.f("ix_Component_is_active"))
        batch_op.drop_index(batch_op.f("ix_Component_component_type_id"))
        batch_op.drop_index(batch_op.f("ix_Component_category_id"))
        batch_op.drop_index("idx_component_type_category")
        batch_op.drop_index("idx_component_stock_active")
        batch_op.drop_index("idx_component_specifications_gin", postgresql_using="gin")
        batch_op.drop_index("idx_component_search")
        batch_op.drop_index("idx_component_metadata_gin", postgresql_using="gin")
        batch_op.drop_index("idx_component_manufacturer_supplier")
        batch_op.drop_index("idx_component_dimensions_gin", postgresql_using="gin")
        batch_op.drop_index("idx_component_active_preferred")

    op.drop_table("Component")
    op.drop_table("UserRoleAssignment")
    op.drop_table("ProjectMember")
    with op.batch_alter_table("ComponentType", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_ComponentType_is_active"))
        batch_op.drop_index(batch_op.f("ix_ComponentType_category_id"))
        batch_op.drop_index("idx_component_type_name_category")
        batch_op.drop_index("idx_component_type_name_active")
        batch_op.drop_index("idx_component_type_category_active")

    op.drop_table("ComponentType")
    op.drop_table("AuditTrail")
    op.drop_table("UserRole")
    op.drop_table("UserPreference")
    op.drop_table("Project")
    with op.batch_alter_table("ComponentCategory", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_ComponentCategory_parent_category_id"))
        batch_op.drop_index(batch_op.f("ix_ComponentCategory_is_active"))
        batch_op.drop_index("idx_component_category_parent_active")
        batch_op.drop_index("idx_component_category_name_active")

    op.drop_table("ComponentCategory")
    op.drop_table("ActivityLog")
    op.drop_table("User")
    # ### end Alembic commands ###
