"""Remove name column from user_role_assignments table

Revision ID: a0798b959785
Revises: d588859ab9e7
Create Date: 2025-08-09 19:56:56.686384

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "a0798b959785"
down_revision = "d588859ab9e7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user_role_assignments", "name")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "user_role_assignments", sa.Column("name", sa.VARCHAR(length=255), autoincrement=False, nullable=False)
    )
    # ### end Alembic commands ###
