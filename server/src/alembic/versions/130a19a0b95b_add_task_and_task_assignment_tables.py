"""add_task_and_task_assignment_tables

Revision ID: 130a19a0b95b
Revises: a1b2c3d4e5f6
Create Date: 2025-08-06 18:43:31.099300

"""

from alembic import op
import sqlalchemy as sa
from src.core.models.base import EnumType
from src.core.utils.json_validation import FlexibleJSON


# revision identifiers, used by Alembic.
revision = "130a19a0b95b"
down_revision = "a1b2c3d4e5f6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "synchronization_logs",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("session_id", sa.String(length=255), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("operation_type", EnumType(), nullable=False),
        sa.Column("sync_direction", EnumType(), nullable=False),
        sa.Column("status", EnumType(), nullable=False),
        sa.Column("started_at", sa.DateTime(), nullable=False),
        sa.Column("completed_at", sa.DateTime(), nullable=True),
        sa.Column("duration_ms", sa.Integer(), nullable=True),
        sa.Column("source_database_url", sa.Text(), nullable=True),
        sa.Column("target_database_url", sa.Text(), nullable=True),
        sa.Column("records_processed", sa.Integer(), nullable=False),
        sa.Column("records_created", sa.Integer(), nullable=False),
        sa.Column("records_updated", sa.Integer(), nullable=False),
        sa.Column("records_deleted", sa.Integer(), nullable=False),
        sa.Column("conflicts_detected", sa.Integer(), nullable=False),
        sa.Column("conflicts_resolved", sa.Integer(), nullable=False),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("error_details", FlexibleJSON(), nullable=True),
        sa.Column("sync_metadata", FlexibleJSON(), nullable=True),
        sa.Column("sync_config", FlexibleJSON(), nullable=True),
        sa.Column("throughput_records_per_second", sa.Float(), nullable=True),
        sa.Column("memory_usage_peak_mb", sa.Integer(), nullable=True),
        sa.Column("network_bytes_transferred", sa.Integer(), nullable=True),
        sa.Column("is_automatic", sa.Boolean(), nullable=False),
        sa.Column("is_retry", sa.Boolean(), nullable=False),
        sa.Column("is_critical", sa.Boolean(), nullable=False),
        sa.Column("retry_count", sa.Integer(), nullable=False),
        sa.Column("max_retries", sa.Integer(), nullable=False),
        sa.Column("next_retry_at", sa.DateTime(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tasks",
        sa.Column("task_id", sa.UUID(as_uuid=False), nullable=False, comment="Unique UUID identifier for the task"),
        sa.Column("project_id", sa.Integer(), nullable=False, comment="Foreign key to the associated project"),
        sa.Column("title", sa.String(length=255), nullable=False, comment="Task title"),
        sa.Column("description", sa.Text(), nullable=True, comment="Detailed task description"),
        sa.Column("due_date", sa.DateTime(), nullable=True, comment="Optional due date for the task"),
        sa.Column("priority", EnumType(), nullable=False, comment="Task priority level"),
        sa.Column("status", EnumType(), nullable=False, comment="Current task status"),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["projects.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("task_id", name="uq_task_task_id"),
    )
    op.create_index(op.f("ix_tasks_priority"), "tasks", ["priority"], unique=False)
    op.create_index(op.f("ix_tasks_project_id"), "tasks", ["project_id"], unique=False)
    op.create_index(op.f("ix_tasks_status"), "tasks", ["status"], unique=False)
    op.create_index(op.f("ix_tasks_task_id"), "tasks", ["task_id"], unique=True)
    op.create_table(
        "synchronization_conflicts",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("sync_log_id", sa.Integer(), nullable=False),
        sa.Column("entity_type", sa.String(length=100), nullable=False),
        sa.Column("entity_id", sa.String(length=255), nullable=False),
        sa.Column("table_name", sa.String(length=100), nullable=False),
        sa.Column("conflict_type", sa.String(length=100), nullable=False),
        sa.Column("field_name", sa.String(length=100), nullable=True),
        sa.Column("local_value", sa.Text(), nullable=True),
        sa.Column("central_value", sa.Text(), nullable=True),
        sa.Column("local_timestamp", sa.DateTime(), nullable=True),
        sa.Column("central_timestamp", sa.DateTime(), nullable=True),
        sa.Column("is_resolved", sa.Boolean(), nullable=False),
        sa.Column("resolution_strategy", sa.String(length=100), nullable=True),
        sa.Column("resolved_value", sa.Text(), nullable=True),
        sa.Column("resolved_at", sa.DateTime(), nullable=True),
        sa.Column("resolved_by_user_id", sa.Integer(), nullable=True),
        sa.Column("severity", EnumType(), nullable=False),
        sa.Column("requires_manual_intervention", sa.Boolean(), nullable=False),
        sa.Column("conflict_metadata", FlexibleJSON(), nullable=True),
        sa.Column("resolution_notes", sa.Text(), nullable=True),
        sa.Column("detected_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["resolved_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["sync_log_id"],
            ["synchronization_logs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "task_assignments",
        sa.Column("task_id", sa.Integer(), nullable=False, comment="Foreign key to the assigned task"),
        sa.Column("user_id", sa.Integer(), nullable=False, comment="Foreign key to the assigned user"),
        sa.Column("assigned_at", sa.DateTime(), nullable=False, comment="Timestamp when the assignment was made"),
        sa.Column("assigned_by_user_id", sa.Integer(), nullable=True, comment="ID of the user who made the assignment"),
        sa.Column("is_active", sa.Boolean(), nullable=False, comment="Whether the assignment is currently active"),
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("notes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.Column("is_deleted", sa.Boolean(), nullable=False),
        sa.Column("deleted_at", sa.DateTime(), nullable=True),
        sa.Column("deleted_by_user_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["assigned_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["deleted_by_user_id"],
            ["users.id"],
        ),
        sa.ForeignKeyConstraint(
            ["task_id"],
            ["tasks.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("task_id", "user_id", name="uq_task_assignment_task_user"),
    )
    op.create_index(op.f("ix_task_assignments_is_active"), "task_assignments", ["is_active"], unique=False)
    op.create_index(op.f("ix_task_assignments_task_id"), "task_assignments", ["task_id"], unique=False)
    op.create_index(op.f("ix_task_assignments_user_id"), "task_assignments", ["user_id"], unique=False)
    op.drop_index(op.f("ix_ComponentCategory_is_active"), table_name="component_categories")
    op.drop_index(op.f("ix_ComponentCategory_parent_category_id"), table_name="component_categories")
    op.create_index(op.f("ix_component_categories_is_active"), "component_categories", ["is_active"], unique=False)
    op.create_index(
        op.f("ix_component_categories_parent_category_id"), "component_categories", ["parent_category_id"], unique=False
    )
    op.drop_index(op.f("ix_ComponentType_category_id"), table_name="component_types")
    op.drop_index(op.f("ix_ComponentType_is_active"), table_name="component_types")
    op.create_index(op.f("ix_component_types_category_id"), "component_types", ["category_id"], unique=False)
    op.create_index(op.f("ix_component_types_is_active"), "component_types", ["is_active"], unique=False)
    op.drop_index(op.f("ix_Component_category_id"), table_name="components")
    op.drop_index(op.f("ix_Component_component_type_id"), table_name="components")
    op.drop_index(op.f("ix_Component_is_active"), table_name="components")
    op.drop_index(op.f("ix_Component_is_preferred"), table_name="components")
    op.drop_index(op.f("ix_Component_manufacturer"), table_name="components")
    op.drop_index(op.f("ix_Component_model_number"), table_name="components")
    op.drop_index(op.f("ix_Component_part_number"), table_name="components")
    op.drop_index(op.f("ix_Component_stock_status"), table_name="components")
    op.drop_index(op.f("ix_Component_supplier"), table_name="components")
    op.create_index(op.f("ix_components_category_id"), "components", ["category_id"], unique=False)
    op.create_index(op.f("ix_components_component_type_id"), "components", ["component_type_id"], unique=False)
    op.create_index(op.f("ix_components_is_active"), "components", ["is_active"], unique=False)
    op.create_index(op.f("ix_components_is_preferred"), "components", ["is_preferred"], unique=False)
    op.create_index(op.f("ix_components_manufacturer"), "components", ["manufacturer"], unique=False)
    op.create_index(op.f("ix_components_model_number"), "components", ["model_number"], unique=False)
    op.create_index(op.f("ix_components_part_number"), "components", ["part_number"], unique=False)
    op.create_index(op.f("ix_components_stock_status"), "components", ["stock_status"], unique=False)
    op.create_index(op.f("ix_components_supplier"), "components", ["supplier"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_components_supplier"), table_name="components")
    op.drop_index(op.f("ix_components_stock_status"), table_name="components")
    op.drop_index(op.f("ix_components_part_number"), table_name="components")
    op.drop_index(op.f("ix_components_model_number"), table_name="components")
    op.drop_index(op.f("ix_components_manufacturer"), table_name="components")
    op.drop_index(op.f("ix_components_is_preferred"), table_name="components")
    op.drop_index(op.f("ix_components_is_active"), table_name="components")
    op.drop_index(op.f("ix_components_component_type_id"), table_name="components")
    op.drop_index(op.f("ix_components_category_id"), table_name="components")
    op.create_index(op.f("ix_Component_supplier"), "components", ["supplier"], unique=False)
    op.create_index(op.f("ix_Component_stock_status"), "components", ["stock_status"], unique=False)
    op.create_index(op.f("ix_Component_part_number"), "components", ["part_number"], unique=False)
    op.create_index(op.f("ix_Component_model_number"), "components", ["model_number"], unique=False)
    op.create_index(op.f("ix_Component_manufacturer"), "components", ["manufacturer"], unique=False)
    op.create_index(op.f("ix_Component_is_preferred"), "components", ["is_preferred"], unique=False)
    op.create_index(op.f("ix_Component_is_active"), "components", ["is_active"], unique=False)
    op.create_index(op.f("ix_Component_component_type_id"), "components", ["component_type_id"], unique=False)
    op.create_index(op.f("ix_Component_category_id"), "components", ["category_id"], unique=False)
    op.drop_index(op.f("ix_component_types_is_active"), table_name="component_types")
    op.drop_index(op.f("ix_component_types_category_id"), table_name="component_types")
    op.create_index(op.f("ix_ComponentType_is_active"), "component_types", ["is_active"], unique=False)
    op.create_index(op.f("ix_ComponentType_category_id"), "component_types", ["category_id"], unique=False)
    op.drop_index(op.f("ix_component_categories_parent_category_id"), table_name="component_categories")
    op.drop_index(op.f("ix_component_categories_is_active"), table_name="component_categories")
    op.create_index(
        op.f("ix_ComponentCategory_parent_category_id"), "component_categories", ["parent_category_id"], unique=False
    )
    op.create_index(op.f("ix_ComponentCategory_is_active"), "component_categories", ["is_active"], unique=False)
    op.drop_index(op.f("ix_task_assignments_user_id"), table_name="task_assignments")
    op.drop_index(op.f("ix_task_assignments_task_id"), table_name="task_assignments")
    op.drop_index(op.f("ix_task_assignments_is_active"), table_name="task_assignments")
    op.drop_table("task_assignments")
    op.drop_table("synchronization_conflicts")
    op.drop_index(op.f("ix_tasks_task_id"), table_name="tasks")
    op.drop_index(op.f("ix_tasks_status"), table_name="tasks")
    op.drop_index(op.f("ix_tasks_project_id"), table_name="tasks")
    op.drop_index(op.f("ix_tasks_priority"), table_name="tasks")
    op.drop_table("tasks")
    op.drop_table("synchronization_logs")
    # ### end Alembic commands ###
