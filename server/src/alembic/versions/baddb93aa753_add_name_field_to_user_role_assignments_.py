"""Add name field to user_role_assignments table

Revision ID: baddb93aa753
Revises: 4272d56e0eda
Create Date: 2025-08-09 23:05:33.088971

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "baddb93aa753"
down_revision = "4272d56e0eda"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add the column as nullable first
    op.add_column("user_role_assignments", sa.Column("name", sa.String(length=100), nullable=True))

    # Update existing records with a default name based on role and user
    op.execute("""
        UPDATE user_role_assignments
        SET name = CONCAT('Role Assignment ', id)
        WHERE name IS NULL
    """)

    # Now make the column NOT NULL
    op.alter_column("user_role_assignments", "name", nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("user_role_assignments", "name")
    # ### end Alembic commands ###
