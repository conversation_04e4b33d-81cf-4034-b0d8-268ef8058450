

"""Add email verification fields to users table

Revision ID: 65496862b022
Revises: 7e4c33faf4c7
Create Date: 2025-08-11 01:04:23.011821

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '65496862b022'
down_revision = '7e4c33faf4c7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('is_email_verified', sa.<PERSON>(), nullable=False, server_default='false'))
    op.add_column('users', sa.Column('email_verification_token', sa.String(), nullable=True))
    op.add_column('users', sa.Column('email_verification_expires', sa.DateTime(timezone=True), nullable=True))
    op.add_column('users', sa.Column('email_verified_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'email_verified_at')
    op.drop_column('users', 'email_verification_expires')
    op.drop_column('users', 'email_verification_token')
    op.drop_column('users', 'is_email_verified')
    # ### end Alembic commands ###
