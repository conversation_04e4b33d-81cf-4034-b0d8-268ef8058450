

"""Add password reset fields to users table

Revision ID: a27501974d9d
Revises: 65496862b022
Create Date: 2025-08-11 01:05:44.468029

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a27501974d9d'
down_revision = '65496862b022'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('password_reset_token', sa.String(), nullable=True))
    op.add_column('users', sa.Column('password_reset_expires', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('users', 'password_reset_expires')
    op.drop_column('users', 'password_reset_token')
    # ### end Alembic commands ###
