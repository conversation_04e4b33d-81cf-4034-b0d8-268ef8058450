# Commands to get the image running locally

# create and start the database and backend containers:
# ```bash
# docker compose up -d db db-test redis backend
# ```

# view the logs for the containers:
# ```bash
# docker logs -f db
# docker logs -f db-test
# docker logs -f backend
# docker logs -f redis
# ```

version: '3.8'

services:
  db:
    image: postgres:16-alpine
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ultimate_electrical_designer}
      POSTGRES_USER: ${POSTGRES_USER:-user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB" ]
      interval: 5s
      timeout: 5s
      retries: 5
  db-test:
    image: postgres:16-alpine
    restart: always
    environment:
      POSTGRES_DB: ${POSTGRES_DB_TEST:-ultimate_electrical_designer_test}
      POSTGRES_USER: ${POSTGRES_USER:-user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
    ports:
      - "5433:5432"
    volumes:
      - db_test_data:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB" ]
      timeout: 5s
      retries: 5

  backend:
    build:
      context: .
      dockerfile: server/Dockerfile
    command: bash -c "cd src && alembic upgrade head && uvicorn main:app --host 0.0.0.0 --port 8000"
    volumes:
      - ./server:/app
    ports:
      - "8000:8000"
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-user}:${POSTGRES_PASSWORD:-password}@db:5432/${POSTGRES_DB:-ultimate_electrical_designer}
      SECRET_KEY: ${SECRET_KEY}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: [ "CMD-SHELL", "redis-cli ping | grep PONG" ]
      interval: 5s
      timeout: 5s
      retries: 5

volumes:
  db_data:
  db_test_data:
  redis_data:
