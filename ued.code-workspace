{"folders": [{"path": "."}], "settings": {"files.associations": {"*.py": "python", "*.pyi": "python", "mypy.ini": "ini", ".pre-commit-config.yaml": "yaml"}, "explorer.autoRevealExclude": {"**/.history": true, "**/.ruff_cache": true, "**/.venv": true}, "pytest.command": "./server/.venv/bin/pytest", "typescript.tsdk": "node_modules/typescript/lib", "terminal.integrated.env.linux": {"PYTHONPATH": "${workspaceFolder}/server/src"}, "terminal.integrated.env.osx": {"PYTHONPATH": "${workspaceFolder}/server/src"}, "terminal.integrated.env.windows": {"PYTHONPATH": "${workspaceFolder}/server/src"}, "terminal.integrated.cursorStyle": "underline", "terminal.integrated.hideOnStartup": "whenEmpty", "terminal.integrated.persistentSessionReviveProcess": "onExitAndWindowClose", "terminal.integrated.profiles.windows": {"PowerShell": {"source": "PowerShell", "icon": "terminal-powershell"}, "Command Prompt": {"path": ["${env:windir}\\Sysnative\\cmd.exe", "${env:windir}\\System32\\cmd.exe"], "args": [], "icon": "terminal-cmd"}, "Git Bash": {"source": "<PERSON><PERSON>"}}, "git.ignoreLimitWarning": true, "errorLens.lintFilePaths": {"ruff": ["**./*pyproject.toml"], "mypy": ["**./*mypy.ini"], "eslint": ["**/*.es<PERSON><PERSON>.{js,cjs,yaml,yml,json}", "**/*package.json"], "Stylelint": ["**/*.stylelintrc", "**/*.stylelintrc.{cjs,js,json,yaml,yml}", "**/*stylelint.config.{cjs,js}", "**/*package.json"]}}}