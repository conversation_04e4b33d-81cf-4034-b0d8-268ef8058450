import type {
  PersistedClient,
  Persister,
} from "@tanstack/react-query-persist-client"
import type { IDBPDatabase } from "idb"

import { openDB } from "idb"

export interface CacheStats {
  isSupported: boolean
  cacheSize?: number
  lastUpdated?: number
  outboxCount?: number
}

/**
 * IndexedDBPersister - Custom persister for React Query using IndexedDB
 *
 * This class implements the Persister interface from @tanstack/react-query-persist-client
 * to provide persistent client-side caching using IndexedDB.
 *
 * Features:
 * - Asynchronous operations that don't block the UI thread
 * - Transactional data operations for integrity
 * - Large storage capacity for comprehensive caching
 * - Structured data storage optimized for JSON objects
 */

/**
 * Configuration options for IndexedDBPersister
 */
export interface IndexedDBPersisterConfig {
  /** Name of the IndexedDB database */
  dbName?: string
  /** Version of the database schema */
  dbVersion?: number
  /** Name of the object store for query cache */
  storeName?: string
  /** Key used to store the persisted client data */
  cacheKey?: string
}

/**
 * Default configuration for IndexedDBPersister
 */
const DEFAULT_CONFIG: Required<IndexedDBPersisterConfig> = {
  dbName: "ultimate-electrical-designer-cache",
  dbVersion: 1,
  storeName: "query-cache",
  cacheKey: "react-query-cache",
}

/**
 * IndexedDBPersister class implementing the Persister interface
 * for React Query client-side caching with IndexedDB
 */
export class IndexedDBPersister implements Persister {
  private readonly config: Required<IndexedDBPersisterConfig>
  private dbPromise: Promise<IDBPDatabase> | null = null

  /**
   * Create a new IndexedDBPersister instance
   *
   * @param config - Configuration options for the persister
   */
  constructor(config: IndexedDBPersisterConfig = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config }
  }

  /**
   * Initialize the IndexedDB database and object store
   * Uses lazy initialization to avoid blocking during construction
   *
   * @returns Promise resolving to the IDBPDatabase instance
   */
  private async initDB(): Promise<IDBPDatabase> {
    if (!this.dbPromise) {
      this.dbPromise = openDB(this.config.dbName, this.config.dbVersion, {
        upgrade(db, _oldVersion, _newVersion, _transaction, _event) {
          // Create object stores if they don't exist
          if (!db.objectStoreNames.contains("query-cache")) {
            db.createObjectStore("query-cache")
          }

          // Create mutation-outbox store for future offline mutation handling
          if (!db.objectStoreNames.contains("mutation-outbox")) {
            const outboxStore = db.createObjectStore("mutation-outbox", {
              keyPath: "id",
              autoIncrement: true,
            })
            // Index by timestamp for ordered processing
            outboxStore.createIndex("timestamp", "timestamp")
          }
        },
        blocked(currentVersion, blockedVersion, _event) {
          console.warn(
            "IndexedDB upgrade blocked. Please close other tabs with this application.",
            { currentVersion, blockedVersion }
          )
        },
        blocking(currentVersion, blockedVersion, _event) {
          console.warn(
            "IndexedDB blocking upgrade. This tab will be refreshed.",
            { currentVersion, blockedVersion }
          )
          // Could trigger a page refresh or show user notification
        },
        terminated: () => {
          console.error("IndexedDB connection terminated unexpectedly")
          // Reset the database promise to allow reconnection
          this.dbPromise = null
        },
      })
    }
    return this.dbPromise
  }

  /**
   * Persist the React Query client data to IndexedDB
   * Called by React Query when cache state changes
   *
   * @param persistedClient - The client data to persist
   * @returns Promise that resolves when persistence is complete
   */
  async persistClient(persistedClient: PersistedClient): Promise<void> {
    try {
      const db = await this.initDB()

      // Store the persisted client data with timestamp for cache management
      const dataToStore = {
        ...persistedClient,
        timestamp: Date.now(),
      }

      await db.put(this.config.storeName, dataToStore, this.config.cacheKey)
    } catch (error) {
      console.error("Failed to persist client data to IndexedDB:", error)
      // Don't throw to prevent breaking React Query operations
      // React Query will continue to work without persistence
    }
  }

  /**
   * Restore React Query client data from IndexedDB
   * Called by React Query during initialization
   *
   * @returns Promise resolving to the persisted client data, or undefined if not found
   */
  async restoreClient(): Promise<PersistedClient | undefined> {
    try {
      const db = await this.initDB()

      const storedData = await db.get(
        this.config.storeName,
        this.config.cacheKey
      )

      if (!storedData) {
        return undefined
      }

      // Remove our added timestamp before returning to React Query
      const { timestamp, ...persistedClient } = storedData

      return persistedClient as PersistedClient
    } catch (error) {
      console.error("Failed to restore client data from IndexedDB:", error)
      // Return undefined to let React Query start with fresh cache
      return undefined
    }
  }

  /**
   * Remove the persisted client data from IndexedDB
   * Can be called to clear the cache
   *
   * @returns Promise that resolves when removal is complete
   */
  async removeClient(): Promise<void> {
    try {
      const db = await this.initDB()
      await db.delete(this.config.storeName, this.config.cacheKey)
    } catch (error) {
      console.error("Failed to remove client data from IndexedDB:", error)
      // Don't throw to prevent breaking cache clearing operations
    }
  }

  /**
   * Get database statistics for debugging and monitoring
   *
   * @returns Promise resolving to cache statistics
   */
  async getCacheStats(): Promise<{
    isSupported: boolean
    cacheSize?: number
    lastUpdated?: number
    outboxCount?: number
  }> {
    try {
      // Check if IndexedDB is supported
      if (typeof indexedDB === "undefined") {
        return { isSupported: false }
      }

      const db = await this.initDB()

      // Get cache data
      const cacheData = await db.get(
        this.config.storeName,
        this.config.cacheKey
      )

      // Get outbox count for future mutation handling
      const outboxCount = await db.count("mutation-outbox")

      return {
        isSupported: true,
        cacheSize: cacheData ? JSON.stringify(cacheData).length : 0,
        lastUpdated: cacheData?.timestamp,
        outboxCount,
      }
    } catch (error) {
      console.error("Failed to get cache statistics:", error)
      return { isSupported: false }
    }
  }

  /**
   * Clear all data from the cache database
   * Useful for debugging or when implementing cache reset functionality
   *
   * @returns Promise that resolves when clearing is complete
   */
  async clearAll(): Promise<void> {
    try {
      const db = await this.initDB()

      // Clear all object stores
      const transaction = db.transaction(
        ["query-cache", "mutation-outbox"],
        "readwrite"
      )

      await Promise.all([
        transaction.objectStore("query-cache").clear(),
        transaction.objectStore("mutation-outbox").clear(),
        transaction.done,
      ])
    } catch (error) {
      console.error("Failed to clear cache database:", error)
      throw error // Re-throw since this is likely a user-initiated action
    }
  }
}

/**
 * Create and configure a new IndexedDBPersister instance
 * Factory function to ensure proper configuration
 *
 * @param config - Configuration options for the persister
 * @returns Configured IndexedDBPersister instance
 */
export function createIndexedDBPersister(
  config: IndexedDBPersisterConfig = {}
): IndexedDBPersister {
  return new IndexedDBPersister(config)
}

/**
 * Type guard to check if IndexedDB is supported in the current environment
 *
 * @returns true if IndexedDB is supported, false otherwise
 */
export function isIndexedDBSupported(): boolean {
  return typeof window !== "undefined" && "indexedDB" in window
}
