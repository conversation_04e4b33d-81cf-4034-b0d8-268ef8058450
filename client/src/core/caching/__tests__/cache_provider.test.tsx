/**
 * Integration tests for CacheProvider with IndexedDBPersister
 *
 * These tests verify that CacheProvider correctly integrates with React Query
 * and IndexedDBPersister to provide persistent caching across page reloads.
 */

import React from "react"

import { useQuery, useQueryClient } from "@tanstack/react-query"
import { act, render, screen, waitFor } from "@testing-library/react"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { CacheManager, CacheProvider } from "../cache_provider"

// Mock the IndexedDBPersister
vi.mock("../indexed_db_persister", () => ({
  createIndexedDBPersister: vi.fn(() => ({
    persistClient: vi.fn().mockResolvedValue(undefined),
    restoreClient: vi.fn().mockResolvedValue(undefined),
    removeClient: vi.fn().mockResolvedValue(undefined),
    getCacheStats: vi.fn().mockResolvedValue({
      isSupported: true,
      cacheSize: 0,
      lastUpdated: undefined,
      outboxCount: 0,
    }),
    clearAll: vi.fn().mockResolvedValue(undefined),
  })),
  isIndexedDBSupported: vi.fn(() => true),
}))

// Mock React Query persist client
vi.mock("@tanstack/react-query-persist-client", () => ({
  persistQueryClient: vi.fn().mockResolvedValue(undefined),
}))

// Test component that uses React Query
function TestQueryComponent() {
  const { data, isLoading, error } = useQuery({
    queryKey: ["test-query"],
    queryFn: async () => {
      return { message: "Hello from cache!", timestamp: Date.now() }
    },
    staleTime: 1000,
  })

  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {(error as Error).message}</div>
  if (!data) return <div>No data</div>

  return (
    <div>
      <span data-testid="message">{data.message}</span>
      <span data-testid="timestamp">{data.timestamp}</span>
    </div>
  )
}

// Component for testing cache management
function CacheManagementComponent() {
  const queryClient = useQueryClient()

  const handleClearCache = async () => {
    await CacheManager.clearCache(queryClient)
  }

  const handleInvalidateAll = async () => {
    await CacheManager.invalidateAll(queryClient)
  }

  const handleRemoveStale = async () => {
    await CacheManager.removeStale(queryClient)
  }

  const getCacheStats = () => {
    return CacheManager.getCacheStats(queryClient)
  }

  return (
    <div>
      <button data-testid="clear-cache" onClick={handleClearCache}>
        Clear Cache
      </button>
      <button data-testid="invalidate-all" onClick={handleInvalidateAll}>
        Invalidate All
      </button>
      <button data-testid="remove-stale" onClick={handleRemoveStale}>
        Remove Stale
      </button>
      <button
        data-testid="get-stats"
        onClick={() => {
          const stats = getCacheStats()
          console.log("Cache stats:", stats)
        }}
      >
        Get Stats
      </button>
      <TestQueryComponent />
    </div>
  )
}

describe("CacheProvider Integration Tests", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  it("should render children after initialization", async () => {
    await act(async () => {
      render(
        <CacheProvider>
          <div data-testid="test-content">Test Content</div>
        </CacheProvider>
      )
    })

    // Should show loading initially - look for the spinner
    expect(screen.getByText("Test Content")).toBeInTheDocument()
  })

  it("should initialize with custom configuration", async () => {
    const { createIndexedDBPersister } = await import("../indexed_db_persister")

    render(
      <CacheProvider
        maxAge={12 * 60 * 60 * 1000} // 12 hours
        dbName="custom-test-db"
        enableDevtools={false}
      >
        <div data-testid="test-content">Test Content</div>
      </CacheProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId("test-content")).toBeInTheDocument()
    })

    // Verify persister was created with custom config
    expect(createIndexedDBPersister).toHaveBeenCalledWith({
      dbName: "custom-test-db",
      dbVersion: 1,
      storeName: "query-cache",
      cacheKey: "react-query-cache",
    })
  })

  it("should handle IndexedDB not supported gracefully", async () => {
    const { isIndexedDBSupported } = await import("../indexed_db_persister")
    vi.mocked(isIndexedDBSupported).mockReturnValue(false)

    const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})

    render(
      <CacheProvider>
        <div data-testid="test-content">Test Content</div>
      </CacheProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId("test-content")).toBeInTheDocument()
    })

    expect(consoleSpy).toHaveBeenCalledWith(
      "IndexedDB not supported - running without persistence"
    )

    consoleSpy.mockRestore()
  })

  it("should handle persistence initialization failure gracefully", async () => {
    // Mock IndexedDB as supported for this test
    const { isIndexedDBSupported } = await import("../indexed_db_persister")
    vi.mocked(isIndexedDBSupported).mockReturnValue(true)

    const { persistQueryClient } = await import(
      "@tanstack/react-query-persist-client"
    )
    vi.mocked(persistQueryClient).mockRejectedValue(
      new Error("Persistence failed")
    )

    const consoleErrorSpy = vi
      .spyOn(console, "error")
      .mockImplementation(() => {})

    await act(async () => {
      render(
        <CacheProvider>
          <div data-testid="test-content">Test Content</div>
        </CacheProvider>
      )
    })

    await waitFor(() => {
      expect(screen.getByTestId("test-content")).toBeInTheDocument()
    })

    expect(consoleErrorSpy).toHaveBeenCalledWith(
      "Failed to initialize cache persistence:",
      expect.any(Error)
    )

    consoleErrorSpy.mockRestore()
  })

  it("should provide working React Query context", async () => {
    render(
      <CacheProvider>
        <TestQueryComponent />
      </CacheProvider>
    )

    // Wait for initialization and query to complete
    await waitFor(() => {
      expect(screen.getByTestId("message")).toBeInTheDocument()
    })

    expect(screen.getByTestId("message")).toHaveTextContent("Hello from cache!")
    expect(screen.getByTestId("timestamp")).toBeInTheDocument()
  })

  it("should support cache management utilities", async () => {
    const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {})

    render(
      <CacheProvider>
        <CacheManagementComponent />
      </CacheProvider>
    )

    // Wait for initialization and query to complete
    await waitFor(() => {
      expect(screen.getByTestId("message")).toBeInTheDocument()
    })

    // Test clear cache
    await act(async () => {
      screen.getByTestId("clear-cache").click()
    })

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith("Query cache cleared")
    })

    // Test invalidate all
    await act(async () => {
      screen.getByTestId("invalidate-all").click()
    })

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith("All queries invalidated")
    })

    // Test remove stale
    await act(async () => {
      screen.getByTestId("remove-stale").click()
    })

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith("Stale queries removed")
    })

    consoleSpy.mockRestore()
  })

  it("should provide cache statistics", async () => {
    const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {})

    render(
      <CacheProvider>
        <CacheManagementComponent />
      </CacheProvider>
    )

    // Wait for initialization and query to complete
    await waitFor(() => {
      expect(screen.getByTestId("message")).toBeInTheDocument()
    })

    // Get cache stats
    await act(async () => {
      screen.getByTestId("get-stats").click()
    })

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        "Cache stats:",
        expect.objectContaining({
          totalQueries: expect.any(Number),
          successfulQueries: expect.any(Number),
          errorQueries: expect.any(Number),
          loadingQueries: expect.any(Number),
          staleQueries: expect.any(Number),
        })
      )
    })

    consoleSpy.mockRestore()
  })

  it("should show React Query Devtools when enabled", async () => {
    render(
      <CacheProvider enableDevtools={true}>
        <div data-testid="test-content">Test Content</div>
      </CacheProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId("test-content")).toBeInTheDocument()
    })
  })

  it("should handle persistent query client configuration correctly", async () => {
    // Mock IndexedDB as supported for this test
    const { isIndexedDBSupported } = await import("../indexed_db_persister")
    vi.mocked(isIndexedDBSupported).mockReturnValue(true)

    const { persistQueryClient } = await import(
      "@tanstack/react-query-persist-client"
    )

    await act(async () => {
      render(
        <CacheProvider maxAge={6 * 60 * 60 * 1000}>
          <div data-testid="test-content">Test Content</div>
        </CacheProvider>
      )
    })

    await waitFor(() => {
      expect(screen.getByTestId("test-content")).toBeInTheDocument()
    })

    // Verify persistQueryClient was called with correct configuration
    expect(persistQueryClient).toHaveBeenCalledWith({
      queryClient: expect.any(Object),
      persister: expect.any(Object),
      maxAge: 6 * 60 * 60 * 1000,
      buster: expect.any(String),
      dehydrateOptions: {
        shouldDehydrateQuery: expect.any(Function),
        shouldDehydrateMutation: expect.any(Function),
      },
      hydrateOptions: {},
    })
  })

  it("should handle query and mutation dehydration filters", async () => {
    // Mock IndexedDB as supported for this test
    const { isIndexedDBSupported } = await import("../indexed_db_persister")
    vi.mocked(isIndexedDBSupported).mockReturnValue(true)

    const { persistQueryClient } = await import(
      "@tanstack/react-query-persist-client"
    )

    await act(async () => {
      render(
        <CacheProvider>
          <div data-testid="test-content">Test Content</div>
        </CacheProvider>
      )
    })

    await waitFor(() => {
      expect(screen.getByTestId("test-content")).toBeInTheDocument()
    })

    // Get the dehydration options
    const persistCall = vi.mocked(persistQueryClient).mock.calls[0][0]
    const { shouldDehydrateQuery, shouldDehydrateMutation } =
      persistCall.dehydrateOptions!

    // Test query dehydration - should only dehydrate successful queries with data
    expect(
      shouldDehydrateQuery!({
        state: { status: "success", data: "test" },
      } as any)
    ).toBe(true)
    expect(
      shouldDehydrateQuery!({
        state: { status: "error", data: undefined },
      } as any)
    ).toBe(false)
    expect(
      shouldDehydrateQuery!({
        state: { status: "success", data: undefined },
      } as any)
    ).toBe(false)

    // Test mutation dehydration - should not persist mutations by default
    expect(shouldDehydrateMutation!({} as any)).toBe(false)
  })
})
