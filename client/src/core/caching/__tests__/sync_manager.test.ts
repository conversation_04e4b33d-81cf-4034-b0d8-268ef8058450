/**
 * Unit tests for SyncManager
 *
 * These tests verify the SyncManager functionality including:
 * - Network status monitoring and transitions
 * - Offline mutation processing
 * - Retry logic with exponential backoff
 * - Event emission and subscription
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import {
  MutationOutboxManager,
  type OfflineMutationRecord,
} from "@/hooks/useOfflineMutation"
import { useNetworkStatusStore } from "@/stores/networkStatusStore"
import { ConnectionQuality, SyncManager, SyncStatus } from "../sync_manager"

// Mock dependencies
vi.mock("@/hooks/useOfflineMutation", () => ({
  MutationOutboxManager: vi.fn(() => ({
    getPendingMutations: vi.fn(),
    removeMutation: vi.fn(),
    getStats: vi.fn(),
  })),
}))

vi.mock("@/stores/networkStatusStore", () => ({
  useNetworkStatusStore: {
    getState: vi.fn(),
    subscribe: vi.fn(),
  },
}))

// Mock fetch
global.fetch = vi.fn()

describe("SyncManager", () => {
  let syncManager: SyncManager
  let mockOutboxManager: any
  let mockNetworkStore: any
  let mockFetch: ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()

    mockFetch = vi.mocked(fetch)
    mockOutboxManager = {
      getPendingMutations: vi.fn().mockResolvedValue([]),
      removeMutation: vi.fn().mockResolvedValue(undefined),
      getStats: vi.fn().mockResolvedValue({
        isSupported: true,
        cacheSize: 0,
        lastUpdated: undefined,
        outboxCount: 0,
      }),
    }

    mockNetworkStore = {
      connection: {
        isOnline: true,
        quality: ConnectionQuality.enum.Fast,
        lastChanged: Date.now(),
      },
      isConnectionStable: vi.fn().mockReturnValue(true),
    }

    // Setup mocks
    const mockedOutboxManager = vi.mocked(MutationOutboxManager)
    mockedOutboxManager.mockImplementation(() => mockOutboxManager)

    const mockedGetState = vi.mocked(useNetworkStatusStore.getState)
    mockedGetState.mockReturnValue(mockNetworkStore)

    const mockedSubscribe = vi.mocked(useNetworkStatusStore.subscribe)
    mockedSubscribe.mockImplementation(() => vi.fn())

    syncManager = new SyncManager({
      enableLogging: false, // Disable for tests
      maxRetries: 2,
      retryBaseDelay: 10,
      maxRetryDelay: 100,
      maxConcurrentSyncs: 2,
    })
  })

  afterEach(() => {
    syncManager.destroy()
  })

  describe("Initialization", () => {
    it("should initialize with default configuration", () => {
      const defaultSyncManager = new SyncManager()
      expect(defaultSyncManager.isCurrentlyProcessing()).toBe(false)
      expect(defaultSyncManager.getCurrentOperation()).toBeNull()
      defaultSyncManager.destroy()
    })

    it("should initialize with custom configuration", () => {
      const customConfig = {
        maxRetries: 5,
        retryBaseDelay: 2000,
        enableLogging: true,
      }

      const customSyncManager = new SyncManager(customConfig)
      expect(customSyncManager.isCurrentlyProcessing()).toBe(false)
      customSyncManager.destroy()
    })

    it("should subscribe to network status changes", () => {
      expect(useNetworkStatusStore.subscribe).toHaveBeenCalled()
    })
  })

  describe("Network Status Monitoring", () => {
    it("should process outbox when coming online", async () => {
      const processOutboxSpy = vi
        .spyOn(syncManager, "processOutbox")
        .mockResolvedValue({
          status: SyncStatus.COMPLETED,
          totalMutations: 0,
          successCount: 0,
          failedCount: 0,
          skippedCount: 0,
          results: [],
          duration: 0,
          startTime: Date.now(),
        })

      // Simulate network status subscription call
      const subscribeCall = vi.mocked(useNetworkStatusStore.subscribe).mock
        .calls[0]
      const callback = subscribeCall[1]

      // Simulate going from offline to online
      await callback(true, false)

      // Wait for the delay and processing
      await new Promise((resolve) => setTimeout(resolve, 1100))

      expect(processOutboxSpy).toHaveBeenCalled()
    })

    it("should not process outbox if connection quality is insufficient", async () => {
      const processOutboxSpy = vi.spyOn(syncManager, "processOutbox")

      mockNetworkStore.connection.quality = ConnectionQuality.enum.Slow

      // Create new sync manager with higher quality requirement
      syncManager.destroy()
      syncManager = new SyncManager({
        minConnectionQuality: ConnectionQuality.enum.Moderate,
        enableLogging: false,
      })

      const subscribeCall = vi.mocked(useNetworkStatusStore.subscribe).mock
        .calls[0]
      const callback = subscribeCall[1]

      await callback(true, false)
      await new Promise((resolve) => setTimeout(resolve, 1100))

      expect(processOutboxSpy).not.toHaveBeenCalled()
    })
  })

  describe("Outbox Processing", () => {
    it("should complete successfully with no pending mutations", async () => {
      mockOutboxManager.getPendingMutations.mockResolvedValue([])

      const result = await syncManager.processOutbox()

      expect(result.status).toBe(SyncStatus.COMPLETED)
      expect(result.totalMutations).toBe(0)
      expect(result.successCount).toBe(0)
      expect(result.failedCount).toBe(0)
    })

    it("should process pending mutations successfully", async () => {
      const mockMutations: OfflineMutationRecord[] = [
        {
          id: 1,
          endpoint: "/api/test1",
          method: "POST",
          payload: { data: "test1" },
          timestamp: new Date().toISOString(),
          mutationKey: "test-1",
        },
        {
          id: 2,
          endpoint: "/api/test2",
          method: "PUT",
          payload: { data: "test2" },
          timestamp: new Date().toISOString(),
          mutationKey: "test-2",
        },
      ]

      mockOutboxManager.getPendingMutations.mockResolvedValue(mockMutations)
      mockFetch
        .mockResolvedValueOnce(
          new Response(JSON.stringify({ success: true }), { status: 200 })
        )
        .mockResolvedValueOnce(
          new Response(JSON.stringify({ success: true }), { status: 200 })
        )

      const result = await syncManager.processOutbox()

      expect(result.status).toBe(SyncStatus.COMPLETED)
      expect(result.totalMutations).toBe(2)
      expect(result.successCount).toBe(2)
      expect(result.failedCount).toBe(0)
      expect(mockOutboxManager.removeMutation).toHaveBeenCalledWith(1)
      expect(mockOutboxManager.removeMutation).toHaveBeenCalledWith(2)
    })

    it("should handle mutations with partial failures", async () => {
      const mockMutations: OfflineMutationRecord[] = [
        {
          id: 1,
          endpoint: "/api/success",
          method: "POST",
          payload: { data: "success" },
          timestamp: new Date().toISOString(),
          mutationKey: "success",
        },
        {
          id: 2,
          endpoint: "/api/failure",
          method: "POST",
          payload: { data: "failure" },
          timestamp: new Date().toISOString(),
          mutationKey: "failure",
        },
      ]

      mockOutboxManager.getPendingMutations.mockResolvedValue(mockMutations)
      mockFetch
        .mockResolvedValueOnce(
          new Response(JSON.stringify({ success: true }), { status: 200 })
        )
        .mockResolvedValueOnce(new Response("Server Error", { status: 500 }))

      const result = await syncManager.processOutbox()

      expect(result.status).toBe(SyncStatus.COMPLETED) // Mixed results still count as completed
      expect(result.totalMutations).toBe(2)
      expect(result.successCount).toBe(1)
      expect(result.failedCount).toBe(1)
      expect(mockOutboxManager.removeMutation).toHaveBeenCalledWith(1)
      expect(mockOutboxManager.removeMutation).not.toHaveBeenCalledWith(2)
    })

    it("should prevent concurrent processing", async () => {
      mockOutboxManager.getPendingMutations.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve([]), 100))
      )

      const promise1 = syncManager.processOutbox()

      await expect(syncManager.processOutbox()).rejects.toThrow(
        "Sync operation already in progress"
      )

      await promise1 // Wait for first operation to complete
    })

    it("should handle outbox manager errors", async () => {
      mockOutboxManager.getPendingMutations.mockRejectedValue(
        new Error("Outbox error")
      )

      await expect(syncManager.processOutbox()).rejects.toThrow("Outbox error")

      expect(syncManager.isCurrentlyProcessing()).toBe(false)
    })
  })

  describe("Retry Logic", () => {
    it("should retry failed requests with exponential backoff", async () => {
      const mockMutation: OfflineMutationRecord = {
        id: 1,
        endpoint: "/api/retry-test",
        method: "POST",
        payload: { data: "retry" },
        timestamp: new Date().toISOString(),
        mutationKey: "retry-test",
      }

      mockOutboxManager.getPendingMutations.mockResolvedValue([mockMutation])

      // Fail first two attempts, succeed on third
      mockFetch
        .mockRejectedValueOnce(new Error("Network error"))
        .mockRejectedValueOnce(new Error("Network error"))
        .mockResolvedValueOnce(
          new Response(JSON.stringify({ success: true }), { status: 200 })
        )

      const result = await syncManager.processOutbox()

      expect(result.successCount).toBe(1)
      expect(mockFetch).toHaveBeenCalledTimes(3)
      expect(mockOutboxManager.removeMutation).toHaveBeenCalledWith(1)
    })

    it("should stop retrying after max attempts", async () => {
      const mockMutation: OfflineMutationRecord = {
        id: 1,
        endpoint: "/api/max-retry-test",
        method: "POST",
        payload: { data: "max-retry" },
        timestamp: new Date().toISOString(),
        mutationKey: "max-retry-test",
      }

      mockOutboxManager.getPendingMutations.mockResolvedValue([mockMutation])

      // Fail all attempts (maxRetries = 2, so 3 total attempts)
      mockFetch.mockRejectedValue(new Error("Persistent network error"))

      const result = await syncManager.processOutbox()

      expect(result.failedCount).toBe(1)
      expect(mockFetch).toHaveBeenCalledTimes(3) // Initial + 2 retries
      expect(mockOutboxManager.removeMutation).not.toHaveBeenCalled()
    })

    it("should not retry non-retryable errors", async () => {
      const mockMutation: OfflineMutationRecord = {
        id: 1,
        endpoint: "/api/non-retryable",
        method: "POST",
        payload: { data: "non-retryable" },
        timestamp: new Date().toISOString(),
        mutationKey: "non-retryable",
      }

      mockOutboxManager.getPendingMutations.mockResolvedValue([mockMutation])

      // Return 404 (client error - non-retryable)
      mockFetch.mockResolvedValue(new Response("Not Found", { status: 404 }))

      const result = await syncManager.processOutbox()

      expect(result.failedCount).toBe(1)
      expect(mockFetch).toHaveBeenCalledTimes(1) // No retries
    })
  })

  describe("Event System", () => {
    it("should emit sync-started event", async () => {
      mockOutboxManager.getPendingMutations.mockResolvedValue([])

      const eventListener = vi.fn()
      syncManager.addEventListener("sync-started", eventListener)

      await syncManager.processOutbox()

      expect(eventListener).toHaveBeenCalledWith({
        type: "sync-started",
        timestamp: expect.any(Number),
        data: { operation: expect.any(Object) },
      })
    })

    it("should emit sync-completed event", async () => {
      mockOutboxManager.getPendingMutations.mockResolvedValue([])

      const eventListener = vi.fn()
      syncManager.addEventListener("sync-completed", eventListener)

      await syncManager.processOutbox()

      expect(eventListener).toHaveBeenCalledWith({
        type: "sync-completed",
        timestamp: expect.any(Number),
        data: { operation: expect.any(Object) },
      })
    })

    it("should emit mutation-synced event for successful mutations", async () => {
      const mockMutation: OfflineMutationRecord = {
        id: 1,
        endpoint: "/api/success",
        method: "POST",
        payload: { data: "success" },
        timestamp: new Date().toISOString(),
        mutationKey: "success",
      }

      mockOutboxManager.getPendingMutations.mockResolvedValue([mockMutation])
      mockFetch.mockResolvedValue(
        new Response(JSON.stringify({ success: true }), { status: 200 })
      )

      const eventListener = vi.fn()
      syncManager.addEventListener("mutation-synced", eventListener)

      await syncManager.processOutbox()

      expect(eventListener).toHaveBeenCalledWith({
        type: "mutation-synced",
        timestamp: expect.any(Number),
        data: {
          mutation: mockMutation,
          result: expect.objectContaining({
            status: SyncStatus.COMPLETED,
          }),
        },
      })
    })

    it("should emit mutation-failed event for failed mutations", async () => {
      const mockMutation: OfflineMutationRecord = {
        id: 1,
        endpoint: "/api/failure",
        method: "POST",
        payload: { data: "failure" },
        timestamp: new Date().toISOString(),
        mutationKey: "failure",
      }

      mockOutboxManager.getPendingMutations.mockResolvedValue([mockMutation])
      mockFetch.mockResolvedValue(new Response("Server Error", { status: 500 }))

      const eventListener = vi.fn()
      syncManager.addEventListener("mutation-failed", eventListener)

      await syncManager.processOutbox()

      expect(eventListener).toHaveBeenCalledWith({
        type: "mutation-failed",
        timestamp: expect.any(Number),
        data: {
          mutation: mockMutation,
          result: expect.objectContaining({
            status: SyncStatus.FAILED,
          }),
          error: expect.any(Error),
        },
      })
    })

    it("should handle event listener errors gracefully", async () => {
      mockOutboxManager.getPendingMutations.mockResolvedValue([])

      const faultyListener = vi.fn().mockImplementation(() => {
        throw new Error("Listener error")
      })

      syncManager.addEventListener("sync-started", faultyListener)

      // Should not throw despite listener error
      await expect(syncManager.processOutbox()).resolves.toBeDefined()
      expect(faultyListener).toHaveBeenCalled()
    })

    it("should support unsubscribing from events", async () => {
      mockOutboxManager.getPendingMutations.mockResolvedValue([])

      const eventListener = vi.fn()
      const unsubscribe = syncManager.addEventListener(
        "sync-started",
        eventListener
      )

      unsubscribe()

      await syncManager.processOutbox()

      expect(eventListener).not.toHaveBeenCalled()
    })
  })

  describe("Concurrency Control", () => {
    it("should process mutations in batches based on maxConcurrentSyncs", async () => {
      const mockMutations: OfflineMutationRecord[] = Array.from(
        { length: 5 },
        (_, i) => ({
          id: i + 1,
          endpoint: `/api/test${i + 1}`,
          method: "POST",
          payload: { data: `test${i + 1}` },
          timestamp: new Date().toISOString(),
          mutationKey: `test-${i + 1}`,
        })
      )

      mockOutboxManager.getPendingMutations.mockResolvedValue(mockMutations)
      mockFetch.mockImplementation(() =>
        Promise.resolve(
          new Response(JSON.stringify({ success: true }), { status: 200 })
        )
      )

      const result = await syncManager.processOutbox()

      expect(result.successCount).toBe(5)
      expect(mockFetch).toHaveBeenCalledTimes(5)

      // All mutations should have been processed
      for (let i = 1; i <= 5; i++) {
        expect(mockOutboxManager.removeMutation).toHaveBeenCalledWith(i)
      }
    })
  })

  describe("Statistics", () => {
    it("should provide sync manager statistics", async () => {
      const stats = await syncManager.getStats()

      expect(stats).toEqual({
        outbox: expect.objectContaining({
          isSupported: true,
          cacheSize: 0,
          outboxCount: 0,
        }),
        network: expect.objectContaining({
          isOnline: true,
          quality: ConnectionQuality.enum.Fast,
          isStable: true,
        }),
        sync: expect.objectContaining({
          isProcessing: false,
          currentOperation: null,
        }),
      })
    })
  })

  describe("Configuration Options", () => {
    it("should use custom fetch function when provided", async () => {
      const customFetch = vi
        .fn()
        .mockResolvedValue(
          new Response(JSON.stringify({ success: true }), { status: 200 })
        )

      syncManager.destroy()
      syncManager = new SyncManager({
        fetchFn: customFetch,
        enableLogging: false,
      })

      const mockMutation: OfflineMutationRecord = {
        id: 1,
        endpoint: "/api/custom-fetch",
        method: "POST",
        payload: { data: "custom" },
        timestamp: new Date().toISOString(),
        mutationKey: "custom-fetch",
      }

      mockOutboxManager.getPendingMutations.mockResolvedValue([mockMutation])

      await syncManager.processOutbox()

      expect(customFetch).toHaveBeenCalled()
      expect(mockFetch).not.toHaveBeenCalled()
    })

    it("should use custom base URL when provided", async () => {
      const customBaseUrl = "https://api.example.com"

      syncManager.destroy()
      syncManager = new SyncManager({
        baseUrl: customBaseUrl,
        enableLogging: false,
      })

      const mockMutation: OfflineMutationRecord = {
        id: 1,
        endpoint: "/api/test",
        method: "POST",
        payload: { data: "test" },
        timestamp: new Date().toISOString(),
        mutationKey: "test",
      }

      mockOutboxManager.getPendingMutations.mockResolvedValue([mockMutation])
      mockFetch.mockResolvedValue(
        new Response(JSON.stringify({ success: true }), { status: 200 })
      )

      await syncManager.processOutbox()

      expect(mockFetch).toHaveBeenCalledWith(
        `${customBaseUrl}/api/test`,
        expect.any(Object)
      )
    })
  })

  describe("Cleanup", () => {
    it("should cleanup resources when destroyed", () => {
      const unsubscribeMock = vi.fn()
      vi.mocked(useNetworkStatusStore.subscribe).mockReturnValue(
        unsubscribeMock
      )

      const testSyncManager = new SyncManager({ enableLogging: false })
      testSyncManager.destroy()

      expect(unsubscribeMock).toHaveBeenCalled()
    })

    it("should clear event listeners when destroyed", () => {
      const eventListener = vi.fn()
      syncManager.addEventListener("sync-started", eventListener)

      syncManager.destroy()

      // Event listeners should be cleared (can't easily test, but destroy should not throw)
      expect(() => syncManager.destroy()).not.toThrow()
    })
  })
})
