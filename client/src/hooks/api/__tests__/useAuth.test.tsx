import { useAuthStore } from "@/stores/authStore"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { renderHook } from "@testing-library/react"
import { beforeEach, describe, expect, it } from "vitest"

import {
  useCurrentUser,
  useLogin,
  useLogout,
  useRegister,
} from "@/hooks/api/useAuth"

// Mock the token manager
vi.mock("@/lib/auth/tokenManager", () => ({
  TokenManager: {
    setAccessToken: vi.fn(),
    clearTokens: vi.fn(),
    initializeTokens: vi.fn(),
  },
}))

// Mock the API client instead of the hooks
vi.mock("@/lib/api/client", () => ({
  apiClient: {
    login: vi.fn(),
    logout: vi.fn(),
    register: vi.fn(),
    getCurrentUser: vi.fn(),
    clearAuthToken: vi.fn(),
    setAuthToken: vi.fn(),
  },
}))

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
  TestWrapper.displayName = "TestWrapper"
  return TestWrapper
}

describe("useAuth Hook", () => {
  beforeEach(() => {
    // Reset auth store
    useAuthStore.getState().clearAuth()
    vi.clearAllMocks()

    // We don't need to mock the hooks since we're testing them directly
    // The API client is already mocked above
  })

  it("should have auth hooks available", () => {
    expect(useLogin).toBeDefined()
    expect(useRegister).toBeDefined()
    expect(useLogout).toBeDefined()
    expect(useCurrentUser).toBeDefined()
  })

  it("useLogin hook should work", () => {
    const { result } = renderHook(() => useLogin(), {
      wrapper: createWrapper(),
    })

    expect(result.current.mutate).toBeDefined()
    expect(result.current.isPending).toBe(false)
    expect(result.current.error).toBeNull()
  })

  it("useRegister hook should work", () => {
    const { result } = renderHook(() => useRegister(), {
      wrapper: createWrapper(),
    })

    expect(result.current.mutate).toBeDefined()
    expect(result.current.isPending).toBe(false)
    expect(result.current.error).toBeNull()
  })

  it("useLogout hook should work", () => {
    const { result } = renderHook(() => useLogout(), {
      wrapper: createWrapper(),
    })

    expect(result.current.mutate).toBeDefined()
    expect(result.current.isPending).toBe(false)
    expect(result.current.error).toBeNull()
  })

  it("useCurrentUser hook should work", () => {
    const { result } = renderHook(() => useCurrentUser(), {
      wrapper: createWrapper(),
    })

    expect(result.current.isLoading).toBeDefined()
    expect(result.current.error).toBeDefined()
  })
})
