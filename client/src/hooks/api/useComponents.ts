/**
 * Component Management React Query Hooks
 *
 * Provides React Query hooks for component management operations
 */

import type {
  ComponentAdvancedSearch,
  ComponentBulkCreate,
  ComponentBulkUpdate,
  ComponentCreate,
  ComponentExportParams,
  ComponentImportData,
  ComponentListParams,
  ComponentUpdate,
} from "../../lib/api/types/components"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import { componentsApi } from "../../lib/api/endpoints"
import { QueryKeys } from "../../lib/api/keys"

/**
 * Component CRUD hooks
 */
export function useComponents(params?: ComponentListParams) {
  return useQuery({
    queryKey: QueryKeys.components.list(params as Record<string, unknown>),
    queryFn: () => componentsApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useComponentsSummary(params?: ComponentListParams) {
  return useQuery({
    queryKey: QueryKeys.components.summary(params as Record<string, unknown>),
    queryFn: () => componentsApi.listSummary(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useComponent(id: number) {
  return useQuery({
    queryKey: QueryKeys.components.detail(id),
    queryFn: () => componentsApi.get(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useCreateComponent() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "create"] as const,
    mutationFn: (data: ComponentCreate) => componentsApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
    },
  })
}

export function useUpdateComponent() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "update"] as const,
    mutationFn: ({ id, data }: { id: number; data: ComponentUpdate }) =>
      componentsApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.detail(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
    },
  })
}

export function useDeleteComponent() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "delete"] as const,
    mutationFn: (id: number) => componentsApi.delete(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: QueryKeys.components.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
    },
  })
}

/**
 * Component search hooks
 */
export function useSearchComponents() {
  return useMutation({
    mutationFn: (criteria: ComponentAdvancedSearch) =>
      componentsApi.search(criteria),
  })
}

export function useQuickSearchComponents(query: string, limit?: number) {
  return useQuery({
    queryKey: QueryKeys.components.quickSearch(query),
    queryFn: () => componentsApi.quickSearch(query, limit),
    enabled: !!query && query.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Component bulk operations hooks
 */
export function useBulkCreateComponents() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "bulkCreate"] as const,
    mutationFn: (data: ComponentBulkCreate) => componentsApi.bulkCreate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
    },
  })
}

export function useBulkUpdateComponents() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "bulkUpdate"] as const,
    mutationFn: (data: ComponentBulkUpdate) => componentsApi.bulkUpdate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
    },
  })
}

export function useBulkDeleteComponents() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "bulkDelete"] as const,
    mutationFn: (componentIds: number[]) =>
      componentsApi.bulkDelete(componentIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
    },
  })
}

/**
 * Component validation hooks
 */
export function useValidateComponents() {
  return useMutation({
    mutationKey: ["components", "validate"] as const,
    mutationFn: (data: ComponentCreate[]) => componentsApi.validate(data),
  })
}

export function useValidateComponent() {
  return useMutation({
    mutationKey: ["components", "validateSingle"] as const,
    mutationFn: (data: ComponentCreate) => componentsApi.validateSingle(data),
  })
}

/**
 * Component import/export hooks
 */
export function useImportComponents() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "import"] as const,
    mutationFn: (data: ComponentImportData[]) => componentsApi.import(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
    },
  })
}

export function useExportComponents() {
  return useMutation({
    mutationFn: (params: ComponentExportParams) => componentsApi.export(params),
  })
}

/**
 * Component statistics hooks
 */
export function useComponentStats() {
  return useQuery({
    queryKey: QueryKeys.components.stats,
    queryFn: () => componentsApi.getStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useComponentStatsByCategory() {
  return useQuery({
    queryKey: QueryKeys.components.statsByCategory,
    queryFn: () => componentsApi.getStatsByCategory(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useComponentStatsByManufacturer() {
  return useQuery({
    queryKey: QueryKeys.components.statsByManufacturer,
    queryFn: () => componentsApi.getStatsByManufacturer(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Component preferences hooks
 */
export function useMarkComponentAsPreferred() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "markAsPreferred"] as const,
    mutationFn: (id: number) => componentsApi.markAsPreferred(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.detail(id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.preferred(),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
    },
  })
}

export function useUnmarkComponentAsPreferred() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "unmarkAsPreferred"] as const,
    mutationFn: (id: number) => componentsApi.unmarkAsPreferred(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.detail(id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.preferred(),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
    },
  })
}

export function usePreferredComponents(params?: ComponentListParams) {
  return useQuery({
    queryKey: QueryKeys.components.preferred(params as Record<string, unknown>),
    queryFn: () => componentsApi.getPreferred(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Component stock management hooks
 */
export function useUpdateComponentStock() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "updateStock"] as const,
    mutationFn: ({ id, status }: { id: number; status: string }) =>
      componentsApi.updateStock(id, status),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.detail(id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
    },
  })
}

export function useComponentsByStockStatus(
  status: string,
  params?: ComponentListParams
) {
  return useQuery({
    queryKey: QueryKeys.components.byStock(
      status,
      params as Record<string, unknown>
    ),
    queryFn: () => componentsApi.getByStockStatus(status, params),
    enabled: !!status,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Component specifications hooks
 */
export function useUpdateComponentSpecifications() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "updateSpecifications"] as const,
    mutationFn: ({
      id,
      specifications,
    }: {
      id: number
      specifications: Record<string, unknown>
    }) => componentsApi.updateSpecifications(id, specifications),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.detail(id),
      })
    },
  })
}

export function useComponentSpecificationKeys() {
  return useQuery({
    queryKey: QueryKeys.components.specificationKeys,
    queryFn: () => componentsApi.getSpecificationKeys(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  })
}

/**
 * Component relationships hooks
 */
export function useSimilarComponents(id: number, limit?: number) {
  return useQuery({
    queryKey: QueryKeys.components.similar(id),
    queryFn: () => componentsApi.getSimilar(id, limit),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useAlternativeComponents(id: number) {
  return useQuery({
    queryKey: QueryKeys.components.alternatives(id),
    queryFn: () => componentsApi.getAlternatives(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Component versioning hooks
 */
export function useComponentVersions(id: number) {
  return useQuery({
    queryKey: QueryKeys.components.versions(id),
    queryFn: () => componentsApi.getVersions(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useCreateComponentVersion() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "createVersion"] as const,
    mutationFn: ({ id, data }: { id: number; data: ComponentUpdate }) =>
      componentsApi.createVersion(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.versions(id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.detail(id),
      })
    },
  })
}

/**
 * Component attachments hooks
 */
export function useComponentAttachments(id: number) {
  return useQuery({
    queryKey: QueryKeys.components.attachments(id),
    queryFn: () => componentsApi.getAttachments(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useUploadComponentAttachment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "uploadAttachment"] as const,
    mutationFn: ({ id, file }: { id: number; file: File }) =>
      componentsApi.uploadAttachment(id, file),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.attachments(id),
      })
    },
  })
}

export function useDeleteComponentAttachment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["components", "deleteAttachment"] as const,
    mutationFn: ({ id, attachmentId }: { id: number; attachmentId: number }) =>
      componentsApi.deleteAttachment(id, attachmentId),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.components.attachments(id),
      })
    },
  })
}
