/**
 * User Management React Query Hooks
 *
 * Provides React Query hooks for user management operations (admin)
 */

import type {
  UserAdvancedSearch,
  UserBulkCreate,
  UserBulkDelete,
  UserBulkUpdate,
  UserCreate,
  UserExportParams,
  UserImportData,
  UserListParams,
  UserNotificationPreferences,
  UserProfileUpdate,
  UserRoleAssignmentCreate,
  UserRoleAssignmentUpdate,
  UserRoleCreate,
  UserRoleUpdate,
  UserSearchParams,
  UserUpdate,
} from "../../lib/api/types/users"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import { usersApi } from "../../lib/api/endpoints"
import { QueryKeys } from "../../lib/api/keys"

/**
 * User CRUD hooks
 */
export function useUsers(params?: UserListParams) {
  return useQuery({
    queryKey: QueryKeys.users.list(params as Record<string, unknown>),
    queryFn: () => usersApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useUser(id: number) {
  return useQuery({
    queryKey: QueryKeys.users.detail(id),
    queryFn: () => usersApi.get(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useCreateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["users", "create"] as const,
    mutationFn: (data: UserCreate) => usersApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

export function useUpdateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["users", "update"] as const,
    mutationFn: ({ id, data }: { id: number; data: UserUpdate }) =>
      usersApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

export function useDeleteUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["users", "delete"] as const,
    mutationFn: (id: number) => usersApi.delete(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: QueryKeys.users.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

/**
 * User search hooks
 */
export function useSearchUsers(params: UserSearchParams) {
  return useQuery({
    queryKey: ["users", "search", params],
    queryFn: () => usersApi.search(params),
    enabled: !!params.query || Object.keys(params).length > 1,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useAdvancedSearchUsers() {
  return useMutation({
    mutationFn: (criteria: UserAdvancedSearch) =>
      usersApi.advancedSearch(criteria),
  })
}

/**
 * User statistics hooks
 */
export function useUserStats() {
  return useQuery({
    queryKey: ["users", "stats"],
    queryFn: () => usersApi.getStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * User bulk operations hooks
 */
export function useBulkCreateUsers() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UserBulkCreate) => usersApi.bulkCreate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

export function useBulkUpdateUsers() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UserBulkUpdate) => usersApi.bulkUpdate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

export function useBulkDeleteUsers() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UserBulkDelete) => usersApi.bulkDelete(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

/**
 * User import/export hooks
 */
export function useImportUsers() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UserImportData[]) => usersApi.import(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

export function useExportUsers() {
  return useMutation({
    mutationFn: (params: UserExportParams) => usersApi.export(params),
  })
}

/**
 * User profile management hooks (admin view)
 */
export function useUserProfile(id: number) {
  return useQuery({
    queryKey: ["users", "profile", id],
    queryFn: () => usersApi.getProfile(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useUpdateUserProfile() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UserProfileUpdate }) =>
      usersApi.updateProfile(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["users", "profile", id] })
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.detail(id) })
    },
  })
}

/**
 * User notification preferences hooks
 */
export function useUserNotificationPreferences(id: number) {
  return useQuery({
    queryKey: ["users", "notificationPreferences", id],
    queryFn: () => usersApi.getNotificationPreferences(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useUpdateUserNotificationPreferences() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number
      data: UserNotificationPreferences
    }) => usersApi.updateNotificationPreferences(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: ["users", "notificationPreferences", id],
      })
    },
  })
}

/**
 * User security management hooks
 */
export function useUserSecuritySettings(id: number) {
  return useQuery({
    queryKey: ["users", "securitySettings", id],
    queryFn: () => usersApi.getSecuritySettings(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useResetUserPassword() {
  return useMutation({
    mutationFn: (id: number) => usersApi.resetPassword(id),
  })
}

export function useDisableUserTwoFactor() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => usersApi.disableTwoFactor(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: ["users", "securitySettings", id],
      })
    },
  })
}

export function useRevokeUserSessions() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => usersApi.revokeAllSessions(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: ["users", "securitySettings", id],
      })
    },
  })
}

/**
 * User activation/deactivation hooks
 */
export function useActivateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => usersApi.activate(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

export function useDeactivateUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => usersApi.deactivate(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.detail(id) })
      queryClient.invalidateQueries({ queryKey: QueryKeys.users.all })
    },
  })
}

/**
 * User role management hooks
 */
export function useUserRoles(params?: { page?: number; size?: number }) {
  return useQuery({
    queryKey: ["users", "roles", params],
    queryFn: () => usersApi.getRoles(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useCreateUserRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UserRoleCreate) => usersApi.createRole(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users", "roles"] })
    },
  })
}

export function useUpdateUserRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UserRoleUpdate }) =>
      usersApi.updateRole(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users", "roles"] })
    },
  })
}

export function useDeleteUserRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => usersApi.deleteRole(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users", "roles"] })
    },
  })
}

/**
 * User role assignment hooks
 */
export function useUserRoleAssignments(userId: number) {
  return useQuery({
    queryKey: ["users", "roleAssignments", userId],
    queryFn: () => usersApi.getUserRoles(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useAssignUserRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (data: UserRoleAssignmentCreate) => usersApi.assignRole(data),
    onSuccess: (_, data) => {
      queryClient.invalidateQueries({
        queryKey: ["users", "roleAssignments", data.user_id],
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.users.detail(data.user_id),
      })
    },
  })
}

export function useUpdateUserRoleAssignment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number
      data: UserRoleAssignmentUpdate
    }) => usersApi.updateRoleAssignment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users", "roleAssignments"] })
    },
  })
}

export function useRemoveUserRoleAssignment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (id: number) => usersApi.removeRoleAssignment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users", "roleAssignments"] })
    },
  })
}

/**
 * User activity monitoring hooks
 */
export function useUserActivity(
  userId: number,
  params?: { page?: number; size?: number }
) {
  return useQuery({
    queryKey: QueryKeys.users.activities(userId),
    queryFn: () => usersApi.getUserActivity(userId, params),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useUserSessions(userId: number) {
  return useQuery({
    queryKey: ["users", "sessions", userId],
    queryFn: () => usersApi.getUserSessions(userId),
    enabled: !!userId,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}
