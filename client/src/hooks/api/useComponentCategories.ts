/**
 * Component Category Management React Query Hooks
 *
 * Provides React Query hooks for component category management operations
 */

import type {
  ComponentCategoryCreate,
  ComponentCategoryListParams,
  ComponentCategoryUpdate,
} from "../../lib/api/types/components"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import { componentCategoriesApi } from "../../lib/api/endpoints"
import { MutationKeys, QueryKeys } from "../../lib/api/keys"

/**
 * Component category CRUD hooks
 */
export function useComponentCategories(params?: ComponentCategoryListParams) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.list(
      params as Record<string, unknown>
    ),
    queryFn: () => componentCategoriesApi.list(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useComponentCategoriesSummary(
  params?: ComponentCategoryListParams
) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.summary(
      params as Record<string, unknown>
    ),
    queryFn: () => componentCategoriesApi.listSummary(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useComponentCategory(id: number) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.detail(id),
    queryFn: () => componentCategoriesApi.get(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export function useCreateComponentCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentCategories.create,
    mutationFn: (data: ComponentCategoryCreate) =>
      componentCategoriesApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.stats,
      })
    },
  })
}

export function useUpdateComponentCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["componentCategories", "update"] as const,
    mutationFn: ({ id, data }: { id: number; data: ComponentCategoryUpdate }) =>
      componentCategoriesApi.update(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.detail(id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
    },
  })
}

export function useDeleteComponentCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["componentCategories", "delete"] as const,
    mutationFn: (id: number) => componentCategoriesApi.delete(id),
    onSuccess: (_, id) => {
      queryClient.removeQueries({
        queryKey: QueryKeys.componentCategories.detail(id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.stats,
      })
    },
  })
}

/**
 * Hierarchical operations hooks
 */
export function useComponentCategoryTree() {
  return useQuery({
    queryKey: QueryKeys.componentCategories.tree,
    queryFn: () => componentCategoriesApi.getTree(),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export function useComponentCategorySubtree(parentId: number) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.subtree(parentId),
    queryFn: () => componentCategoriesApi.getSubtree(parentId),
    enabled: !!parentId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useRootComponentCategories() {
  return useQuery({
    queryKey: QueryKeys.componentCategories.roots,
    queryFn: () => componentCategoriesApi.getRootCategories(),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export function useComponentCategoryChildren(parentId: number) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.children(parentId),
    queryFn: () => componentCategoriesApi.getChildren(parentId),
    enabled: !!parentId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useComponentCategoryParents(id: number) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.parents(id),
    queryFn: () => componentCategoriesApi.getParents(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export function useComponentCategoryPath(id: number) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.path(id),
    queryFn: () => componentCategoriesApi.getPath(id),
    enabled: !!id,
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

/**
 * Category search hooks
 */
export function useSearchComponentCategories(query: string) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.search(query),
    queryFn: () => componentCategoriesApi.search(query),
    enabled: !!query && query.length >= 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useComponentCategoriesByLevel(level: number) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.byLevel(level),
    queryFn: () => componentCategoriesApi.searchByLevel(level),
    enabled: level >= 0,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Category statistics hooks
 */
export function useComponentCategoryStats() {
  return useQuery({
    queryKey: QueryKeys.componentCategories.stats,
    queryFn: () => componentCategoriesApi.getStats(),
    staleTime: 15 * 60 * 1000, // 15 minutes
  })
}

export function useComponentCategoryStatsDetail(id: number) {
  return useQuery({
    queryKey: QueryKeys.componentCategories.categoryStats(id),
    queryFn: () => componentCategoriesApi.getCategoryStats(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

/**
 * Category bulk operations hooks
 */
export function useBulkCreateComponentCategories() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentCategories.bulkCreate,
    mutationFn: (categories: ComponentCategoryCreate[]) =>
      componentCategoriesApi.bulkCreate(categories),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.stats,
      })
    },
  })
}

export function useBulkUpdateComponentCategories() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentCategories.bulkUpdate,
    mutationFn: (
      updates: Array<{ id: number; data: ComponentCategoryUpdate }>
    ) => componentCategoriesApi.bulkUpdate(updates),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
    },
  })
}

export function useBulkDeleteComponentCategories() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentCategories.bulkDelete,
    mutationFn: (categoryIds: number[]) =>
      componentCategoriesApi.bulkDelete(categoryIds),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.stats,
      })
    },
  })
}

/**
 * Category validation hooks
 */
export function useValidateComponentCategoryHierarchy() {
  return useQuery({
    queryKey: ["componentCategories", "validateHierarchy"],
    queryFn: () => componentCategoriesApi.validateHierarchy(),
    enabled: false, // Only run when explicitly called
  })
}

export function useValidateComponentCategoryMove() {
  return useMutation({
    mutationFn: ({
      categoryId,
      newParentId,
    }: {
      categoryId: number
      newParentId: number | null
    }) => componentCategoriesApi.validateMove(categoryId, newParentId),
  })
}

/**
 * Category reorganization hooks
 */
export function useMoveComponentCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["componentCategories", "move"] as const,
    mutationFn: ({
      categoryId,
      newParentId,
    }: {
      categoryId: number
      newParentId: number | null
    }) => componentCategoriesApi.move(categoryId, newParentId),
    onSuccess: (_, { categoryId }) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.detail(categoryId),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
    },
  })
}

export function useReorderComponentCategories() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentCategories.reorder,
    mutationFn: ({
      parentId,
      categoryIds,
    }: {
      parentId: number | null
      categoryIds: number[]
    }) => componentCategoriesApi.reorder(parentId, categoryIds),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
    },
  })
}

/**
 * Category import/export hooks
 */
export function useExportComponentCategories() {
  return useMutation({
    mutationFn: (format: "csv" | "xlsx" | "json" = "json") =>
      componentCategoriesApi.export(format),
  })
}

export function useImportComponentCategories() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.componentCategories.import,
    mutationFn: (
      data: Array<{
        name: string
        description?: string
        parent_category_name?: string
      }>
    ) => componentCategoriesApi.import(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.tree,
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.stats,
      })
    },
  })
}

/**
 * Category activation/deactivation hooks
 */
export function useActivateComponentCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["componentCategories", "activate"] as const,
    mutationFn: (id: number) => componentCategoriesApi.activate(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.detail(id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
    },
  })
}

export function useDeactivateComponentCategory() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: ["componentCategories", "deactivate"] as const,
    mutationFn: (id: number) => componentCategoriesApi.deactivate(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.detail(id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.componentCategories.all,
      })
    },
  })
}
