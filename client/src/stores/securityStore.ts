/**
 * Security Store for FR-1 Security Features
 * 
 * This module implements the Zustand store for managing security-related state
 * including account lockout, email verification, and password reset states.
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { EmailVerificationState } from '@/components/modules/auth/types/emailVerification';
import type { PasswordResetState } from '@/components/modules/auth/types/passwordReset';
import type { AccountLockoutState } from '@/components/modules/auth/types/security';

/**
 * Complete security state interface
 */
export interface SecurityState {
  // Account Lockout State
  lockoutState: AccountLockoutState;
  
  // Email Verification State
  emailVerificationState: EmailVerificationState;
  
  // Password Reset State
  passwordResetState: PasswordResetState;
  
  // Actions for lockout state
  setLockoutState: (state: Partial<AccountLockoutState>) => void;
  updateFailedAttempts: (attempts: number, attemptsRemaining: number) => void;
  setAccountLocked: (lockoutExpiresAt: Date) => void;
  clearLockoutState: () => void;
  
  // Actions for email verification state
  setEmailVerificationState: (state: Partial<EmailVerificationState>) => void;
  setVerificationSent: (email: string) => void;
  setVerificationResendCooldown: (availableAt: Date) => void;
  clearEmailVerificationState: () => void;
  
  // Actions for password reset state
  setPasswordResetState: (state: Partial<PasswordResetState>) => void;
  setResetEmailSent: (email: string) => void;
  setResetToken: (token: string, expiresAt: Date) => void;
  clearPasswordResetState: () => void;
  
  // Global actions
  resetAllSecurityState: () => void;
}

/**
 * Initial state values
 */
const initialLockoutState: AccountLockoutState = {
  isLocked: false,
  lockoutExpiresAt: null,
  failedAttempts: 0,
  attemptsRemaining: 5
};

const initialEmailVerificationState: EmailVerificationState = {
  isVerificationSent: false,
  sentToEmail: null,
  canResend: true,
  resendAvailableAt: null
};

const initialPasswordResetState: PasswordResetState = {
  isResetSent: false,
  sentToEmail: null,
  resetToken: null,
  resetExpiresAt: null
};

/**
 * Security store implementation
 */
export const useSecurityStore = create<SecurityState>()(
  devtools(
    persist(
      (set) => ({
        // Initial state
        lockoutState: initialLockoutState,
        emailVerificationState: initialEmailVerificationState,
        passwordResetState: initialPasswordResetState,
        
        // Lockout state actions
        setLockoutState: (state) =>
          set(
            (current) => ({
              lockoutState: { ...current.lockoutState, ...state }
            }),
            false,
            'setLockoutState'
          ),
        
        updateFailedAttempts: (attempts, attemptsRemaining) =>
          set(
            (current) => ({
              lockoutState: {
                ...current.lockoutState,
                failedAttempts: attempts,
                attemptsRemaining: attemptsRemaining,
                isLocked: attemptsRemaining <= 0
              }
            }),
            false,
            'updateFailedAttempts'
          ),
        
        setAccountLocked: (lockoutExpiresAt) =>
          set(
            (current) => ({
              lockoutState: {
                ...current.lockoutState,
                isLocked: true,
                lockoutExpiresAt,
                attemptsRemaining: 0
              }
            }),
            false,
            'setAccountLocked'
          ),
        
        clearLockoutState: () =>
          set(
            { lockoutState: initialLockoutState },
            false,
            'clearLockoutState'
          ),
        
        // Email verification state actions
        setEmailVerificationState: (state) =>
          set(
            (current) => ({
              emailVerificationState: { ...current.emailVerificationState, ...state }
            }),
            false,
            'setEmailVerificationState'
          ),
        
        setVerificationSent: (email) =>
          set(
            (current) => ({
              emailVerificationState: {
                ...current.emailVerificationState,
                isVerificationSent: true,
                sentToEmail: email,
                canResend: false,
                resendAvailableAt: new Date(Date.now() + 60000) // 1 minute cooldown
              }
            }),
            false,
            'setVerificationSent'
          ),
        
        setVerificationResendCooldown: (availableAt) =>
          set(
            (current) => ({
              emailVerificationState: {
                ...current.emailVerificationState,
                canResend: false,
                resendAvailableAt: availableAt
              }
            }),
            false,
            'setVerificationResendCooldown'
          ),
        
        clearEmailVerificationState: () =>
          set(
            { emailVerificationState: initialEmailVerificationState },
            false,
            'clearEmailVerificationState'
          ),
        
        // Password reset state actions
        setPasswordResetState: (state) =>
          set(
            (current) => ({
              passwordResetState: { ...current.passwordResetState, ...state }
            }),
            false,
            'setPasswordResetState'
          ),
        
        setResetEmailSent: (email) =>
          set(
            (current) => ({
              passwordResetState: {
                ...current.passwordResetState,
                isResetSent: true,
                sentToEmail: email
              }
            }),
            false,
            'setResetEmailSent'
          ),
        
        setResetToken: (token, expiresAt) =>
          set(
            (current) => ({
              passwordResetState: {
                ...current.passwordResetState,
                resetToken: token,
                resetExpiresAt: expiresAt
              }
            }),
            false,
            'setResetToken'
          ),
        
        clearPasswordResetState: () =>
          set(
            { passwordResetState: initialPasswordResetState },
            false,
            'clearPasswordResetState'
          ),
        
        // Global reset action
        resetAllSecurityState: () =>
          set(
            {
              lockoutState: initialLockoutState,
              emailVerificationState: initialEmailVerificationState,
              passwordResetState: initialPasswordResetState
            },
            false,
            'resetAllSecurityState'
          )
      }),
      {
        name: 'security-store',
        // Only persist certain security state (not sensitive tokens)
        partialize: (state) => ({
          lockoutState: {
            isLocked: state.lockoutState.isLocked,
            lockoutExpiresAt: state.lockoutState.lockoutExpiresAt,
            failedAttempts: state.lockoutState.failedAttempts,
            attemptsRemaining: state.lockoutState.attemptsRemaining
          },
          emailVerificationState: {
            isVerificationSent: state.emailVerificationState.isVerificationSent,
            sentToEmail: state.emailVerificationState.sentToEmail,
            canResend: state.emailVerificationState.canResend,
            resendAvailableAt: state.emailVerificationState.resendAvailableAt
          },
          passwordResetState: {
            isResetSent: state.passwordResetState.isResetSent,
            sentToEmail: state.passwordResetState.sentToEmail,
            // Don't persist tokens for security
            resetToken: null,
            resetExpiresAt: null
          }
        }),
        // Storage configuration with date serialization
        storage: {
          getItem: (name: string) => {
            const value = localStorage.getItem(name);
            if (!value) return null;
            return JSON.parse(value, (_key, val) => {
              if (val && typeof val === 'object' && val.__type === 'Date') {
                return new Date(val.value);
              }
              return val;
            });
          },
          setItem: (name: string, value: any) => {
            const serialized = JSON.stringify(value, (_key, val) => {
              if (val instanceof Date) {
                return { __type: 'Date', value: val.toISOString() };
              }
              return val;
            });
            localStorage.setItem(name, serialized);
          },
          removeItem: (name: string) => localStorage.removeItem(name)
        },
        // Version for migration support
        version: 1
      }
    ),
    {
      name: 'security-store-devtools',
      enabled: process.env.NODE_ENV === 'development'
    }
  )
);

/**
 * Security store selectors for convenient access
 */
export const useSecuritySelectors = {
  // Lockout selectors
  useLockoutState: () => useSecurityStore((state) => state.lockoutState),
  useIsAccountLocked: () => useSecurityStore((state) => state.lockoutState.isLocked),
  useAttemptsRemaining: () => useSecurityStore((state) => state.lockoutState.attemptsRemaining),
  useLockoutExpiresAt: () => useSecurityStore((state) => state.lockoutState.lockoutExpiresAt),
  
  // Email verification selectors
  useEmailVerificationState: () => useSecurityStore((state) => state.emailVerificationState),
  useIsVerificationSent: () => useSecurityStore((state) => state.emailVerificationState.isVerificationSent),
  useCanResendVerification: () => useSecurityStore((state) => state.emailVerificationState.canResend),
  
  // Password reset selectors
  usePasswordResetState: () => useSecurityStore((state) => state.passwordResetState),
  useIsResetSent: () => useSecurityStore((state) => state.passwordResetState.isResetSent),
  useResetToken: () => useSecurityStore((state) => state.passwordResetState.resetToken)
};

/**
 * Security store actions for convenient access
 */
export const useSecurityActions = () => {
  const store = useSecurityStore();
  return {
    // Lockout actions
    setLockoutState: store.setLockoutState,
    updateFailedAttempts: store.updateFailedAttempts,
    setAccountLocked: store.setAccountLocked,
    clearLockoutState: store.clearLockoutState,
    
    // Email verification actions
    setEmailVerificationState: store.setEmailVerificationState,
    setVerificationSent: store.setVerificationSent,
    setVerificationResendCooldown: store.setVerificationResendCooldown,
    clearEmailVerificationState: store.clearEmailVerificationState,
    
    // Password reset actions
    setPasswordResetState: store.setPasswordResetState,
    setResetEmailSent: store.setResetEmailSent,
    setResetToken: store.setResetToken,
    clearPasswordResetState: store.clearPasswordResetState,
    
    // Global actions
    resetAllSecurityState: store.resetAllSecurityState
  };
};