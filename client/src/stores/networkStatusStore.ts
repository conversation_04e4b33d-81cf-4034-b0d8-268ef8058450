/**
 * Network Status Store - Global online/offline state management
 *
 * This Zustand store manages the global network status and provides
 * reactive updates to components and services that need to respond
 * to online/offline transitions.
 *
 * Features:
 * - Real-time network status tracking
 * - Online/offline transition events
 * - Connection quality monitoring
 * - Historical connection data
 * - TypeScript strict mode compliance
 */

import { create } from "zustand"
import { subscribeWithSelector } from "zustand/middleware"

import { ConnectionQualityEnum, connectionQualitySchema } from "@/lib/api/types"

/**
 * Network connection information
 */
export interface NetworkConnection {
  /** Whether the device is online */
  isOnline: boolean
  /** Estimated connection quality */
  quality: ConnectionQualityEnum
  /** Effective connection type if available */
  effectiveType?: string
  /** Downlink bandwidth estimate in Mbps */
  downlink?: number
  /** Round-trip time estimate in ms */
  rtt?: number
  /** Whether connection is metered */
  saveData?: boolean
  /** Last time connection status changed */
  lastChanged: number
}

/**
 * Network status store state
 */
export interface NetworkStatusState {
  /** Current connection information */
  connection: NetworkConnection
  /** History of connection changes (last 10) */
  connectionHistory: NetworkConnection[]
  /** Total time spent online in current session (ms) */
  totalOnlineTime: number
  /** Total time spent offline in current session (ms) */
  totalOfflineTime: number
  /** Number of times went offline in current session */
  offlineTransitions: number
  /** Timestamp when current session started */
  sessionStart: number

  // Actions
  /** Update connection status */
  updateConnection: (
    isOnline: boolean,
    connectionInfo?: Partial<NetworkConnection>
  ) => void
  /** Get connection quality based on network information */
  getConnectionQuality: () => ConnectionQualityEnum
  /** Check if connection is stable (not frequently changing) */
  isConnectionStable: () => boolean
  /** Get session statistics */
  getSessionStats: () => {
    totalOnlineTime: number
    totalOfflineTime: number
    offlineTransitions: number
    uptime: number
    sessionDuration: number
  }
  /** Reset session statistics */
  resetSession: () => void
}

/**
 * Detect connection quality based on network information
 */
function detectConnectionQualityEnum(
  isOnline: boolean,
  connectionInfo?: any
): ConnectionQualityEnum {
  if (!isOnline) {
    return connectionQualitySchema.enum.Offline
  }

  // Use provided connection info first, then fallback to navigator
  const effectiveType =
    connectionInfo?.effectiveType ||
    (typeof navigator !== "undefined" && "connection" in navigator
      ? (navigator as any).connection?.effectiveType
      : undefined)

  const downlink =
    connectionInfo?.downlink !== undefined
      ? connectionInfo.downlink
      : typeof navigator !== "undefined" && "connection" in navigator
        ? (navigator as any).connection?.downlink
        : undefined

  // Check effectiveType first
  if (effectiveType) {
    switch (effectiveType) {
      case "slow-2g":
      case "2g":
        return connectionQualitySchema.enum.Slow
      case "3g":
        return connectionQualitySchema.enum.Moderate
      case "4g":
      case "5g":
        return connectionQualitySchema.enum.Fast
      default:
        break
    }
  }

  // Use downlink if available
  if (downlink !== undefined) {
    if (downlink < 1) {
      return connectionQualitySchema.enum.Slow
    } else if (downlink < 5) {
      return connectionQualitySchema.enum.Moderate
    } else {
      return connectionQualitySchema.enum.Fast
    }
  }

  // Default to moderate if online but no specific information
  return connectionQualitySchema.enum.Moderate
}

/**
 * Create network status store
 */
export const useNetworkStatusStore = create<NetworkStatusState>()(
  subscribeWithSelector((set, get) => {
    const now = Date.now()

    // Initial connection state
    const initialConnection: NetworkConnection = {
      isOnline: typeof navigator !== "undefined" ? navigator.onLine : true,
      quality: detectConnectionQualityEnum(
        typeof navigator !== "undefined" ? navigator.onLine : true
      ),
      lastChanged: now,
    }

    return {
      connection: initialConnection,
      connectionHistory: [initialConnection],
      totalOnlineTime: 0,
      totalOfflineTime: 0,
      offlineTransitions: 0,
      sessionStart: now,

      updateConnection: (
        isOnline: boolean,
        connectionInfo?: Partial<NetworkConnection>
      ) => {
        const state = get()
        const now = Date.now()
        const previousConnection = state.connection

        // Calculate time spent in previous state
        const timeDiff = now - previousConnection.lastChanged
        const newTotalOnlineTime = previousConnection.isOnline
          ? state.totalOnlineTime + timeDiff
          : state.totalOnlineTime
        const newTotalOfflineTime = !previousConnection.isOnline
          ? state.totalOfflineTime + timeDiff
          : state.totalOfflineTime

        // Count offline transitions
        const newOfflineTransitions =
          !previousConnection.isOnline && isOnline
            ? state.offlineTransitions + 1
            : state.offlineTransitions

        // Get network information if available
        let networkInfo = {}
        if (typeof navigator !== "undefined" && "connection" in navigator) {
          const connection = (navigator as any).connection
          networkInfo = {
            effectiveType: connection?.effectiveType,
            downlink: connection?.downlink,
            rtt: connection?.rtt,
            saveData: connection?.saveData,
          }
        }

        // Merge with provided connection info (prioritize provided values)
        const finalConnectionInfo = { ...networkInfo, ...connectionInfo }

        const newConnection: NetworkConnection = {
          isOnline,
          quality: detectConnectionQualityEnum(isOnline, finalConnectionInfo),
          lastChanged: now,
          ...finalConnectionInfo,
        }

        // Update history (keep last 10 entries)
        const newHistory = [...state.connectionHistory, newConnection].slice(
          -10
        )

        set({
          connection: newConnection,
          connectionHistory: newHistory,
          totalOnlineTime: newTotalOnlineTime,
          totalOfflineTime: newTotalOfflineTime,
          offlineTransitions: newOfflineTransitions,
        })

        // Log significant changes
        if (previousConnection.isOnline !== isOnline) {
          console.log(
            `[NetworkStatus] Connection changed: ${isOnline ? "ONLINE" : "OFFLINE"}`,
            {
              quality: newConnection.quality,
              previousQuality: previousConnection.quality,
              timeInPreviousState: timeDiff,
            }
          )
        }
      },

      getConnectionQuality: () => {
        return get().connection.quality
      },

      isConnectionStable: () => {
        const state = get()
        const recentHistory = state.connectionHistory.slice(-5)

        if (recentHistory.length < 2) {
          return true
        }

        // Check if connection changed more than 3 times in the last 5 records
        let changes = 0
        for (let i = 1; i < recentHistory.length; i++) {
          if (recentHistory[i].isOnline !== recentHistory[i - 1].isOnline) {
            changes++
          }
        }

        return changes <= 3
      },

      getSessionStats: () => {
        const state = get()
        const now = Date.now()
        const sessionDuration = now - state.sessionStart

        // Add current state time
        const currentStateDuration = now - state.connection.lastChanged
        const totalOnlineTime = state.connection.isOnline
          ? state.totalOnlineTime + currentStateDuration
          : state.totalOnlineTime
        const totalOfflineTime = !state.connection.isOnline
          ? state.totalOfflineTime + currentStateDuration
          : state.totalOfflineTime

        const uptime = totalOnlineTime / sessionDuration

        return {
          totalOnlineTime,
          totalOfflineTime,
          offlineTransitions: state.offlineTransitions,
          uptime,
          sessionDuration,
        }
      },

      resetSession: () => {
        const now = Date.now()
        const currentConnection = { ...get().connection, lastChanged: now }
        set({
          totalOnlineTime: 0,
          totalOfflineTime: 0,
          offlineTransitions: 0,
          sessionStart: now,
          connection: currentConnection,
          connectionHistory: [currentConnection],
        })
      },
    }
  })
)

/**
 * Hook for accessing network status with reactive updates
 */
export function useNetworkStatus() {
  const {
    connection,
    connectionHistory,
    getConnectionQuality,
    isConnectionStable,
    getSessionStats,
    resetSession,
  } = useNetworkStatusStore()

  return {
    /** Current connection information */
    connection,
    /** Whether device is online */
    isOnline: connection.isOnline,
    /** Current connection quality */
    quality: connection.quality,
    /** Connection history */
    history: connectionHistory,
    /** Get current connection quality */
    getQuality: getConnectionQuality,
    /** Check if connection is stable */
    isStable: isConnectionStable,
    /** Get session statistics */
    getStats: getSessionStats,
    /** Reset session data */
    reset: resetSession,
  }
}

/**
 * Hook for subscribing to specific network status changes
 */
export function useNetworkStatusSubscription() {
  return {
    /** Subscribe to online/offline changes */
    onOnlineChange: (
      callback: (isOnline: boolean, previousIsOnline?: boolean) => void
    ) => {
      return useNetworkStatusStore.subscribe(
        (state) => state.connection.isOnline,
        callback
      )
    },

    /** Subscribe to connection quality changes */
    onQualityChange: (
      callback: (
        quality: ConnectionQualityEnum,
        previousQuality?: ConnectionQualityEnum
      ) => void
    ) => {
      return useNetworkStatusStore.subscribe(
        (state) => state.connection.quality,
        callback
      )
    },

    /** Subscribe to any connection changes */
    onConnectionChange: (
      callback: (
        connection: NetworkConnection,
        previousConnection?: NetworkConnection
      ) => void
    ) => {
      return useNetworkStatusStore.subscribe(
        (state) => state.connection,
        callback
      )
    },
  }
}

/**
 * Initialize network status monitoring
 * Call this once in your app root to start monitoring
 */
export function initializeNetworkMonitoring() {
  if (typeof window === "undefined") {
    return () => {} // No cleanup needed for SSR
  }

  const updateConnection = useNetworkStatusStore.getState().updateConnection

  // Initial update
  updateConnection(navigator.onLine)

  // Set up event listeners
  const handleOnline = () => updateConnection(true)
  const handleOffline = () => updateConnection(false)

  window.addEventListener("online", handleOnline)
  window.addEventListener("offline", handleOffline)

  // Monitor connection changes if Network Information API is available
  let connectionChangeHandler: (() => void) | undefined
  if ("connection" in navigator) {
    const connection = (navigator as any).connection
    connectionChangeHandler = () => updateConnection(navigator.onLine)
    connection?.addEventListener("change", connectionChangeHandler)
  }

  // Return cleanup function
  return () => {
    window.removeEventListener("online", handleOnline)
    window.removeEventListener("offline", handleOffline)

    if (connectionChangeHandler && "connection" in navigator) {
      const connection = (navigator as any).connection
      connection?.removeEventListener("change", connectionChangeHandler)
    }
  }
}
