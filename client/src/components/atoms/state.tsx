/**
 * Unified State Components - UI Primitives Layer
 * Consolidates LoadingSpinner, EmptyState and other state feedback components
 *
 * Features:
 * - Unified loading states with multiple variants
 * - Comprehensive empty state components with actions
 * - Skeleton loading patterns
 * - Error state components
 * - Accessibility-first design (WCAG 2.1 AA compliance)
 * - TypeScript strict mode compliance
 * - Performance optimized with conditional rendering
 * - 100% backward compatibility
 */

import React from "react"

import type { VariantProps } from "class-variance-authority"

import { cva } from "class-variance-authority"
import {
  AlertCircle,
  Archive,
  Calendar,
  Database,
  FileText,
  Folder,
  Loader2,
  Package,
  PlusCircle,
  RefreshCw,
  Search,
  Server,
  Settings,
  ShoppingCart,
  Star,
  Users,
  WifiOff,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Button } from "@/components/atoms/button"

// Loading variants using CVA
const loadingVariants = cva("flex items-center transition-opacity", {
  variants: {
    size: {
      xs: "gap-1",
      sm: "gap-1.5",
      md: "gap-2",
      lg: "gap-3",
      xl: "gap-4",
    },
    layout: {
      inline: "inline-flex",
      center: "justify-center",
      left: "justify-start",
      right: "justify-end",
    },
  },
  defaultVariants: {
    size: "md",
    layout: "center",
  },
})

// Empty state variants using CVA
const emptyStateVariants = cva(
  "flex flex-col items-center text-center transition-opacity",
  {
    variants: {
      size: {
        sm: "py-6 space-y-2",
        md: "py-8 space-y-3",
        lg: "py-12 space-y-4",
        xl: "py-16 space-y-6",
      },
    },
    defaultVariants: {
      size: "md",
    },
  }
)

// Size configurations
const loadingSizeConfig = {
  xs: {
    icon: "h-3 w-3",
    text: "text-xs",
  },
  sm: {
    icon: "h-4 w-4",
    text: "text-sm",
  },
  md: {
    icon: "h-5 w-5",
    text: "text-sm",
  },
  lg: {
    icon: "h-6 w-6",
    text: "text-base",
  },
  xl: {
    icon: "h-8 w-8",
    text: "text-lg",
  },
} as const

const emptyStateSizeConfig = {
  sm: {
    icon: "h-8 w-8",
    title: "text-base font-medium",
    description: "text-sm",
    button: "h-8 px-3 text-xs",
  },
  md: {
    icon: "h-12 w-12",
    title: "text-lg font-semibold",
    description: "text-sm",
    button: "h-10 px-4 text-sm",
  },
  lg: {
    icon: "h-16 w-16",
    title: "text-xl font-semibold",
    description: "text-base",
    button: "h-12 px-6 text-base",
  },
  xl: {
    icon: "h-20 w-20",
    title: "text-2xl font-bold",
    description: "text-lg",
    button: "h-14 px-8 text-lg",
  },
} as const

// Loading spinner types
export type LoadingVariant = "spinner" | "pulse" | "dots" | "bars" | "refresh"
export type LoadingSize = keyof typeof loadingSizeConfig
export type LoadingLayout = "inline" | "center" | "left" | "right"

// Empty state types
export type EmptyStateVariant =
  | "search"
  | "folder"
  | "users"
  | "documents"
  | "data"
  | "settings"
  | "calendar"
  | "archive"
  | "package"
  | "cart"
  | "favorites"
  | "network"
  | "server"
  | "custom"
export type EmptyStateSize = keyof typeof emptyStateSizeConfig

// Empty state configuration
const emptyStateConfig = {
  search: {
    icon: Search,
    color: "text-blue-400",
    title: "No results found",
    description: "Try adjusting your search terms or filters",
  },
  folder: {
    icon: Folder,
    color: "text-yellow-400",
    title: "No items found",
    description: "This folder is empty or no items match your criteria",
  },
  users: {
    icon: Users,
    color: "text-green-400",
    title: "No users found",
    description: "No users have been added or match your search",
  },
  documents: {
    icon: FileText,
    color: "text-purple-400",
    title: "No documents found",
    description: "No documents are available or match your criteria",
  },
  data: {
    icon: Database,
    color: "text-gray-400",
    title: "No data available",
    description: "There is no data to display at this time",
  },
  settings: {
    icon: Settings,
    color: "text-blue-400",
    title: "No configuration found",
    description: "Settings have not been configured yet",
  },
  calendar: {
    icon: Calendar,
    color: "text-indigo-400",
    title: "No events scheduled",
    description: "You have no upcoming events or appointments",
  },
  archive: {
    icon: Archive,
    color: "text-amber-400",
    title: "Archive is empty",
    description: "No archived items are currently available",
  },
  package: {
    icon: Package,
    color: "text-orange-400",
    title: "No packages found",
    description: "No packages or components are available",
  },
  cart: {
    icon: ShoppingCart,
    color: "text-emerald-400",
    title: "Cart is empty",
    description: "You haven't added any items to your cart yet",
  },
  favorites: {
    icon: Star,
    color: "text-yellow-400",
    title: "No favorites yet",
    description: "Items you mark as favorites will appear here",
  },
  network: {
    icon: WifiOff,
    color: "text-red-400",
    title: "Connection lost",
    description: "Please check your internet connection and try again",
  },
  server: {
    icon: Server,
    color: "text-slate-400",
    title: "Server unavailable",
    description: "The server is currently unavailable. Please try again later",
  },
  custom: {
    icon: null,
    color: "text-muted-foreground",
    title: "No content",
    description: "No content is currently available",
  },
} as const

// Loading Spinner Component
export interface UnifiedLoadingProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingVariants> {
  variant?: LoadingVariant
  text?: string
  showText?: boolean
  "data-testid"?: string
}

export const UnifiedLoading = React.forwardRef<
  HTMLDivElement,
  UnifiedLoadingProps
>(
  (
    {
      variant = "spinner",
      size = "md",
      layout = "center",
      text,
      showText = true,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const sizeStyles = loadingSizeConfig[size ?? "md"]

    const renderLoadingIcon = () => {
      const iconClasses = cn(
        sizeStyles.icon,
        "text-muted-foreground",
        variant !== "pulse" && "animate-spin"
      )

      switch (variant) {
        case "refresh":
          return <RefreshCw className={iconClasses} />
        case "pulse":
          return (
            <div
              className={cn(
                sizeStyles.icon,
                "animate-pulse rounded-full bg-current opacity-60"
              )}
            />
          )
        case "dots":
          return (
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className={cn(
                    "animate-bounce rounded-full bg-current",
                    size === "xs"
                      ? "h-1 w-1"
                      : size === "sm"
                        ? "h-1.5 w-1.5"
                        : size === "md"
                          ? "h-2 w-2"
                          : size === "lg"
                            ? "h-2.5 w-2.5"
                            : "h-3 w-3"
                  )}
                  style={{ animationDelay: `${i * 0.1}s` }}
                />
              ))}
            </div>
          )
        case "bars":
          return (
            <div className="flex items-end space-x-1">
              {[0, 1, 2, 3].map((i) => (
                <div
                  key={i}
                  className={cn(
                    "animate-pulse bg-current",
                    size === "xs"
                      ? "w-0.5"
                      : size === "sm"
                        ? "w-0.5"
                        : size === "md"
                          ? "w-1"
                          : size === "lg"
                            ? "w-1"
                            : "w-1.5",
                    `h-${2 + i}`
                  )}
                  style={{ animationDelay: `${i * 0.15}s` }}
                />
              ))}
            </div>
          )
        default:
          return <Loader2 className={iconClasses} />
      }
    }

    return (
      <div
        ref={ref}
        className={cn(loadingVariants({ size, layout }), className)}
        role="status"
        aria-label={text || "Loading"}
        data-testid={testId || `unified-loading-${variant}`}
        {...props}
      >
        {renderLoadingIcon()}
        {showText && text && (
          <span className={cn(sizeStyles.text, "text-muted-foreground")}>
            {text}
          </span>
        )}
      </div>
    )
  }
)

UnifiedLoading.displayName = "UnifiedLoading"

// Empty State Component
export interface UnifiedEmptyStateProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof emptyStateVariants> {
  variant?: EmptyStateVariant
  icon?: React.ReactNode
  title?: string
  description?: string
  action?: React.ReactNode
  customIcon?: React.ComponentType<{ className?: string }>
  "data-testid"?: string
}

export const UnifiedEmptyState = React.forwardRef<
  HTMLDivElement,
  UnifiedEmptyStateProps
>(
  (
    {
      variant = "data",
      size = "md",
      icon: customIcon,
      title: customTitle,
      description: customDescription,
      action,
      customIcon: CustomIconComponent,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const config = emptyStateConfig[variant]
    const sizeStyles = emptyStateSizeConfig[size ?? "md"]

    const IconComponent = CustomIconComponent || config.icon
    const title = customTitle || config.title
    const description = customDescription || config.description

    return (
      <div
        ref={ref}
        className={cn(emptyStateVariants({ size }), className)}
        data-testid={testId || `unified-empty-state-${variant}`}
        {...props}
      >
        {/* Icon */}
        <div className={cn(sizeStyles.icon, config.color)}>
          {customIcon ? (
            customIcon
          ) : IconComponent ? (
            <IconComponent className="h-full w-full" />
          ) : (
            <Database className="h-full w-full" />
          )}
        </div>

        {/* Content */}
        <div className="max-w-md space-y-2">
          <h3 className={cn(sizeStyles.title, "text-foreground")}>{title}</h3>

          {description && (
            <p className={cn(sizeStyles.description, "text-muted-foreground")}>
              {description}
            </p>
          )}
        </div>

        {/* Action */}
        {action && <div className="pt-2">{action}</div>}
      </div>
    )
  }
)

UnifiedEmptyState.displayName = "UnifiedEmptyState"

// Error State Component
export interface UnifiedErrorStateProps extends UnifiedEmptyStateProps {
  error?: Error | string
  retry?: () => void
  retryText?: string
}

export const UnifiedErrorState = React.forwardRef<
  HTMLDivElement,
  UnifiedErrorStateProps
>(
  (
    {
      error,
      retry,
      retryText = "Try Again",
      title: customTitle,
      description: customDescription,
      action: customAction,
      size = "md",
      className,
      ...props
    },
    ref
  ) => {
    const errorMessage = typeof error === "string" ? error : error?.message
    const title = customTitle || "Something went wrong"
    const description =
      customDescription ||
      errorMessage ||
      "An unexpected error occurred. Please try again."

    const action =
      customAction ||
      (retry && (
        <Button
          onClick={retry}
          variant="outline"
          className={emptyStateSizeConfig[size ?? "md"].button}
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          {retryText}
        </Button>
      ))

    return (
      <UnifiedEmptyState
        ref={ref}
        variant="custom"
        size={size}
        icon={<AlertCircle className="text-destructive h-full w-full" />}
        title={title}
        description={description}
        action={action}
        className={className}
        {...props}
      />
    )
  }
)

UnifiedErrorState.displayName = "UnifiedErrorState"

// Backward compatibility components

// LoadingSpinner (backward compatibility)
export const LoadingSpinner = React.forwardRef<
  HTMLDivElement,
  Omit<UnifiedLoadingProps, "layout"> & { inline?: boolean }
>(({ inline, ...props }, ref) => (
  <UnifiedLoading ref={ref} layout={inline ? "inline" : "center"} {...props} />
))

LoadingSpinner.displayName = "LoadingSpinner"

// EmptyState (backward compatibility)
export const EmptyState = UnifiedEmptyState
EmptyState.displayName = "EmptyState"

// Specialized loading components
export const LoadingOverlay: React.FC<{
  text?: string
  variant?: LoadingVariant
  size?: LoadingSize
  className?: string
}> = ({ text = "Loading...", variant, size = "lg", className }) => (
  <div className={cn("flex items-center justify-center py-8", className)}>
    <UnifiedLoading variant={variant} size={size} text={text} />
  </div>
)

export const InlineLoading: React.FC<{
  text?: string
  variant?: LoadingVariant
  size?: LoadingSize
}> = ({ text, variant, size = "sm" }) => (
  <UnifiedLoading variant={variant} size={size} text={text} layout="inline" />
)

// Specialized empty state components
export const EmptyProjectList: React.FC<{
  onCreateProject?: () => void
  createText?: string
}> = ({ onCreateProject, createText = "Create Project" }) => (
  <UnifiedEmptyState
    variant="folder"
    title="No projects found"
    description="Get started by creating your first project or adjust your search filters."
    action={
      onCreateProject && (
        <Button onClick={onCreateProject}>
          <PlusCircle className="mr-2 h-4 w-4" />
          {createText}
        </Button>
      )
    }
  />
)

export const EmptySearchResults: React.FC<{
  searchQuery?: string
  onClearSearch?: () => void
  clearText?: string
}> = ({ searchQuery, onClearSearch, clearText = "Clear Search" }) => (
  <UnifiedEmptyState
    variant="search"
    title="No results found"
    description={
      searchQuery
        ? `No items match "${searchQuery}". Try adjusting your search terms.`
        : "No items match your current filters."
    }
    action={
      onClearSearch && (
        <Button variant="outline" onClick={onClearSearch}>
          {clearText}
        </Button>
      )
    }
  />
)

export const EmptyTeamMembers: React.FC<{
  onAddMember?: () => void
  addText?: string
}> = ({ onAddMember, addText = "Add Team Member" }) => (
  <UnifiedEmptyState
    variant="users"
    title="No team members"
    description="This project doesn't have any team members yet. Add members to start collaborating."
    action={
      onAddMember && (
        <Button onClick={onAddMember}>
          <Users className="mr-2 h-4 w-4" />
          {addText}
        </Button>
      )
    }
    size="sm"
  />
)

export const EmptyComponents: React.FC<{
  onAddComponent?: () => void
  addText?: string
}> = ({ onAddComponent, addText = "Add Component" }) => (
  <UnifiedEmptyState
    variant="package"
    title="No components found"
    description="No electrical components are available or match your search criteria."
    action={
      onAddComponent && (
        <Button onClick={onAddComponent}>
          <PlusCircle className="mr-2 h-4 w-4" />
          {addText}
        </Button>
      )
    }
  />
)

export const NetworkError: React.FC<{
  onRetry?: () => void
  retryText?: string
}> = ({ onRetry, retryText = "Retry" }) => (
  <UnifiedErrorState
    title="Connection Error"
    description="Unable to connect to the server. Please check your internet connection and try again."
    retry={onRetry}
    retryText={retryText}
    icon={<WifiOff className="text-destructive h-full w-full" />}
  />
)

// Skeleton loading patterns
export const SkeletonLine: React.FC<{
  width?: string | number
  height?: string | number
  className?: string
}> = ({ width = "100%", height = "1rem", className }) => (
  <div
    className={cn("bg-muted animate-pulse rounded", className)}
    style={{ width, height }}
  />
)

export const SkeletonCard: React.FC<{ className?: string }> = ({
  className,
}) => (
  <div className={cn("space-y-3 rounded-lg border p-4", className)}>
    <SkeletonLine width="60%" height="1.25rem" />
    <SkeletonLine width="100%" height="1rem" />
    <SkeletonLine width="80%" height="1rem" />
    <div className="flex space-x-2 pt-2">
      <SkeletonLine width="4rem" height="2rem" />
      <SkeletonLine width="4rem" height="2rem" />
    </div>
  </div>
)

// Utility functions
export const getEmptyStateConfig = (variant: EmptyStateVariant) => {
  return emptyStateConfig[variant]
}

export const isValidEmptyStateVariant = (
  variant: string
): variant is EmptyStateVariant => {
  return Object.keys(emptyStateConfig).includes(variant)
}

export const isValidLoadingVariant = (
  variant: string
): variant is LoadingVariant => {
  const validVariants: LoadingVariant[] = [
    "spinner",
    "pulse",
    "dots",
    "bars",
    "refresh",
  ]
  return validVariants.includes(variant as LoadingVariant)
}

// Export types for external use
export type UnifiedLoadingSize = LoadingSize
export type UnifiedLoadingVariant = LoadingVariant
export type UnifiedLoadingLayout = LoadingLayout
export type UnifiedEmptyStateSize = EmptyStateSize
export type UnifiedEmptyStateVariant = EmptyStateVariant

// Export configurations for external use
export {
  loadingSizeConfig,
  emptyStateSizeConfig,
  emptyStateConfig,
  loadingVariants,
  emptyStateVariants,
}
