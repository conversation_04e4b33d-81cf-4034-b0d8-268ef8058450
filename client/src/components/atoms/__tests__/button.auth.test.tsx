/**
 * @file Auth-specific Button component tests
 * @description TDD tests for Button component auth features
 */

import { fireEvent, render, screen } from "@testing-library/react"
import { describe, expect, it, vi } from "vitest"

import { Button } from "../button"

describe("Button - Auth Features", () => {
  describe("Form submission buttons", () => {
    it("should render login button correctly", () => {
      render(
        <Button type="submit" data-testid="login-button">
          Login
        </Button>
      )

      const button = screen.getByTestId("login-button")
      expect(button).toBeInTheDocument()
      expect(button).toHaveAttribute("type", "submit")
      expect(button).toHaveTextContent("Login")
    })

    it("should render register button correctly", () => {
      render(
        <Button type="submit" data-testid="register-button">
          Register
        </Button>
      )

      const button = screen.getByTestId("register-button")
      expect(button).toBeInTheDocument()
      expect(button).toHaveAttribute("type", "submit")
      expect(button).toHaveTextContent("Register")
    })

    it("should handle loading state during form submission", () => {
      render(
        <Button disabled data-testid="loading-button">
          Loading...
        </Button>
      )

      const button = screen.getByTestId("loading-button")
      expect(button).toBeDisabled()
      expect(button).toHaveClass("disabled:opacity-50")
    })
  })

  describe("Button variants for auth flows", () => {
    it("should render primary button for main auth actions", () => {
      render(
        <Button variant="default" data-testid="primary-auth-button">
          Sign In
        </Button>
      )

      const button = screen.getByTestId("primary-auth-button")
      expect(button).toHaveClass("bg-primary")
    })

    it("should render secondary button for alternate actions", () => {
      render(
        <Button variant="secondary" data-testid="secondary-auth-button">
          Cancel
        </Button>
      )

      const button = screen.getByTestId("secondary-auth-button")
      expect(button).toHaveClass("bg-secondary")
    })

    it("should render outline button for less prominent actions", () => {
      render(
        <Button variant="outline" data-testid="outline-auth-button">
          Forgot Password?
        </Button>
      )

      const button = screen.getByTestId("outline-auth-button")
      expect(button).toHaveClass("border-input")
    })

    it("should render destructive button for dangerous actions", () => {
      render(
        <Button variant="destructive" data-testid="destructive-auth-button">
          Delete Account
        </Button>
      )

      const button = screen.getByTestId("destructive-auth-button")
      expect(button).toHaveClass("bg-destructive")
    })
  })

  describe("Interactive elements", () => {
    it("should handle password visibility toggle", () => {
      const mockToggle = vi.fn()

      render(
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={mockToggle}
          data-testid="toggle-visibility-button"
          aria-label="Toggle password visibility"
        >
          Show
        </Button>
      )

      const button = screen.getByTestId("toggle-visibility-button")
      fireEvent.click(button)

      expect(mockToggle).toHaveBeenCalledOnce()
      expect(button).toHaveAttribute("aria-label", "Toggle password visibility")
    })

    it("should handle social login buttons", () => {
      const mockSocialLogin = vi.fn()

      render(
        <Button
          type="button"
          variant="outline"
          onClick={mockSocialLogin}
          data-testid="social-login-button"
        >
          Continue with Google
        </Button>
      )

      const button = screen.getByTestId("social-login-button")
      fireEvent.click(button)

      expect(mockSocialLogin).toHaveBeenCalledOnce()
    })
  })

  describe("Button sizing for auth forms", () => {
    it("should support different sizes", () => {
      render(
        <>
          <Button size="sm" data-testid="small-button">
            Small
          </Button>
          <Button size="default" data-testid="default-button">
            Default
          </Button>
          <Button size="lg" data-testid="large-button">
            Large
          </Button>
        </>
      )

      expect(screen.getByTestId("small-button")).toHaveClass("h-8")
      expect(screen.getByTestId("default-button")).toHaveClass("h-9")
      expect(screen.getByTestId("large-button")).toHaveClass("h-10")
    })

    it("should support full width for form buttons", () => {
      render(
        <Button className="w-full" data-testid="full-width-button">
          Sign In
        </Button>
      )

      const button = screen.getByTestId("full-width-button")
      expect(button).toHaveClass("w-full")
    })
  })

  describe("Accessibility", () => {
    it("should support aria-describedby for additional context", () => {
      render(
        <Button
          data-testid="accessible-button"
          aria-describedby="button-description"
        >
          Submit
        </Button>
      )

      const button = screen.getByTestId("accessible-button")
      expect(button).toHaveAttribute("aria-describedby", "button-description")
    })

    it("should maintain focus styling", () => {
      render(<Button data-testid="focus-button">Focus Test</Button>)

      const button = screen.getByTestId("focus-button")
      expect(button).toHaveClass("focus-visible:ring-ring/50")
      expect(button).toHaveClass("focus-visible:ring-[3px]")
    })

    it("should support keyboard navigation", () => {
      const mockClick = vi.fn()

      render(
        <Button onClick={mockClick} data-testid="keyboard-button">
          Keyboard Test
        </Button>
      )

      const button = screen.getByTestId("keyboard-button")

      // Simulate Enter key press
      fireEvent.keyDown(button, { key: "Enter", code: "Enter" })
      button.focus()
      fireEvent.click(button)

      expect(mockClick).toHaveBeenCalled()
    })
  })

  describe("Link buttons", () => {
    it("should render link variant for text-like actions", () => {
      render(
        <Button variant="link" data-testid="link-button">
          Forgot your password?
        </Button>
      )

      const button = screen.getByTestId("link-button")
      expect(button).toHaveClass("underline-offset-4")
      expect(button).toHaveClass("hover:underline")
    })

    it("should support asChild prop for router integration", () => {
      // Mock next/link or react-router Link component
      const MockLink = ({
        children,
        href,
      }: {
        children: React.ReactNode
        href: string
      }) => (
        <a href={href} data-testid="router-link">
          {children}
        </a>
      )

      render(
        <Button asChild data-testid="router-button">
          <MockLink href="/register">Create Account</MockLink>
        </Button>
      )

      // The button should render as a link due to asChild prop
      const link = screen.getByTestId("router-link")
      expect(link).toBeInTheDocument()
      expect(link).toHaveAttribute("href", "/register")
    })
  })
})
