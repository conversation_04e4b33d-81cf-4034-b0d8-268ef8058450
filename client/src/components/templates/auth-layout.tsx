/**
 * @file AuthLayout template component
 * @description Centralized layout for authentication pages with branding and responsive design
 */

import * as React from "react"

import { cn } from "@/lib/utils"

import { UnifiedIcon as Icon } from "@/components/atoms/icon"

export interface AuthLayoutProps {
  /** Child components to render in the auth container */
  children: React.ReactNode
  /** Optional title for the auth section */
  title?: string
  /** Optional description for the auth section */
  description?: string
  /** Whether to show the brand logo */
  showBrandLogo?: boolean
  /** Additional CSS classes */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

export const AuthLayout = React.forwardRef<HTMLElement, AuthLayoutProps>(
  (
    {
      children,
      title,
      description,
      showBrandLogo = true,
      className,
      "data-testid": dataTestId,
    },
    ref
  ) => {
    return (
      <main
        ref={ref}
        className="bg-background text-foreground flex min-h-screen items-center justify-center px-4 py-12 sm:px-6 lg:px-8"
        data-testid={dataTestId}
      >
        <div
          data-testid="auth-container"
          className={cn("w-full max-w-md space-y-8", className)}
        >
          {/* Brand Logo Section */}
          {showBrandLogo && (
            <div
              data-testid="brand-logo"
              className="flex items-center justify-center space-x-2"
            >
              <Icon
                type="zap"
                data-testid="electrical-icon"
                className="text-primary h-8 w-8"
                aria-hidden="true"
              />
              <span className="text-primary text-2xl font-bold">
                Ultimate Electrical Designer
              </span>
            </div>
          )}

          {/* Header Section */}
          {(title || description) && (
            <div className="space-y-2 text-center">
              {title && (
                <h1 className="text-foreground text-3xl font-extrabold">
                  {title}
                </h1>
              )}
              {description && (
                <p className="text-muted-foreground text-sm">{description}</p>
              )}
            </div>
          )}

          {/* Content Section */}
          {children}
        </div>
      </main>
    )
  }
)

AuthLayout.displayName = "AuthLayout"
