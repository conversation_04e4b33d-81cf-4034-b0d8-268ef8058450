/**
 * @file LoginPage template component
 * @description Complete login page with AuthLayout integration and comprehensive form handling
 */

import * as React from "react"

import { LoginForm } from "@/components/organisms/login-form"

import { AuthLayout } from "./auth-layout"

export interface LoginPageProps {
  /** Custom title for the login page */
  title?: string
  /** Custom description for the login page */
  description?: string
  /** Whether to show brand logo */
  showBrandLogo?: boolean
  /** Whether to show forgot password link */
  showForgotPassword?: boolean
  /** Whether to show sign up link */
  showSignUpLink?: boolean
  /** Callback fired on successful login */
  onSuccess?: (response: any) => void
  /** Callback fired on login error */
  onError?: (error: any) => void
  /** Callback fired when forgot password link is clicked */
  onForgotPassword?: () => void
  /** Callback fired when sign up link is clicked */
  onSignUp?: () => void
  /** Additional CSS classes */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

export const LoginPage = React.forwardRef<HTMLElement, LoginPageProps>(
  (
    {
      title = "Sign in to your account",
      description = "Enter your credentials to access the Ultimate Electrical Designer platform",
      showBrandLogo = true,
      showForgotPassword = true,
      showSignUpLink = true,
      onSuccess,
      onError,
      onForgotPassword,
      onSignUp,
      className,
      "data-testid": dataTestId,
    },
    ref
  ) => {
    return (
      <AuthLayout
        ref={ref}
        title={title}
        description={description}
        showBrandLogo={showBrandLogo}
        className={className}
        data-testid={dataTestId}
      >
        <LoginForm
          title=""
          description=""
          onSuccess={onSuccess}
          onError={onError}
          onForgotPassword={onForgotPassword}
          onSignUp={onSignUp}
          showForgotPassword={showForgotPassword}
          showSignUpLink={showSignUpLink}
          className="w-full"
        />
      </AuthLayout>
    )
  }
)

LoginPage.displayName = "LoginPage"
