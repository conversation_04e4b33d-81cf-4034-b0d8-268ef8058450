/**
 * @file LoginPage template tests
 * @description TDD tests for login page component
 */
import type { LoginPageProps } from "../login-page"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { LoginPage } from "../login-page"

// Mock auth hooks
const mockLoginMutation = {
  mutate: vi.fn(),
  isPending: false,
  error: null as any,
  isSuccess: false,
  data: undefined as any,
}

const mockAuthStore = {
  isLoading: false,
  setLoading: vi.fn(),
}

vi.mock("@/hooks/api/useAuth", () => ({
  useLogin: () => mockLoginMutation,
}))

vi.mock("@/stores/authStore", () => ({
  useAuthStore: () => mockAuthStore,
}))

// Test wrapper with React Query
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient()
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

const defaultProps: LoginPageProps = {
  onSuccess: vi.fn(),
  onError: vi.fn(),
}

describe("LoginPage", () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
    vi.clearAllMocks()
    // Reset mock state
    mockLoginMutation.mutate.mockClear()
    mockLoginMutation.isPending = false
    mockLoginMutation.error = null
    mockLoginMutation.isSuccess = false
    mockLoginMutation.data = undefined
    mockAuthStore.isLoading = false
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  // Rendering Tests
  describe("Rendering", () => {
    it("renders with correct layout and title", () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      // Check AuthLayout integration
      expect(screen.getByRole("main")).toBeInTheDocument()
      expect(screen.getByTestId("auth-container")).toBeInTheDocument()
      expect(screen.getByTestId("brand-logo")).toBeInTheDocument()

      // Check page title
      expect(
        screen.getByRole("heading", { name: /sign in to your account/i })
      ).toBeInTheDocument()
    })

    it("renders login form with all required elements", () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      // Form elements
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
      expect(
        screen.getByPlaceholderText(/enter your password/i)
      ).toBeInTheDocument()
      expect(
        screen.getByRole("button", { name: /sign in/i })
      ).toBeInTheDocument()

      // Forgot password link
      expect(screen.getByText(/forgot password/i)).toBeInTheDocument()

      // Sign up link
      expect(screen.getByText(/don't have an account/i)).toBeInTheDocument()
    })

    it("renders with custom title when provided", () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} title="Welcome Back" />
        </TestWrapper>
      )

      expect(
        screen.getByRole("heading", { name: /welcome back/i })
      ).toBeInTheDocument()
    })

    it("renders with custom description when provided", () => {
      render(
        <TestWrapper>
          <LoginPage
            {...defaultProps}
            description="Please enter your credentials"
          />
        </TestWrapper>
      )

      expect(
        screen.getByText(/please enter your credentials/i)
      ).toBeInTheDocument()
    })

    it("hides optional links when disabled", () => {
      render(
        <TestWrapper>
          <LoginPage
            {...defaultProps}
            showForgotPassword={false}
            showSignUpLink={false}
          />
        </TestWrapper>
      )

      expect(screen.queryByText(/forgot password/i)).not.toBeInTheDocument()
      expect(
        screen.queryByText(/don't have an account/i)
      ).not.toBeInTheDocument()
    })

    it("applies custom className correctly", () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} className="custom-login-page" />
        </TestWrapper>
      )

      const authContainer = screen.getByTestId("auth-container")
      expect(authContainer).toHaveClass("custom-login-page")
    })
  })

  // Form Interaction Tests
  describe("Form Interaction", () => {
    it("handles successful login submission", async () => {
      const mockOnSuccess = vi.fn()

      render(
        <TestWrapper>
          <LoginPage {...defaultProps} onSuccess={mockOnSuccess} />
        </TestWrapper>
      )

      // Fill form
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )

      // Submit
      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockLoginMutation.mutate).toHaveBeenCalledWith({
          username: "<EMAIL>",
          password: "SecurePass123!",
        })
      })
    })

    it("validates required fields on submission", async () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText(/email or username is required/i)
        ).toBeInTheDocument()
        expect(screen.getByText(/password is required/i)).toBeInTheDocument()
      })

      expect(mockLoginMutation.mutate).not.toHaveBeenCalled()
    })

    it("clears validation errors when user types", async () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      // Trigger validation errors
      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText(/email or username is required/i)
        ).toBeInTheDocument()
      })

      // Start typing to clear error
      const emailInput = screen.getByLabelText(/email address/i)
      await user.type(emailInput, "t")

      await waitFor(() => {
        expect(
          screen.queryByText(/email or username is required/i)
        ).not.toBeInTheDocument()
      })
    })
  })

  // Loading State Tests
  describe("Loading States", () => {
    it("shows loading state during login", () => {
      mockLoginMutation.isPending = true

      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", { name: /signing in/i })
      expect(submitButton).toBeDisabled()
      expect(submitButton).toHaveAttribute("aria-disabled", "true")
    })

    it("disables form fields during login", () => {
      mockLoginMutation.isPending = true

      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByLabelText(/email address/i)).toBeDisabled()
      expect(screen.getByPlaceholderText(/enter your password/i)).toBeDisabled()
    })
  })

  // Error Handling Tests
  describe("Error Handling", () => {
    it("displays server error messages", () => {
      const errorMessage = "Invalid credentials"
      mockLoginMutation.error = { message: errorMessage }

      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByRole("alert")).toBeInTheDocument()
    })

    it("calls onError callback when error occurs", () => {
      const mockOnError = vi.fn()
      const error = { message: "Network error" }

      mockLoginMutation.error = error

      render(
        <TestWrapper>
          <LoginPage {...defaultProps} onError={mockOnError} />
        </TestWrapper>
      )

      expect(mockOnError).toHaveBeenCalledWith(error)
    })

    it("handles validation errors from server", () => {
      const validationError = {
        message: "Validation failed",
        errors: {
          username: "Email is required",
          password: "Password is too short",
        },
      }
      mockLoginMutation.error = validationError

      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByText(/validation failed/i)).toBeInTheDocument()
    })
  })

  // Success State Tests
  describe("Success States", () => {
    it("calls onSuccess callback when login succeeds", async () => {
      const mockOnSuccess = vi.fn()

      // Mock successful mutation state
      mockLoginMutation.isSuccess = true
      mockLoginMutation.data = {
        token: "fake-token",
        user: { id: 1, email: "<EMAIL>" },
      }

      render(
        <TestWrapper>
          <LoginPage {...defaultProps} onSuccess={mockOnSuccess} />
        </TestWrapper>
      )

      // Wait for useEffect to trigger
      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalledWith(mockLoginMutation.data)
      })
    })
  })

  // Navigation Tests
  describe("Navigation", () => {
    it("handles forgot password link click", async () => {
      const mockOnForgotPassword = vi.fn()

      render(
        <TestWrapper>
          <LoginPage
            {...defaultProps}
            onForgotPassword={mockOnForgotPassword}
          />
        </TestWrapper>
      )

      const forgotPasswordLink = screen.getByText(/forgot password/i)
      await user.click(forgotPasswordLink)

      expect(mockOnForgotPassword).toHaveBeenCalled()
    })

    it("handles sign up link click", async () => {
      const mockOnSignUp = vi.fn()

      render(
        <TestWrapper>
          <LoginPage {...defaultProps} onSignUp={mockOnSignUp} />
        </TestWrapper>
      )

      const signUpLink = screen.getByText(/sign up/i)
      await user.click(signUpLink)

      expect(mockOnSignUp).toHaveBeenCalled()
    })
  })

  // Accessibility Tests
  describe("Accessibility", () => {
    it("has proper form structure with labels", () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText(/enter your password/i)

      expect(emailInput).toHaveAttribute("type", "email")
      expect(emailInput).toHaveAttribute("required")
      expect(emailInput).toHaveAttribute("aria-invalid", "false")

      expect(passwordInput).toHaveAttribute("type", "password")
      expect(passwordInput).toHaveAttribute("required")
    })

    it("provides proper keyboard navigation", async () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText(/enter your password/i)
      const submitButton = screen.getByRole("button", { name: /sign in/i })

      // Tab navigation
      emailInput.focus()
      expect(emailInput).toHaveFocus()

      await user.tab()
      expect(passwordInput).toHaveFocus()

      await user.tab() // Password visibility toggle
      await user.tab() // Forgot password button
      await user.tab() // Submit button
      expect(submitButton).toHaveFocus()
    })

    it("announces validation errors to screen readers", async () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        const errorElements = screen.getAllByRole("alert")
        expect(errorElements.length).toBeGreaterThanOrEqual(2) // At least email and password errors
      })
    })
  })

  // Integration Tests
  describe("Integration", () => {
    it("integrates properly with AuthLayout", () => {
      render(
        <TestWrapper>
          <LoginPage {...defaultProps} />
        </TestWrapper>
      )

      // Verify AuthLayout structure
      expect(screen.getByRole("main")).toBeInTheDocument()
      expect(screen.getByTestId("auth-container")).toBeInTheDocument()
      expect(screen.getByTestId("brand-logo")).toBeInTheDocument()
    })

    it("works correctly with different props combinations", () => {
      render(
        <TestWrapper>
          <LoginPage
            {...defaultProps}
            title="Custom Login"
            description="Please authenticate"
            showForgotPassword={false}
            className="custom-class"
          />
        </TestWrapper>
      )

      expect(
        screen.getByRole("heading", { name: /custom login/i })
      ).toBeInTheDocument()
      expect(screen.getByText(/please authenticate/i)).toBeInTheDocument()
      expect(screen.queryByText(/forgot password/i)).not.toBeInTheDocument()
    })
  })
})
