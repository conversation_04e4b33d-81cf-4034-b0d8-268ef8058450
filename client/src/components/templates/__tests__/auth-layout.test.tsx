/**
 * @file AuthLayout template tests
 * @description TDD tests for authentication layout component
 */
import type { AuthLayoutProps } from "../auth-layout"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, screen } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { AuthLayout } from "../auth-layout"

// Test wrapper with React Query
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient()
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

const defaultProps: AuthLayoutProps = {
  children: <div data-testid="auth-content">Test Content</div>,
}

describe("AuthLayout", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  // Rendering Tests
  describe("Rendering", () => {
    it("renders children content correctly", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByTestId("auth-content")).toBeInTheDocument()
    })

    it("renders with default layout structure", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      // Check main container
      const mainContainer = screen.getByRole("main")
      expect(mainContainer).toBeInTheDocument()
      expect(mainContainer).toHaveClass(
        "min-h-screen",
        "flex",
        "items-center",
        "justify-center"
      )

      // Check auth container
      const authContainer = screen.getByTestId("auth-container")
      expect(authContainer).toBeInTheDocument()
      expect(authContainer).toHaveClass("w-full", "max-w-md", "space-y-8")
    })

    it("renders with custom title when provided", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} title="Welcome Back" />
        </TestWrapper>
      )

      expect(
        screen.getByRole("heading", { name: /welcome back/i })
      ).toBeInTheDocument()
    })

    it("renders with custom description when provided", () => {
      render(
        <TestWrapper>
          <AuthLayout
            {...defaultProps}
            description="Please sign in to continue"
          />
        </TestWrapper>
      )

      expect(
        screen.getByText(/please sign in to continue/i)
      ).toBeInTheDocument()
    })

    it("renders without title and description when not provided", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.queryByRole("heading")).not.toBeInTheDocument()
      expect(
        screen.queryByText(/please sign in to continue/i)
      ).not.toBeInTheDocument()
    })

    it("renders with brand logo by default", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      const logoContainer = screen.getByTestId("brand-logo")
      expect(logoContainer).toBeInTheDocument()
      expect(
        screen.getByText(/ultimate electrical designer/i)
      ).toBeInTheDocument()
    })

    it("hides brand logo when showBrandLogo is false", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} showBrandLogo={false} />
        </TestWrapper>
      )

      expect(screen.queryByTestId("brand-logo")).not.toBeInTheDocument()
      expect(
        screen.queryByText(/ultimate electrical designer/i)
      ).not.toBeInTheDocument()
    })

    it("applies custom className correctly", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} className="custom-auth-layout" />
        </TestWrapper>
      )

      const authContainer = screen.getByTestId("auth-container")
      expect(authContainer).toHaveClass("custom-auth-layout")
    })
  })

  // Responsive Design Tests
  describe("Responsive Design", () => {
    it("applies responsive styles to main container", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      const mainContainer = screen.getByRole("main")
      expect(mainContainer).toHaveClass(
        "min-h-screen",
        "flex",
        "items-center",
        "justify-center",
        "bg-background",
        "px-4",
        "py-12",
        "sm:px-6",
        "lg:px-8"
      )
    })

    it("applies responsive styles to auth container", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      const authContainer = screen.getByTestId("auth-container")
      expect(authContainer).toHaveClass("w-full", "max-w-md", "space-y-8")
    })
  })

  // Content Structure Tests
  describe("Content Structure", () => {
    it("renders content in correct hierarchical order", () => {
      render(
        <TestWrapper>
          <AuthLayout
            {...defaultProps}
            title="Sign In"
            description="Access your account"
          />
        </TestWrapper>
      )

      const container = screen.getByTestId("auth-container")
      const elements = container.children

      // Brand logo should be first (if shown)
      expect(elements[0]).toHaveAttribute("data-testid", "brand-logo")

      // Header section with title/description should be second
      expect(elements[1]).toHaveClass("text-center")

      // Content should be last (children are rendered directly now)
      expect(elements[2]).toHaveAttribute("data-testid", "auth-content")
    })

    it("maintains proper spacing between sections", () => {
      render(
        <TestWrapper>
          <AuthLayout
            {...defaultProps}
            title="Sign In"
            description="Access your account"
          />
        </TestWrapper>
      )

      const authContainer = screen.getByTestId("auth-container")
      expect(authContainer).toHaveClass("space-y-8")

      const headerSection = screen
        .getByText(/access your account/i)
        .closest("div")
      expect(headerSection).toHaveClass("space-y-2")
    })
  })

  // Accessibility Tests
  describe("Accessibility", () => {
    it("has proper semantic structure", () => {
      render(
        <TestWrapper>
          <AuthLayout
            {...defaultProps}
            title="Authentication"
            description="Please authenticate"
          />
        </TestWrapper>
      )

      // Main container should be a main landmark
      expect(screen.getByRole("main")).toBeInTheDocument()

      // Title should be a proper heading
      const heading = screen.getByRole("heading", { name: /authentication/i })
      expect(heading).toBeInTheDocument()
      expect(heading.tagName).toBe("H1")
    })

    it("provides proper contrast for dark/light themes", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      const mainContainer = screen.getByRole("main")
      expect(mainContainer).toHaveClass("bg-background", "text-foreground")
    })

    it("handles focus management correctly", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      const mainContainer = screen.getByRole("main")
      expect(mainContainer).not.toHaveAttribute("tabindex")
    })
  })

  // Brand Logo Tests
  describe("Brand Logo", () => {
    it("renders brand logo with correct content", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      const logoContainer = screen.getByTestId("brand-logo")
      expect(logoContainer).toHaveClass(
        "flex",
        "justify-center",
        "items-center",
        "space-x-2"
      )

      // Check for electrical icon
      expect(screen.getByTestId("electrical-icon")).toBeInTheDocument()

      // Check for brand text
      expect(
        screen.getByText(/ultimate electrical designer/i)
      ).toBeInTheDocument()
    })

    it("applies correct styling to brand elements", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      const brandText = screen.getByText(/ultimate electrical designer/i)
      expect(brandText).toHaveClass("text-2xl", "font-bold", "text-primary")
    })
  })

  // Integration Tests
  describe("Integration", () => {
    it("integrates properly with theme system", () => {
      render(
        <TestWrapper>
          <AuthLayout {...defaultProps} />
        </TestWrapper>
      )

      const mainContainer = screen.getByRole("main")
      expect(mainContainer).toHaveClass("bg-background")

      const brandText = screen.getByText(/ultimate electrical designer/i)
      expect(brandText).toHaveClass("text-primary")
    })

    it("works correctly with different content types", () => {
      const complexContent = (
        <div>
          <form data-testid="auth-form">
            <input type="email" placeholder="Email" />
            <button type="submit">Submit</button>
          </form>
        </div>
      )

      render(
        <TestWrapper>
          <AuthLayout {...defaultProps}>{complexContent}</AuthLayout>
        </TestWrapper>
      )

      expect(screen.getByTestId("auth-form")).toBeInTheDocument()
      expect(screen.getByPlaceholderText(/email/i)).toBeInTheDocument()
      expect(
        screen.getByRole("button", { name: /submit/i })
      ).toBeInTheDocument()
    })
  })
})
