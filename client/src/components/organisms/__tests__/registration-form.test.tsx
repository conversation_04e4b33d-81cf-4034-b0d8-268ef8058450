/**
 * @file RegistrationForm organism tests
 * @description Comprehensive TDD tests for registration form with professional credentials
 */
import type { RegistrationFormProps } from "../registration-form"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { RegistrationForm } from "../registration-form"

// Mock auth hooks
const mockRegisterMutation = {
  mutate: vi.fn(),
  isPending: false,
  error: null as any,
  isSuccess: false,
}

const mockAuthStore = {
  isLoading: false,
  setLoading: vi.fn(),
}

vi.mock("@/hooks/api/useAuth", () => ({
  useRegister: () => mockRegisterMutation,
}))

vi.mock("@/stores/authStore", () => ({
  useAuthStore: () => mockAuthStore,
}))

// Test wrapper with React Query
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient()
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

const defaultProps: RegistrationFormProps = {
  onSuccess: vi.fn(),
  onError: vi.fn(),
}

describe("RegistrationForm", () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
    vi.clearAllMocks()
    // Reset mock state
    mockRegisterMutation.mutate.mockClear()
    mockRegisterMutation.isPending = false
    mockRegisterMutation.error = null
    mockRegisterMutation.isSuccess = false
    mockAuthStore.isLoading = false
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  // Rendering Tests
  describe("Rendering", () => {
    it("renders all required form fields correctly", () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      // Title and description
      expect(
        screen.getByRole("heading", { name: /create your account/i })
      ).toBeInTheDocument()
      expect(
        screen.getByText(/join the ultimate electrical designer/i)
      ).toBeInTheDocument()

      // Personal information fields
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
      expect(
        screen.getByPlaceholderText(/enter your password/i)
      ).toBeInTheDocument()
      expect(
        screen.getByPlaceholderText(/confirm your password/i)
      ).toBeInTheDocument()

      // Submit button
      expect(
        screen.getByRole("button", { name: /create account/i })
      ).toBeInTheDocument()

      // Optional links
      expect(screen.getByText(/already have an account/i)).toBeInTheDocument()
    })

    it("renders professional credentials section", () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      // Professional fields section
      expect(screen.getByText(/professional credentials/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/professional license/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/company name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/job title/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/years of experience/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/specializations/i)).toBeInTheDocument()
    })

    it("renders with custom title when provided", () => {
      render(
        <TestWrapper>
          <RegistrationForm
            {...defaultProps}
            title="Professional Registration"
          />
        </TestWrapper>
      )

      expect(
        screen.getByRole("heading", { name: /professional registration/i })
      ).toBeInTheDocument()
    })

    it("renders without optional links when disabled", () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} showSignInLink={false} />
        </TestWrapper>
      )

      expect(
        screen.queryByText(/already have an account/i)
      ).not.toBeInTheDocument()
    })
  })

  // Form Validation Tests
  describe("Form Validation", () => {
    it("shows validation errors for empty required fields on submit", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/full name is required/i)).toBeInTheDocument()
        expect(
          screen.getByText(/email or username is required/i)
        ).toBeInTheDocument()
        expect(screen.getByText(/password is required/i)).toBeInTheDocument()
      })
    })

    it("shows password confirmation validation error", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const passwordInput = screen.getByPlaceholderText(/enter your password/i)
      const confirmPasswordInput = screen.getByPlaceholderText(
        /confirm your password/i
      )

      await user.type(passwordInput, "password123")
      await user.type(confirmPasswordInput, "differentpassword")

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/passwords do not match/i)).toBeInTheDocument()
      })
    })

    it("shows email validation error for invalid email format", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      await user.type(emailInput, "invalid-email")

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText(/please enter a valid email address/i)
        ).toBeInTheDocument()
      })
    })

    it("validates professional license format when provided", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      const licenseInput = screen.getByLabelText(/professional license/i)
      await user.type(licenseInput, "INVALID")

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/invalid license format/i)).toBeInTheDocument()
      })
    })

    it("validates years of experience is a positive number", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      const experienceInput = screen.getByLabelText(/years of experience/i)
      await user.type(experienceInput, "-5")

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText(/years of experience must be a positive number/i)
        ).toBeInTheDocument()
      })
    })

    it("clears validation errors when valid input is provided", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      // Trigger validation errors
      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText(/email or username is required/i)
        ).toBeInTheDocument()
      })

      // Fix email
      const emailInput = screen.getByLabelText(/email address/i)
      await user.type(emailInput, "<EMAIL>")

      await waitFor(() => {
        expect(
          screen.queryByText(/email or username is required/i)
        ).not.toBeInTheDocument()
      })
    })
  })

  // Form Interaction Tests
  describe("Form Interaction", () => {
    it("handles form input changes correctly", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const nameInput = screen.getByLabelText(/full name/i) as HTMLInputElement
      const emailInput = screen.getByLabelText(
        /email address/i
      ) as HTMLInputElement

      await user.type(nameInput, "John Doe")
      await user.type(emailInput, "<EMAIL>")

      expect(nameInput.value).toBe("John Doe")
      expect(emailInput.value).toBe("<EMAIL>")
    })

    it("toggles password visibility for both password fields", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const passwordInput = screen.getByPlaceholderText(
        /enter your password/i
      ) as HTMLInputElement
      const confirmPasswordInput = screen.getByPlaceholderText(
        /confirm your password/i
      ) as HTMLInputElement

      const passwordToggleButtons = screen.getAllByRole("button", {
        name: /toggle password visibility/i,
      })

      // Initially passwords should be hidden
      expect(passwordInput.type).toBe("password")
      expect(confirmPasswordInput.type).toBe("password")

      // Click to show first password
      await user.click(passwordToggleButtons[0])
      expect(passwordInput.type).toBe("text")

      // Click to show confirm password
      await user.click(passwordToggleButtons[1])
      expect(confirmPasswordInput.type).toBe("text")
    })

    it("handles specializations selection", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      // Check if specializations field is interactive
      const specializationsInput = screen.getByLabelText(/specializations/i)
      expect(specializationsInput).toBeInTheDocument()

      // Type in specializations
      await user.type(specializationsInput, "Power Systems, Industrial Control")

      expect((specializationsInput as HTMLInputElement).value).toBe(
        "Power Systems, Industrial Control"
      )
    })

    it("submits form with complete professional data", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      // Fill all required fields
      await user.type(screen.getByLabelText(/full name/i), "John Doe")
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "SecurePass123!"
      )

      // Fill professional fields
      await user.type(
        screen.getByLabelText(/professional license/i),
        "PE-12345"
      )
      await user.type(
        screen.getByLabelText(/company name/i),
        "Engineering Solutions Inc"
      )
      await user.type(
        screen.getByLabelText(/job title/i),
        "Senior Electrical Engineer"
      )
      await user.type(screen.getByLabelText(/years of experience/i), "8")
      await user.type(
        screen.getByLabelText(/specializations/i),
        "Power Systems, Industrial Control"
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockRegisterMutation.mutate).toHaveBeenCalledWith({
          name: "John Doe",
          email: "<EMAIL>",
          password: "SecurePass123!",
          confirm_password: "SecurePass123!",
          professional_license: "PE-12345",
          company_name: "Engineering Solutions Inc",
          job_title: "Senior Electrical Engineer",
          years_experience: 8,
          specializations: ["Power Systems", "Industrial Control"],
        })
      })
    })

    it("submits form with minimal required data only", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      // Fill only required fields
      await user.type(screen.getByLabelText(/full name/i), "Jane Smith")
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "SecurePass123!"
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockRegisterMutation.mutate).toHaveBeenCalledWith({
          name: "Jane Smith",
          email: "<EMAIL>",
          password: "SecurePass123!",
          confirm_password: "SecurePass123!",
        })
      })
    })
  })

  // Accessibility Tests
  describe("Accessibility", () => {
    it("has proper form structure with labels", () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const nameInput = screen.getByLabelText(/full name/i)
      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText(/enter your password/i)

      expect(nameInput).toHaveAttribute("type", "text")
      expect(nameInput).toHaveAttribute("required")
      expect(nameInput).toHaveAttribute("aria-invalid", "false")

      expect(emailInput).toHaveAttribute("type", "email")
      expect(emailInput).toHaveAttribute("required")

      expect(passwordInput).toHaveAttribute("type", "password")
      expect(passwordInput).toHaveAttribute("required")
    })

    it("sets aria-invalid to true when validation errors exist", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        const nameInput = screen.getByLabelText(/full name/i)
        const emailInput = screen.getByLabelText(/email address/i)

        expect(nameInput).toHaveAttribute("aria-invalid", "true")
        expect(emailInput).toHaveAttribute("aria-invalid", "true")
      })
    })

    it("has proper keyboard navigation", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const nameInput = screen.getByLabelText(/full name/i)
      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText(/enter your password/i)
      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })

      // Tab navigation
      nameInput.focus()
      expect(nameInput).toHaveFocus()

      await user.tab()
      expect(emailInput).toHaveFocus()

      await user.tab()
      expect(passwordInput).toHaveFocus()

      // Continue tabbing to submit button
      await user.tab()
      await user.tab() // Confirm password
      // Skip through professional fields if shown
      while (document.activeElement !== submitButton) {
        await user.tab()
      }
      expect(submitButton).toHaveFocus()
    })

    it("announces validation errors to screen readers", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      await waitFor(() => {
        const errorElements = screen.getAllByRole("alert")
        expect(errorElements.length).toBeGreaterThanOrEqual(3) // At least name, email, and password errors
      })
    })
  })

  // Loading State Tests
  describe("Loading States", () => {
    it("shows loading state when form is being submitted", () => {
      // Mock pending state
      mockRegisterMutation.isPending = true

      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", {
        name: /creating account/i,
      })
      expect(submitButton).toBeDisabled()
      expect(submitButton).toHaveAttribute("aria-disabled", "true")
    })

    it("disables form during submission", async () => {
      // Mock pending state
      mockRegisterMutation.isPending = true

      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      const nameInput = screen.getByLabelText(/full name/i)
      const emailInput = screen.getByLabelText(/email address/i)

      expect(nameInput).toBeDisabled()
      expect(emailInput).toBeDisabled()
    })
  })

  // Error Handling Tests
  describe("Error Handling", () => {
    it("displays server error messages", () => {
      // Mock error state
      const errorMessage = "Registration failed: Email already exists"
      mockRegisterMutation.error = { message: errorMessage }

      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByRole("alert")).toBeInTheDocument()
    })

    it("calls onError callback when provided and error occurs", () => {
      const mockOnError = vi.fn()
      const errorMessage = "Network error"

      mockRegisterMutation.error = { message: errorMessage }

      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} onError={mockOnError} />
        </TestWrapper>
      )

      expect(mockOnError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: errorMessage,
        })
      )
    })
  })

  // Success State Tests
  describe("Success States", () => {
    it("calls onSuccess callback when registration succeeds", async () => {
      const mockOnSuccess = vi.fn()

      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} onSuccess={mockOnSuccess} />
        </TestWrapper>
      )

      // Submit valid form
      await user.type(screen.getByLabelText(/full name/i), "Test User")
      await user.type(
        screen.getByLabelText(/email address/i),
        "<EMAIL>"
      )
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "SecurePass123!"
      )

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      // Verify onSuccess is called on successful submission
      await waitFor(() => {
        expect(mockRegisterMutation.mutate).toHaveBeenCalled()
      })
    })
  })

  // Integration Tests
  describe("Integration", () => {
    it("integrates properly with auth hooks", async () => {
      render(
        <TestWrapper>
          <RegistrationForm {...defaultProps} showProfessionalFields={true} />
        </TestWrapper>
      )

      // Fill comprehensive form data
      await user.type(
        screen.getByLabelText(/full name/i),
        "Professional Engineer"
      )
      await user.type(screen.getByLabelText(/email address/i), "<EMAIL>")
      await user.type(
        screen.getByPlaceholderText(/enter your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByPlaceholderText(/confirm your password/i),
        "SecurePass123!"
      )
      await user.type(
        screen.getByLabelText(/professional license/i),
        "PE-54321"
      )
      await user.type(
        screen.getByLabelText(/company name/i),
        "Engineering Firm LLC"
      )
      await user.type(screen.getByLabelText(/job title/i), "Principal Engineer")
      await user.type(screen.getByLabelText(/years of experience/i), "15")

      const submitButton = screen.getByRole("button", {
        name: /create account/i,
      })
      await user.click(submitButton)

      // Verify auth hook called with comprehensive professional data
      await waitFor(() => {
        expect(mockRegisterMutation.mutate).toHaveBeenCalledWith(
          expect.objectContaining({
            name: "Professional Engineer",
            email: "<EMAIL>",
            password: "SecurePass123!",
            confirm_password: "SecurePass123!",
            professional_license: "PE-54321",
            company_name: "Engineering Firm LLC",
            job_title: "Principal Engineer",
            years_experience: 15,
          })
        )
      })
    })
  })
})
