/**
 * @file LoginForm organism tests
 * @description Comprehensive tests for login form with validation and auth integration
 */
import type { LoginFormProps } from "../login-form"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { render, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { LoginForm } from "../login-form"

// Mock auth hooks
const mockLoginMutation = {
  mutate: vi.fn(),
  isPending: false,
  error: null as any,
  isSuccess: false,
}

const mockAuthStore = {
  isLoading: false,
  setLoading: vi.fn(),
}

vi.mock("@/hooks/api/useAuth", () => ({
  useLogin: () => mockLoginMutation,
}))

vi.mock("@/stores/authStore", () => ({
  useAuthStore: () => mockAuthStore,
}))

// Test wrapper with React Query
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient()
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
}

const defaultProps: LoginFormProps = {
  onSuccess: vi.fn(),
  onError: vi.fn(),
}

describe("LoginForm", () => {
  let user: ReturnType<typeof userEvent.setup>

  beforeEach(() => {
    user = userEvent.setup()
    vi.clearAllMocks()
    // Reset mock state
    mockLoginMutation.mutate.mockClear()
    mockLoginMutation.isPending = false
    mockLoginMutation.error = null
    mockLoginMutation.isSuccess = false
    mockAuthStore.isLoading = false
    mockLoginMutation.error = null
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  // Rendering Tests
  describe("Rendering", () => {
    it("renders all form elements correctly", () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      // Title and description
      expect(
        screen.getByRole("heading", { name: /welcome back/i })
      ).toBeInTheDocument()
      expect(screen.getByText(/sign in to your account/i)).toBeInTheDocument()

      // Form fields
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
      expect(
        screen.getByPlaceholderText("Enter your password")
      ).toBeInTheDocument()

      // Submit button
      expect(
        screen.getByRole("button", { name: /sign in/i })
      ).toBeInTheDocument()

      // Optional elements
      expect(screen.getByText(/forgot password/i)).toBeInTheDocument()
      expect(screen.getByText(/don't have an account/i)).toBeInTheDocument()
    })

    it("renders with custom title when provided", () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} title="Custom Login Title" />
        </TestWrapper>
      )

      expect(
        screen.getByRole("heading", { name: /custom login title/i })
      ).toBeInTheDocument()
    })

    it("renders without optional links when disabled", () => {
      render(
        <TestWrapper>
          <LoginForm
            {...defaultProps}
            showForgotPassword={false}
            showSignUpLink={false}
          />
        </TestWrapper>
      )

      expect(screen.queryByText(/forgot password/i)).not.toBeInTheDocument()
      expect(
        screen.queryByText(/don't have an account/i)
      ).not.toBeInTheDocument()
    })
  })

  // Form Validation Tests
  describe("Form Validation", () => {
    it("shows validation errors for empty fields on submit", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText(/email or username is required/i)
        ).toBeInTheDocument()
        expect(screen.getByText(/password is required/i)).toBeInTheDocument()
      })
    })

    it("shows email validation error for invalid email format", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      await user.type(emailInput, "invalid-email")

      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText(/please enter a valid email address/i)
        ).toBeInTheDocument()
      })
    })

    it("shows password validation error for short password", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const passwordInput = screen.getByPlaceholderText("Enter your password")
      await user.type(passwordInput, "123")

      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText(/password must be at least 8 characters/i)
        ).toBeInTheDocument()
      })
    })

    it("clears validation errors when valid input is provided", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      // Trigger validation errors
      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      })

      // Fix email
      const emailInput = screen.getByLabelText(/email address/i)
      await user.type(emailInput, "<EMAIL>")

      await waitFor(() => {
        expect(
          screen.queryByText(/email or username is required/i)
        ).not.toBeInTheDocument()
      })
    })
  })

  // Form Interaction Tests
  describe("Form Interaction", () => {
    it("handles form input changes correctly", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(
        /email address/i
      ) as HTMLInputElement
      const passwordInput = screen.getByPlaceholderText(
        "Enter your password"
      ) as HTMLInputElement

      await user.type(emailInput, "<EMAIL>")
      await user.type(passwordInput, "password123")

      expect(emailInput.value).toBe("<EMAIL>")
      expect(passwordInput.value).toBe("password123")
    })

    it("toggles password visibility when eye icon is clicked", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const passwordInput = screen.getByPlaceholderText(
        "Enter your password"
      ) as HTMLInputElement
      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })

      // Initially password should be hidden
      expect(passwordInput.type).toBe("password")

      // Click to show password
      await user.click(toggleButton)
      expect(passwordInput.type).toBe("text")

      // Click to hide password again
      await user.click(toggleButton)
      expect(passwordInput.type).toBe("password")
    })

    it("submits form with valid data", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText("Enter your password")
      const submitButton = screen.getByRole("button", { name: /sign in/i })

      await user.type(emailInput, "<EMAIL>")
      await user.type(passwordInput, "password123")
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockLoginMutation.mutate).toHaveBeenCalledWith({
          username: "<EMAIL>",
          password: "password123",
        })
      })
    })

    it("calls onSuccess callback when provided", async () => {
      const mockOnSuccess = vi.fn()

      render(
        <TestWrapper>
          <LoginForm {...defaultProps} onSuccess={mockOnSuccess} />
        </TestWrapper>
      )

      // Submit valid form
      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText("Enter your password")
      const submitButton = screen.getByRole("button", { name: /sign in/i })

      await user.type(emailInput, "<EMAIL>")
      await user.type(passwordInput, "password123")
      await user.click(submitButton)

      // Verify mutation is called on successful submission
      await waitFor(() => {
        expect(mockLoginMutation.mutate).toHaveBeenCalled()
      })
    })
  })

  // Accessibility Tests
  describe("Accessibility", () => {
    it("has proper form structure with labels", () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText("Enter your password")

      expect(emailInput).toHaveAttribute("type", "email")
      expect(emailInput).toHaveAttribute("required")
      expect(emailInput).toHaveAttribute("aria-invalid", "false")

      expect(passwordInput).toHaveAttribute("type", "password")
      expect(passwordInput).toHaveAttribute("required")
      expect(passwordInput).toHaveAttribute("aria-invalid", "false")
    })

    it("sets aria-invalid to true when validation errors exist", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        const emailInput = screen.getByLabelText(/email address/i)
        const passwordInput = screen.getByPlaceholderText("Enter your password")

        expect(emailInput).toHaveAttribute("aria-invalid", "true")
        expect(passwordInput).toHaveAttribute("aria-invalid", "true")
      })
    })

    it("has proper keyboard navigation", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText("Enter your password")
      const submitButton = screen.getByRole("button", { name: /sign in/i })

      // Tab navigation
      emailInput.focus()
      expect(emailInput).toHaveFocus()

      await user.tab()
      expect(passwordInput).toHaveFocus()

      await user.tab() // Password visibility toggle
      const passwordToggle = screen.getByLabelText(
        /toggle password visibility/i
      )
      expect(passwordToggle).toHaveFocus()

      await user.tab() // Forgot password button
      await user.tab() // Submit button
      expect(submitButton).toHaveFocus()
    })

    it("announces validation errors to screen readers", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        const errorElements = screen.getAllByRole("alert")
        expect(errorElements).toHaveLength(2) // Email and password errors
      })
    })
  })

  // Loading State Tests
  describe("Loading States", () => {
    it("shows loading state when form is being submitted", () => {
      // Mock pending state
      mockLoginMutation.isPending = true

      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const submitButton = screen.getByRole("button", { name: /signing in/i })
      expect(submitButton).toBeDisabled()
      expect(submitButton).toHaveAttribute("aria-disabled", "true")
    })

    it("disables form during submission", async () => {
      // Mock pending state
      mockLoginMutation.isPending = true

      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText("Enter your password")

      expect(emailInput).toBeDisabled()
      expect(passwordInput).toBeDisabled()
    })
  })

  // Error Handling Tests
  describe("Error Handling", () => {
    it("displays server error messages", () => {
      // Mock error state
      const errorMessage = "Invalid credentials"
      mockLoginMutation.error = { message: errorMessage }

      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      expect(screen.getByText(errorMessage)).toBeInTheDocument()
      expect(screen.getByRole("alert")).toBeInTheDocument()
    })

    it("calls onError callback when provided and error occurs", () => {
      const mockOnError = vi.fn()
      const errorMessage = "Network error"

      mockLoginMutation.error = { message: errorMessage }

      render(
        <TestWrapper>
          <LoginForm {...defaultProps} onError={mockOnError} />
        </TestWrapper>
      )

      expect(mockOnError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: errorMessage,
        })
      )
    })
  })

  // Integration Tests
  describe("Integration", () => {
    it("integrates properly with auth hooks", async () => {
      render(
        <TestWrapper>
          <LoginForm {...defaultProps} />
        </TestWrapper>
      )

      // Fill and submit form
      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByPlaceholderText("Enter your password")
      const submitButton = screen.getByRole("button", { name: /sign in/i })

      await user.type(emailInput, "<EMAIL>")
      await user.type(passwordInput, "SecurePass123!")
      await user.click(submitButton)

      // Verify auth hook called with correct data
      await waitFor(() => {
        expect(mockLoginMutation.mutate).toHaveBeenCalledWith({
          username: "<EMAIL>",
          password: "SecurePass123!",
        })
      })
    })
  })
})
