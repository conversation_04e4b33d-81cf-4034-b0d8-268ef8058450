/**
 * @file RegistrationForm organism component
 * @description Complete registration form with professional credentials validation and auth integration
 */

import * as React from "react"

import type { RegisterRequest } from "@/components/modules/auth/types"

import { useAuthStore } from "@/stores/authStore"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"

import { cn } from "@/lib/utils"
import { registrationFormSchema } from "@/lib/validation/forms"
import { useRegister } from "@/hooks/api/useAuth"

import { Button } from "@/components/atoms/button"
import { UnifiedIcon as Icon } from "@/components/atoms/icon"
import { InputField } from "@/components/molecules/input-field"
import { PasswordInput } from "@/components/molecules/password-input"

type RegistrationFormData = z.infer<typeof registrationFormSchema>

export interface RegistrationFormProps {
  /** Custom title for the form */
  title?: string
  /** Custom description for the form */
  description?: string
  /** Show professional credentials fields */
  showProfessionalFields?: boolean
  /** Show sign in link */
  showSignInLink?: boolean
  /** Callback fired on successful registration */
  onSuccess?: (response: any) => void
  /** Callback fired on registration error */
  onError?: (error: any) => void
  /** Callback fired when sign in link is clicked */
  onSignIn?: () => void
  /** Additional CSS classes */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

export const RegistrationForm = React.forwardRef<
  HTMLFormElement,
  RegistrationFormProps
>(
  (
    {
      title = "Create Your Account",
      description = "Join the Ultimate Electrical Designer platform",
      showProfessionalFields = false,
      showSignInLink = true,
      onSuccess,
      onError,
      onSignIn,
      className,
      "data-testid": dataTestId,
    },
    ref
  ) => {
    // Auth hooks and state
    const register = useRegister()
    const { isLoading } = useAuthStore()

    // React Hook Form with Zod
    const form = useForm({
      resolver: zodResolver(registrationFormSchema),
      defaultValues: {
        name: "",
        email: "",
        password: "",
        confirm_password: "",
        professional_license: "",
        company_name: "",
        job_title: "",
        years_experience: undefined,
        specializations: "",
        terms_accepted: false,
      },
      mode: "onBlur",
    })

    const {
      register: formRegister,
      handleSubmit,
      formState: { errors, isSubmitting },
    } = form

    // Submit handler
    const onSubmit = React.useCallback(
      async (data: RegistrationFormData) => {
        const registerData: RegisterRequest = {
          name: data.name,
          email: data.email,
          password: data.password,
          confirm_password: data.confirm_password,
        }

        if (showProfessionalFields) {
          if (data.professional_license?.trim()) {
            registerData.professional_license = data.professional_license.trim()
          }
          if (data.company_name?.trim()) {
            registerData.company_name = data.company_name.trim()
          }
          if (data.job_title?.trim()) {
            registerData.job_title = data.job_title.trim()
          }
          if (data.years_experience) {
            registerData.years_experience = data.years_experience
          }
          if (
            typeof data.specializations === "string" &&
            data.specializations.trim()
          ) {
            registerData.specializations = data.specializations
              .split(",")
              .map((s) => s.trim())
              .filter(Boolean)
          }
        }

        register.mutate(registerData)
      },
      [register, showProfessionalFields]
    )

    // Handle registration success
    React.useEffect(() => {
      if (register.isSuccess && register.data) {
        onSuccess?.(register.data)
      }
    }, [register.isSuccess, register.data, onSuccess])

    // Handle registration error
    React.useEffect(() => {
      if (register.error) {
        onError?.(register.error)
      }
    }, [register.error, onError])

    // Determine loading state
    const isPending = register.isPending || isLoading
    const hasServerError = Boolean(register.error)

    return (
      <form
        ref={ref}
        onSubmit={handleSubmit(onSubmit)}
        className={cn(
          "bg-card w-full max-w-lg space-y-6 rounded-lg border p-6 shadow-sm",
          className
        )}
        data-testid={dataTestId}
        noValidate
      >
        {/* Header */}
        <div className="space-y-2 text-center">
          <h1 className="text-foreground text-2xl font-semibold tracking-tight">
            {title}
          </h1>
          {description && (
            <p className="text-muted-foreground text-sm">{description}</p>
          )}
        </div>

        {/* Server Error Display */}
        {hasServerError && (
          <div
            role="alert"
            className="border-destructive bg-destructive/10 rounded-md border p-3"
          >
            <div className="flex items-center space-x-2">
              <Icon type="alert" className="text-destructive h-4 w-4" />
              <p className="text-destructive text-sm">
                {register.error?.message ||
                  "An error occurred during registration"}
              </p>
            </div>
          </div>
        )}

        {/* Personal Information Section */}
        <div className="space-y-4">
          <h2 className="text-foreground text-lg font-medium">
            Personal Information
          </h2>

          {/* Full Name Field */}
          <InputField
            label="Full Name"
            type="text"
            {...formRegister("name")}
            error={errors.name?.message as string}
            required
            disabled={isPending || isSubmitting}
            placeholder="Enter your full name"
            autoComplete="name"
            className="w-full"
          />

          {/* Email Field */}
          <InputField
            label="Email Address"
            type="email"
            {...formRegister("email")}
            error={errors.email?.message as string}
            required
            disabled={isPending || isSubmitting}
            placeholder="Enter your email address"
            autoComplete="email"
            className="w-full"
          />

          {/* Password Field */}
          <PasswordInput
            label="Password"
            {...formRegister("password")}
            error={errors.password?.message as string}
            required
            disabled={isPending || isSubmitting}
            placeholder="Enter your password"
            autoComplete="new-password"
            className="w-full"
          />

          {/* Confirm Password Field */}
          <PasswordInput
            label="Confirm Password"
            {...formRegister("confirm_password")}
            error={errors.confirm_password?.message as string}
            required
            disabled={isPending || isSubmitting}
            placeholder="Confirm your password"
            autoComplete="new-password"
            className="w-full"
          />
        </div>

        {/* Professional Credentials Section */}
        {showProfessionalFields && (
          <div className="space-y-4">
            <div className="border-t pt-4">
              <h2 className="text-foreground text-lg font-medium">
                Professional Credentials
              </h2>
              <p className="text-muted-foreground text-sm">
                Optional professional information for electrical design
                verification
              </p>
            </div>

            {/* Professional License */}
            <InputField
              label="Professional License"
              type="text"
              {...formRegister("professional_license")}
              error={errors.professional_license?.message as string}
              disabled={isPending || isSubmitting}
              placeholder="e.g., PE-12345, EIT-67890"
              helpText="Professional Engineer (PE) or Engineer in Training (EIT) license number"
              className="w-full"
            />

            {/* Company Name */}
            <InputField
              label="Company Name"
              type="text"
              {...formRegister("company_name")}
              error={errors.company_name?.message as string}
              disabled={isPending || isSubmitting}
              placeholder="Enter your company or organization"
              autoComplete="organization"
              className="w-full"
            />

            {/* Job Title */}
            <InputField
              label="Job Title"
              type="text"
              {...formRegister("job_title")}
              error={errors.job_title?.message as string}
              disabled={isPending || isSubmitting}
              placeholder="e.g., Electrical Engineer, Senior Designer"
              autoComplete="organization-title"
              className="w-full"
            />

            {/* Years of Experience */}
            <InputField
              label="Years of Experience"
              type="number"
              {...formRegister("years_experience")}
              error={errors.years_experience?.message as string}
              disabled={isPending || isSubmitting}
              placeholder="0"
              min="0"
              max="50"
              className="w-full"
            />

            {/* Specializations */}
            <InputField
              label="Specializations"
              type="text"
              {...formRegister("specializations")}
              error={errors.specializations?.message as string}
              disabled={isPending || isSubmitting}
              placeholder="e.g., Power Systems, Industrial Control, Renewable Energy"
              helpText="Comma-separated list of your electrical engineering specializations"
              className="w-full"
            />
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          disabled={isPending || isSubmitting}
          aria-disabled={isPending || isSubmitting}
        >
          {isPending || isSubmitting ? (
            <>
              <Icon type="refresh" className="mr-2 h-4 w-4 animate-spin" />
              Creating Account...
            </>
          ) : (
            <>
              <Icon type="user" className="mr-2 h-4 w-4" />
              Create Account
            </>
          )}
        </Button>

        {/* Sign In Link */}
        {showSignInLink && (
          <div className="text-muted-foreground text-center text-sm">
            Already have an account?{" "}
            <button
              type="button"
              onClick={onSignIn}
              className="text-primary hover:text-primary/80 focus:ring-primary font-medium focus:ring-2 focus:ring-offset-2 focus:outline-none"
              disabled={isPending || isSubmitting}
            >
              Sign in
            </button>
          </div>
        )}
      </form>
    )
  }
)

RegistrationForm.displayName = "RegistrationForm"
