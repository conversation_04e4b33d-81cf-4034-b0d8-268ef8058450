/**
 * @file PasswordInput molecule component
 * @description Specialized InputField for password inputs with visibility toggle
 */

import * as React from "react"

import type { InputFieldProps } from "./input-field"

import { cn } from "@/lib/utils"

import { Button } from "@/components/atoms/button"
import { UnifiedIcon } from "@/components/atoms/icon"

import { InputField } from "./input-field"

export interface PasswordInputProps
  extends Omit<InputFieldProps, "type" | "data-testid"> {
  autoComplete?: "current-password" | "new-password" | string
  "data-testid"?: string
}

export const PasswordInput = React.forwardRef<
  HTMLInputElement,
  PasswordInputProps
>(
  (
    {
      className,
      autoComplete = "current-password",
      "data-testid": dataTestId,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false)

    const togglePasswordVisibility = React.useCallback(() => {
      setShowPassword((prev) => !prev)
    }, [])

    const handleKeyDown = React.useCallback(
      (event: React.KeyboardEvent<HTMLButtonElement>) => {
        if (event.key === "Enter" || event.key === " ") {
          event.preventDefault()
          togglePasswordVisibility()
        }
      },
      [togglePasswordVisibility]
    )

    return (
      <div className={cn("relative", className)} data-testid={dataTestId}>
        <InputField
          ref={ref}
          type={showPassword ? "text" : "password"}
          autoComplete={autoComplete}
          className="pr-12" // Make room for the toggle button
          {...props}
        />

        <Button
          type="button"
          variant="ghost"
          size="icon"
          className="absolute top-0 right-0 h-9 w-9 rounded-md hover:bg-transparent focus-visible:bg-transparent"
          style={{ top: props.label ? "2rem" : "0" }} // Adjust position based on label presence
          onClick={togglePasswordVisibility}
          onKeyDown={handleKeyDown}
          aria-label="Toggle password visibility"
        >
          {showPassword ? (
            <UnifiedIcon
              type="hide_password"
              size="sm"
              data-testid="hide-password-icon"
              className="text-muted-foreground hover:text-foreground"
            />
          ) : (
            <UnifiedIcon
              type="show_password"
              size="sm"
              data-testid="show-password-icon"
              className="text-muted-foreground hover:text-foreground"
            />
          )}
        </Button>
      </div>
    )
  }
)

PasswordInput.displayName = "PasswordInput"
