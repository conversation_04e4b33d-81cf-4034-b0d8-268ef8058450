/**
 * @file PasswordInput molecule tests
 * @description TDD tests for PasswordInput molecule component
 */

import { fireEvent, render, screen } from "@testing-library/react"
import { describe, expect, it } from "vitest"

import { PasswordInput } from "../password-input"

describe("PasswordInput Molecule", () => {
  describe("Basic functionality", () => {
    it("should render as password input by default", () => {
      render(
        <PasswordInput
          label="Password"
          id="test-password"
          data-testid="password-field"
        />
      )

      const input = screen.getByLabelText("Password") as HTMLInputElement
      expect(input).toHaveAttribute("type", "password")
    })

    it("should include visibility toggle button", () => {
      render(
        <PasswordInput
          label="Password"
          id="toggle-password"
          data-testid="toggle-password-field"
        />
      )

      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })
      expect(toggleButton).toBeInTheDocument()
      expect(toggleButton).toHaveAttribute("type", "button")
    })

    it("should toggle password visibility when button is clicked", () => {
      render(
        <PasswordInput
          label="Password"
          id="visibility-password"
          data-testid="visibility-password-field"
        />
      )

      const input = screen.getByLabelText("Password") as HTMLInputElement
      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })

      // Initially should be password type
      expect(input).toHaveAttribute("type", "password")

      // Click to show password
      fireEvent.click(toggleButton)
      expect(input).toHaveAttribute("type", "text")

      // Click again to hide password
      fireEvent.click(toggleButton)
      expect(input).toHaveAttribute("type", "password")
    })
  })

  describe("Visual indicators", () => {
    it("should show appropriate icon when password is hidden", () => {
      render(
        <PasswordInput
          label="Password"
          id="hidden-password"
          data-testid="hidden-password-field"
        />
      )

      // Look for the show password icon (eye)
      const showIcon = screen.getByTestId("show-password-icon")
      expect(showIcon).toBeInTheDocument()
    })

    it("should show appropriate icon when password is visible", () => {
      render(
        <PasswordInput
          label="Password"
          id="visible-password"
          data-testid="visible-password-field"
        />
      )

      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })

      // Click to show password
      fireEvent.click(toggleButton)

      // Look for the hide password icon (eye-off)
      const hideIcon = screen.getByTestId("hide-password-icon")
      expect(hideIcon).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("should have proper ARIA attributes on toggle button", () => {
      render(
        <PasswordInput
          label="Password"
          id="aria-password"
          data-testid="aria-password-field"
        />
      )

      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })
      expect(toggleButton).toHaveAttribute(
        "aria-label",
        "Toggle password visibility"
      )
      expect(toggleButton).toHaveAttribute("type", "button")
    })

    it("should update button state for screen readers", () => {
      render(
        <PasswordInput
          label="Password"
          id="screen-reader-password"
          data-testid="screen-reader-password-field"
        />
      )

      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })

      // Initially should indicate password is hidden
      expect(toggleButton).toHaveAttribute(
        "aria-label",
        "Toggle password visibility"
      )

      // After clicking, should update for screen readers
      fireEvent.click(toggleButton)
      expect(toggleButton).toHaveAttribute(
        "aria-label",
        "Toggle password visibility"
      )
    })

    it("should support keyboard navigation", () => {
      render(
        <PasswordInput
          label="Password"
          id="keyboard-password"
          data-testid="keyboard-password-field"
        />
      )

      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })

      // Should be focusable
      toggleButton.focus()
      expect(toggleButton).toHaveFocus()

      // Should respond to Enter key
      fireEvent.keyDown(toggleButton, { key: "Enter", code: "Enter" })
      // The input type should change after keydown
      const input = screen.getByLabelText("Password") as HTMLInputElement
      expect(input).toHaveAttribute("type", "text")
    })
  })

  describe("Form integration", () => {
    it("should handle value changes correctly", () => {
      render(
        <PasswordInput
          label="Password"
          id="value-password"
          data-testid="value-password-field"
        />
      )

      const input = screen.getByLabelText("Password") as HTMLInputElement
      fireEvent.change(input, { target: { value: "secret123" } })

      expect(input.value).toBe("secret123")
    })

    it("should maintain value when toggling visibility", () => {
      render(
        <PasswordInput
          label="Password"
          id="maintain-password"
          data-testid="maintain-password-field"
        />
      )

      const input = screen.getByLabelText("Password") as HTMLInputElement
      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })

      // Enter a value
      fireEvent.change(input, { target: { value: "test-password" } })
      expect(input.value).toBe("test-password")

      // Toggle visibility
      fireEvent.click(toggleButton)
      expect(input.value).toBe("test-password")
      expect(input).toHaveAttribute("type", "text")

      // Toggle back
      fireEvent.click(toggleButton)
      expect(input.value).toBe("test-password")
      expect(input).toHaveAttribute("type", "password")
    })

    it("should support autoComplete attributes", () => {
      render(
        <PasswordInput
          label="Current Password"
          id="current-password"
          autoComplete="current-password"
          data-testid="current-password-field"
        />
      )

      const input = screen.getByLabelText("Current Password")
      expect(input).toHaveAttribute("autoComplete", "current-password")
    })

    it("should support new password autoComplete", () => {
      render(
        <PasswordInput
          label="New Password"
          id="new-password"
          autoComplete="new-password"
          data-testid="new-password-field"
        />
      )

      const input = screen.getByLabelText("New Password")
      expect(input).toHaveAttribute("autoComplete", "new-password")
    })
  })

  describe("Error handling", () => {
    it("should display password validation errors", () => {
      render(
        <PasswordInput
          label="Password"
          id="error-password"
          error={[
            "Password must be at least 8 characters",
            "Password must contain uppercase letter",
          ]}
          data-testid="error-password-field"
        />
      )

      expect(
        screen.getByText("Password must be at least 8 characters")
      ).toBeInTheDocument()
      expect(
        screen.getByText("Password must contain uppercase letter")
      ).toBeInTheDocument()
    })

    it("should apply error styling", () => {
      render(
        <PasswordInput
          label="Password"
          id="styled-error-password"
          error="Password is required"
          data-testid="styled-error-password-field"
        />
      )

      const input = screen.getByLabelText("Password")
      expect(input).toHaveAttribute("aria-invalid", "true")
    })
  })

  describe("Help text and guidance", () => {
    it("should display password requirements as help text", () => {
      render(
        <PasswordInput
          label="Password"
          id="help-password"
          helpText="Password must be 8+ characters with uppercase, lowercase, numbers, and symbols"
          data-testid="help-password-field"
        />
      )

      const helpText = screen.getByText(
        "Password must be 8+ characters with uppercase, lowercase, numbers, and symbols"
      )
      expect(helpText).toBeInTheDocument()
    })

    it("should associate help text with input for accessibility", () => {
      render(
        <PasswordInput
          label="Password"
          id="accessible-help-password"
          helpText="Password requirements help text"
          data-testid="accessible-help-password-field"
        />
      )

      const input = screen.getByLabelText("Password")
      const describedBy = input.getAttribute("aria-describedby")

      expect(describedBy).toBeTruthy()
      expect(
        screen.getByText("Password requirements help text")
      ).toHaveAttribute(
        "id",
        expect.stringContaining(describedBy?.split(" ")[0] || "")
      )
    })
  })

  describe("Required field handling", () => {
    it("should show required indicator", () => {
      render(
        <PasswordInput
          label="Password"
          id="required-password"
          required
          data-testid="required-password-field"
        />
      )

      expect(screen.getByText("*")).toBeInTheDocument()

      const input = screen.getByLabelText(/Password/)
      expect(input).toHaveAttribute("required")
    })
  })

  describe("Styling and customization", () => {
    it("should support custom className", () => {
      render(
        <PasswordInput
          label="Password"
          id="custom-password"
          className="custom-password-field"
          data-testid="custom-password-field"
        />
      )

      const container = screen.getByTestId("custom-password-field")
      expect(container).toHaveClass("custom-password-field")
    })

    it("should position toggle button correctly", () => {
      render(
        <PasswordInput
          label="Password"
          id="positioned-password"
          data-testid="positioned-password-field"
        />
      )

      const toggleButton = screen.getByRole("button", {
        name: /toggle password visibility/i,
      })
      expect(toggleButton).toHaveClass("absolute")
      expect(toggleButton).toHaveClass("right-0")
    })
  })
})
