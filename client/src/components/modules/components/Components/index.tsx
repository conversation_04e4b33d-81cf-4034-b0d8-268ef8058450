/**
 * Component Management - Main Components Data Table
 *
 * Professional TanStack Table implementation for electrical component catalog management.
 * Features advanced filtering, sorting, CRUD operations, favorites system, and comprehensive
 * component specifications management with real-time validation and React Query integration.
 *
 * Key Features:
 * - Advanced TanStack Table with faceted filtering and sorting
 * - Full CRUD operations with optimistic updates
 * - Multi-currency pricing support (EUR, USD, GBP, CAD)
 * - Stock status management with visual indicators
 * - Favorites toggle functionality with persistent storage
 * - Category/Type hierarchical organization
 * - Technical specifications and weight tracking
 * - Professional UI with shadcn-ui components
 *
 * Technical Implementation:
 * - React Query for server state management and caching
 * - React Hook Form + Zod validation for type-safe forms
 * - UnifiedBadge system for consistent status display
 * - Responsive design with accessibility compliance
 * - Error handling with user-friendly toast notifications
 *
 * @module ComponentsManagement
 * @version 1.0.0
 * <AUTHOR> Electrical Designer Team
 * @since 2025-01-10
 */

"use client"

import { useId, useMemo, useState } from "react"

import type { ComponentRead } from "@/lib/api/types/components"

import {
  Column,
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
} from "@tanstack/react-table"
import {
  ChevronDownIcon,
  ChevronUpIcon,
  EditIcon,
  MoreHorizontalIcon,
  PlusIcon,
  SearchIcon,
  StarIcon,
  StarOffIcon,
  TrashIcon,
} from "lucide-react"

import { cn } from "@/lib/utils"
import {
  useComponents,
  useDeleteComponent,
  useMarkComponentAsPreferred,
  useUnmarkComponentAsPreferred,
} from "@/hooks/api/useComponents"
import { toast } from "@/hooks/useToast"

import { UnifiedBadge } from "@/components/atoms/badge"
import { Button } from "@/components/atoms/button"
import { Checkbox } from "@/components/atoms/checkbox"
import { Input } from "@/components/atoms/input"
import { Label } from "@/components/atoms/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/molecules/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/molecules/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/organisms/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/organisms/dropdown-menu"

import { ComponentForm } from "./ComponentForm"

/**
 * TanStack Table column metadata extension for advanced filtering capabilities.
 * Defines filter variants for different column types to enable specialized filtering UI.
 */
declare module "@tanstack/react-table" {
  interface ColumnMeta<TData extends RowData, TValue> {
    /** Filter UI variant - determines the input type for column filtering */
    filterVariant?: "text" | "range" | "select"
  }
}

/**
 * Creates column definitions for the Components data table with full CRUD functionality.
 *
 * Implements a comprehensive column structure with:
 * - Row selection checkboxes for bulk operations
 * - Name column with favorites star indicator
 * - Manufacturer, model number, and technical specifications
 * - Category/Type hierarchical display
 * - Multi-currency pricing with proper formatting
 * - Stock status with visual badge indicators
 * - Active/inactive status with filtering support
 * - Action dropdown with edit/favorite/delete operations
 *
 * @param deleteComponent - React Query mutation for component deletion
 * @param markAsPreferred - React Query mutation for marking as favorite
 * @param unmarkAsPreferred - React Query mutation for removing from favorites
 * @param setEditingComponent - State setter for edit mode activation
 * @returns Array of TanStack Table column definitions
 */
const createColumns = (
  deleteComponent: ReturnType<typeof useDeleteComponent>,
  markAsPreferred: ReturnType<typeof useMarkComponentAsPreferred>,
  unmarkAsPreferred: ReturnType<typeof useUnmarkComponentAsPreferred>,
  setEditingComponent: (component: ComponentRead) => void
): ColumnDef<ComponentRead>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => {
      const component = row.original
      const isPreferred = component.is_preferred

      return (
        <div className="flex items-center space-x-2">
          <div className="font-medium">{component.name}</div>
          {isPreferred && (
            <StarIcon className="h-4 w-4 fill-yellow-500 text-yellow-500" />
          )}
        </div>
      )
    },
  },
  {
    header: "Manufacturer",
    accessorKey: "manufacturer",
    cell: ({ row }) => {
      const manufacturer = row.getValue("manufacturer") as string
      return <div className="text-sm">{manufacturer || "—"}</div>
    },
    meta: {
      filterVariant: "select",
    },
  },
  {
    header: "Model Number",
    accessorKey: "model_number",
    cell: ({ row }) => {
      const modelNumber = row.getValue("model_number") as string
      return <div className="font-mono text-sm">{modelNumber || "—"}</div>
    },
  },
  {
    header: "Category",
    accessorKey: "category_name",
    cell: ({ row }) => {
      const categoryName = row.getValue("category_name") as string
      return <div className="text-sm">{categoryName || "—"}</div>
    },
    meta: {
      filterVariant: "select",
    },
  },
  {
    header: "Type",
    accessorKey: "type_name",
    cell: ({ row }) => {
      const typeName = row.getValue("type_name") as string
      return <div className="text-sm">{typeName || "—"}</div>
    },
    meta: {
      filterVariant: "select",
    },
  },
  {
    header: "Price",
    accessorKey: "unit_price",
    cell: ({ row }) => {
      const price = row.getValue("unit_price") as number
      const currency = (row.original as ComponentRead).currency || "EUR"

      if (!price) return <div className="text-muted-foreground">—</div>

      return (
        <div className="text-sm">
          {new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: currency,
          }).format(price)}
        </div>
      )
    },
    meta: {
      filterVariant: "range",
    },
  },
  {
    header: "Stock",
    accessorKey: "stock_status",
    cell: ({ row }) => {
      const stockStatus = row.getValue("stock_status") as string

      const getIntent = () => {
        switch (stockStatus) {
          case "available":
            return "available"
          case "limited":
            return "limited"
          case "out_of_stock":
            return "out_of_stock"
          case "discontinued":
            return "discontinued"
          default:
            return "available"
        }
      }

      return (
        <UnifiedBadge
          type="component"
          intent={getIntent() as any}
          size="sm"
          className="text-xs"
        />
      )
    },
    meta: {
      filterVariant: "select",
    },
  },
  {
    header: "Status",
    accessorKey: "is_active",
    cell: ({ row }) => {
      const isActive = row.getValue("is_active") as boolean
      return (
        <UnifiedBadge
          type="component"
          intent={isActive ? "active" : "inactive"}
          size="sm"
        />
      )
    },
    meta: {
      filterVariant: "select",
    },
    filterFn: (row, id, filterValue) => {
      const rowValue = row.getValue(id) as boolean
      return (
        filterValue === "all" ||
        (filterValue === "active" && rowValue) ||
        (filterValue === "inactive" && !rowValue)
      )
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const component = row.original
      return (
        <ComponentActionsCell
          component={component}
          deleteComponent={deleteComponent}
          markAsPreferred={markAsPreferred}
          unmarkAsPreferred={unmarkAsPreferred}
          onEdit={setEditingComponent}
        />
      )
    },
  },
]

/**
 * Props interface for the ComponentActionsCell component.
 * Defines the required mutations and callbacks for component actions.
 */
interface ComponentActionsCellProps {
  /** Component data for the current row */
  component: ComponentRead
  /** React Query mutation for component deletion with optimistic updates */
  deleteComponent: ReturnType<typeof useDeleteComponent>
  /** React Query mutation for marking component as preferred/favorite */
  markAsPreferred: ReturnType<typeof useMarkComponentAsPreferred>
  /** React Query mutation for removing component from favorites */
  unmarkAsPreferred: ReturnType<typeof useUnmarkComponentAsPreferred>
  /** Callback to activate edit mode for the component */
  onEdit: (component: ComponentRead) => void
}

/**
 * Action cell component for component table rows with edit, favorite, and delete operations.
 *
 * Provides a dropdown menu with:
 * - Edit action to modify component details
 * - Favorite toggle to add/remove from preferred components
 * - Delete action with confirmation dialog and loading states
 *
 * Features comprehensive error handling and user feedback via toast notifications.
 * Implements optimistic updates for better user experience.
 *
 * @param props - Component properties including mutations and callbacks
 * @returns JSX element with dropdown menu for row actions
 */
function ComponentActionsCell({
  component,
  deleteComponent,
  markAsPreferred,
  unmarkAsPreferred,
  onEdit,
}: ComponentActionsCellProps) {
  const handleDelete = async () => {
    try {
      await deleteComponent.mutateAsync(component.id)
      toast({
        title: "Success",
        description: `Component "${component.name}" deleted successfully`,
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete component",
        variant: "destructive",
      })
    }
  }

  const handleTogglePreferred = async () => {
    try {
      if (component.is_preferred) {
        await unmarkAsPreferred.mutateAsync(component.id)
        toast({
          title: "Success",
          description: `Component "${component.name}" removed from favorites`,
        })
      } else {
        await markAsPreferred.mutateAsync(component.id)
        toast({
          title: "Success",
          description: `Component "${component.name}" added to favorites`,
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update favorite status",
        variant: "destructive",
      })
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontalIcon className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onEdit(component)}>
          <EditIcon className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleTogglePreferred}>
          {component.is_preferred ? (
            <>
              <StarOffIcon className="mr-2 h-4 w-4" />
              Remove from favorites
            </>
          ) : (
            <>
              <StarIcon className="mr-2 h-4 w-4" />
              Add to favorites
            </>
          )}
        </DropdownMenuItem>
        <Dialog>
          <DialogTrigger asChild>
            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
              <TrashIcon className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Are you sure?</DialogTitle>
              <DialogDescription>
                This action cannot be undone. This will permanently delete the
                component &ldquo;{component.name}&rdquo; and all its data.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline">Cancel</Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleteComponent.isPending}
              >
                {deleteComponent.isPending ? "Deleting..." : "Delete"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * Dynamic filter component for table columns with adaptive UI based on filter variant.
 *
 * Supports multiple filter types:
 * - Text filters: Search input with icon for string columns
 * - Range filters: Min/Max number inputs for numeric columns
 * - Select filters: Dropdown with unique values for categorical columns
 *
 * Features:
 * - Auto-detection of filter variant from column metadata
 * - Special handling for boolean and enum values
 * - Accessible form controls with proper labeling
 * - Real-time filtering with debounced input
 *
 * @param props - Object containing the TanStack Table column instance
 * @returns JSX element with appropriate filter UI for the column type
 */
function Filter({ column }: { column: Column<any, unknown> }) {
  const id = useId()
  const columnFilterValue = column.getFilterValue()
  const { filterVariant } = column.columnDef.meta ?? {}
  const columnHeader =
    typeof column.columnDef.header === "string" ? column.columnDef.header : ""
  const facetedUniqueValues = column.getFacetedUniqueValues()
  const sortedUniqueValues = useMemo(() => {
    if (filterVariant === "range") return []

    // Get all unique values from the column
    const values = Array.from(facetedUniqueValues.keys())

    // Special handling for boolean values (status)
    if (columnHeader === "Status") {
      return ["active", "inactive"]
    }

    // Special handling for stock status
    if (columnHeader === "Stock") {
      return ["available", "limited", "out_of_stock", "discontinued"]
    }

    // Filter out null/undefined values and sort
    return values.filter((value) => value != null && value !== "").sort()
  }, [facetedUniqueValues, filterVariant, columnHeader])

  if (filterVariant === "range") {
    return (
      <div className="space-y-2">
        <Label>{columnHeader}</Label>
        <div className="flex">
          <Input
            id={`${id}-range-1`}
            className="flex-1 rounded-e-none focus:z-10"
            value={(columnFilterValue as [number, number])?.[0] ?? ""}
            onChange={(e) =>
              column.setFilterValue((old: [number, number]) => [
                e.target.value ? Number(e.target.value) : undefined,
                old?.[1],
              ])
            }
            placeholder="Min"
            type="number"
            aria-label={`${columnHeader} min`}
          />
          <Input
            id={`${id}-range-2`}
            className="-ms-px flex-1 rounded-s-none focus:z-10"
            value={(columnFilterValue as [number, number])?.[1] ?? ""}
            onChange={(e) =>
              column.setFilterValue((old: [number, number]) => [
                old?.[0],
                e.target.value ? Number(e.target.value) : undefined,
              ])
            }
            placeholder="Max"
            type="number"
            aria-label={`${columnHeader} max`}
          />
        </div>
      </div>
    )
  }

  if (filterVariant === "select") {
    return (
      <div className="space-y-2">
        <Label htmlFor={`${id}-select`}>{columnHeader}</Label>
        <Select
          value={columnFilterValue?.toString() ?? "all"}
          onValueChange={(value) => {
            column.setFilterValue(value === "all" ? undefined : value)
          }}
        >
          <SelectTrigger id={`${id}-select`}>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            {sortedUniqueValues.map((value) => (
              <SelectItem key={String(value)} value={String(value)}>
                {String(value).charAt(0).toUpperCase() + String(value).slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={`${id}-input`}>{columnHeader}</Label>
      <div className="relative">
        <Input
          id={`${id}-input`}
          className="peer ps-9"
          value={(columnFilterValue ?? "") as string}
          onChange={(e) => column.setFilterValue(e.target.value)}
          placeholder={`Search ${columnHeader.toLowerCase()}`}
          type="text"
        />
        <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
          <SearchIcon size={16} />
        </div>
      </div>
    </div>
  )
}

/**
 * Main Components data table component with comprehensive component management functionality.
 *
 * Provides a complete interface for electrical component catalog management including:
 *
 * Features:
 * - Advanced TanStack Table with sorting, filtering, and selection
 * - Real-time search across multiple component properties
 * - Multi-faceted filtering (manufacturer, category, type, stock status)
 * - Price range filtering with currency support
 * - Bulk selection for batch operations
 * - Responsive design with mobile-friendly layout
 * - Loading states and error handling
 * - Create/Edit dialogs with comprehensive forms
 *
 * Data Management:
 * - React Query integration for server state management
 * - Optimistic updates for immediate UI feedback
 * - Cache invalidation and background refetching
 * - Error boundary handling with user-friendly messages
 *
 * Accessibility:
 * - WCAG 2.1 AA compliance
 * - Keyboard navigation support
 * - Screen reader compatibility
 * - Focus management for modal dialogs
 *
 * @returns JSX element containing the complete components management interface
 */
export default function ComponentsTable() {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "name",
      desc: false,
    },
  ])
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingComponent, setEditingComponent] =
    useState<ComponentRead | null>(null)

  const { data: componentsData, isLoading, error } = useComponents()
  const deleteComponent = useDeleteComponent()
  const markAsPreferred = useMarkComponentAsPreferred()
  const unmarkAsPreferred = useUnmarkComponentAsPreferred()
  const components = componentsData?.items ?? []

  const columns = useMemo(
    () =>
      createColumns(
        deleteComponent,
        markAsPreferred,
        unmarkAsPreferred,
        setEditingComponent
      ),
    [deleteComponent, markAsPreferred, unmarkAsPreferred, setEditingComponent]
  )

  const table = useReactTable({
    data: components,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
  })

  if (error) {
    return (
      <div className="border-destructive/50 bg-destructive/10 text-destructive rounded-md border p-4 text-sm">
        Error loading components: {(error as Error).message}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Components</h1>
          <p className="text-muted-foreground text-sm">
            Manage electrical components catalog and specifications
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Component
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-3">
        <div className="w-44">
          <Filter column={table.getColumn("name")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("manufacturer")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("category_name")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("type_name")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("stock_status")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("is_active")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("unit_price")!} />
        </div>
      </div>

      {/* Table */}
      {isLoading ? (
        <div className="flex h-24 items-center justify-center">
          <div className="text-muted-foreground text-sm">Loading...</div>
        </div>
      ) : (
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="bg-muted/50">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="relative h-10 border-t select-none"
                      aria-sort={
                        header.column.getIsSorted() === "asc"
                          ? "ascending"
                          : header.column.getIsSorted() === "desc"
                            ? "descending"
                            : "none"
                      }
                    >
                      {header.isPlaceholder ? null : header.column.getCanSort() ? (
                        <div
                          className={cn(
                            header.column.getCanSort() &&
                              "flex h-full cursor-pointer items-center justify-between gap-2 select-none"
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                          onKeyDown={(e) => {
                            if (
                              header.column.getCanSort() &&
                              (e.key === "Enter" || e.key === " ")
                            ) {
                              e.preventDefault()
                              header.column.getToggleSortingHandler()?.(e)
                            }
                          }}
                          tabIndex={header.column.getCanSort() ? 0 : undefined}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {{
                            asc: (
                              <ChevronUpIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                            desc: (
                              <ChevronDownIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                          }[header.column.getIsSorted() as string] ?? (
                            <span className="size-4" aria-hidden="true" />
                          )}
                        </div>
                      ) : (
                        flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )
                      )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No components found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}

      {/* Create Component Dialog */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Create Component</DialogTitle>
            <DialogDescription>
              Add a new electrical component to the catalog.
            </DialogDescription>
          </DialogHeader>
          <ComponentForm
            onSuccess={() => setShowCreateForm(false)}
            onCancel={() => setShowCreateForm(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Component Dialog */}
      <Dialog
        open={!!editingComponent}
        onOpenChange={() => setEditingComponent(null)}
      >
        <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[800px]">
          <DialogHeader>
            <DialogTitle>Edit Component</DialogTitle>
            <DialogDescription>
              Modify the component details and specifications.
            </DialogDescription>
          </DialogHeader>
          {editingComponent && (
            <ComponentForm
              component={editingComponent}
              onSuccess={() => setEditingComponent(null)}
              onCancel={() => setEditingComponent(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
