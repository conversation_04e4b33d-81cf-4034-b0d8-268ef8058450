"use client"

import { useEffect } from "react"

import type { ComponentTypeRead } from "@/lib/api/types/components"
import type {
  ComponentTypeCreate,
  ComponentTypeUpdate,
} from "@/lib/validation/api/components"

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"

import {
  componentTypeCreateSchema,
  componentTypeUpdateSchema,
} from "@/lib/validation/api/components"
import { useComponentCategories } from "@/hooks/api/useComponentCategories"
import {
  useCreateComponentType,
  useUpdateComponentType,
} from "@/hooks/api/useComponentTypes"
import { toast } from "@/hooks/useToast"

import { Button } from "@/components/atoms/button"
import { Checkbox } from "@/components/atoms/checkbox"
import { Input } from "@/components/atoms/input"
import { Textarea } from "@/components/atoms/textarea"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/molecules/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/molecules/select"

interface TypeFormProps {
  type?: ComponentTypeRead
  onSuccess?: () => void
  onCancel?: () => void
}

type FormData = ComponentTypeCreate | ComponentTypeUpdate

export function TypeForm({ type, onSuccess, onCancel }: TypeFormProps) {
  const isEditing = !!type
  const schema = isEditing
    ? componentTypeUpdateSchema
    : componentTypeCreateSchema

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
          name: type.name,
          description: type.description || "",
          category_id: type.category_id,
          is_active: type.is_active,
          specifications_template: type.specifications_template || {},
          metadata: type.metadata || {},
        }
      : {
          name: "",
          description: "",
          category_id: 0,
          is_active: true,
          specifications_template: {},
          metadata: {},
        },
  })

  const createMutation = useCreateComponentType()
  const updateMutation = useUpdateComponentType()

  // Get categories for selection
  const { data: categoriesData } = useComponentCategories()
  const categories = categoriesData?.items || []
  const activeCategories = categories.filter((cat) => cat.is_active)

  useEffect(() => {
    if (isEditing && type) {
      form.reset({
        name: type.name,
        description: type.description || "",
        category_id: type.category_id,
        is_active: type.is_active,
        specifications_template: type.specifications_template || {},
        metadata: type.metadata || {},
      })
    }
  }, [form, isEditing, type])

  const onSubmit = async (data: FormData) => {
    try {
      if (isEditing && type) {
        await updateMutation.mutateAsync({
          id: type.id,
          data: data as ComponentTypeUpdate,
        })
        toast({
          title: "Success",
          description: `Type "${data.name}" updated successfully`,
        })
      } else {
        await createMutation.mutateAsync(data as ComponentTypeCreate)
        toast({
          title: "Success",
          description: `Type "${data.name}" created successfully`,
        })
      }
      onSuccess?.()
    } catch (error: any) {
      toast({
        title: "Error",
        description:
          error.message || `Failed to ${isEditing ? "update" : "create"} type`,
        variant: "destructive",
      })
    }
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Type Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter component type name" {...field} />
              </FormControl>
              <FormDescription>
                A descriptive name for the component type
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter type description (optional)"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Detailed description of what components belong to this type
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category</FormLabel>
              <FormControl>
                <Select
                  value={field.value?.toString() || ""}
                  onValueChange={(value) => {
                    field.onChange(value ? parseInt(value, 10) : 0)
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {activeCategories.map((cat) => (
                      <SelectItem key={cat.id} value={cat.id.toString()}>
                        {cat.full_path}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormDescription>
                Choose the category this component type belongs to
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-y-0 space-x-3">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>Active Type</FormLabel>
                <FormDescription>
                  Active types are available for use in component classification
                </FormDescription>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? isEditing
                ? "Updating..."
                : "Creating..."
              : isEditing
                ? "Update Type"
                : "Create Type"}
          </Button>
        </div>
      </form>
    </Form>
  )
}
