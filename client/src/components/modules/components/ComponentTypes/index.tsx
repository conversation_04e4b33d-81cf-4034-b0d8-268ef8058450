"use client"

import { useId, useMemo, useState } from "react"

import type { ComponentTypeRead } from "@/lib/api/types/components"

import {
  Column,
  ColumnDef,
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedMinMaxValues,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
} from "@tanstack/react-table"
import {
  ChevronDownIcon,
  ChevronUpIcon,
  EditIcon,
  FileTextIcon,
  MoreHorizontalIcon,
  PlusIcon,
  SearchIcon,
  TrashIcon,
} from "lucide-react"

import { cn } from "@/lib/utils"
import {
  useComponentTypes,
  useDeleteComponentType,
} from "@/hooks/api/useComponentTypes"
import { toast } from "@/hooks/useToast"

import { UnifiedBadge } from "@/components/atoms/badge"
import { Button } from "@/components/atoms/button"
import { Checkbox } from "@/components/atoms/checkbox"
import { Input } from "@/components/atoms/input"
import { Label } from "@/components/atoms/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/molecules/select"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/molecules/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/organisms/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/organisms/dropdown-menu"

import { TypeForm } from "./TypeForm"

declare module "@tanstack/react-table" {
  interface ColumnMeta<TData extends RowData, TValue> {
    filterVariant?: "text" | "range" | "select"
  }
}

const createColumns = (
  deleteType: ReturnType<typeof useDeleteComponentType>,
  setEditingType: (type: ComponentTypeRead) => void
): ColumnDef<ComponentTypeRead>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <div className="font-medium">{row.getValue("name")}</div>
    ),
  },
  {
    header: "Description",
    accessorKey: "description",
    cell: ({ row }) => {
      const description = row.getValue("description") as string
      return (
        <div className="text-muted-foreground max-w-xs truncate text-sm">
          {description || "—"}
        </div>
      )
    },
  },
  {
    header: "Category",
    accessorKey: "category_name",
    cell: ({ row }) => {
      const categoryName = row.getValue("category_name") as string
      return <div className="text-sm">{categoryName || "—"}</div>
    },
    meta: {
      filterVariant: "select",
    },
  },
  {
    header: "Category Path",
    accessorKey: "category_path",
    cell: ({ row }) => (
      <div className="text-muted-foreground text-xs">
        {row.getValue("category_path")}
      </div>
    ),
  },
  {
    header: "Components",
    accessorKey: "component_count",
    cell: ({ row }) => {
      const count = parseInt(row.getValue("component_count"))
      return (
        <div className="text-center">
          {new Intl.NumberFormat("en-US", {
            notation: "compact",
            maximumFractionDigits: 1,
          }).format(count)}
        </div>
      )
    },
    meta: {
      filterVariant: "range",
    },
  },
  {
    header: "Template",
    accessorKey: "has_specifications_template",
    cell: ({ row }) => {
      const hasTemplate = row.getValue("has_specifications_template") as boolean
      return (
        <div className="flex justify-center">
          {hasTemplate ? (
            <UnifiedBadge
              type="generic"
              intent="success"
              size="sm"
              customLabel="Yes"
              customIcon={FileTextIcon}
              className="text-xs"
            />
          ) : (
            <UnifiedBadge
              type="generic"
              intent="info"
              size="sm"
              customLabel="No"
              showIcon={false}
              className="text-xs"
            />
          )}
        </div>
      )
    },
    meta: {
      filterVariant: "select",
    },
    filterFn: (row, id, filterValue) => {
      const rowValue = row.getValue(id) as boolean
      return (
        filterValue === "all" ||
        (filterValue === "yes" && rowValue) ||
        (filterValue === "no" && !rowValue)
      )
    },
  },
  {
    header: "Status",
    accessorKey: "is_active",
    cell: ({ row }) => {
      const isActive = row.getValue("is_active") as boolean
      return (
        <UnifiedBadge
          type="component"
          intent={isActive ? "active" : "inactive"}
          size="sm"
        />
      )
    },
    meta: {
      filterVariant: "select",
    },
    filterFn: (row, id, filterValue) => {
      const rowValue = row.getValue(id) as boolean
      return (
        filterValue === "all" ||
        (filterValue === "active" && rowValue) ||
        (filterValue === "inactive" && !rowValue)
      )
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const type = row.original
      return (
        <TypeActionsCell
          type={type}
          deleteType={deleteType}
          onEdit={setEditingType}
        />
      )
    },
  },
]

interface TypeActionsCellProps {
  type: ComponentTypeRead
  deleteType: ReturnType<typeof useDeleteComponentType>
  onEdit: (type: ComponentTypeRead) => void
}

function TypeActionsCell({ type, deleteType, onEdit }: TypeActionsCellProps) {
  const handleDelete = async () => {
    try {
      await deleteType.mutateAsync(type.id)
      toast({
        title: "Success",
        description: `Type "${type.name}" deleted successfully`,
      })
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete type",
        variant: "destructive",
      })
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontalIcon className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onEdit(type)}>
          <EditIcon className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <Dialog>
          <DialogTrigger asChild>
            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
              <TrashIcon className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Are you sure?</DialogTitle>
              <DialogDescription>
                This action cannot be undone. This will permanently delete the
                type &ldquo;{type.name}&rdquo; and all its data.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline">Cancel</Button>
              <Button
                variant="destructive"
                onClick={handleDelete}
                disabled={deleteType.isPending}
              >
                {deleteType.isPending ? "Deleting..." : "Delete"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

function Filter({ column }: { column: Column<any, unknown> }) {
  const id = useId()
  const columnFilterValue = column.getFilterValue()
  const { filterVariant } = column.columnDef.meta ?? {}
  const columnHeader =
    typeof column.columnDef.header === "string" ? column.columnDef.header : ""
  const facetedUniqueValues = column.getFacetedUniqueValues()
  const sortedUniqueValues = useMemo(() => {
    if (filterVariant === "range") return []

    // Get all unique values from the column
    const values = Array.from(facetedUniqueValues.keys())

    // Special handling for boolean values (status, template)
    if (columnHeader === "Status") {
      return ["active", "inactive"]
    }
    if (columnHeader === "Template") {
      return ["yes", "no"]
    }

    // If the values are arrays, flatten them and get unique items
    const flattenedValues = values.reduce((acc: string[], curr) => {
      if (Array.isArray(curr)) {
        return [...acc, ...curr]
      }
      return [...acc, curr]
    }, [])

    // Get unique values and sort them
    return Array.from(new Set(flattenedValues)).sort()
  }, [facetedUniqueValues, filterVariant, columnHeader])

  if (filterVariant === "range") {
    return (
      <div className="space-y-2">
        <Label>{columnHeader}</Label>
        <div className="flex">
          <Input
            id={`${id}-range-1`}
            className="flex-1 rounded-e-none focus:z-10"
            value={(columnFilterValue as [number, number])?.[0] ?? ""}
            onChange={(e) =>
              column.setFilterValue((old: [number, number]) => [
                e.target.value ? Number(e.target.value) : undefined,
                old?.[1],
              ])
            }
            placeholder="Min"
            type="number"
            aria-label={`${columnHeader} min`}
          />
          <Input
            id={`${id}-range-2`}
            className="-ms-px flex-1 rounded-s-none focus:z-10"
            value={(columnFilterValue as [number, number])?.[1] ?? ""}
            onChange={(e) =>
              column.setFilterValue((old: [number, number]) => [
                old?.[0],
                e.target.value ? Number(e.target.value) : undefined,
              ])
            }
            placeholder="Max"
            type="number"
            aria-label={`${columnHeader} max`}
          />
        </div>
      </div>
    )
  }

  if (filterVariant === "select") {
    return (
      <div className="space-y-2">
        <Label htmlFor={`${id}-select`}>{columnHeader}</Label>
        <Select
          value={columnFilterValue?.toString() ?? "all"}
          onValueChange={(value) => {
            column.setFilterValue(value === "all" ? undefined : value)
          }}
        >
          <SelectTrigger id={`${id}-select`}>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            {sortedUniqueValues.map((value) => (
              <SelectItem key={String(value)} value={String(value)}>
                {String(value).charAt(0).toUpperCase() + String(value).slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={`${id}-input`}>{columnHeader}</Label>
      <div className="relative">
        <Input
          id={`${id}-input`}
          className="peer ps-9"
          value={(columnFilterValue ?? "") as string}
          onChange={(e) => column.setFilterValue(e.target.value)}
          placeholder={`Search ${columnHeader.toLowerCase()}`}
          type="text"
        />
        <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
          <SearchIcon size={16} />
        </div>
      </div>
    </div>
  )
}

export default function ComponentTypesTable() {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "name",
      desc: false,
    },
  ])
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingType, setEditingType] = useState<ComponentTypeRead | null>(null)

  const { data: typesData, isLoading, error } = useComponentTypes()
  const deleteType = useDeleteComponentType()
  const types = typesData?.items ?? []

  // Get categories for filter dropdown - used in the future for advanced filtering
  // const { data: categoriesData } = useComponentCategories()
  // const _categories = categoriesData?.items ?? [] // Available for future filtering features

  const columns = useMemo(
    () => createColumns(deleteType, setEditingType),
    [deleteType, setEditingType]
  )

  const table = useReactTable({
    data: types,
    columns,
    state: {
      sorting,
      columnFilters,
    },
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    getFacetedMinMaxValues: getFacetedMinMaxValues(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
  })

  if (error) {
    return (
      <div className="border-destructive/50 bg-destructive/10 text-destructive rounded-md border p-4 text-sm">
        Error loading component types: {(error as Error).message}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold">Component Types</h1>
          <p className="text-muted-foreground text-sm">
            Manage electrical component types and their specifications
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Type
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-3">
        <div className="w-44">
          <Filter column={table.getColumn("name")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("category_name")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("is_active")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("has_specifications_template")!} />
        </div>
        <div className="w-36">
          <Filter column={table.getColumn("component_count")!} />
        </div>
      </div>

      {/* Table */}
      {isLoading ? (
        <div className="flex h-24 items-center justify-center">
          <div className="text-muted-foreground text-sm">Loading...</div>
        </div>
      ) : (
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id} className="bg-muted/50">
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="relative h-10 border-t select-none"
                      aria-sort={
                        header.column.getIsSorted() === "asc"
                          ? "ascending"
                          : header.column.getIsSorted() === "desc"
                            ? "descending"
                            : "none"
                      }
                    >
                      {header.isPlaceholder ? null : header.column.getCanSort() ? (
                        <div
                          className={cn(
                            header.column.getCanSort() &&
                              "flex h-full cursor-pointer items-center justify-between gap-2 select-none"
                          )}
                          onClick={header.column.getToggleSortingHandler()}
                          onKeyDown={(e) => {
                            if (
                              header.column.getCanSort() &&
                              (e.key === "Enter" || e.key === " ")
                            ) {
                              e.preventDefault()
                              header.column.getToggleSortingHandler()?.(e)
                            }
                          }}
                          tabIndex={header.column.getCanSort() ? 0 : undefined}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {{
                            asc: (
                              <ChevronUpIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                            desc: (
                              <ChevronDownIcon
                                className="shrink-0 opacity-60"
                                size={16}
                                aria-hidden="true"
                              />
                            ),
                          }[header.column.getIsSorted() as string] ?? (
                            <span className="size-4" aria-hidden="true" />
                          )}
                        </div>
                      ) : (
                        flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )
                      )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No types found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}

      {/* Create Type Dialog */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Create Component Type</DialogTitle>
            <DialogDescription>
              Create a new component type for classifying electrical components.
            </DialogDescription>
          </DialogHeader>
          <TypeForm
            onSuccess={() => setShowCreateForm(false)}
            onCancel={() => setShowCreateForm(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Type Dialog */}
      <Dialog open={!!editingType} onOpenChange={() => setEditingType(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Component Type</DialogTitle>
            <DialogDescription>
              Modify the component type details and settings.
            </DialogDescription>
          </DialogHeader>
          {editingType && (
            <TypeForm
              type={editingType}
              onSuccess={() => setEditingType(null)}
              onCancel={() => setEditingType(null)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
