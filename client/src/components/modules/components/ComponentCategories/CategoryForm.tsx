"use client"

import { useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"

import { <PERSON><PERSON> } from "@/components/atoms/button"
import { Checkbox } from "@/components/atoms/checkbox"
import { Input } from "@/components/atoms/input"
import { Label } from "@/components/atoms/label"
import { Textarea } from "@/components/atoms/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/molecules/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/molecules/form"
import { toast } from "@/hooks/useToast"

import {
  useCreateComponentCategory,
  useUpdateComponentCategory,
  useComponentCategories,
} from "@/hooks/api/useComponentCategories"
import {
  componentCategoryCreateSchema,
  componentCategoryUpdateSchema,
  type ComponentCategoryCreate,
  type ComponentCategoryUpdate,
} from "@/lib/validation/api/components"
import type { ComponentCategoryRead } from "@/lib/api/types/components"

interface CategoryFormProps {
  category?: ComponentCategoryRead
  onSuccess?: () => void
  onCancel?: () => void
}

type FormData = ComponentCategoryCreate | ComponentCategoryUpdate

export function CategoryForm({ category, onSuccess, onCancel }: CategoryFormProps) {
  const isEditing = !!category
  const schema = isEditing ? componentCategoryUpdateSchema : componentCategoryCreateSchema

  const form = useForm<FormData>({
    resolver: zodResolver(schema),
    defaultValues: isEditing
      ? {
          name: category.name,
          description: category.description || "",
          parent_category_id: category.parent_category_id || undefined,
          is_active: category.is_active,
        }
      : {
          name: "",
          description: "",
          parent_category_id: undefined,
          is_active: true,
        },
  })

  const createMutation = useCreateComponentCategory()
  const updateMutation = useUpdateComponentCategory()

  // Get parent categories for selection
  const { data: categoriesData } = useComponentCategories()
  const categories = categoriesData?.categories || []
  const availableParentCategories = categories.filter(
    (cat) => cat.id !== category?.id // Exclude self from parent options
  )

  useEffect(() => {
    if (isEditing && category) {
      form.reset({
        name: category.name,
        description: category.description || "",
        parent_category_id: category.parent_category_id || undefined,
        is_active: category.is_active,
      })
    }
  }, [form, isEditing, category])

  const onSubmit = async (data: FormData) => {
    try {
      if (isEditing && category) {
        await updateMutation.mutateAsync({
          id: category.id,
          data: data as ComponentCategoryUpdate,
        })
        toast({
          title: "Success",
          description: `Category "${data.name}" updated successfully`,
        })
      } else {
        await createMutation.mutateAsync(data as ComponentCategoryCreate)
        toast({
          title: "Success",
          description: `Category "${data.name}" created successfully`,
        })
      }
      onSuccess?.()
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${isEditing ? "update" : "create"} category`,
        variant: "destructive",
      })
    }
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Category Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter category name" {...field} />
              </FormControl>
              <FormDescription>
                A descriptive name for the component category
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter category description (optional)"
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                Detailed description of what components belong in this category
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="parent_category_id"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Parent Category</FormLabel>
              <FormControl>
                <Select
                  value={field.value?.toString() || ""}
                  onValueChange={(value) => {
                    field.onChange(value ? parseInt(value, 10) : undefined)
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select parent category (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No parent (root category)</SelectItem>
                    {availableParentCategories.map((cat) => (
                      <SelectItem key={cat.id} value={cat.id.toString()}>
                        {cat.full_path}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormDescription>
                Choose a parent category to create a hierarchical structure
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="is_active"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  Active Category
                </FormLabel>
                <FormDescription>
                  Active categories are available for use in component classification
                </FormDescription>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting
              ? isEditing
                ? "Updating..."
                : "Creating..."
              : isEditing
                ? "Update Category"
                : "Create Category"}
          </Button>
        </div>
      </form>
    </Form>
  )
}