/**
 * Test Setup Configuration for FR-1 Security Features
 * 
 * This module provides test setup, utilities, and mocks specifically
 * for security-related testing of authentication features.
 */

import { vi, beforeEach, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';

/**
 * Security test data factory
 */
export const securityTestDataFactory = {
  /**
   * Mock user data
   */
  mockUser: {
    id: 1,
    email: '<EMAIL>',
    name: 'Test User',
    is_active: true,
    is_email_verified: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },

  /**
   * Mock lockout information
   */
  mockLockoutInfo: {
    failed_attempts: 3,
    attempts_remaining: 2,
    locked_until: null as string | null
  },

  /**
   * Mock lockout information (locked account)
   */
  mockLockedAccount: {
    failed_attempts: 5,
    attempts_remaining: 0,
    locked_until: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes from now
  },

  /**
   * Mock login responses
   */
  mockLoginSuccess: {
    access_token: 'mock-jwt-token',
    token_type: 'Bearer',
    user: {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      is_active: true,
      is_email_verified: true
    }
  },

  mockLoginFailure: {
    message: 'Invalid credentials',
    lockout_info: {
      failed_attempts: 3,
      attempts_remaining: 2,
      locked_until: null
    }
  },

  mockLoginLocked: {
    message: 'Account locked due to too many failed attempts',
    lockout_info: {
      failed_attempts: 5,
      attempts_remaining: 0,
      locked_until: new Date(Date.now() + 30 * 60 * 1000).toISOString()
    }
  },

  /**
   * Mock registration responses
   */
  mockRegistrationSuccess: {
    user: {
      id: 2,
      email: '<EMAIL>',
      name: 'New User',
      is_active: true,
      is_email_verified: false
    },
    message: 'Registration successful',
    email_verification_required: true,
    verification_email_sent: true
  },

  /**
   * Mock email verification responses
   */
  mockEmailVerificationSuccess: {
    message: 'Email verified successfully',
    user: {
      id: 1,
      email: '<EMAIL>',
      name: 'Test User',
      is_active: true,
      is_email_verified: true
    }
  },

  mockEmailVerificationFailure: {
    message: 'Invalid or expired token',
    code: 'TOKEN_INVALID'
  },

  /**
   * Mock password reset responses
   */
  mockPasswordResetRequest: {
    message: 'Password reset email sent',
    email_sent_to: '<EMAIL>'
  },

  mockPasswordResetSuccess: {
    message: 'Password reset successful'
  },

  mockPasswordResetFailure: {
    message: 'Invalid or expired reset token',
    code: 'TOKEN_EXPIRED'
  },

  /**
   * Mock security tokens
   */
  mockTokens: {
    validEmailToken: 'eyJhbGciOiJIUzI1NiJ9.**********************************************************.mock-signature',
    expiredEmailToken: 'eyJhbGciOiJIUzI1NiJ9.**********************************************************.mock-signature',
    validResetToken: 'reset_eyJhbGciOiJIUzI1NiJ9.**********************************************************.mock-signature',
    expiredResetToken: 'reset_eyJhbGciOiJIUzI1NiJ9.**********************************************************.mock-signature',
    invalidToken: 'invalid-token-format',
    shortToken: 'short'
  }
};

/**
 * Mock API responses for security endpoints
 */
export const mockSecurityApiResponses = {
  login: {
    success: securityTestDataFactory.mockLoginSuccess,
    failure: securityTestDataFactory.mockLoginFailure,
    locked: securityTestDataFactory.mockLoginLocked
  },
  
  register: {
    success: securityTestDataFactory.mockRegistrationSuccess
  },
  
  verifyEmail: {
    success: securityTestDataFactory.mockEmailVerificationSuccess,
    failure: securityTestDataFactory.mockEmailVerificationFailure
  },
  
  passwordReset: {
    request: securityTestDataFactory.mockPasswordResetRequest,
    success: securityTestDataFactory.mockPasswordResetSuccess,
    failure: securityTestDataFactory.mockPasswordResetFailure
  }
};

/**
 * Mock fetch function for API calls
 */
export function createMockFetch(responses: Record<string, any> = {}) {
  return vi.fn().mockImplementation(async (url: string, options: RequestInit = {}) => {
    const method = options.method || 'GET';
    const key = `${method.toUpperCase()} ${url}`;
    
    if (responses[key]) {
      const response = responses[key];
      
      return {
        ok: response.ok !== false,
        status: response.status || 200,
        statusText: response.statusText || 'OK',
        json: async () => response.data || response,
        text: async () => JSON.stringify(response.data || response),
        headers: new Headers(response.headers || {}),
        url
      };
    }
    
    // Default response
    return {
      ok: false,
      status: 404,
      statusText: 'Not Found',
      json: async () => ({ message: 'Not Found' }),
      text: async () => '{"message":"Not Found"}',
      headers: new Headers(),
      url
    };
  });
}

/**
 * Mock localStorage for token storage testing
 */
export function createMockLocalStorage() {
  const storage = new Map<string, string>();
  
  return {
    getItem: vi.fn((key: string) => storage.get(key) || null),
    setItem: vi.fn((key: string, value: string) => {
      storage.set(key, value);
    }),
    removeItem: vi.fn((key: string) => {
      storage.delete(key);
    }),
    clear: vi.fn(() => {
      storage.clear();
    }),
    key: vi.fn((index: number) => {
      const keys = Array.from(storage.keys());
      return keys[index] || null;
    }),
    get length() {
      return storage.size;
    }
  };
}

/**
 * Mock crypto for token generation testing
 */
export function createMockCrypto() {
  return {
    getRandomValues: vi.fn((array: Uint8Array) => {
      // Fill with predictable values for testing
      for (let i = 0; i < array.length; i++) {
        array[i] = i % 256;
      }
      return array;
    })
  };
}

/**
 * Mock timer functions for lockout testing
 */
export function setupMockTimers() {
  vi.useFakeTimers();
  
  return {
    advanceTime: (ms: number) => {
      vi.advanceTimersByTime(ms);
    },
    runAllTimers: () => {
      vi.runAllTimers();
    },
    clearAllTimers: () => {
      vi.clearAllTimers();
    }
  };
}

/**
 * Security error factory for testing error handling
 */
export const securityErrorFactory = {
  accountLocked: (minutesUntilUnlock = 30) => ({
    response: {
      status: 401,
      data: {
        message: 'Account locked due to too many failed attempts',
        code: 'ACCOUNT_LOCKED',
        lockout_info: {
          failed_attempts: 5,
          attempts_remaining: 0,
          locked_until: new Date(Date.now() + minutesUntilUnlock * 60 * 1000).toISOString()
        }
      }
    }
  }),

  invalidCredentials: () => ({
    response: {
      status: 401,
      data: {
        message: 'Invalid email or password',
        code: 'INVALID_CREDENTIALS',
        lockout_info: {
          failed_attempts: 2,
          attempts_remaining: 3,
          locked_until: null
        }
      }
    }
  }),

  emailNotVerified: () => ({
    response: {
      status: 422,
      data: {
        message: 'Please verify your email address',
        code: 'EMAIL_NOT_VERIFIED'
      }
    }
  }),

  tokenExpired: () => ({
    response: {
      status: 400,
      data: {
        message: 'Token has expired',
        code: 'TOKEN_EXPIRED'
      }
    }
  }),

  rateLimited: () => ({
    response: {
      status: 429,
      data: {
        message: 'Too many requests',
        code: 'RATE_LIMITED'
      }
    }
  }),

  networkError: () => ({
    code: 'NETWORK_ERROR',
    message: 'Network request failed'
  })
};

/**
 * Test utilities for security features
 */
export const securityTestUtils = {
  /**
   * Wait for next tick (useful for async state updates)
   */
  waitForNextTick: () => new Promise(resolve => setTimeout(resolve, 0)),

  /**
   * Create a date in the future
   */
  createFutureDate: (minutesFromNow: number) => 
    new Date(Date.now() + minutesFromNow * 60 * 1000),

  /**
   * Create a date in the past
   */
  createPastDate: (minutesAgo: number) => 
    new Date(Date.now() - minutesAgo * 60 * 1000),

  /**
   * Simulate user input with proper event handling
   */
  simulateUserInput: async (element: HTMLInputElement, value: string) => {
    const { fireEvent } = await import('@testing-library/react');
    
    fireEvent.change(element, { target: { value } });
    fireEvent.blur(element);
  },

  /**
   * Assert error state in components
   */
  expectErrorState: (container: HTMLElement, errorMessage: string) => {
    const errorElement = container.querySelector('[role="alert"]') || 
                        container.querySelector('.error-message') ||
                        container.querySelector('[data-testid*="error"]');
    
    expect(errorElement).toBeTruthy();
    expect(errorElement?.textContent).toContain(errorMessage);
  },

  /**
   * Assert loading state in components
   */
  expectLoadingState: (container: HTMLElement) => {
    const loadingElement = container.querySelector('[aria-label*="loading"]') ||
                          container.querySelector('.loading') ||
                          container.querySelector('[data-testid*="loading"]');
    
    expect(loadingElement).toBeTruthy();
  }
};

/**
 * Global test setup
 */
beforeEach(() => {
  // Clean up DOM
  cleanup();
  
  // Reset all mocks
  vi.clearAllMocks();
  
  // Mock console methods in test environment
  if (process.env.NODE_ENV === 'test') {
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  }
});

afterEach(() => {
  // Clean up DOM
  cleanup();
  
  // Restore all mocks
  vi.restoreAllMocks();
  
  // Clear any remaining timers
  vi.clearAllTimers();
});

/**
 * Custom render function with security context
 */
export async function renderWithSecurityContext(ui: React.ReactElement, options?: any) {
  const { render } = await import('@testing-library/react');
  
  // You can add security context providers here when they're created
  return render(ui, {
    ...options
  });
}