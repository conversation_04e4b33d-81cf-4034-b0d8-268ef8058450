/**
 * Mock Implementations for FR-1 Security Features Testing
 * 
 * This module provides comprehensive mocks for all security-related
 * APIs, services, and utilities used in testing.
 */

import { vi } from 'vitest';
import type { 
  EmailVerificationRequest,
  ResendVerificationRequest
} from '../types/emailVerification';
import type {
  PasswordResetRequest,
  ResetPasswordRequest
} from '../types/passwordReset';
import type {
  AccountLockoutInfo
} from '../types/security';
import { securityTestDataFactory, securityErrorFactory } from './setup';

/**
 * Mock implementation of security enhanced API
 */
export const mockSecurityEnhancedApi = {
  /**
   * Mock enhanced login with lockout information
   */
  loginEnhanced: vi.fn().mockImplementation(async (credentials) => {
    // Simulate different scenarios based on email
    if (credentials.username === '<EMAIL>') {
      throw securityErrorFactory.accountLocked(30);
    }
    
    if (credentials.username === '<EMAIL>') {
      throw securityErrorFactory.invalidCredentials();
    }
    
    if (credentials.username === '<EMAIL>') {
      throw securityErrorFactory.emailNotVerified();
    }
    
    // Default successful login
    return securityTestDataFactory.mockLoginSuccess;
  }),

  /**
   * Mock enhanced registration
   */
  registerEnhanced: vi.fn().mockImplementation(async (userData) => {
    if (userData.email === '<EMAIL>') {
      throw {
        response: {
          status: 400,
          data: {
            message: 'Email already registered',
            code: 'EMAIL_EXISTS'
          }
        }
      };
    }
    
    return securityTestDataFactory.mockRegistrationSuccess;
  }),

  /**
   * Mock email verification
   */
  verifyEmail: vi.fn().mockImplementation(async (data: EmailVerificationRequest) => {
    if (data.token === securityTestDataFactory.mockTokens.expiredEmailToken) {
      throw securityErrorFactory.tokenExpired();
    }
    
    if (data.token === securityTestDataFactory.mockTokens.invalidToken) {
      throw {
        response: {
          status: 400,
          data: {
            message: 'Invalid token format',
            code: 'TOKEN_INVALID'
          }
        }
      };
    }
    
    return securityTestDataFactory.mockEmailVerificationSuccess;
  }),

  /**
   * Mock resend verification email
   */
  resendVerificationEmail: vi.fn().mockImplementation(async (data: ResendVerificationRequest) => {
    if (data.email === '<EMAIL>') {
      throw securityErrorFactory.rateLimited();
    }
    
    return {
      message: 'Verification email sent successfully',
      email_sent_to: data.email,
      resend_available_at: new Date(Date.now() + 60000).toISOString() // 1 minute cooldown
    };
  }),

  /**
   * Mock password reset request
   */
  requestPasswordReset: vi.fn().mockImplementation(async (data: PasswordResetRequest) => {
    return {
      message: 'Password reset email sent',
      email_sent_to: data.email
    };
  }),

  /**
   * Mock password reset completion
   */
  resetPassword: vi.fn().mockImplementation(async (data: ResetPasswordRequest) => {
    if (data.token === securityTestDataFactory.mockTokens.expiredResetToken) {
      throw securityErrorFactory.tokenExpired();
    }
    
    if (data.token === securityTestDataFactory.mockTokens.invalidToken) {
      throw {
        response: {
          status: 400,
          data: {
            message: 'Invalid reset token',
            code: 'TOKEN_INVALID'
          }
        }
      };
    }
    
    return securityTestDataFactory.mockPasswordResetSuccess;
  }),

  /**
   * Mock security status check
   */
  getAccountSecurityStatus: vi.fn().mockImplementation(async () => {
    return {
      is_locked: false,
      lockout_expires_at: null,
      failed_attempts: 0,
      attempts_remaining: 5,
      is_email_verified: true,
      two_factor_enabled: false
    };
  }),

  /**
   * Mock lockout status check
   */
  checkLockoutStatus: vi.fn().mockImplementation(async (email: string) => {
    if (email === '<EMAIL>') {
      return {
        is_locked: true,
        lockout_expires_at: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
        attempts_remaining: 0
      };
    }
    
    return {
      is_locked: false,
      lockout_expires_at: null,
      attempts_remaining: 5
    };
  }),

  /**
   * Mock security event logging
   */
  logSecurityEvent: vi.fn().mockImplementation(async (_event) => {
    // Just return success for testing
    return { success: true };
  })
};

/**
 * Mock SecurityService class
 */
export const mockSecurityService = {
  verifyEmail: vi.fn().mockImplementation((token: string) => 
    mockSecurityEnhancedApi.verifyEmail({ token })
  ),

  resendVerification: vi.fn().mockImplementation((email: string) =>
    mockSecurityEnhancedApi.resendVerificationEmail({ email })
  ),

  requestPasswordReset: vi.fn().mockImplementation((email: string) =>
    mockSecurityEnhancedApi.requestPasswordReset({ email })
  ),

  resetPassword: vi.fn().mockImplementation((token: string, new_password: string) =>
    mockSecurityEnhancedApi.resetPassword({ token, new_password })
  ),

  loginWithLockoutInfo: vi.fn().mockImplementation((email: string, password: string) =>
    mockSecurityEnhancedApi.loginEnhanced({ username: email, password })
  ),

  registerWithVerification: vi.fn().mockImplementation((userData) =>
    mockSecurityEnhancedApi.registerEnhanced(userData)
  ),

  getSecurityStatus: vi.fn().mockImplementation(() =>
    mockSecurityEnhancedApi.getAccountSecurityStatus()
  ),

  logSecurityEvent: vi.fn().mockImplementation((eventType: string, metadata?: any) =>
    mockSecurityEnhancedApi.logSecurityEvent({ event_type: eventType, metadata })
  ),

  checkLockoutStatus: vi.fn().mockImplementation((email: string) =>
    mockSecurityEnhancedApi.checkLockoutStatus(email)
  )
};

/**
 * Mock Zustand security store
 */
export const createMockSecurityStore = () => {
  const mockStore = {
    // State
    lockoutState: {
      isLocked: false,
      lockoutExpiresAt: null,
      failedAttempts: 0,
      attemptsRemaining: 5
    },
    emailVerificationState: {
      isVerificationSent: false,
      sentToEmail: null,
      canResend: true,
      resendAvailableAt: null
    },
    passwordResetState: {
      isResetSent: false,
      sentToEmail: null,
      resetToken: null,
      resetExpiresAt: null
    },

    // Actions
    setLockoutState: vi.fn(),
    updateFailedAttempts: vi.fn(),
    setAccountLocked: vi.fn(),
    clearLockoutState: vi.fn(),
    
    setEmailVerificationState: vi.fn(),
    setVerificationSent: vi.fn(),
    setVerificationResendCooldown: vi.fn(),
    clearEmailVerificationState: vi.fn(),
    
    setPasswordResetState: vi.fn(),
    setResetEmailSent: vi.fn(),
    setResetToken: vi.fn(),
    clearPasswordResetState: vi.fn(),
    
    resetAllSecurityState: vi.fn()
  };

  return mockStore;
};

/**
 * Mock useSecurityStore hook
 */
export const mockUseSecurityStore = vi.fn().mockImplementation(() => 
  createMockSecurityStore()
);

/**
 * Mock useSecurityActions hook
 */
export const mockUseSecurityActions = vi.fn().mockImplementation(() => {
  const store = createMockSecurityStore();
  return {
    setLockoutState: store.setLockoutState,
    updateFailedAttempts: store.updateFailedAttempts,
    setAccountLocked: store.setAccountLocked,
    clearLockoutState: store.clearLockoutState,
    setEmailVerificationState: store.setEmailVerificationState,
    setVerificationSent: store.setVerificationSent,
    setVerificationResendCooldown: store.setVerificationResendCooldown,
    clearEmailVerificationState: store.clearEmailVerificationState,
    setPasswordResetState: store.setPasswordResetState,
    setResetEmailSent: store.setResetEmailSent,
    setResetToken: store.setResetToken,
    clearPasswordResetState: store.clearPasswordResetState,
    resetAllSecurityState: store.resetAllSecurityState
  };
});

/**
 * Mock token security utilities
 */
export const mockTokenSecurity = {
  validateToken: vi.fn().mockImplementation((token: string, _expiresAt?: Date | string) => {
    if (token === securityTestDataFactory.mockTokens.invalidToken) {
      return {
        isValid: false,
        isExpired: false,
        isFormatValid: false,
        errors: ['Token format is invalid']
      };
    }
    
    if (token === securityTestDataFactory.mockTokens.expiredEmailToken ||
        token === securityTestDataFactory.mockTokens.expiredResetToken) {
      return {
        isValid: false,
        isExpired: true,
        isFormatValid: true,
        errors: ['Token has expired']
      };
    }
    
    return {
      isValid: true,
      isExpired: false,
      isFormatValid: true,
      remainingTime: 60000, // 1 minute
      errors: []
    };
  }),

  extractTokenFromUrl: vi.fn().mockImplementation((url?: string) => {
    if (!url) url = 'https://example.com/verify?token=valid-token';
    
    if (url.includes('token=')) {
      return url.split('token=')[1].split('&')[0];
    }
    
    return null;
  }),

  sanitizeTokenForLogging: vi.fn().mockImplementation((token: string) => {
    if (token.length < 8) return '***';
    return `${token.substring(0, 4)}...${token.substring(token.length - 4)}`;
  }),

  generateClientCorrelationId: vi.fn().mockReturnValue('mock-correlation-id-1234'),

  formatRemainingTime: vi.fn().mockImplementation((remainingTimeMs: number) => {
    if (remainingTimeMs <= 0) return 'Expired';
    
    const minutes = Math.floor(remainingTimeMs / (1000 * 60));
    const seconds = Math.floor((remainingTimeMs % (1000 * 60)) / 1000);
    
    if (minutes > 0) {
      return `${minutes} minute${minutes === 1 ? '' : 's'} ${seconds} second${seconds === 1 ? '' : 's'}`;
    } else {
      return `${seconds} second${seconds === 1 ? '' : 's'}`;
    }
  }),

  isTokenCloseToExpiring: vi.fn().mockImplementation((expiresAt: Date | string) => {
    const expirationDate = typeof expiresAt === 'string' ? new Date(expiresAt) : expiresAt;
    const fiveMinutesMs = 5 * 60 * 1000;
    return (expirationDate.getTime() - Date.now()) < fiveMinutesMs;
  })
};

/**
 * Mock secure error handling utilities
 */
export const mockSecureErrorHandling = {
  parseSecurityError: vi.fn().mockImplementation((error: unknown) => {
    if (typeof error === 'object' && error && 'response' in error) {
      const apiError = error as any;
      return {
        name: 'SecurityError',
        message: apiError.response?.data?.message || 'Security error occurred',
        code: apiError.response?.data?.code || 'UNKNOWN_ERROR',
        statusCode: apiError.response?.status,
        lockoutInfo: apiError.response?.data?.lockout_info
      };
    }
    
    return {
      name: 'SecurityError',
      message: 'Unknown security error',
      code: 'UNKNOWN_ERROR'
    };
  }),

  getSecurityErrorMessage: vi.fn().mockImplementation((error: any) => {
    return error.message || 'An unexpected error occurred';
  }),

  isAccountLockoutError: vi.fn().mockImplementation((error: any) => {
    return error.code === 'ACCOUNT_LOCKED' || Boolean(error.lockoutInfo);
  }),

  isEmailVerificationError: vi.fn().mockImplementation((error: any) => {
    return error.code === 'EMAIL_NOT_VERIFIED';
  }),

  isTokenExpiredError: vi.fn().mockImplementation((error: any) => {
    return error.code === 'TOKEN_EXPIRED';
  }),

  formatLockoutMessage: vi.fn().mockImplementation((lockoutInfo: AccountLockoutInfo) => {
    if (!lockoutInfo.locked_until) {
      return `Too many failed attempts. ${lockoutInfo.attempts_remaining} attempts remaining.`;
    }
    
    const minutesRemaining = Math.ceil(
      (new Date(lockoutInfo.locked_until).getTime() - Date.now()) / (1000 * 60)
    );
    
    if (minutesRemaining <= 0) {
      return 'Account lockout has expired. You may try logging in again.';
    }
    
    return `Account is locked for ${minutesRemaining} more minute${minutesRemaining === 1 ? '' : 's'}.`;
  }),

  handleSecurityError: vi.fn().mockImplementation((error: unknown, _context?: string) => {
    return mockSecureErrorHandling.parseSecurityError(error);
  })
};

/**
 * Mock React Router navigation
 */
export const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn()
};

/**
 * Mock Next.js router hooks
 */
export const mockUseRouter = vi.fn().mockReturnValue(mockRouter);
export const mockUseSearchParams = vi.fn().mockReturnValue(new URLSearchParams());
export const mockUsePathname = vi.fn().mockReturnValue('/');

/**
 * Mock React Query hooks
 */
export const mockUseMutation = vi.fn().mockImplementation((mutationFn, options) => ({
  mutate: vi.fn().mockImplementation(async (variables) => {
    try {
      const result = await mutationFn(variables);
      options?.onSuccess?.(result, variables);
      return result;
    } catch (error) {
      options?.onError?.(error, variables);
      throw error;
    }
  }),
  mutateAsync: vi.fn().mockImplementation(mutationFn),
  isLoading: false,
  isError: false,
  isSuccess: false,
  error: null,
  data: null,
  reset: vi.fn()
}));

/**
 * Export all mocks for easy importing in tests
 */
export const securityMocks = {
  api: mockSecurityEnhancedApi,
  service: mockSecurityService,
  store: {
    useSecurityStore: mockUseSecurityStore,
    useSecurityActions: mockUseSecurityActions,
    createStore: createMockSecurityStore
  },
  utils: {
    tokenSecurity: mockTokenSecurity,
    errorHandling: mockSecureErrorHandling
  },
  router: {
    useRouter: mockUseRouter,
    useSearchParams: mockUseSearchParams,
    usePathname: mockUsePathname
  },
  reactQuery: {
    useMutation: mockUseMutation
  }
};

export default securityMocks;