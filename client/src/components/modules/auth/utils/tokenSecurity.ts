/**
 * Token Security Utilities for FR-1 Security Features
 * 
 * This module provides utilities for secure token handling, validation,
 * and management for email verification and password reset workflows.
 */

/**
 * Token security configuration
 */
export const TokenSecurityConfig = {
  // Minimum token length for security
  MIN_TOKEN_LENGTH: 32,
  
  // Maximum token length to prevent abuse
  MAX_TOKEN_LENGTH: 256,
  
  // Token format validation (base64url characters)
  TOKEN_FORMAT_REGEX: /^[A-Za-z0-9_-]+$/,
  
  // Expiration buffer in minutes (tokens considered expired if within this buffer)
  EXPIRATION_BUFFER_MINUTES: 5,
  
  // Maximum storage time for tokens in memory (15 minutes)
  MAX_MEMORY_STORAGE_MINUTES: 15
} as const;

/**
 * Token validation result
 */
export interface TokenValidationResult {
  isValid: boolean;
  isExpired: boolean;
  isFormatValid: boolean;
  remainingTime?: number;
  errors: string[];
}

/**
 * Secure token information
 */
export interface SecureTokenInfo {
  token: string;
  expiresAt: Date;
  createdAt: Date;
  purpose: 'email-verification' | 'password-reset';
  isUsed?: boolean;
}

/**
 * Token storage manager for secure in-memory token handling
 */
export class SecureTokenManager {
  private static instance: SecureTokenManager;
  private tokenStorage = new Map<string, SecureTokenInfo>();
  private cleanupInterval: NodeJS.Timeout | null = null;
  
  private constructor() {
    // Start cleanup interval to remove expired tokens
    this.startCleanupInterval();
  }
  
  static getInstance(): SecureTokenManager {
    if (!SecureTokenManager.instance) {
      SecureTokenManager.instance = new SecureTokenManager();
    }
    return SecureTokenManager.instance;
  }
  
  /**
   * Store token securely in memory
   */
  storeToken(tokenInfo: SecureTokenInfo): void {
    // Validate token before storing
    const validation = this.validateToken(tokenInfo.token);
    if (!validation.isValid) {
      throw new Error(`Invalid token format: ${validation.errors.join(', ')}`);
    }
    
    // Store token with cleanup timeout
    this.tokenStorage.set(tokenInfo.token, {
      ...tokenInfo,
      createdAt: new Date()
    });
    
    // Schedule automatic cleanup
    setTimeout(() => {
      this.removeToken(tokenInfo.token);
    }, TokenSecurityConfig.MAX_MEMORY_STORAGE_MINUTES * 60 * 1000);
  }
  
  /**
   * Retrieve token information
   */
  getToken(token: string): SecureTokenInfo | null {
    const tokenInfo = this.tokenStorage.get(token);
    
    if (!tokenInfo) {
      return null;
    }
    
    // Check if token is expired
    if (this.isTokenExpired(tokenInfo.expiresAt)) {
      this.removeToken(token);
      return null;
    }
    
    return tokenInfo;
  }
  
  /**
   * Remove token from storage
   */
  removeToken(token: string): void {
    this.tokenStorage.delete(token);
  }
  
  /**
   * Mark token as used
   */
  markTokenAsUsed(token: string): boolean {
    const tokenInfo = this.tokenStorage.get(token);
    if (!tokenInfo) {
      return false;
    }
    
    tokenInfo.isUsed = true;
    return true;
  }
  
  /**
   * Clear all tokens
   */
  clearAllTokens(): void {
    this.tokenStorage.clear();
  }
  
  /**
   * Get token count (for debugging)
   */
  getTokenCount(): number {
    return this.tokenStorage.size;
  }
  
  /**
   * Start periodic cleanup of expired tokens
   */
  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredTokens();
    }, 5 * 60 * 1000); // Clean up every 5 minutes
  }
  
  /**
   * Clean up expired tokens from memory
   */
  private cleanupExpiredTokens(): void {
    const now = new Date();
    const expiredTokens: string[] = [];
    
    for (const [token, tokenInfo] of this.tokenStorage.entries()) {
      if (this.isTokenExpired(tokenInfo.expiresAt) || 
          (now.getTime() - tokenInfo.createdAt.getTime()) > 
          (TokenSecurityConfig.MAX_MEMORY_STORAGE_MINUTES * 60 * 1000)) {
        expiredTokens.push(token);
      }
    }
    
    expiredTokens.forEach(token => this.removeToken(token));
    
    if (process.env.NODE_ENV === 'development' && expiredTokens.length > 0) {
      console.log(`🧹 Cleaned up ${expiredTokens.length} expired tokens`);
    }
  }
  
  /**
   * Check if token is expired
   */
  private isTokenExpired(expiresAt: Date): boolean {
    const now = new Date();
    const bufferTime = TokenSecurityConfig.EXPIRATION_BUFFER_MINUTES * 60 * 1000;
    return (expiresAt.getTime() - now.getTime()) < bufferTime;
  }
  
  /**
   * Validate token format and security
   */
  private validateToken(token: string): TokenValidationResult {
    const errors: string[] = [];
    let isValid = true;
    
    // Check token length
    if (token.length < TokenSecurityConfig.MIN_TOKEN_LENGTH) {
      errors.push('Token is too short');
      isValid = false;
    }
    
    if (token.length > TokenSecurityConfig.MAX_TOKEN_LENGTH) {
      errors.push('Token is too long');
      isValid = false;
    }
    
    // Check token format
    if (!TokenSecurityConfig.TOKEN_FORMAT_REGEX.test(token)) {
      errors.push('Token contains invalid characters');
      isValid = false;
    }
    
    return {
      isValid,
      isExpired: false,
      isFormatValid: TokenSecurityConfig.TOKEN_FORMAT_REGEX.test(token),
      errors
    };
  }
  
  /**
   * Cleanup on instance destruction
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clearAllTokens();
  }
}

/**
 * Validate token format and expiration
 */
export function validateToken(
  token: string, 
  expiresAt?: Date | string
): TokenValidationResult {
  const errors: string[] = [];
  let isValid = true;
  let isExpired = false;
  let remainingTime: number | undefined;
  
  // Validate token format
  if (!token || typeof token !== 'string') {
    errors.push('Token is required and must be a string');
    isValid = false;
  } else {
    // Check token length
    if (token.length < TokenSecurityConfig.MIN_TOKEN_LENGTH) {
      errors.push('Token is too short');
      isValid = false;
    }
    
    if (token.length > TokenSecurityConfig.MAX_TOKEN_LENGTH) {
      errors.push('Token is too long');
      isValid = false;
    }
    
    // Check token format
    if (!TokenSecurityConfig.TOKEN_FORMAT_REGEX.test(token)) {
      errors.push('Token format is invalid');
      isValid = false;
    }
  }
  
  // Validate expiration if provided
  if (expiresAt) {
    const expirationDate = typeof expiresAt === 'string' ? new Date(expiresAt) : expiresAt;
    const now = new Date();
    const bufferTime = TokenSecurityConfig.EXPIRATION_BUFFER_MINUTES * 60 * 1000;
    
    if (isNaN(expirationDate.getTime())) {
      errors.push('Invalid expiration date');
      isValid = false;
    } else {
      remainingTime = Math.max(0, expirationDate.getTime() - now.getTime());
      isExpired = remainingTime < bufferTime;
      
      if (isExpired) {
        errors.push('Token has expired');
        isValid = false;
      }
    }
  }
  
  return {
    isValid,
    isExpired,
    isFormatValid: TokenSecurityConfig.TOKEN_FORMAT_REGEX.test(token || ''),
    remainingTime,
    errors
  };
}

/**
 * Extract token from URL parameters or query string
 */
export function extractTokenFromUrl(url?: string): string | null {
  try {
    const urlToCheck = url || window.location.href;
    const urlObj = new URL(urlToCheck);
    
    // Check for token in query parameters
    const tokenFromQuery = urlObj.searchParams.get('token') || 
                          urlObj.searchParams.get('t') ||
                          urlObj.searchParams.get('verification_token') ||
                          urlObj.searchParams.get('reset_token');
    
    if (tokenFromQuery) {
      return tokenFromQuery;
    }
    
    // Check for token in URL hash
    const hash = urlObj.hash.substring(1);
    if (hash) {
      const hashParams = new URLSearchParams(hash);
      const tokenFromHash = hashParams.get('token') || hashParams.get('t');
      if (tokenFromHash) {
        return tokenFromHash;
      }
    }
    
    // Check for token in pathname (e.g., /verify/TOKEN)
    const pathSegments = urlObj.pathname.split('/');
    const tokenIndex = pathSegments.findIndex(segment => 
      segment === 'verify' || segment === 'reset-password'
    );
    
    if (tokenIndex >= 0 && tokenIndex < pathSegments.length - 1) {
      const potentialToken = pathSegments[tokenIndex + 1];
      if (potentialToken && potentialToken.length >= TokenSecurityConfig.MIN_TOKEN_LENGTH) {
        return potentialToken;
      }
    }
    
    return null;
  } catch (error) {
    console.warn('Failed to extract token from URL:', error);
    return null;
  }
}

/**
 * Sanitize token for logging (show only first and last 4 characters)
 */
export function sanitizeTokenForLogging(token: string): string {
  if (!token || token.length < 8) {
    return '***';
  }
  
  return `${token.substring(0, 4)}...${token.substring(token.length - 4)}`;
}

/**
 * Generate secure random string for client-side token generation
 * (Note: This should not be used for actual security tokens, only for client-side correlation IDs)
 */
export function generateClientCorrelationId(): string {
  const array = new Uint8Array(16);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Format remaining time for display
 */
export function formatRemainingTime(remainingTimeMs: number): string {
  if (remainingTimeMs <= 0) {
    return 'Expired';
  }
  
  const minutes = Math.floor(remainingTimeMs / (1000 * 60));
  const seconds = Math.floor((remainingTimeMs % (1000 * 60)) / 1000);
  
  if (minutes > 0) {
    return `${minutes} minute${minutes === 1 ? '' : 's'} ${seconds} second${seconds === 1 ? '' : 's'}`;
  } else {
    return `${seconds} second${seconds === 1 ? '' : 's'}`;
  }
}

/**
 * Check if token is close to expiring (within 5 minutes)
 */
export function isTokenCloseToExpiring(expiresAt: Date | string): boolean {
  const expirationDate = typeof expiresAt === 'string' ? new Date(expiresAt) : expiresAt;
  const now = new Date();
  const fiveMinutesMs = 5 * 60 * 1000;
  
  return (expirationDate.getTime() - now.getTime()) < fiveMinutesMs;
}

/**
 * Create secure token storage key for localStorage (if needed)
 */
export function createSecureStorageKey(tokenType: string, email?: string): string {
  const baseKey = `ued_security_${tokenType}`;
  
  if (email) {
    // Create a simple hash of the email for the key (not cryptographic, just for organization)
    const emailHash = btoa(email).replace(/[^a-zA-Z0-9]/g, '').substring(0, 8);
    return `${baseKey}_${emailHash}`;
  }
  
  return baseKey;
}

/**
 * Cleanup function for component unmount
 */
export function cleanupTokenSecurity(): void {
  SecureTokenManager.getInstance().clearAllTokens();
}

/**
 * Initialize token security manager
 */
export function initializeTokenSecurity(): void {
  // Initialize singleton
  SecureTokenManager.getInstance();
  
  // Add cleanup on page unload
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', cleanupTokenSecurity);
  }
}

// Export singleton instance
export const tokenManager = SecureTokenManager.getInstance();