/**
 * Secure Error Handling Utilities for FR-1 Security Features
 * 
 * This module provides secure error handling utilities that properly manage
 * security-sensitive errors while preventing information disclosure.
 */

import type { 
  AccountLockoutInfo
} from '../types/security';

/**
 * Security-aware error types
 */
export interface SecurityError extends Error {
  code?: string;
  statusCode?: number;
  lockoutInfo?: AccountLockoutInfo;
  details?: Record<string, unknown>;
}

/**
 * Standardized error codes for security operations
 */
export const SecurityErrorCodes = {
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_INACTIVE: 'ACCOUNT_INACTIVE',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  RATE_LIMITED: 'RATE_LIMITED',
  SECURITY_VIOLATION: 'SECURITY_VIOLATION',
  NETWORK_ERROR: 'NETWORK_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR'
} as const;

export type SecurityErrorCode = typeof SecurityErrorCodes[keyof typeof SecurityErrorCodes];

/**
 * User-friendly error messages (safe for display)
 */
export const SecurityErrorMessages: Record<SecurityErrorCode, string> = {
  ACCOUNT_LOCKED: 'Your account has been temporarily locked due to multiple failed login attempts. Please try again later.',
  ACCOUNT_INACTIVE: 'Your account is currently inactive. Please contact support for assistance.',
  EMAIL_NOT_VERIFIED: 'Please verify your email address before logging in. Check your email for verification instructions.',
  INVALID_CREDENTIALS: 'The email address or password you entered is incorrect. Please try again.',
  TOKEN_EXPIRED: 'Your verification link has expired. Please request a new one.',
  TOKEN_INVALID: 'The verification link is invalid or has already been used. Please request a new one.',
  RATE_LIMITED: 'Too many requests. Please wait a moment before trying again.',
  SECURITY_VIOLATION: 'A security violation was detected. Please try again or contact support.',
  NETWORK_ERROR: 'Unable to connect to the server. Please check your internet connection and try again.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.'
};

/**
 * Parse API error response and extract security information
 */
export function parseSecurityError(error: unknown): SecurityError {
  const securityError: SecurityError = new Error('Security error occurred');
  
  // Handle axios/fetch error structure
  if (error && typeof error === 'object' && 'response' in error) {
    const apiError = error as any;
    const responseData = apiError.response?.data;
    const statusCode = apiError.response?.status;
    
    securityError.statusCode = statusCode;
    
    if (responseData) {
      // Extract error message
      securityError.message = responseData.message || responseData.detail || SecurityErrorMessages.UNKNOWN_ERROR;
      
      // Extract security-specific information
      if (responseData.lockout_info) {
        securityError.lockoutInfo = responseData.lockout_info;
        securityError.code = SecurityErrorCodes.ACCOUNT_LOCKED;
      }
      
      // Map HTTP status codes to security error codes
      switch (statusCode) {
        case 401:
          securityError.code = responseData.lockout_info 
            ? SecurityErrorCodes.ACCOUNT_LOCKED 
            : SecurityErrorCodes.INVALID_CREDENTIALS;
          break;
        case 403:
          securityError.code = SecurityErrorCodes.ACCOUNT_INACTIVE;
          break;
        case 422:
          securityError.code = SecurityErrorCodes.EMAIL_NOT_VERIFIED;
          break;
        case 429:
          securityError.code = SecurityErrorCodes.RATE_LIMITED;
          break;
        default:
          securityError.code = SecurityErrorCodes.UNKNOWN_ERROR;
      }
      
      // Store additional details safely
      if (responseData.details && typeof responseData.details === 'object') {
        securityError.details = responseData.details;
      }
    }
  }
  // Handle network errors
  else if (error && typeof error === 'object' && 'code' in error && error.code === 'NETWORK_ERROR') {
    securityError.code = SecurityErrorCodes.NETWORK_ERROR;
    securityError.message = SecurityErrorMessages.NETWORK_ERROR;
  }
  // Handle direct Error objects
  else if (error instanceof Error) {
    securityError.message = error.message;
    securityError.code = SecurityErrorCodes.UNKNOWN_ERROR;
  }
  // Handle unknown error types
  else {
    securityError.message = SecurityErrorMessages.UNKNOWN_ERROR;
    securityError.code = SecurityErrorCodes.UNKNOWN_ERROR;
  }
  
  return securityError;
}

/**
 * Get user-friendly error message for display
 */
export function getSecurityErrorMessage(error: SecurityError): string {
  if (error.code && error.code in SecurityErrorMessages) {
    return SecurityErrorMessages[error.code as SecurityErrorCode];
  }
  return error.message || SecurityErrorMessages.UNKNOWN_ERROR;
}

/**
 * Check if error indicates account lockout
 */
export function isAccountLockoutError(error: SecurityError): boolean {
  return error.code === SecurityErrorCodes.ACCOUNT_LOCKED || Boolean(error.lockoutInfo);
}

/**
 * Check if error indicates email verification required
 */
export function isEmailVerificationError(error: SecurityError): boolean {
  return error.code === SecurityErrorCodes.EMAIL_NOT_VERIFIED;
}

/**
 * Check if error indicates token expiration
 */
export function isTokenExpiredError(error: SecurityError): boolean {
  return error.code === SecurityErrorCodes.TOKEN_EXPIRED;
}

/**
 * Check if error indicates rate limiting
 */
export function isRateLimitedError(error: SecurityError): boolean {
  return error.code === SecurityErrorCodes.RATE_LIMITED;
}

/**
 * Format lockout information for display
 */
export function formatLockoutMessage(lockoutInfo: AccountLockoutInfo): string {
  if (!lockoutInfo.locked_until) {
    return `Too many failed attempts. ${lockoutInfo.attempts_remaining} attempts remaining.`;
  }
  
  const lockoutExpiry = new Date(lockoutInfo.locked_until);
  const now = new Date();
  const minutesRemaining = Math.ceil((lockoutExpiry.getTime() - now.getTime()) / (1000 * 60));
  
  if (minutesRemaining <= 0) {
    return 'Account lockout has expired. You may try logging in again.';
  }
  
  return `Account is locked for ${minutesRemaining} more minute${minutesRemaining === 1 ? '' : 's'}.`;
}

/**
 * Enhanced error handler with security logging
 */
export class SecurityErrorHandler {
  private static instance: SecurityErrorHandler;
  private errorLog: SecurityError[] = [];
  
  private constructor() {}
  
  static getInstance(): SecurityErrorHandler {
    if (!SecurityErrorHandler.instance) {
      SecurityErrorHandler.instance = new SecurityErrorHandler();
    }
    return SecurityErrorHandler.instance;
  }
  
  /**
   * Handle and log security error
   */
  handleError(error: unknown, context?: string): SecurityError {
    const securityError = parseSecurityError(error);
    
    // Log error for debugging (in development only)
    if (process.env.NODE_ENV === 'development') {
      console.group(`🔒 Security Error ${context ? `(${context})` : ''}`);
      console.error('Original error:', error);
      console.error('Parsed security error:', securityError);
      console.groupEnd();
    }
    
    // Store in memory log (last 10 errors for debugging)
    this.errorLog.unshift(securityError);
    if (this.errorLog.length > 10) {
      this.errorLog.pop();
    }
    
    return securityError;
  }
  
  /**
   * Get recent error log (for debugging)
   */
  getErrorLog(): readonly SecurityError[] {
    return Object.freeze([...this.errorLog]);
  }
  
  /**
   * Clear error log
   */
  clearErrorLog(): void {
    this.errorLog = [];
  }
}

/**
 * Convenience function to handle security errors
 */
export function handleSecurityError(error: unknown, context?: string): SecurityError {
  return SecurityErrorHandler.getInstance().handleError(error, context);
}

/**
 * Create a secure error response for API calls
 */
export function createSecureErrorResponse(error: SecurityError): {
  success: false;
  error: {
    message: string;
    code: string;
    lockoutInfo?: AccountLockoutInfo;
  };
} {
  return {
    success: false,
    error: {
      message: getSecurityErrorMessage(error),
      code: error.code || SecurityErrorCodes.UNKNOWN_ERROR,
      ...(error.lockoutInfo && { lockoutInfo: error.lockoutInfo })
    }
  };
}

/**
 * Validate and sanitize error data before display
 */
export function sanitizeErrorForDisplay(error: SecurityError): {
  message: string;
  canRetry: boolean;
  requiresAction: boolean;
  actionType?: 'verify-email' | 'wait-lockout' | 'contact-support';
} {
  const message = getSecurityErrorMessage(error);
  
  switch (error.code) {
    case SecurityErrorCodes.ACCOUNT_LOCKED:
      return {
        message,
        canRetry: false,
        requiresAction: true,
        actionType: 'wait-lockout'
      };
      
    case SecurityErrorCodes.EMAIL_NOT_VERIFIED:
      return {
        message,
        canRetry: false,
        requiresAction: true,
        actionType: 'verify-email'
      };
      
    case SecurityErrorCodes.ACCOUNT_INACTIVE:
    case SecurityErrorCodes.SECURITY_VIOLATION:
      return {
        message,
        canRetry: false,
        requiresAction: true,
        actionType: 'contact-support'
      };
      
    case SecurityErrorCodes.RATE_LIMITED:
      return {
        message,
        canRetry: true,
        requiresAction: true,
        actionType: 'wait-lockout'
      };
      
    case SecurityErrorCodes.INVALID_CREDENTIALS:
    case SecurityErrorCodes.TOKEN_EXPIRED:
    case SecurityErrorCodes.TOKEN_INVALID:
    case SecurityErrorCodes.NETWORK_ERROR:
      return {
        message,
        canRetry: true,
        requiresAction: false
      };
      
    default:
      return {
        message,
        canRetry: true,
        requiresAction: false
      };
  }
}