/**
 * LockoutTimer Component for FR-1 Account Lockout Protection
 * 
 * This component displays a countdown timer showing when an account lockout will expire,
 * automatically updating and clearing the lockout state when expired.
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Clock, Shield, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSecurityStore } from '@/stores/securityStore';
import type { LockoutTimerProps } from '../../types/security';

/**
 * LockoutTimer displays a real-time countdown for account lockout expiration
 */
export const LockoutTimer: React.FC<LockoutTimerProps> = ({
  lockoutInfo,
  className,
  showIcon = true,
  compact = false,
  onLockoutExpired,
  expirationMessage = 'Account lockout has expired. You may try logging in again.'
}) => {
  const { lockoutState, clearLockoutState } = useSecurityStore();
  const [remainingTime, setRemainingTime] = useState<number>(0);
  const [isExpired, setIsExpired] = useState<boolean>(false);

  // Use lockoutInfo prop or fallback to store state
  const effectiveLockoutInfo = lockoutInfo || {
    failed_attempts: lockoutState.failedAttempts,
    attempts_remaining: lockoutState.attemptsRemaining,
    locked_until: lockoutState.lockoutExpiresAt?.toISOString() || null
  };

  /**
   * Calculate remaining time in milliseconds
   */
  const calculateRemainingTime = useCallback((): number => {
    if (!effectiveLockoutInfo.locked_until) {
      return 0;
    }

    const lockoutExpiry = new Date(effectiveLockoutInfo.locked_until);
    const now = new Date();
    return Math.max(0, lockoutExpiry.getTime() - now.getTime());
  }, [effectiveLockoutInfo.locked_until]);

  /**
   * Update timer every second
   */
  useEffect(() => {
    const updateTimer = () => {
      const remaining = calculateRemainingTime();
      setRemainingTime(remaining);

      // Check if lockout has expired
      if (remaining <= 0 && !isExpired) {
        setIsExpired(true);
        clearLockoutState();
        onLockoutExpired?.();
      }
    };

    // Initial update
    updateTimer();

    // Set up interval for real-time updates
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [calculateRemainingTime, isExpired, clearLockoutState, onLockoutExpired]);

  /**
   * Format remaining time for display
   */
  const formatRemainingTime = (timeMs: number): string => {
    if (timeMs <= 0) {
      return '00:00';
    }

    const totalSeconds = Math.floor(timeMs / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  /**
   * Get time description for accessibility
   */
  const getTimeDescription = (timeMs: number): string => {
    if (timeMs <= 0) {
      return 'Expired';
    }

    const totalSeconds = Math.floor(timeMs / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;

    if (minutes > 0) {
      return `${minutes} minute${minutes === 1 ? '' : 's'} and ${seconds} second${seconds === 1 ? '' : 's'} remaining`;
    } else {
      return `${seconds} second${seconds === 1 ? '' : 's'} remaining`;
    }
  };

  // Don't render if no lockout or already expired
  if (!effectiveLockoutInfo.locked_until || isExpired) {
    if (isExpired && expirationMessage) {
      return (
        <div
          className={cn(
            'flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md text-green-800',
            compact && 'p-2 text-sm',
            className
          )}
          role="status"
          aria-live="polite"
          data-testid="lockout-expired-message"
        >
          <Shield className={cn('h-4 w-4 text-green-600', compact && 'h-3 w-3')} />
          <span>{expirationMessage}</span>
        </div>
      );
    }
    return null;
  }

  return (
    <div
      className={cn(
        'flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800',
        compact && 'p-2 text-sm',
        className
      )}
      role="status"
      aria-live="polite"
      aria-label={`Account locked. ${getTimeDescription(remainingTime)}`}
      data-testid="lockout-timer"
    >
      {showIcon && (
        <div className={cn('flex-shrink-0', compact && 'text-sm')}>
          {remainingTime > 60000 ? (
            <Clock className={cn('h-4 w-4 text-red-600', compact && 'h-3 w-3')} />
          ) : (
            <AlertTriangle className={cn('h-4 w-4 text-red-600 animate-pulse', compact && 'h-3 w-3')} />
          )}
        </div>
      )}
      
      <div className="flex-1">
        <div className={cn('font-medium', compact && 'text-sm')}>
          Account Locked
        </div>
        
        <div className={cn('text-sm text-red-600', compact && 'text-xs')}>
          Try again in{' '}
          <span 
            className="font-mono font-semibold"
            aria-label={getTimeDescription(remainingTime)}
          >
            {formatRemainingTime(remainingTime)}
          </span>
        </div>
        
        {!compact && effectiveLockoutInfo.failed_attempts > 0 && (
          <div className="text-xs text-red-500 mt-1">
            {effectiveLockoutInfo.failed_attempts} failed attempt{effectiveLockoutInfo.failed_attempts === 1 ? '' : 's'}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Hook to manage lockout timer state
 */
export const useLockoutTimer = (lockoutExpiresAt?: Date | string | null) => {
  const [remainingTime, setRemainingTime] = useState<number>(0);
  const [isExpired, setIsExpired] = useState<boolean>(false);

  useEffect(() => {
    if (!lockoutExpiresAt) {
      setRemainingTime(0);
      setIsExpired(true);
      return;
    }

    const calculateTime = () => {
      const expiry = typeof lockoutExpiresAt === 'string' 
        ? new Date(lockoutExpiresAt) 
        : lockoutExpiresAt;
      const now = new Date();
      const remaining = Math.max(0, expiry.getTime() - now.getTime());
      
      setRemainingTime(remaining);
      setIsExpired(remaining <= 0);
      
      return remaining;
    };

    // Initial calculation
    calculateTime();

    // Update every second
    const interval = setInterval(() => {
      const remaining = calculateTime();
      if (remaining <= 0) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [lockoutExpiresAt]);

  return {
    remainingTime,
    isExpired,
    formattedTime: (() => {
      if (remainingTime <= 0) return '00:00';
      const totalSeconds = Math.floor(remainingTime / 1000);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    })()
  };
};

export default LockoutTimer;