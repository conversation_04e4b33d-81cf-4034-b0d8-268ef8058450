/**
 * LockoutTimer Component Tests
 * 
 * Comprehensive tests for the LockoutTimer component including
 * real-time countdown, expiration handling, and accessibility.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { LockoutTimer } from '../LockoutTimer';
import { securityTestUtils, setupMockTimers } from '../../../__tests__/setup';
import type { AccountLockoutInfo } from '../../../types/security';

// Mock the security store
vi.mock('@/stores/securityStore', () => ({
  useSecurityStore: vi.fn(() => ({
    lockoutState: {
      failedAttempts: 3,
      attemptsRemaining: 2,
      lockoutExpiresAt: null
    },
    clearLockoutState: vi.fn()
  }))
}));

describe('LockoutTimer', () => {
  let mockTimers: ReturnType<typeof setupMockTimers>;

  beforeEach(() => {
    mockTimers = setupMockTimers();
    vi.clearAllMocks();
  });

  afterEach(() => {
    mockTimers.clearAllTimers();
    vi.useRealTimers();
  });

  describe('Rendering', () => {
    it('should not render when no lockout info provided', () => {
      const { container } = render(<LockoutTimer />);
      expect(container.firstChild).toBeNull();
    });

    it('should not render when lockout has no locked_until date', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 3,
        attempts_remaining: 2,
        locked_until: null
      };

      const { container } = render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      expect(container.firstChild).toBeNull();
    });

    it('should render timer when account is locked', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: securityTestUtils.createFutureDate(30).toISOString()
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      
      expect(screen.getByTestId('lockout-timer')).toBeInTheDocument();
      expect(screen.getByText('Account Locked')).toBeInTheDocument();
    });

    it('should show correct countdown format', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: securityTestUtils.createFutureDate(25).toISOString() // 25 minutes
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      
      expect(screen.getByText(/Try again in/)).toBeInTheDocument();
      expect(screen.getByText(/2[0-9]:[0-9]{2}/)).toBeInTheDocument(); // Should show around 25:xx
    });

    it('should render in compact mode', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: securityTestUtils.createFutureDate(10).toISOString()
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} compact />);
      
      const timer = screen.getByTestId('lockout-timer');
      expect(timer).toHaveClass('p-2', 'text-sm');
    });

    it('should hide icon when showIcon is false', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: securityTestUtils.createFutureDate(5).toISOString()
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} showIcon={false} />);
      
      const icons = screen.queryAllByRole('img', { hidden: true });
      expect(icons).toHaveLength(0);
    });
  });

  describe('Timer Functionality', () => {
    it('should update countdown every second', async () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: new Date(Date.now() + 120000).toISOString() // 2 minutes
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      
      // Initial state should show 02:xx
      expect(screen.getByText(/01:[0-9]{2}/)).toBeInTheDocument();
      
      // Advance time by 1 second
      mockTimers.advanceTime(1000);
      
      await waitFor(() => {
        expect(screen.getByText(/01:5[0-9]/)).toBeInTheDocument();
      });
    });

    it('should call onLockoutExpired when timer reaches zero', async () => {
      const onLockoutExpired = vi.fn();
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: new Date(Date.now() + 2000).toISOString() // 2 seconds
      };

      render(
        <LockoutTimer 
          lockoutInfo={lockoutInfo} 
          onLockoutExpired={onLockoutExpired}
        />
      );

      // Advance time beyond expiration
      mockTimers.advanceTime(3000);

      await waitFor(() => {
        expect(onLockoutExpired).toHaveBeenCalledTimes(1);
      });
    });

    it('should show expiration message when timer expires', async () => {
      const customMessage = 'Custom expiration message';
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: new Date(Date.now() + 1000).toISOString() // 1 second
      };

      render(
        <LockoutTimer 
          lockoutInfo={lockoutInfo}
          expirationMessage={customMessage}
        />
      );

      // Advance time beyond expiration
      mockTimers.advanceTime(2000);

      await waitFor(() => {
        expect(screen.getByTestId('lockout-expired-message')).toBeInTheDocument();
        expect(screen.getByText(customMessage)).toBeInTheDocument();
      });
    });

    it('should show alert triangle icon when time is low', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: new Date(Date.now() + 30000).toISOString() // 30 seconds (low time)
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      
      // Should show alert triangle (animate-pulse class indicates urgency)
      const alertIcon = screen.getByRole('img', { hidden: true });
      expect(alertIcon).toHaveClass('animate-pulse');
    });

    it('should show clock icon when time is not low', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: new Date(Date.now() + 120000).toISOString() // 2 minutes (not low)
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      
      // Should show clock icon (no animate-pulse class)
      const clockIcon = screen.getByRole('img', { hidden: true });
      expect(clockIcon).not.toHaveClass('animate-pulse');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and live region', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: securityTestUtils.createFutureDate(15).toISOString()
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      
      const timer = screen.getByTestId('lockout-timer');
      expect(timer).toHaveAttribute('role', 'status');
      expect(timer).toHaveAttribute('aria-live', 'polite');
      expect(timer).toHaveAttribute('aria-label');
    });

    it('should provide descriptive aria-label with remaining time', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: new Date(Date.now() + 90000).toISOString() // 1.5 minutes
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      
      const timer = screen.getByTestId('lockout-timer');
      const ariaLabel = timer.getAttribute('aria-label');
      
      expect(ariaLabel).toContain('Account locked');
      expect(ariaLabel).toContain('minute');
      expect(ariaLabel).toContain('second');
    });

    it('should announce expiration message to screen readers', async () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: new Date(Date.now() + 1000).toISOString()
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);

      mockTimers.advanceTime(2000);

      await waitFor(() => {
        const expiredMessage = screen.getByTestId('lockout-expired-message');
        expect(expiredMessage).toHaveAttribute('role', 'status');
        expect(expiredMessage).toHaveAttribute('aria-live', 'polite');
      });
    });
  });

  describe('Custom Props', () => {
    it('should apply custom className', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: securityTestUtils.createFutureDate(5).toISOString()
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} className="custom-class" />);
      
      expect(screen.getByTestId('lockout-timer')).toHaveClass('custom-class');
    });

    it('should show failed attempts count when not in compact mode', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 4,
        attempts_remaining: 1,
        locked_until: securityTestUtils.createFutureDate(10).toISOString()
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} />);
      
      expect(screen.getByText('4 failed attempts')).toBeInTheDocument();
    });

    it('should hide failed attempts count in compact mode', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 4,
        attempts_remaining: 1,
        locked_until: securityTestUtils.createFutureDate(10).toISOString()
      };

      render(<LockoutTimer lockoutInfo={lockoutInfo} compact />);
      
      expect(screen.queryByText('4 failed attempts')).not.toBeInTheDocument();
    });
  });

  describe('useLockoutTimer Hook', () => {
    it('should calculate remaining time correctly', async () => {
      const { useLockoutTimer } = await import('../LockoutTimer');
      const { renderHook } = await import('@testing-library/react');

      const lockoutExpiresAt = securityTestUtils.createFutureDate(5);
      
      const { result } = renderHook(() => useLockoutTimer(lockoutExpiresAt));
      
      expect(result.current.remainingTime).toBeGreaterThan(0);
      expect(result.current.isExpired).toBe(false);
      expect(result.current.formattedTime).toMatch(/0[0-4]:[0-9]{2}/);
    });

    it('should mark as expired for past dates', async () => {
      const { useLockoutTimer } = await import('../LockoutTimer');
      const { renderHook } = await import('@testing-library/react');

      const pastDate = securityTestUtils.createPastDate(5);
      
      const { result } = renderHook(() => useLockoutTimer(pastDate));
      
      expect(result.current.remainingTime).toBe(0);
      expect(result.current.isExpired).toBe(true);
      expect(result.current.formattedTime).toBe('00:00');
    });
  });
});