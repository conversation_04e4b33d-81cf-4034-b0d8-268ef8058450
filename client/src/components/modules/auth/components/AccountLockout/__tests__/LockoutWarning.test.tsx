/**
 * LockoutWarning Component Tests
 * 
 * Comprehensive tests for the LockoutWarning component including
 * severity levels, visual indicators, and user interaction.
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { LockoutWarning } from '../LockoutWarning';
import type { AccountLockoutInfo } from '../../../types/security';

// Mock the security store
vi.mock('@/stores/securityStore', () => ({
  useSecurityStore: vi.fn(() => ({
    lockoutState: {
      failedAttempts: 0,
      attemptsRemaining: 5,
      lockoutExpiresAt: null
    }
  }))
}));

describe('LockoutWarning', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering Conditions', () => {
    it('should not render when no failed attempts', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 0,
        attempts_remaining: 5,
        locked_until: null
      };

      const { container } = render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      expect(container.firstChild).toBeNull();
    });

    it('should not render when account is already locked', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 5,
        attempts_remaining: 0,
        locked_until: new Date().toISOString()
      };

      const { container } = render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      expect(container.firstChild).toBeNull();
    });

    it('should render when there are failed attempts but not locked', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      expect(screen.getByTestId('lockout-warning')).toBeInTheDocument();
      expect(screen.getByText('Security Warning')).toBeInTheDocument();
    });
  });

  describe('Severity Levels', () => {
    it('should show low severity for 4+ remaining attempts', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 1,
        attempts_remaining: 4,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      const warning = screen.getByTestId('lockout-warning');
      expect(warning).toHaveAttribute('data-severity', 'low');
      expect(warning).toHaveClass('bg-blue-50', 'border-blue-300');
    });

    it('should show medium severity for 3 remaining attempts', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      const warning = screen.getByTestId('lockout-warning');
      expect(warning).toHaveAttribute('data-severity', 'medium');
      expect(warning).toHaveClass('bg-yellow-50', 'border-yellow-300');
    });

    it('should show high severity for 2 remaining attempts', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 3,
        attempts_remaining: 2,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      const warning = screen.getByTestId('lockout-warning');
      expect(warning).toHaveAttribute('data-severity', 'high');
      expect(warning).toHaveClass('bg-orange-50', 'border-orange-300');
    });

    it('should show critical severity for 1 remaining attempt', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 4,
        attempts_remaining: 1,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      const warning = screen.getByTestId('lockout-warning');
      expect(warning).toHaveAttribute('data-severity', 'critical');
      expect(warning).toHaveClass('bg-red-50', 'border-red-300');
      
      // Critical severity should have animated icon
      const icon = warning.querySelector('[class*="animate-pulse"]');
      expect(icon).toBeInTheDocument();
    });
  });

  describe('Warning Messages', () => {
    it('should show critical message for last attempt', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 4,
        attempts_remaining: 1,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      expect(screen.getByText(/Critical.*one more failed login attempt/)).toBeInTheDocument();
    });

    it('should show warning message for 2 attempts remaining', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 3,
        attempts_remaining: 2,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      expect(screen.getByText(/Warning.*2 login attempts remaining/)).toBeInTheDocument();
    });

    it('should show general message for more attempts', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 1,
        attempts_remaining: 4,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      expect(screen.getByText(/You have 4 login attempts remaining/)).toBeInTheDocument();
      expect(screen.getByText(/1 previous attempt failed/)).toBeInTheDocument();
    });

    it('should use custom message when provided', () => {
      const customMessage = 'Custom warning message';
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} customMessage={customMessage} />);
      
      expect(screen.getByText(customMessage)).toBeInTheDocument();
    });
  });

  describe('Details Display', () => {
    it('should show detailed attempt information', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 3,
        attempts_remaining: 2,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} showDetails />);
      
      expect(screen.getByText('Failed attempts: 3')).toBeInTheDocument();
      expect(screen.getByText('Remaining: 2')).toBeInTheDocument();
    });

    it('should hide details in compact mode', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 3,
        attempts_remaining: 2,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} compact />);
      
      expect(screen.queryByText('Failed attempts: 3')).not.toBeInTheDocument();
    });

    it('should show visual progress indicator', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} showDetails />);
      
      // Should have 5 progress bars (total allowed attempts)
      const progressBars = screen.getAllByLabelText(/attempt/);
      expect(progressBars).toHaveLength(5);
      
      // First 2 should be marked as failed attempts
      const failedBars = screen.getAllByLabelText('Failed attempt');
      expect(failedBars).toHaveLength(2);
      
      // Remaining 3 should be marked as remaining attempts
      const remainingBars = screen.getAllByLabelText('Remaining attempt');
      expect(remainingBars).toHaveLength(3);
    });
  });

  describe('Password Help', () => {
    it('should show password help for high severity', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 3,
        attempts_remaining: 2,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} showPasswordHelp />);
      
      expect(screen.getByText('Having trouble remembering your password?')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /reset your password/i })).toBeInTheDocument();
    });

    it('should show password help for critical severity', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 4,
        attempts_remaining: 1,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} showPasswordHelp />);
      
      expect(screen.getByText('Having trouble remembering your password?')).toBeInTheDocument();
    });

    it('should not show password help for low severity', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 1,
        attempts_remaining: 4,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} showPasswordHelp />);
      
      expect(screen.queryByText('Having trouble remembering your password?')).not.toBeInTheDocument();
    });

    it('should call onPasswordHelpClick when button is clicked', () => {
      const onPasswordHelpClick = vi.fn();
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 3,
        attempts_remaining: 2,
        locked_until: null
      };

      render(
        <LockoutWarning 
          lockoutInfo={lockoutInfo} 
          showPasswordHelp 
          onPasswordHelpClick={onPasswordHelpClick}
        />
      );
      
      const helpButton = screen.getByRole('button', { name: /reset your password/i });
      fireEvent.click(helpButton);
      
      expect(onPasswordHelpClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('Compact Mode', () => {
    it('should apply compact styling', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} compact />);
      
      const warning = screen.getByTestId('lockout-warning');
      expect(warning).toHaveClass('p-2', 'text-sm');
    });

    it('should hide detailed information', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} compact />);
      
      expect(screen.queryByText('Failed attempts:')).not.toBeInTheDocument();
      expect(screen.queryByText('Having trouble remembering')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      const warning = screen.getByTestId('lockout-warning');
      expect(warning).toHaveAttribute('role', 'alert');
      expect(warning).toHaveAttribute('aria-live', 'polite');
    });

    it('should have proper icon ARIA labels', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} />);
      
      const icon = screen.getByRole('img', { hidden: true });
      expect(icon).toHaveAttribute('aria-hidden', 'true');
    });
  });

  describe('useLockoutWarning Hook', () => {
    it('should determine when to show warning', async () => {
      const { useLockoutWarning } = await import('../LockoutWarning');
      const { renderHook } = await import('@testing-library/react');

      const { result: showResult } = renderHook(() => 
        useLockoutWarning(2, 3, false) // 2 failed, 3 remaining, not locked
      );
      
      expect(showResult.current.shouldShowWarning).toBe(true);
      expect(showResult.current.severity).toBe('medium');
      expect(showResult.current.isHighRisk).toBe(false);

      const { result: hideResult } = renderHook(() => 
        useLockoutWarning(0, 5, false) // No failed attempts
      );
      
      expect(hideResult.current.shouldShowWarning).toBe(false);
    });

    it('should identify high risk scenarios', async () => {
      const { useLockoutWarning } = await import('../LockoutWarning');
      const { renderHook } = await import('@testing-library/react');

      const { result } = renderHook(() => 
        useLockoutWarning(4, 1, false) // 4 failed, 1 remaining, not locked
      );
      
      expect(result.current.severity).toBe('critical');
      expect(result.current.isHighRisk).toBe(true);
    });
  });

  describe('LockoutWarningInline', () => {
    it('should render as compact without details', async () => {
      const { LockoutWarningInline } = await import('../LockoutWarning');
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 2,
        attempts_remaining: 3,
        locked_until: null
      };

      render(<LockoutWarningInline lockoutInfo={lockoutInfo} />);
      
      const warning = screen.getByTestId('lockout-warning');
      expect(warning).toHaveClass('p-2', 'text-sm');
      expect(screen.queryByText('Failed attempts:')).not.toBeInTheDocument();
    });
  });

  describe('Custom Props', () => {
    it('should apply custom className', () => {
      const lockoutInfo: AccountLockoutInfo = {
        failed_attempts: 1,
        attempts_remaining: 4,
        locked_until: null
      };

      render(<LockoutWarning lockoutInfo={lockoutInfo} className="custom-class" />);
      
      expect(screen.getByTestId('lockout-warning')).toHaveClass('custom-class');
    });
  });
});