/**
 * LockoutWarning Component for FR-1 Account Lockout Protection
 * 
 * This component displays warnings about failed login attempts and remaining
 * attempts before account lockout, helping users understand the security measures.
 */

'use client';

import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/atoms/button';
import { useSecurityStore } from '@/stores/securityStore';
import type { AccountLockoutInfo } from '../../types/security';

export interface LockoutWarningProps {
  /** Lockout information from API or store */
  lockoutInfo?: AccountLockoutInfo;
  /** Custom CSS classes */
  className?: string;
  /** Show detailed warning (attempts count) */
  showDetails?: boolean;
  /** Compact mode for smaller displays */
  compact?: boolean;
  /** Custom warning message */
  customMessage?: string;
  /** Show help text about password recovery */
  showPasswordHelp?: boolean;
  /** Callback when user requests password help */
  onPasswordHelpClick?: () => void;
}

/**
 * LockoutWarning displays warnings about failed attempts and approaching lockout
 */
export const LockoutWarning: React.FC<LockoutWarningProps> = ({
  lockoutInfo,
  className,
  showDetails = true,
  compact = false,
  customMessage,
  showPasswordHelp = true,
  onPasswordHelpClick
}) => {
  const { lockoutState } = useSecurityStore();

  // Use lockoutInfo prop or fallback to store state
  const effectiveLockoutInfo = lockoutInfo || {
    failed_attempts: lockoutState.failedAttempts,
    attempts_remaining: lockoutState.attemptsRemaining,
    locked_until: lockoutState.lockoutExpiresAt?.toISOString() || null
  };

  // Don't show warning if no failed attempts or account is already locked
  if (effectiveLockoutInfo.failed_attempts === 0 || effectiveLockoutInfo.locked_until) {
    return null;
  }

  /**
   * Get warning severity based on remaining attempts
   */
  const getWarningSeverity = (): 'low' | 'medium' | 'high' | 'critical' => {
    const remaining = effectiveLockoutInfo.attempts_remaining;
    
    if (remaining <= 1) return 'critical';
    if (remaining <= 2) return 'high';
    if (remaining <= 3) return 'medium';
    return 'low';
  };

  /**
   * Get appropriate styling based on severity
   */
  const getSeverityStyles = (severity: 'low' | 'medium' | 'high' | 'critical') => {
    switch (severity) {
      case 'critical':
        return {
          container: 'bg-red-50 border-red-300 text-red-900',
          icon: 'text-red-600 animate-pulse',
          text: 'text-red-800'
        };
      case 'high':
        return {
          container: 'bg-orange-50 border-orange-300 text-orange-900',
          icon: 'text-orange-600',
          text: 'text-orange-800'
        };
      case 'medium':
        return {
          container: 'bg-yellow-50 border-yellow-300 text-yellow-900',
          icon: 'text-yellow-600',
          text: 'text-yellow-800'
        };
      case 'low':
        return {
          container: 'bg-blue-50 border-blue-300 text-blue-900',
          icon: 'text-blue-600',
          text: 'text-blue-800'
        };
    }
  };

  /**
   * Generate warning message based on attempts remaining
   */
  const getWarningMessage = (): string => {
    if (customMessage) {
      return customMessage;
    }

    const remaining = effectiveLockoutInfo.attempts_remaining;
    const failed = effectiveLockoutInfo.failed_attempts;

    if (remaining <= 1) {
      return 'Critical: Your account will be locked after one more failed login attempt.';
    }

    if (remaining <= 2) {
      return `Warning: Only ${remaining} login attempts remaining before your account is locked.`;
    }

    return `You have ${remaining} login attempts remaining. ${failed} previous attempt${failed === 1 ? '' : 's'} failed.`;
  };

  const severity = getWarningSeverity();
  const styles = getSeverityStyles(severity);

  return (
    <div
      className={cn(
        'border rounded-md p-3 transition-all duration-200',
        styles.container,
        compact && 'p-2 text-sm',
        className
      )}
      role="alert"
      aria-live="polite"
      data-testid="lockout-warning"
      data-severity={severity}
    >
      <div className="flex items-start gap-2">
        <div className={cn('flex-shrink-0 mt-0.5', compact && 'mt-0')}>
          <AlertTriangle 
            className={cn(
              'h-4 w-4',
              styles.icon,
              compact && 'h-3 w-3'
            )} 
            aria-hidden="true"
          />
        </div>
        
        <div className="flex-1">
          <div className={cn('font-medium mb-1', compact && 'text-sm mb-0.5')}>
            Security Warning
          </div>
          
          <div className={cn('text-sm', styles.text, compact && 'text-xs')}>
            {getWarningMessage()}
          </div>
          
          {showDetails && !compact && (
            <div className={cn('mt-2 text-xs', styles.text)}>
              <div className="flex items-center justify-between">
                <span>Failed attempts: {effectiveLockoutInfo.failed_attempts}</span>
                <span>Remaining: {effectiveLockoutInfo.attempts_remaining}</span>
              </div>
              
              {/* Visual progress indicator */}
              <div className="mt-2">
                <div className="flex gap-1">
                  {[...Array(5)].map((_, index) => {
                    const isUsed = index < effectiveLockoutInfo.failed_attempts;
                    return (
                      <div
                        key={index}
                        className={cn(
                          'h-1.5 w-full rounded-full',
                          isUsed 
                            ? (severity === 'critical' ? 'bg-red-400' : 
                               severity === 'high' ? 'bg-orange-400' :
                               severity === 'medium' ? 'bg-yellow-400' : 'bg-blue-400')
                            : 'bg-gray-200'
                        )}
                        aria-label={isUsed ? 'Failed attempt' : 'Remaining attempt'}
                      />
                    );
                  })}
                </div>
              </div>
            </div>
          )}
          
          {showPasswordHelp && severity === 'high' || severity === 'critical' && (
            <div className="mt-3 pt-3 border-t border-current/20">
              <p className={cn('text-xs mb-2', styles.text)}>
                Having trouble remembering your password?
              </p>
              <Button
                variant="ghost"
                size="sm"
                onClick={onPasswordHelpClick}
                className={cn(
                  'p-0 h-auto font-normal underline',
                  styles.text,
                  'hover:no-underline'
                )}
              >
                Reset your password
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Hook to determine if lockout warning should be shown
 */
export const useLockoutWarning = (
  failedAttempts: number,
  attemptsRemaining: number,
  isLocked: boolean
) => {
  const shouldShowWarning = failedAttempts > 0 && attemptsRemaining > 0 && !isLocked;
  const severity = (() => {
    if (attemptsRemaining <= 1) return 'critical';
    if (attemptsRemaining <= 2) return 'high';
    if (attemptsRemaining <= 3) return 'medium';
    return 'low';
  })();

  return {
    shouldShowWarning,
    severity,
    isHighRisk: severity === 'critical' || severity === 'high'
  };
};

/**
 * Compact version of LockoutWarning for inline use
 */
export const LockoutWarningInline: React.FC<Omit<LockoutWarningProps, 'compact'>> = (props) => (
  <LockoutWarning {...props} compact showDetails={false} showPasswordHelp={false} />
);

export default LockoutWarning;