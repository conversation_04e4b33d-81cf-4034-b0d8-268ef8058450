/**
 * EnhancedRegistrationForm Component for FR-1 Security Features
 * 
 * This component provides an enhanced registration form with email verification
 * workflow integration, password strength validation, and proper error handling.
 */

'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Mail, Lock, User, RefreshCw, CheckCircle, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { Button } from '@/components/atoms/button';
import { Input } from '@/components/atoms/input';
import { Label } from '@/components/atoms/label';
import { Checkbox } from '@/components/atoms/checkbox';

import { useSecurityStore } from '@/stores/securityStore';
import { SecurityService } from '@/lib/api/endpoints/securityEnhanced';
import { handleSecurityError } from '../../utils/secureErrorHandling';
import { registrationRequestEnhancedSchema, type RegistrationRequestEnhancedType } from '../../validation/securitySchemas';

import { PasswordStrengthMeter } from '../PasswordReset/PasswordStrengthMeter';
import { EmailVerificationBanner } from '../EmailVerification/EmailVerificationBanner';

export interface EnhancedRegistrationFormProps {
  /** Initial email value */
  email?: string;
  /** Custom CSS classes */
  className?: string;
  /** Callback on successful registration */
  onRegistrationSuccess?: (result: any) => void;
  /** Callback on registration error */
  onRegistrationError?: (error: any) => void;
  /** Show login link */
  showLoginLink?: boolean;
  /** Compact mode for smaller displays */
  compact?: boolean;
  /** Auto-focus first field */
  autoFocus?: boolean;
  /** Show terms and conditions checkbox */
  showTermsCheckbox?: boolean;
}

/**
 * EnhancedRegistrationForm with email verification integration
 */
export const EnhancedRegistrationForm: React.FC<EnhancedRegistrationFormProps> = ({
  email: initialEmail,
  className,
  onRegistrationSuccess,
  onRegistrationError,
  showLoginLink = true,
  compact = false,
  autoFocus = false,
  showTermsCheckbox = true
}) => {
  const {
    setVerificationSent
  } = useSecurityStore();

  const [isRegistering, setIsRegistering] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [registeredEmail, setRegisteredEmail] = useState<string>('');
  const [acceptTerms, setAcceptTerms] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset
  } = useForm<RegistrationRequestEnhancedType>({
    resolver: zodResolver(registrationRequestEnhancedSchema),
    defaultValues: {
      name: '',
      email: initialEmail || '',
      password: '',
      confirm_password: ''
    }
  });

  const watchedPassword = watch('password');
  const watchedEmail = watch('email');
  const watchedName = watch('name');

  /**
   * Handle registration form submission
   */
  const handleRegistration = async (data: RegistrationRequestEnhancedType) => {
    if (showTermsCheckbox && !acceptTerms) {
      setErrorMessage('Please accept the terms and conditions to continue');
      return;
    }

    setIsRegistering(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      const result = await SecurityService.registerWithVerification({
        name: data.name,
        email: data.email,
        password: data.password,
        confirm_password: data.confirm_password
      });

      // Registration successful
      setSuccessMessage('Account created successfully! Please check your email for verification instructions.');
      setRegisteredEmail(data.email);
      
      // Update verification state
      if (result.email_verification_required) {
        setVerificationSent(data.email);
      }

      onRegistrationSuccess?.(result);
      
      // Clear form
      reset();
      setAcceptTerms(false);
      
    } catch (error) {
      const securityError = handleSecurityError(error, 'registration');
      setErrorMessage(securityError.message);
      onRegistrationError?.(securityError);
    } finally {
      setIsRegistering(false);
    }
  };

  /**
   * Handle email verification resend success
   */
  const handleResendSuccess = (email: string) => {
    setSuccessMessage(`Verification email resent to ${email}`);
    setTimeout(() => setSuccessMessage(''), 5000);
  };

  return (
    <div 
      className={cn('space-y-4', compact && 'space-y-3', className)}
      data-testid="enhanced-registration-form"
    >
      {/* Success Message */}
      {successMessage && (
        <div 
          className={cn(
            'flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md text-green-800',
            compact && 'p-2 text-sm'
          )}
          role="status"
          aria-live="polite"
          data-testid="registration-success"
        >
          <CheckCircle className={cn('h-4 w-4 text-green-600', compact && 'h-3 w-3')} />
          <span>{successMessage}</span>
        </div>
      )}

      {/* Email Verification Banner */}
      {registeredEmail && (
        <EmailVerificationBanner
          email={registeredEmail}
          compact={compact}
          customMessage={`Welcome! Please verify your email (${registeredEmail}) to complete your account setup.`}
          onResendSuccess={handleResendSuccess}
        />
      )}

      {/* Error Message */}
      {errorMessage && (
        <div 
          className={cn(
            'flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800',
            compact && 'p-2 text-sm'
          )}
          role="alert"
          aria-live="polite"
          data-testid="registration-error"
        >
          <AlertTriangle className={cn('h-4 w-4 text-red-600', compact && 'h-3 w-3')} />
          <span>{errorMessage}</span>
        </div>
      )}

      <form onSubmit={handleSubmit(handleRegistration)} className="space-y-4">
        {/* Name Field */}
        <div className="space-y-2">
          <Label htmlFor="registration-name" className={cn(compact && 'text-sm')}>
            Full Name
          </Label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="registration-name"
              type="text"
              placeholder="Enter your full name"
              {...register('name')}
              className={cn('pl-10', compact && 'h-9 text-sm')}
              disabled={isRegistering}
              autoFocus={autoFocus}
            />
          </div>
          {errors.name && (
            <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
              {errors.name.message}
            </p>
          )}
        </div>

        {/* Email Field */}
        <div className="space-y-2">
          <Label htmlFor="registration-email" className={cn(compact && 'text-sm')}>
            Email Address
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="registration-email"
              type="email"
              placeholder="Enter your email address"
              {...register('email')}
              className={cn('pl-10', compact && 'h-9 text-sm')}
              disabled={isRegistering}
            />
          </div>
          {errors.email && (
            <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
              {errors.email.message}
            </p>
          )}
        </div>

        {/* Password Field */}
        <div className="space-y-2">
          <Label htmlFor="registration-password" className={cn(compact && 'text-sm')}>
            Password
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="registration-password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Create a secure password"
              {...register('password')}
              className={cn('pl-10 pr-10', compact && 'h-9 text-sm')}
              disabled={isRegistering}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
              tabIndex={-1}
              disabled={isRegistering}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </Button>
          </div>
          {errors.password && (
            <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
              {errors.password.message}
            </p>
          )}
          
          {/* Password Strength Meter */}
          {watchedPassword && (
            <PasswordStrengthMeter
              password={watchedPassword}
              compact={compact}
              showCriteria={!compact}
            />
          )}
        </div>

        {/* Confirm Password Field */}
        <div className="space-y-2">
          <Label htmlFor="registration-confirm-password" className={cn(compact && 'text-sm')}>
            Confirm Password
          </Label>
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="registration-confirm-password"
              type={showConfirmPassword ? 'text' : 'password'}
              placeholder="Confirm your password"
              {...register('confirm_password')}
              className={cn('pl-10 pr-10', compact && 'h-9 text-sm')}
              disabled={isRegistering}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
              tabIndex={-1}
              disabled={isRegistering}
            >
              {showConfirmPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </Button>
          </div>
          {errors.confirm_password && (
            <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
              {errors.confirm_password.message}
            </p>
          )}
        </div>

        {/* Terms and Conditions Checkbox */}
        {showTermsCheckbox && (
          <div className="flex items-start space-x-2">
            <Checkbox
              id="terms"
              checked={acceptTerms}
              onCheckedChange={(checked) => setAcceptTerms(checked as boolean)}
              disabled={isRegistering}
              className="mt-1"
            />
            <Label 
              htmlFor="terms" 
              className={cn('text-sm text-gray-600 leading-relaxed', compact && 'text-xs')}
            >
              I agree to the{' '}
              <Button
                variant="link"
                className="p-0 h-auto text-blue-600 hover:text-blue-800"
                type="button"
              >
                Terms of Service
              </Button>
              {' '}and{' '}
              <Button
                variant="link"
                className="p-0 h-auto text-blue-600 hover:text-blue-800"
                type="button"
              >
                Privacy Policy
              </Button>
            </Label>
          </div>
        )}

        {/* Registration Button */}
        <Button
          type="submit"
          disabled={
            isRegistering || 
            (showTermsCheckbox && !acceptTerms) ||
            !watchedName ||
            !watchedEmail ||
            !watchedPassword
          }
          className={cn('w-full', compact && 'h-9 text-sm')}
          data-testid="registration-button"
        >
          {isRegistering && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
          {isRegistering ? 'Creating Account...' : 'Create Account'}
        </Button>
      </form>

      {/* Login Link */}
      {showLoginLink && !compact && (
        <div className="text-center text-sm text-gray-600">
          Already have an account?{' '}
          <Button
            variant="link"
            className="p-0 h-auto text-blue-600 hover:text-blue-800"
          >
            Sign in here
          </Button>
        </div>
      )}

      {/* Help Text */}
      {!compact && (
        <div className="text-xs text-gray-500 space-y-1">
          <p>• You&apos;ll receive an email verification link after registration</p>
          <p>• Your account will be active after email verification</p>
          <p>• Choose a strong password to keep your account secure</p>
        </div>
      )}
    </div>
  );
};

export default EnhancedRegistrationForm;