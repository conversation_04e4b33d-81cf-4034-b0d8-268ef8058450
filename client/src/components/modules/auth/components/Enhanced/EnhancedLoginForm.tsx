/**
 * EnhancedLoginForm Component for FR-1 Security Features
 * 
 * This component provides an enhanced login form with account lockout protection,
 * security warnings, and proper error handling with lockout information.
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Mail, Lock, RefreshCw, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { Button } from '@/components/atoms/button';
import { Input } from '@/components/atoms/input';
import { Label } from '@/components/atoms/label';

import { useSecurityStore } from '@/stores/securityStore';
import { SecurityService } from '@/lib/api/endpoints/securityEnhanced';
import { handleSecurityError, isAccountLockoutError, formatLockoutMessage } from '../../utils/secureErrorHandling';
import { loginRequestEnhancedSchema, type LoginRequestEnhancedType } from '../../validation/securitySchemas';

import { LockoutTimer } from '../AccountLockout/LockoutTimer';
import { LockoutWarning } from '../AccountLockout/LockoutWarning';
import { EmailVerificationBanner } from '../EmailVerification/EmailVerificationBanner';

export interface EnhancedLoginFormProps {
  /** Initial email value */
  email?: string;
  /** Custom CSS classes */
  className?: string;
  /** Callback on successful login */
  onLoginSuccess?: (result: any) => void;
  /** Callback on login error */
  onLoginError?: (error: any) => void;
  /** Callback when user needs to verify email */
  onEmailVerificationNeeded?: (email: string) => void;
  /** Callback when user wants to reset password */
  onPasswordResetRequest?: (email: string) => void;
  /** Show registration link */
  showRegisterLink?: boolean;
  /** Compact mode for smaller displays */
  compact?: boolean;
  /** Auto-focus email field */
  autoFocus?: boolean;
}

/**
 * EnhancedLoginForm with security features
 */
export const EnhancedLoginForm: React.FC<EnhancedLoginFormProps> = ({
  email: initialEmail,
  className,
  onLoginSuccess,
  onLoginError,
  onEmailVerificationNeeded,
  onPasswordResetRequest,
  showRegisterLink = true,
  compact = false,
  autoFocus = false
}) => {
  const {
    lockoutState,
    setLockoutState,
    updateFailedAttempts,
    setAccountLocked,
    clearLockoutState
  } = useSecurityStore();

  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [needsEmailVerification, setNeedsEmailVerification] = useState<string>('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<LoginRequestEnhancedType>({
    resolver: zodResolver(loginRequestEnhancedSchema),
    defaultValues: {
      email: initialEmail || '',
      password: ''
    }
  });

  const watchedEmail = watch('email');

  /**
   * Check if email is locked out
   */
  const checkLockoutStatus = useCallback(async (email: string) => {
    try {
      const status = await SecurityService.checkLockoutStatus(email);
      
      if (status.is_locked) {
        setAccountLocked(
          status.lockout_expires_at ? new Date(status.lockout_expires_at) : new Date()
        );
      } else {
        setLockoutState({
          isLocked: false,
          lockoutExpiresAt: null,
          failedAttempts: Math.max(0, 5 - (status.attempts_remaining || 5)),
          attemptsRemaining: status.attempts_remaining || 5
        });
      }
    } catch (error) {
      // Silently handle lockout check errors
      console.debug('Lockout status check failed:', error);
    }
  }, [setAccountLocked, setLockoutState]);

  /**
   * Check lockout status on email change
   */
  useEffect(() => {
    if (watchedEmail && watchedEmail.includes('@')) {
      checkLockoutStatus(watchedEmail);
    }
  }, [watchedEmail, checkLockoutStatus]);

  /**
   * Auto-clear error message when user starts typing
   */
  useEffect(() => {
    if (errorMessage) {
      const timeout = setTimeout(() => setErrorMessage(''), 5000);
      return () => clearTimeout(timeout);
    }
  }, [errorMessage]);

  /**
   * Handle login form submission
   */
  const handleLogin = async (data: LoginRequestEnhancedType) => {
    // Don't proceed if account is locked
    if (lockoutState.isLocked && lockoutState.lockoutExpiresAt && lockoutState.lockoutExpiresAt > new Date()) {
      setErrorMessage('Your account is currently locked. Please wait before trying again.');
      return;
    }

    setIsLoggingIn(true);
    setErrorMessage('');
    setNeedsEmailVerification('');

    try {
      const result = await SecurityService.loginWithLockoutInfo(data.email, data.password);
      
      // Login successful - clear lockout state
      clearLockoutState();
      onLoginSuccess?.(result);
      
    } catch (error) {
      const securityError = handleSecurityError(error, 'login');
      
      if (isAccountLockoutError(securityError)) {
        // Handle account lockout
        if (securityError.lockoutInfo) {
          const lockoutInfo = securityError.lockoutInfo;
          
          if (lockoutInfo.locked_until) {
            setAccountLocked(
              new Date(lockoutInfo.locked_until)
            );
            setErrorMessage(formatLockoutMessage(lockoutInfo));
          } else {
            updateFailedAttempts(
              lockoutInfo.failed_attempts,
              lockoutInfo.attempts_remaining
            );
            setErrorMessage(securityError.message);
          }
        }
      } else if (securityError.code === 'EMAIL_NOT_VERIFIED') {
        // Handle email verification needed
        setNeedsEmailVerification(data.email);
        setErrorMessage('Please verify your email address before logging in.');
        onEmailVerificationNeeded?.(data.email);
      } else {
        // Handle other errors
        setErrorMessage(securityError.message);
      }
      
      onLoginError?.(securityError);
    } finally {
      setIsLoggingIn(false);
    }
  };

  /**
   * Handle password reset request
   */
  const handlePasswordResetClick = () => {
    const email = watchedEmail;
    if (email) {
      onPasswordResetRequest?.(email);
    }
  };

  /**
   * Handle lockout expiration
   */
  const handleLockoutExpired = () => {
    clearLockoutState();
    setErrorMessage('');
  };

  return (
    <div 
      className={cn('space-y-4', compact && 'space-y-3', className)}
      data-testid="enhanced-login-form"
    >
      {/* Email Verification Banner */}
      {needsEmailVerification && (
        <EmailVerificationBanner
          email={needsEmailVerification}
          compact={compact}
          customMessage={`Please verify your email (${needsEmailVerification}) before logging in.`}
        />
      )}

      {/* Account Lockout Timer */}
      {lockoutState.isLocked && lockoutState.lockoutExpiresAt && (
        <LockoutTimer
          lockoutInfo={{
            failed_attempts: lockoutState.failedAttempts,
            attempts_remaining: lockoutState.attemptsRemaining,
            locked_until: lockoutState.lockoutExpiresAt.toISOString()
          }}
          compact={compact}
          onLockoutExpired={handleLockoutExpired}
        />
      )}

      {/* Lockout Warning */}
      {!lockoutState.isLocked && lockoutState.failedAttempts > 0 && (
        <LockoutWarning
          lockoutInfo={{
            failed_attempts: lockoutState.failedAttempts,
            attempts_remaining: lockoutState.attemptsRemaining,
            locked_until: undefined
          }}
          compact={compact}
          onPasswordHelpClick={handlePasswordResetClick}
        />
      )}

      {/* Error Message */}
      {errorMessage && !lockoutState.isLocked && (
        <div 
          className={cn(
            'flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800',
            compact && 'p-2 text-sm'
          )}
          role="alert"
          aria-live="polite"
          data-testid="login-error"
        >
          <AlertTriangle className={cn('h-4 w-4 text-red-600', compact && 'h-3 w-3')} />
          <span>{errorMessage}</span>
        </div>
      )}

      <form onSubmit={handleSubmit(handleLogin)} className="space-y-4">
        {/* Email Field */}
        <div className="space-y-2">
          <Label htmlFor="login-email" className={cn(compact && 'text-sm')}>
            Email Address
          </Label>
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="login-email"
              type="email"
              placeholder="Enter your email address"
              {...register('email')}
              className={cn('pl-10', compact && 'h-9 text-sm')}
              disabled={isLoggingIn || lockoutState.isLocked}
              autoFocus={autoFocus}
            />
          </div>
          {errors.email && (
            <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
              {errors.email.message}
            </p>
          )}
        </div>

        {/* Password Field */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="login-password" className={cn(compact && 'text-sm')}>
              Password
            </Label>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={handlePasswordResetClick}
              className={cn(
                'h-auto p-0 text-sm text-blue-600 hover:text-blue-800 hover:bg-transparent',
                compact && 'text-xs'
              )}
              disabled={!watchedEmail}
            >
              Forgot password?
            </Button>
          </div>
          
          <div className="relative">
            <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              id="login-password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Enter your password"
              {...register('password')}
              className={cn('pl-10 pr-10', compact && 'h-9 text-sm')}
              disabled={isLoggingIn || lockoutState.isLocked}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
              tabIndex={-1}
              disabled={isLoggingIn || lockoutState.isLocked}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-400" />
              ) : (
                <Eye className="h-4 w-4 text-gray-400" />
              )}
            </Button>
          </div>
          {errors.password && (
            <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
              {errors.password.message}
            </p>
          )}
        </div>

        {/* Login Button */}
        <Button
          type="submit"
          disabled={
            isLoggingIn || 
            lockoutState.isLocked || 
            lockoutState.attemptsRemaining <= 0
          }
          className={cn('w-full', compact && 'h-9 text-sm')}
          data-testid="login-button"
        >
          {isLoggingIn && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
          {isLoggingIn ? 'Signing In...' : 'Sign In'}
        </Button>
      </form>

      {/* Registration Link */}
      {showRegisterLink && !compact && (
        <div className="text-center text-sm text-gray-600">
          Don&apos;t have an account?{' '}
          <Button
            variant="link"
            className="p-0 h-auto text-blue-600 hover:text-blue-800"
          >
            Create one here
          </Button>
        </div>
      )}

      {/* Help Text */}
      {!compact && !lockoutState.isLocked && (
        <div className="text-xs text-gray-500 space-y-1">
          <p>• Your account will be locked after 5 failed login attempts</p>
          <p>• Locked accounts are unlocked automatically after 30 minutes</p>
          <p>• Make sure your email is verified to log in</p>
        </div>
      )}
    </div>
  );
};

export default EnhancedLoginForm;