/**
 * PasswordStrengthMeter Component for FR-1 Enhanced Password Reset
 * 
 * This component displays a visual password strength indicator with
 * detailed feedback and security recommendations.
 */

'use client';

import React from 'react';
import { CheckCircle, X, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Progress } from '@/components/atoms/progress';

export interface PasswordStrengthMeterProps {
  /** Password to analyze */
  password: string;
  /** Custom CSS classes */
  className?: string;
  /** Show detailed criteria */
  showCriteria?: boolean;
  /** Compact mode for smaller displays */
  compact?: boolean;
  /** Minimum required score (0-100) */
  minScore?: number;
}

interface PasswordCriterion {
  id: string;
  label: string;
  test: (password: string) => boolean;
  weight: number;
}

const passwordCriteria: PasswordCriterion[] = [
  {
    id: 'length',
    label: 'At least 8 characters',
    test: (password) => password.length >= 8,
    weight: 25
  },
  {
    id: 'lowercase',
    label: 'Contains lowercase letters',
    test: (password) => /[a-z]/.test(password),
    weight: 15
  },
  {
    id: 'uppercase',
    label: 'Contains uppercase letters',
    test: (password) => /[A-Z]/.test(password),
    weight: 15
  },
  {
    id: 'numbers',
    label: 'Contains numbers',
    test: (password) => /\d/.test(password),
    weight: 15
  },
  {
    id: 'special',
    label: 'Contains special characters',
    test: (password) => /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password),
    weight: 15
  },
  {
    id: 'longLength',
    label: 'At least 12 characters (bonus)',
    test: (password) => password.length >= 12,
    weight: 15
  }
];

/**
 * Calculate password strength score and analysis
 */
const analyzePassword = (password: string) => {
  let score = 0;
  const passedCriteria: string[] = [];
  const failedCriteria: string[] = [];

  passwordCriteria.forEach(criterion => {
    if (criterion.test(password)) {
      score += criterion.weight;
      passedCriteria.push(criterion.id);
    } else {
      failedCriteria.push(criterion.id);
    }
  });

  // Bonus for longer passwords
  if (password.length >= 16) {
    score += 10;
  }

  // Penalty for common patterns
  if (/(.)\1{2,}/.test(password)) {
    score -= 10; // Repeated characters
  }
  if (/123|abc|qwe/i.test(password)) {
    score -= 15; // Sequential patterns
  }

  score = Math.max(0, Math.min(100, score));

  const strength = (() => {
    if (score < 30) return { level: 'weak', label: 'Weak', color: 'red' };
    if (score < 50) return { level: 'fair', label: 'Fair', color: 'yellow' };
    if (score < 70) return { level: 'good', label: 'Good', color: 'blue' };
    if (score < 85) return { level: 'strong', label: 'Strong', color: 'green' };
    return { level: 'excellent', label: 'Excellent', color: 'emerald' };
  })();

  return {
    score,
    strength,
    passedCriteria,
    failedCriteria
  };
};

/**
 * PasswordStrengthMeter displays password strength analysis
 */
export const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({
  password,
  className,
  showCriteria = true,
  compact = false,
  minScore = 60
}) => {
  const analysis = analyzePassword(password);
  const { score, strength, passedCriteria, failedCriteria } = analysis;

  // Don't render anything if no password
  if (!password) {
    return null;
  }

  const strengthColors = {
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'text-red-800',
      progress: 'bg-red-500'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-800',
      progress: 'bg-yellow-500'
    },
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      text: 'text-blue-800',
      progress: 'bg-blue-500'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'text-green-800',
      progress: 'bg-green-500'
    },
    emerald: {
      bg: 'bg-emerald-50',
      border: 'border-emerald-200',
      text: 'text-emerald-800',
      progress: 'bg-emerald-500'
    }
  };

  const colors = strengthColors[strength.color as keyof typeof strengthColors];
  const meetsMinimum = score >= minScore;

  return (
    <div
      className={cn(
        'space-y-3 p-3 rounded-md border',
        colors.bg,
        colors.border,
        compact && 'p-2 space-y-2',
        className
      )}
      data-testid="password-strength-meter"
      data-strength={strength.level}
      data-score={score}
    >
      {/* Header with strength label and score */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className={cn('font-medium', colors.text, compact && 'text-sm')}>
            Password Strength: {strength.label}
          </div>
          {meetsMinimum ? (
            <CheckCircle className={cn('h-4 w-4 text-green-600', compact && 'h-3 w-3')} />
          ) : (
            <AlertCircle className={cn('h-4 w-4 text-orange-600', compact && 'h-3 w-3')} />
          )}
        </div>
        <div className={cn('text-sm font-mono', colors.text, compact && 'text-xs')}>
          {score}/100
        </div>
      </div>

      {/* Progress bar */}
      <div className="space-y-1">
        <Progress 
          value={score} 
          className={cn('h-2', compact && 'h-1.5')}
          data-testid="strength-progress"
        />
        <div className="flex justify-between text-xs text-gray-500">
          <span>Weak</span>
          <span>Strong</span>
        </div>
      </div>

      {/* Minimum score indicator */}
      {!meetsMinimum && (
        <div className={cn('text-sm text-orange-600', compact && 'text-xs')}>
          Password must score at least {minScore} points to be accepted
        </div>
      )}

      {/* Detailed criteria */}
      {showCriteria && !compact && (
        <div className="space-y-2">
          <div className={cn('text-sm font-medium', colors.text)}>
            Security Requirements:
          </div>
          
          <div className="grid gap-1.5">
            {passwordCriteria.map(criterion => {
              const isPassed = passedCriteria.includes(criterion.id);
              return (
                <div
                  key={criterion.id}
                  className={cn(
                    'flex items-center gap-2 text-sm',
                    isPassed ? 'text-green-700' : 'text-gray-600'
                  )}
                >
                  {isPassed ? (
                    <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                  ) : (
                    <X className="h-3 w-3 text-gray-400 flex-shrink-0" />
                  )}
                  <span className={isPassed ? 'line-through' : ''}>{criterion.label}</span>
                  {criterion.id === 'longLength' && (
                    <span className="text-xs text-gray-500">(optional)</span>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Compact criteria summary */}
      {showCriteria && compact && (
        <div className="text-xs text-gray-600">
          {passedCriteria.length}/{passwordCriteria.filter(c => c.id !== 'longLength').length} requirements met
        </div>
      )}

      {/* Security tips */}
      {!meetsMinimum && !compact && (
        <div className="pt-2 border-t border-current/20">
          <div className="text-xs text-gray-600 space-y-1">
            <div className="font-medium">Tips for a stronger password:</div>
            {failedCriteria.includes('length') && <div>• Use at least 8 characters</div>}
            {failedCriteria.includes('uppercase') && <div>• Add uppercase letters (A-Z)</div>}
            {failedCriteria.includes('lowercase') && <div>• Add lowercase letters (a-z)</div>}
            {failedCriteria.includes('numbers') && <div>• Include numbers (0-9)</div>}
            {failedCriteria.includes('special') && <div>• Use special characters (!@#$%^&*)</div>}
            <div>• Avoid common words and patterns</div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Hook for password strength validation
 */
export const usePasswordStrength = (password: string, minScore = 60) => {
  const analysis = analyzePassword(password);
  
  return {
    score: analysis.score,
    strength: analysis.strength,
    isValid: analysis.score >= minScore,
    passedCriteria: analysis.passedCriteria,
    failedCriteria: analysis.failedCriteria,
    analysis
  };
};

/**
 * Compact version for inline use
 */
export const PasswordStrengthMeterCompact: React.FC<Omit<PasswordStrengthMeterProps, 'compact'>> = (props) => (
  <PasswordStrengthMeter {...props} compact showCriteria={false} />
);

export default PasswordStrengthMeter;