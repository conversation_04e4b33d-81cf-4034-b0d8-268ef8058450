/**
 * PasswordResetForm Component for FR-1 Enhanced Password Reset
 * 
 * This component handles the complete password reset workflow including
 * request reset, token validation, and password update with security features.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Eye, EyeOff, Mail, CheckCircle, AlertCircle, RefreshCw, Shield, Lock } from 'lucide-react';
import { cn } from '@/lib/utils';

import { Button } from '@/components/atoms/button';
import { Input } from '@/components/atoms/input';
import { Label } from '@/components/atoms/label';
import { Progress } from '@/components/atoms/progress';

import { useSecurityStore } from '@/stores/securityStore';
import { SecurityService } from '@/lib/api/endpoints/securityEnhanced';
import { extractTokenFromUrl, validateToken } from '../../utils/tokenSecurity';
import { handleSecurityError, isTokenExpiredError } from '../../utils/secureErrorHandling';
import { z } from 'zod';
import { 
  passwordResetRequestSchema,
  resetPasswordRequestSchema,
  type PasswordResetRequestType,
  type ResetPasswordRequestType
} from '../../validation/passwordResetSchemas';

export interface PasswordResetFormProps {
  /** Form mode */
  mode?: 'request' | 'reset';
  /** Initial email for request mode */
  email?: string;
  /** Auto-extract token from URL for reset mode */
  autoExtractToken?: boolean;
  /** Custom CSS classes */
  className?: string;
  /** Callback on successful password reset */
  onResetSuccess?: (result: any) => void;
  /** Callback on reset request success */
  onRequestSuccess?: (email: string) => void;
  /** Callback on error */
  onError?: (error: any) => void;
  /** Compact mode for smaller displays */
  compact?: boolean;
}

type RequestFormData = PasswordResetRequestType;
type ResetFormData = ResetPasswordRequestType;

/**
 * Calculate password strength score
 */
const calculatePasswordStrength = (password: string): number => {
  let score = 0;
  
  if (password.length >= 8) score += 20;
  if (password.length >= 12) score += 10;
  if (/[a-z]/.test(password)) score += 20;
  if (/[A-Z]/.test(password)) score += 20;
  if (/\d/.test(password)) score += 20;
  if (/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) score += 10;
  
  return Math.min(score, 100);
};

/**
 * Get password strength label and color
 */
const getPasswordStrengthInfo = (score: number) => {
  if (score < 30) return { label: 'Weak', color: 'bg-red-500' };
  if (score < 60) return { label: 'Fair', color: 'bg-yellow-500' };
  if (score < 80) return { label: 'Good', color: 'bg-blue-500' };
  return { label: 'Strong', color: 'bg-green-500' };
};

/**
 * PasswordResetForm handles password reset workflow
 */
export const PasswordResetForm: React.FC<PasswordResetFormProps> = ({
  mode = 'request',
  email: initialEmail,
  autoExtractToken = true,
  className,
  onResetSuccess,
  onRequestSuccess,
  onError,
  compact = false
}) => {
  const {
    passwordResetState,
    setResetEmailSent,
    clearPasswordResetState
  } = useSecurityStore();

  const [currentMode, setCurrentMode] = useState<'request' | 'reset'>(mode);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [extractedToken, setExtractedToken] = useState<string>('');

  // Request form
  const requestForm = useForm<RequestFormData>({
    resolver: zodResolver(passwordResetRequestSchema),
    defaultValues: {
      email: initialEmail || passwordResetState.sentToEmail || ''
    }
  });

  // Reset form with confirmation validation
  const resetForm = useForm<ResetFormData & { confirm_password: string }>({
    resolver: zodResolver(resetPasswordRequestSchema.extend({
      confirm_password: z.string().min(1, 'Please confirm your password')
    }).refine((data) => data.new_password === data.confirm_password, {
      message: "Passwords don't match",
      path: ["confirm_password"]
    })),
    defaultValues: {
      token: '',
      new_password: '',
      confirm_password: ''
    }
  });

  const watchedPassword = resetForm.watch('new_password');
  const passwordStrength = calculatePasswordStrength(watchedPassword || '');
  const strengthInfo = getPasswordStrengthInfo(passwordStrength);

  /**
   * Auto-extract token from URL and switch to reset mode
   */
  useEffect(() => {
    if (autoExtractToken && currentMode === 'request') {
      const urlToken = extractTokenFromUrl();
      if (urlToken) {
        setExtractedToken(urlToken);
        resetForm.setValue('token', urlToken);
        setCurrentMode('reset');
        
        // Validate token
        const validation = validateToken(urlToken);
        if (!validation.isValid) {
          setErrorMessage('Invalid reset token format');
        } else if (validation.isExpired) {
          setErrorMessage('Reset token has expired');
        }
      }
    }
  }, [autoExtractToken, currentMode, resetForm]);

  /**
   * Handle password reset request
   */
  const handleResetRequest = async (data: RequestFormData) => {
    setIsSubmitting(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      await SecurityService.requestPasswordReset(data.email);
      
      setResetEmailSent(data.email);
      setSuccessMessage(`Password reset email sent to ${data.email}`);
      
      onRequestSuccess?.(data.email);
      
    } catch (error) {
      const securityError = handleSecurityError(error, 'password reset request');
      setErrorMessage(securityError.message);
      onError?.(securityError);
    } finally {
      setIsSubmitting(false);
    }
  };

  /**
   * Handle password reset completion
   */
  const handlePasswordReset = async (data: ResetFormData & { confirm_password: string }) => {
    setIsSubmitting(true);
    setErrorMessage('');
    setSuccessMessage('');

    try {
      // Validate token first
      const validation = validateToken(data.token);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      // Only send required fields to API (exclude confirm_password)
      const resetData: ResetFormData = {
        token: data.token,
        new_password: data.new_password
      };

      const result = await SecurityService.resetPassword(resetData.token, resetData.new_password);
      
      clearPasswordResetState();
      setSuccessMessage('Password reset successfully! You can now log in with your new password.');
      
      onResetSuccess?.(result);
      
      // Clear form
      resetForm.reset();
      
    } catch (error) {
      const securityError = handleSecurityError(error, 'password reset');
      setErrorMessage(securityError.message);
      
      if (isTokenExpiredError(securityError)) {
        // Switch back to request mode for expired tokens
        setTimeout(() => {
          setCurrentMode('request');
          setErrorMessage('');
        }, 3000);
      }
      
      onError?.(securityError);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div 
      className={cn('space-y-4', compact && 'space-y-3', className)}
      data-testid="password-reset-form"
    >
      {/* Success Message */}
      {successMessage && (
        <div 
          className={cn(
            'flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md text-green-800',
            compact && 'p-2 text-sm'
          )}
          role="status"
          aria-live="polite"
          data-testid="reset-success"
        >
          <CheckCircle className={cn('h-4 w-4 text-green-600', compact && 'h-3 w-3')} />
          <span>{successMessage}</span>
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div 
          className={cn(
            'flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md text-red-800',
            compact && 'p-2 text-sm'
          )}
          role="alert"
          aria-live="polite"
          data-testid="reset-error"
        >
          <AlertCircle className={cn('h-4 w-4 text-red-600', compact && 'h-3 w-3')} />
          <span>{errorMessage}</span>
        </div>
      )}

      {currentMode === 'request' ? (
        /* Request Reset Form */
        <form onSubmit={requestForm.handleSubmit(handleResetRequest)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="reset-email" className={cn(compact && 'text-sm')}>
              Email Address
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="reset-email"
                type="email"
                placeholder="Enter your email address"
                {...requestForm.register('email')}
                className={cn('pl-10', compact && 'h-9 text-sm')}
                disabled={isSubmitting}
              />
            </div>
            {requestForm.formState.errors.email && (
              <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
                {requestForm.formState.errors.email.message}
              </p>
            )}
          </div>

          <Button
            type="submit"
            disabled={isSubmitting}
            className={cn('w-full', compact && 'h-9 text-sm')}
            data-testid="request-reset-button"
          >
            {isSubmitting && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
            {isSubmitting ? 'Sending Reset Email...' : 'Send Reset Email'}
          </Button>
        </form>
      ) : (
        /* Reset Password Form */
        <form onSubmit={resetForm.handleSubmit(handlePasswordReset)} className="space-y-4">
          {/* Token Field */}
          <div className="space-y-2">
            <Label htmlFor="reset-token" className={cn(compact && 'text-sm')}>
              Reset Token
            </Label>
            <Input
              id="reset-token"
              type="text"
              placeholder="Enter reset token from email"
              {...resetForm.register('token')}
              className={cn('font-mono', compact && 'h-9 text-sm')}
              disabled={isSubmitting}
              defaultValue={extractedToken}
            />
            {resetForm.formState.errors.token && (
              <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
                {resetForm.formState.errors.token.message}
              </p>
            )}
          </div>

          {/* New Password Field */}
          <div className="space-y-2">
            <Label htmlFor="new-password" className={cn(compact && 'text-sm')}>
              New Password
            </Label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="new-password"
                type={showPassword ? 'text' : 'password'}
                placeholder="Enter new password"
                {...resetForm.register('new_password')}
                className={cn('pl-10 pr-10', compact && 'h-9 text-sm')}
                disabled={isSubmitting}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
            
            {/* Password Strength Indicator */}
            {watchedPassword && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">Password strength:</span>
                  <span className={cn(
                    'font-medium',
                    strengthInfo.color === 'bg-red-500' && 'text-red-600',
                    strengthInfo.color === 'bg-yellow-500' && 'text-yellow-600',
                    strengthInfo.color === 'bg-blue-500' && 'text-blue-600',
                    strengthInfo.color === 'bg-green-500' && 'text-green-600'
                  )}>
                    {strengthInfo.label}
                  </span>
                </div>
                <Progress value={passwordStrength} className="h-1.5" />
              </div>
            )}
            
            {resetForm.formState.errors.new_password && (
              <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
                {resetForm.formState.errors.new_password.message}
              </p>
            )}
          </div>

          {/* Confirm Password Field */}
          <div className="space-y-2">
            <Label htmlFor="confirm-password" className={cn(compact && 'text-sm')}>
              Confirm New Password
            </Label>
            <div className="relative">
              <Shield className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="confirm-password"
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm new password"
                {...resetForm.register('confirm_password')}
                className={cn('pl-10 pr-10', compact && 'h-9 text-sm')}
                disabled={isSubmitting}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                tabIndex={-1}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
            {resetForm.formState.errors.confirm_password && (
              <p className={cn('text-sm text-red-600', compact && 'text-xs')} role="alert">
                {resetForm.formState.errors.confirm_password.message}
              </p>
            )}
          </div>

          <Button
            type="submit"
            disabled={isSubmitting || passwordStrength < 60}
            className={cn('w-full', compact && 'h-9 text-sm')}
            data-testid="reset-password-button"
          >
            {isSubmitting && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
            {isSubmitting ? 'Resetting Password...' : 'Reset Password'}
          </Button>
        </form>
      )}

      {/* Help Text */}
      {!compact && (
        <div className="text-sm text-gray-600 space-y-1">
          {currentMode === 'request' ? (
            <>
              <p>• Enter the email address associated with your account</p>
              <p>• We&apos;ll send you a secure reset link</p>
              <p>• Reset links expire after 1 hour</p>
            </>
          ) : (
            <>
              <p>• Use the token from your reset email</p>
              <p>• Choose a strong password (at least 8 characters)</p>
              <p>• Include uppercase, lowercase, and numbers</p>
            </>
          )}
        </div>
      )}

      {/* Mode Switch Button */}
      {!extractedToken && (
        <div className="pt-4 border-t">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => {
              setCurrentMode(currentMode === 'request' ? 'reset' : 'request');
              setErrorMessage('');
              setSuccessMessage('');
            }}
            className="w-full text-sm"
          >
            {currentMode === 'request' ? 'Already have a reset token?' : 'Need to request a reset?'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default PasswordResetForm;