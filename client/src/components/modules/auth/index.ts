/**
 * Auth Module Main Export Index
 * 
 * This file provides the main interface for importing all FR-1 security enhanced
 * authentication functionality including components, utilities, stores, and types.
 */

// Core Components
export * from './components';

// Types
export type * from './types/emailVerification';
export type * from './types/passwordReset';
export type * from './types/security';

// Validation Schemas
export {
  emailVerificationFormSchema,
  resendVerificationRequestSchema
} from './validation/emailVerificationSchemas';

export {
  passwordResetRequestSchema,
  passwordResetTokenSchema
} from './validation/passwordResetSchemas';

export {
  loginRequestEnhancedSchema,
  registrationRequestEnhancedSchema,
  accountLockoutInfoSchema
} from './validation/securitySchemas';

// Store
export { useSecurityStore } from '@/stores/securityStore';

// Utilities
export {
  parseSecurityError,
  getSecurityErrorMessage,
  isAccountLockoutError,
  isEmailVerificationError,
  isTokenExpiredError,
  formatLockoutMessage,
  handleSecurityError
} from './utils/secureErrorHandling';

export {
  validateToken,
  extractTokenFromUrl,
  sanitizeTokenForLogging,
  formatRemainingTime,
  isTokenCloseToExpiring
} from './utils/tokenSecurity';

// API Service
export { SecurityService } from '@/lib/api/endpoints/securityEnhanced';