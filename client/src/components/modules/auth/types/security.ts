/**
 * Security Types for FR-1 Security Features
 * 
 * This module defines TypeScript types and interfaces for account lockout protection
 * and general security features.
 */

/**
 * Account lockout information from backend
 */
export interface AccountLockoutInfo {
  /** Number of failed login attempts */
  failed_attempts: number;
  /** Number of attempts remaining before lockout */
  attempts_remaining: number;
  /** Timestamp when account will be unlocked (if locked) */
  locked_until?: string | null;
}

/**
 * Account lockout state for store management
 */
export interface AccountLockoutState {
  /** Whether account is currently locked */
  isLocked: boolean;
  /** When the lockout expires */
  lockoutExpiresAt: Date | null;
  /** Current number of failed attempts */
  failedAttempts: number;
  /** Number of attempts remaining */
  attemptsRemaining: number;
}

/**
 * Enhanced login response with lockout information
 */
export interface LoginResponseEnhanced {
  /** JWT access token (if successful) */
  access_token?: string;
  /** Token type (typically 'Bearer') */
  token_type?: string;
  /** User information (if successful) */
  user?: {
    id: number;
    email: string;
    name: string;
    is_active: boolean;
    is_email_verified: boolean;
  };
  /** Error or success message */
  message?: string;
  /** Lockout information (if applicable) */
  lockout_info?: AccountLockoutInfo;
}

/**
 * Enhanced registration response with email verification info
 */
export interface RegistrationResponseEnhanced {
  /** Created user information */
  user: {
    id: number;
    email: string;
    name: string;
    is_active: boolean;
    is_email_verified: boolean;
  };
  /** Success message */
  message: string;
  /** Whether email verification is required */
  email_verification_required: boolean;
  /** Whether verification email was sent */
  verification_email_sent: boolean;
}

/**
 * Lockout warning banner component props
 */
export interface LockoutWarningBannerProps {
  /** Number of attempts remaining */
  attemptsRemaining: number;
  /** Callback for password reset link click */
  onPasswordResetClick: () => void;
  /** Whether to show the banner */
  show: boolean;
  /** Callback for banner dismissal */
  onDismiss?: () => void;
}

/**
 * Account locked message component props
 */
export interface AccountLockedMessageProps {
  /** When the lockout expires */
  lockoutExpiresAt: Date;
  /** Callback for password reset link click */
  onPasswordResetClick: () => void;
  /** Whether to show countdown timer */
  showTimer?: boolean;
}

/**
 * Lockout timer component props
 */
export interface LockoutTimerProps {
  /** Lockout information from the store or API */
  lockoutInfo?: AccountLockoutInfo;
  /** Custom CSS classes */
  className?: string;
  /** Show/hide the shield icon */
  showIcon?: boolean;
  /** Compact mode for smaller displays */
  compact?: boolean;
  /** Callback when lockout expires */
  onLockoutExpired?: () => void;
  /** Custom expiration message */
  expirationMessage?: string;
}

/**
 * Security event types for logging
 */
export type SecurityEventType = 
  | 'LOGIN_ATTEMPT'
  | 'LOGIN_SUCCESS'
  | 'LOGIN_FAILURE'
  | 'ACCOUNT_LOCKED'
  | 'ACCOUNT_UNLOCKED'
  | 'EMAIL_VERIFICATION_SENT'
  | 'EMAIL_VERIFIED'
  | 'PASSWORD_RESET_REQUESTED'
  | 'PASSWORD_RESET_COMPLETED';

/**
 * Security event for audit logging
 */
export interface SecurityEvent {
  /** Type of security event */
  type: SecurityEventType;
  /** Timestamp of event */
  timestamp: Date;
  /** User ID (if applicable) */
  userId?: number;
  /** Email address involved */
  email?: string;
  /** Additional event metadata */
  metadata?: Record<string, any>;
}

/**
 * Security configuration constants
 */
export interface SecurityConfig {
  /** Maximum failed login attempts before lockout */
  maxFailedAttempts: number;
  /** Lockout duration in minutes */
  lockoutDurationMinutes: number;
  /** Email verification token expiration hours */
  emailVerificationExpirationHours: number;
  /** Password reset token expiration hours */
  passwordResetExpirationHours: number;
  /** Minimum time between resend requests in seconds */
  resendCooldownSeconds: number;
}

/**
 * Common security error codes
 */
export type SecurityErrorCode = 
  | 'ACCOUNT_LOCKED'
  | 'ACCOUNT_INACTIVE'
  | 'EMAIL_NOT_VERIFIED'
  | 'INVALID_CREDENTIALS'
  | 'TOKEN_EXPIRED'
  | 'TOKEN_INVALID'
  | 'RATE_LIMITED'
  | 'SECURITY_VIOLATION';