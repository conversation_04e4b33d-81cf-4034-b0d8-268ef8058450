/**
 * Email Verification Types for FR-1 Security Features
 * 
 * This module defines TypeScript types and interfaces for the email verification workflow,
 * including verification requests, responses, and component props.
 */

/**
 * Email verification token request interface
 */
export interface EmailVerificationRequest {
  /** Verification token received via email */
  token: string;
}

/**
 * Email verification response from backend
 */
export interface EmailVerificationResponse {
  /** Whether verification was successful */
  success: boolean;
  /** Human-readable message about verification result */
  message: string;
  /** Whether the user account was successfully activated */
  user_activated: boolean;
}

/**
 * Request to resend verification email
 */
export interface ResendVerificationRequest {
  /** Email address to send verification to */
  email: string;
}

/**
 * Response for resend verification request
 */
export interface ResendVerificationResponse {
  /** Whether resend request was successful */
  success: boolean;
  /** Human-readable message about resend result */
  message: string;
  /** Whether the verification email was actually sent */
  email_sent: boolean;
  /** When resend will be available again (ISO timestamp) */
  resend_available_at?: string;
}

/**
 * Email verification page component props
 */
export interface EmailVerificationPageProps {
  /** Verification token from URL parameter */
  token: string;
  /** Callback for successful verification */
  onSuccess: () => void;
  /** Callback for verification error */
  onError: (error: string) => void;
}

/**
 * Email sent notification component props
 */
export interface EmailSentNotificationProps {
  /** Email address verification was sent to */
  email: string;
  /** Callback for resend button click */
  onResendClick: () => void;
  /** Whether resend is currently available */
  canResend: boolean;
  /** Countdown seconds until resend is available */
  resendCountdown?: number;
}

/**
 * Verification failed page component props
 */
export interface VerificationFailedPageProps {
  /** Error message to display */
  error: string;
  /** Callback for resend verification request */
  onResendClick: () => void;
  /** Whether to show resend option */
  showResendOption: boolean;
}

/**
 * Resend verification form component props
 */
export interface ResendVerificationFormProps {
  /** Initial email value */
  email?: string;
  /** Callback for successful resend */
  onResendSuccess: (email: string) => void;
  /** Callback for resend error */
  onResendError: (error: string) => void;
}

/**
 * Email verification state for store management
 */
export interface EmailVerificationState {
  /** Whether verification email has been sent */
  isVerificationSent: boolean;
  /** Email address verification was sent to */
  sentToEmail: string | null;
  /** Whether user can request resend */
  canResend: boolean;
  /** Timestamp when resend will be available */
  resendAvailableAt: Date | null;
}

/**
 * Email verification error types
 */
export type EmailVerificationErrorCode = 
  | 'TOKEN_EXPIRED'
  | 'INVALID_TOKEN'
  | 'TOKEN_NOT_FOUND'
  | 'EMAIL_ALREADY_VERIFIED'
  | 'VERIFICATION_FAILED'
  | 'RATE_LIMITED';