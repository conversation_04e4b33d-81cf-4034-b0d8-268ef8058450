/**
 * Password Reset Types for FR-1 Security Features
 * 
 * This module defines TypeScript types and interfaces for the password reset workflow,
 * including reset requests, responses, and component props.
 */

/**
 * Password reset request interface
 */
export interface PasswordResetRequest {
  /** Email address to send reset link to */
  email: string;
}

/**
 * Password reset response from backend
 */
export interface PasswordResetResponse {
  /** Whether reset request was successful */
  success: boolean;
  /** Human-readable message about reset result */
  message: string;
  /** Whether the reset email was actually sent */
  reset_email_sent: boolean;
}

/**
 * Reset password with token request
 */
export interface ResetPasswordRequest {
  /** Reset token from email link */
  token: string;
  /** New password to set */
  new_password: string;
}

/**
 * Reset password response from backend
 */
export interface ResetPasswordResponse {
  /** Whether password reset was successful */
  success: boolean;
  /** Human-readable message about reset result */
  message: string;
  /** Whether the password was actually updated */
  password_updated: boolean;
}

/**
 * Forgot password form component props
 */
export interface ForgotPasswordFormProps {
  /** Callback for successful reset request */
  onResetRequested: (email: string) => void;
  /** Callback for reset request error */
  onResetError: (error: string) => void;
}

/**
 * Reset email sent page component props
 */
export interface ResetEmailSentPageProps {
  /** Email address reset was sent to */
  email: string;
  /** Callback for resend request */
  onResendClick: () => void;
  /** Whether resend is currently available */
  canResend: boolean;
  /** Countdown seconds until resend is available */
  resendCountdown?: number;
}

/**
 * New password form component props
 */
export interface NewPasswordFormProps {
  /** Reset token from URL parameter */
  token: string;
  /** Callback for successful password reset */
  onSuccess: () => void;
  /** Callback for reset error */
  onError: (error: string) => void;
}

/**
 * Reset failed page component props
 */
export interface ResetFailedPageProps {
  /** Error message to display */
  error: string;
  /** Callback for requesting new reset link */
  onRequestNewLink: () => void;
  /** Whether to show request new link option */
  showRequestNewOption: boolean;
}

/**
 * Password reset state for store management
 */
export interface PasswordResetState {
  /** Whether reset email has been sent */
  isResetSent: boolean;
  /** Email address reset was sent to */
  sentToEmail: string | null;
  /** Current reset token (if any) */
  resetToken: string | null;
  /** When reset token expires */
  resetExpiresAt: Date | null;
}

/**
 * Password strength levels
 */
export type PasswordStrength = 'weak' | 'fair' | 'good' | 'strong' | 'very-strong';

/**
 * Password strength validation result
 */
export interface PasswordStrengthResult {
  /** Overall strength level */
  strength: PasswordStrength;
  /** Score from 0-100 */
  score: number;
  /** Individual requirement checks */
  checks: {
    length: boolean;
    uppercase: boolean;
    lowercase: boolean;
    number: boolean;
    special: boolean;
  };
  /** Suggestions for improvement */
  suggestions: string[];
}

/**
 * Password reset error types
 */
export type PasswordResetErrorCode = 
  | 'TOKEN_EXPIRED'
  | 'INVALID_TOKEN'
  | 'TOKEN_NOT_FOUND'
  | 'EMAIL_NOT_FOUND'
  | 'RESET_FAILED'
  | 'RATE_LIMITED'
  | 'PASSWORD_TOO_WEAK';