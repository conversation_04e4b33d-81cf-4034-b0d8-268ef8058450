/**
 * Email Verification Validation Schemas for FR-1 Security Features
 * 
 * This module provides Zod schemas for validating email verification requests,
 * responses, and form data with security-focused validation rules.
 */

import { z } from 'zod';

/**
 * Email verification token validation schema
 * Ensures tokens meet security requirements for format and length
 */
export const emailVerificationTokenSchema = z.string()
  .min(32, 'Invalid verification token format')
  .max(128, 'Invalid verification token format')
  .regex(/^[A-Za-z0-9_-]+$/, 'Invalid verification token characters')
  .describe('Email verification token from URL parameter');

/**
 * Email verification request schema
 */
export const emailVerificationRequestSchema = z.object({
  token: emailVerificationTokenSchema
});

/**
 * Email verification response schema
 */
export const emailVerificationResponseSchema = z.object({
  success: z.boolean().describe('Whether verification was successful'),
  message: z.string().min(1, 'Message is required').describe('Human-readable result message'),
  user_activated: z.boolean().describe('Whether user account was activated')
});

/**
 * Resend verification email schema
 */
export const resendVerificationRequestSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long')
    .refine((email) => !email.includes('<script>'), 'Invalid email format')
    .describe('Email address to send verification to')
});

/**
 * Resend verification response schema
 */
export const resendVerificationResponseSchema = z.object({
  success: z.boolean().describe('Whether resend request was successful'),
  message: z.string().min(1, 'Message is required').describe('Human-readable result message'),
  email_sent: z.boolean().describe('Whether verification email was actually sent')
});

/**
 * Email verification form data schema
 * Used for forms that collect email for resend functionality
 */
export const emailVerificationFormSchema = z.object({
  token: z.string()
    .min(1, 'Verification token is required')
    .describe('Email verification token'),
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long')
    .transform(email => email.toLowerCase().trim())
    .optional()
    .describe('Email address for verification')
});

/**
 * Email verification error response schema
 */
export const emailVerificationErrorSchema = z.object({
  success: z.literal(false),
  message: z.string().min(1, 'Error message is required'),
  code: z.enum([
    'TOKEN_EXPIRED',
    'INVALID_TOKEN',
    'TOKEN_NOT_FOUND',
    'EMAIL_ALREADY_VERIFIED',
    'VERIFICATION_FAILED',
    'RATE_LIMITED'
  ]).optional().describe('Machine-readable error code')
});

/**
 * Combined email verification response schema (success or error)
 */
export const emailVerificationCombinedResponseSchema = z.union([
  emailVerificationResponseSchema,
  emailVerificationErrorSchema
]);

/**
 * Type exports derived from schemas
 */
export type EmailVerificationTokenType = z.infer<typeof emailVerificationTokenSchema>;
export type EmailVerificationRequestType = z.infer<typeof emailVerificationRequestSchema>;
export type EmailVerificationResponseType = z.infer<typeof emailVerificationResponseSchema>;
export type ResendVerificationRequestType = z.infer<typeof resendVerificationRequestSchema>;
export type ResendVerificationResponseType = z.infer<typeof resendVerificationResponseSchema>;
export type EmailVerificationFormType = z.infer<typeof emailVerificationFormSchema>;
export type EmailVerificationErrorType = z.infer<typeof emailVerificationErrorSchema>;