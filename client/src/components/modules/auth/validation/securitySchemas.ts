/**
 * Security Validation Schemas for FR-1 Security Features
 * 
 * This module provides Zod schemas for validating security-related data,
 * including account lockout information and enhanced authentication responses.
 */

import { z } from 'zod';

/**
 * Account lockout information schema
 */
export const accountLockoutInfoSchema = z.object({
  failed_attempts: z.number().int().min(0).describe('Number of failed login attempts'),
  attempts_remaining: z.number().int().min(0).describe('Attempts remaining before lockout'),
  locked_until: z.string().datetime().optional().describe('ISO timestamp when lockout expires')
});

/**
 * Enhanced login response schema with lockout information
 */
export const loginResponseEnhancedSchema = z.object({
  access_token: z.string().optional().describe('JWT access token if login successful'),
  token_type: z.string().optional().describe('Token type (typically Bearer)'),
  user: z.object({
    id: z.number().int().positive(),
    email: z.string().email(),
    name: z.string().min(1),
    is_active: z.boolean(),
    is_email_verified: z.boolean()
  }).optional().describe('User information if login successful'),
  message: z.string().optional().describe('Success or error message'),
  lockout_info: accountLockoutInfoSchema.optional().describe('Account lockout details if applicable')
});

/**
 * Enhanced registration response schema with email verification info
 */
export const registrationResponseEnhancedSchema = z.object({
  user: z.object({
    id: z.number().int().positive(),
    email: z.string().email(),
    name: z.string().min(1),
    is_active: z.boolean(),
    is_email_verified: z.boolean()
  }).describe('Created user information'),
  message: z.string().min(1).describe('Success message'),
  email_verification_required: z.boolean().describe('Whether email verification is required'),
  verification_email_sent: z.boolean().describe('Whether verification email was sent')
});

/**
 * Login request schema with enhanced validation
 */
export const loginRequestEnhancedSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long')
    .transform(email => email.toLowerCase().trim()),
  password: z.string()
    .min(1, 'Password is required')
    .max(128, 'Password is too long')
});

/**
 * Registration request schema with enhanced validation
 */
export const registrationRequestEnhancedSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long')
    .transform(email => email.toLowerCase().trim()),
  password: z.string()
    .min(8, 'Password must be at least 8 characters long')
    .max(128, 'Password is too long')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/\d/, 'Password must contain at least one number'),
  name: z.string()
    .min(1, 'Name is required')
    .max(100, 'Name is too long')
    .trim(),
  confirm_password: z.string().min(1, 'Please confirm your password')
})
.refine((data) => data.password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"]
});

/**
 * Security event schema for audit logging
 */
export const securityEventSchema = z.object({
  type: z.enum([
    'LOGIN_ATTEMPT',
    'LOGIN_SUCCESS',
    'LOGIN_FAILURE',
    'ACCOUNT_LOCKED',
    'ACCOUNT_UNLOCKED',
    'EMAIL_VERIFICATION_SENT',
    'EMAIL_VERIFIED',
    'PASSWORD_RESET_REQUESTED',
    'PASSWORD_RESET_COMPLETED'
  ]).describe('Type of security event'),
  timestamp: z.date().describe('When the event occurred'),
  userId: z.number().int().positive().optional().describe('User ID if applicable'),
  email: z.string().email().optional().describe('Email address involved'),
  metadata: z.record(z.string(), z.any()).optional().describe('Additional event data')
});

/**
 * Security configuration schema
 */
export const securityConfigSchema = z.object({
  maxFailedAttempts: z.number().int().min(1).max(10).default(5)
    .describe('Maximum failed attempts before lockout'),
  lockoutDurationMinutes: z.number().int().min(1).max(1440).default(30)
    .describe('Lockout duration in minutes'),
  emailVerificationExpirationHours: z.number().int().min(1).max(168).default(24)
    .describe('Email verification token expiration in hours'),
  passwordResetExpirationHours: z.number().int().min(1).max(24).default(1)
    .describe('Password reset token expiration in hours'),
  resendCooldownSeconds: z.number().int().min(30).max(300).default(60)
    .describe('Minimum time between resend requests in seconds')
});

/**
 * Security error response schema
 */
export const securityErrorSchema = z.object({
  message: z.string().min(1, 'Error message is required'),
  code: z.enum([
    'ACCOUNT_LOCKED',
    'ACCOUNT_INACTIVE',
    'EMAIL_NOT_VERIFIED',
    'INVALID_CREDENTIALS',
    'TOKEN_EXPIRED',
    'TOKEN_INVALID',
    'RATE_LIMITED',
    'SECURITY_VIOLATION'
  ]).describe('Machine-readable error code'),
  lockout_info: accountLockoutInfoSchema.optional().describe('Lockout details if applicable')
});

/**
 * Lockout timer data schema
 */
export const lockoutTimerDataSchema = z.object({
  expiresAt: z.date().describe('When the lockout expires'),
  remainingTime: z.number().int().min(0).describe('Remaining time in seconds'),
  isExpired: z.boolean().describe('Whether the lockout has expired')
});

/**
 * Type exports derived from schemas
 */
export type AccountLockoutInfoType = z.infer<typeof accountLockoutInfoSchema>;
export type LoginResponseEnhancedType = z.infer<typeof loginResponseEnhancedSchema>;
export type RegistrationResponseEnhancedType = z.infer<typeof registrationResponseEnhancedSchema>;
export type LoginRequestEnhancedType = z.infer<typeof loginRequestEnhancedSchema>;
export type RegistrationRequestEnhancedType = z.infer<typeof registrationRequestEnhancedSchema>;
export type SecurityEventType = z.infer<typeof securityEventSchema>;
export type SecurityConfigType = z.infer<typeof securityConfigSchema>;
export type SecurityErrorType = z.infer<typeof securityErrorSchema>;
export type LockoutTimerDataType = z.infer<typeof lockoutTimerDataSchema>;