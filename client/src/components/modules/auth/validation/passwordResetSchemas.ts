/**
 * Password Reset Validation Schemas for FR-1 Security Features
 * 
 * This module provides Zod schemas for validating password reset requests,
 * responses, and form data with comprehensive password security requirements.
 */

import { z } from 'zod';

/**
 * Password reset token validation schema
 * Ensures tokens meet security requirements for format and length
 */
export const passwordResetTokenSchema = z.string()
  .min(32, 'Invalid reset token format')
  .max(128, 'Invalid reset token format')
  .regex(/^[A-Za-z0-9_-]+$/, 'Invalid reset token characters')
  .describe('Password reset token from email link');

/**
 * Password strength validation schema
 * Implements comprehensive password security requirements
 */
export const passwordStrengthSchema = z.string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password is too long (maximum 128 characters)')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/\d/, 'Password must contain at least one number')
  .regex(/[^a-zA-Z\d]/, 'Password must contain at least one special character')
  .refine((password) => {
    // Check for common weak passwords
    const commonPasswords = [
      'password', 'password123', '123456', '12345678', 'qwerty', 'abc123',
      'letmein', 'welcome', 'admin', 'password1', '123456789', 'Password123'
    ];
    return !commonPasswords.includes(password.toLowerCase());
  }, 'Please choose a stronger, less common password')
  .describe('Strong password meeting security requirements');

/**
 * Password reset request schema
 */
export const passwordResetRequestSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long')
    .transform(email => email.toLowerCase().trim())
    .describe('Email address to send reset link to')
});

/**
 * Password reset response schema
 */
export const passwordResetResponseSchema = z.object({
  success: z.boolean().describe('Whether reset request was successful'),
  message: z.string().min(1, 'Message is required').describe('Human-readable result message'),
  reset_email_sent: z.boolean().describe('Whether reset email was actually sent')
});

/**
 * Reset password with token schema
 */
export const resetPasswordRequestSchema = z.object({
  token: passwordResetTokenSchema,
  new_password: passwordStrengthSchema
});

/**
 * Reset password response schema
 */
export const resetPasswordResponseSchema = z.object({
  success: z.boolean().describe('Whether password reset was successful'),
  message: z.string().min(1, 'Message is required').describe('Human-readable result message'),
  password_updated: z.boolean().describe('Whether password was actually updated')
});

/**
 * New password form schema with confirmation
 */
export const newPasswordFormSchema = z.object({
  password: passwordStrengthSchema,
  confirmPassword: z.string().min(1, 'Please confirm your password')
})
.refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})
.describe('New password form with confirmation matching');

/**
 * Forgot password form schema
 */
export const forgotPasswordFormSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .max(254, 'Email address is too long')
    .transform(email => email.toLowerCase().trim())
    .describe('Email address for password reset')
});

/**
 * Password reset error response schema
 */
export const passwordResetErrorSchema = z.object({
  success: z.literal(false),
  message: z.string().min(1, 'Error message is required'),
  code: z.enum([
    'TOKEN_EXPIRED',
    'INVALID_TOKEN',
    'TOKEN_NOT_FOUND',
    'EMAIL_NOT_FOUND',
    'RESET_FAILED',
    'RATE_LIMITED',
    'PASSWORD_TOO_WEAK'
  ]).optional().describe('Machine-readable error code')
});

/**
 * Combined password reset response schema (success or error)
 */
export const passwordResetCombinedResponseSchema = z.union([
  passwordResetResponseSchema,
  passwordResetErrorSchema
]);

/**
 * Password strength analysis result schema
 */
export const passwordStrengthResultSchema = z.object({
  strength: z.enum(['weak', 'fair', 'good', 'strong', 'very-strong']).describe('Overall strength level'),
  score: z.number().min(0).max(100).describe('Strength score from 0-100'),
  checks: z.object({
    length: z.boolean().describe('Meets minimum length requirement'),
    uppercase: z.boolean().describe('Contains uppercase letter'),
    lowercase: z.boolean().describe('Contains lowercase letter'),
    number: z.boolean().describe('Contains number'),
    special: z.boolean().describe('Contains special character')
  }).describe('Individual requirement checks'),
  suggestions: z.array(z.string()).describe('Suggestions for improvement')
});

/**
 * Type exports derived from schemas
 */
export type PasswordResetTokenType = z.infer<typeof passwordResetTokenSchema>;
export type PasswordStrengthType = z.infer<typeof passwordStrengthSchema>;
export type PasswordResetRequestType = z.infer<typeof passwordResetRequestSchema>;
export type PasswordResetResponseType = z.infer<typeof passwordResetResponseSchema>;
export type ResetPasswordRequestType = z.infer<typeof resetPasswordRequestSchema>;
export type ResetPasswordResponseType = z.infer<typeof resetPasswordResponseSchema>;
export type NewPasswordFormType = z.infer<typeof newPasswordFormSchema>;
export type ForgotPasswordFormType = z.infer<typeof forgotPasswordFormSchema>;
export type PasswordResetErrorType = z.infer<typeof passwordResetErrorSchema>;
export type PasswordStrengthResultType = z.infer<typeof passwordStrengthResultSchema>;