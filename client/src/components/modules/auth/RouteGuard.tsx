"use client"

import React from "react"

import { usePathname, useRouter } from "next/navigation"

import { useAuth } from "@/hooks/useAuth"

interface RouteGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  fallback?: React.ReactNode
}

export function RouteGuard({
  children,
  requireAuth = false,
  requireAdmin = false,
  fallback = null,
}: RouteGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { isAuthenticated, user, isLoading } = useAuth() as any

  React.useEffect(() => {
    if (isLoading) return

    if (requireAuth && !isAuthenticated) {
      router.push("/login")
      return
    }

    if (requireAdmin && !user?.is_superuser) {
      // Redirect non-admins away from admin routes
      router.push("/dashboard")
      return
    }
  }, [
    isAuthenticated,
    isLoading,
    requireAuth,
    requireAdmin,
    router,
    user,
    pathname,
  ])

  if (requireAuth && !isAuthenticated) {
    return <>{fallback}</>
  }

  if (requireAdmin && !user?.is_superuser) {
    return <>{fallback}</>
  }

  return <>{children}</>
}
