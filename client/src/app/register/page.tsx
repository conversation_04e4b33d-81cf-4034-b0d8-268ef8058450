/**
 * Registration Page Route
 *
 * This page renders the RegisterPage template component for user registration.
 * It provides a complete registration interface with form validation, error handling,
 * and navigation to the dashboard upon successful account creation.
 */

"use client"

import { useAuthStore } from "@/stores/authStore"
import { useRouter } from "next/navigation"

import { RegisterPage } from "@/components/templates/register-page"

export default function Register() {
  const router = useRouter()
  const setAuth = useAuthStore((state) => state.setAuth)

  const handleRegistrationSuccess = (response: any) => {
    // If registration includes immediate login (tokens), update auth store
    if (response.access_token) {
      setAuth(response.user, response.access_token)
      // Navigate to dashboard
      router.push("/")
    } else {
      // If registration doesn't include immediate login, navigate to login page
      router.push("/login")
    }
  }

  const handleRegistrationError = (error: Error) => {
    console.error("Registration error:", error)
    // Error handling is managed by the RegistrationForm component
  }

  const handleSignIn = () => {
    // Navigate to login page
    router.push("/login")
  }

  return (
    <RegisterPage
      onSuccess={handleRegistrationSuccess}
      onError={handleRegistrationError}
      onSignIn={handleSignIn}
      showProfessionalFields={true}
    />
  )
}
