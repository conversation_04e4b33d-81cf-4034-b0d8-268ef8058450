"use client"

/**
 * Role Management Page
 *
 * This page provides administrators with the ability to manage roles,
 * permissions, and user role assignments in the RBAC system.
 */
// import { useState } from "react"
import type { Permission } from "@/lib/api/types/authorization"

import { AlertCircle, Edit, Plus, Shield, Users } from "lucide-react"

import { usePermissions, useRoles } from "@/hooks/api/useAuthorization"
import { useHasPermission } from "@/hooks/useHasPermission"

import { Badge } from "@/components/atoms/badge-alt"
import { Button } from "@/components/atoms/button"
import { Skeleton } from "@/components/atoms/skeleton"
import { Alert, AlertDescription } from "@/components/molecules/alert"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/molecules/card"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/templates/tabs"

/**
 * Role Management Page Component
 */
export default function RoleManagementPage() {
  const { hasPermission: canManageRoles, isLoading: permissionLoading } =
    useHasPermission({
      resource: "role",
      action: "manage",
    })

  // If permission check is still loading
  if (permissionLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    )
  }

  // If user doesn't have permission
  if (!canManageRoles) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            You don&apos;t have permission to manage roles and permissions.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-6">
        {/* Page Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Role Management</h1>
          <p className="text-muted-foreground">
            Manage roles, permissions, and user access control.
          </p>
        </div>

        {/* Main Content */}
        <Tabs defaultValue="roles" className="space-y-4">
          <TabsList>
            <TabsTrigger value="roles">Roles</TabsTrigger>
            <TabsTrigger value="permissions">Permissions</TabsTrigger>
            <TabsTrigger value="assignments">User Assignments</TabsTrigger>
          </TabsList>

          <TabsContent value="roles" className="space-y-4">
            <RolesTab />
          </TabsContent>

          <TabsContent value="permissions" className="space-y-4">
            <PermissionsTab />
          </TabsContent>

          <TabsContent value="assignments" className="space-y-4">
            <UserAssignmentsTab />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

/**
 * Roles Tab Component
 */
function RolesTab() {
  // const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const { data: roles, isLoading, error } = useRoles()
  // const { data: permissions } = usePermissions()

  if (isLoading) {
    return <div>Loading roles...</div>
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load roles. Please try again.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Roles</h2>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Create Role
        </Button>
      </div>

      {/* Roles Grid */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {roles?.map((role) => (
          <Card
            key={role.id}
            className="cursor-pointer transition-shadow hover:shadow-md"
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{role.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {role.description || "No description"}
                  </CardDescription>
                </div>
                <div className="flex space-x-1">
                  {role.is_system_role && (
                    <Badge variant="secondary">System</Badge>
                  )}
                  <Badge variant={role.is_active ? "default" : "destructive"}>
                    {role.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="text-muted-foreground text-sm">
                  Priority: {role.priority}
                </div>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => console.log("Edit role:", role.id)}
                  >
                    <Edit className="mr-1 h-4 w-4" />
                    Edit
                  </Button>
                  <Button variant="outline" size="sm">
                    <Users className="mr-1 h-4 w-4" />
                    Users
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {roles?.length === 0 && (
        <Card className="p-6 text-center">
          <Shield className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
          <h3 className="mb-2 text-lg font-semibold">No roles found</h3>
          <p className="text-muted-foreground mb-4">
            Get started by creating your first role.
          </p>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Role
          </Button>
        </Card>
      )}
    </div>
  )
}

/**
 * Permissions Tab Component
 */
function PermissionsTab() {
  const { data: permissions, isLoading, error } = usePermissions()

  if (isLoading) {
    return <div>Loading permissions...</div>
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load permissions. Please try again.
        </AlertDescription>
      </Alert>
    )
  }

  // Group permissions by resource
  const groupedPermissions =
    permissions?.reduce(
      (acc, permission) => {
        if (!acc[permission.resource]) {
          acc[permission.resource] = []
        }
        acc[permission.resource].push(permission)
        return acc
      },
      {} as Record<string, Permission[]>
    ) || {}

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Permissions</h2>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Create Permission
        </Button>
      </div>

      {/* Permissions by Resource */}
      <div className="space-y-4">
        {Object.entries(groupedPermissions).map(([resource, perms]) => (
          <Card key={resource}>
            <CardHeader>
              <CardTitle className="capitalize">
                {resource} Permissions
              </CardTitle>
              <CardDescription>
                Permissions available for {resource} resources
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-2 md:grid-cols-3 lg:grid-cols-4">
                {perms.map((permission) => (
                  <div
                    key={permission.id}
                    className="flex items-center space-x-2 rounded border p-2"
                  >
                    <Badge variant="outline" className="text-xs">
                      {permission.action}
                    </Badge>
                    <span className="text-sm font-medium">
                      {permission.name}
                    </span>
                    {permission.is_system_permission && (
                      <Badge variant="secondary" className="text-xs">
                        System
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {permissions?.length === 0 && (
        <Card className="p-6 text-center">
          <Shield className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
          <h3 className="mb-2 text-lg font-semibold">No permissions found</h3>
          <p className="text-muted-foreground mb-4">
            Get started by creating your first permission.
          </p>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Create Permission
          </Button>
        </Card>
      )}
    </div>
  )
}

/**
 * User Assignments Tab Component
 */
function UserAssignmentsTab() {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">User Role Assignments</h2>
        <Button>
          <Users className="mr-2 h-4 w-4" />
          Assign Role
        </Button>
      </div>

      <Card className="p-6 text-center">
        <Users className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
        <h3 className="mb-2 text-lg font-semibold">
          User assignments coming soon
        </h3>
        <p className="text-muted-foreground">
          This feature will allow you to assign roles to users and manage their
          permissions.
        </p>
      </Card>
    </div>
  )
}
