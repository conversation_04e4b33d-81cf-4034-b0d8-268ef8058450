/**
 * Login Page Route
 *
 * This page renders the LoginPage template component for user authentication.
 * It provides a complete login interface with form validation, error handling,
 * and navigation to the dashboard upon successful authentication.
 */

"use client"

import { useAuthStore } from "@/stores/authStore"
import { useRouter } from "next/navigation"

import { LoginPage } from "@/components/templates/login-page"

export default function Login() {
  const router = useRouter()
  const setAuth = useAuthStore((state) => state.setAuth)

  const handleLoginSuccess = (response: any) => {
    // Update auth store with user data and token
    setAuth(response.user, response.access_token)

    // Navigate to dashboard
    router.push("/")
  }

  const handleLoginError = (error: Error) => {
    console.error("Login error:", error)
    // Error handling is managed by the LoginForm component
  }

  const handleForgotPassword = () => {
    // Navigate to forgot password page (to be implemented)
    router.push("/forgot-password")
  }

  const handleSignUp = () => {
    // Navigate to registration page
    router.push("/register")
  }

  return (
    <LoginPage
      onSuccess={handleLoginSuccess}
      onError={handleLoginError}
      onForgotPassword={handleForgotPassword}
      onSignUp={handleSignUp}
    />
  )
}
