"use client"

import * as React from "react"

import { CacheProvider } from "@/core/caching/cache_provider"
import { ThemeProvider } from "@/providers/theme-provider"
import { ActiveThemeProvider } from "@/utils/active-theme"

import { LayoutProvider } from "@/hooks/useLayout"

import { SidebarProvider } from "@/components/organisms/sidebar"

export function Providers({ children }: { children: React.ReactNode }) {
  // Start MSW browser worker during E2E runs only
  React.useEffect(() => {
    if (process.env.NEXT_PUBLIC_E2E === "1") {
      import("@tests/mocks/browser")
        .then(({ startMockWorker }) => startMockWorker())
        .catch((err) => {
          console.warn("MSW browser worker failed to start:", err)
        })
    }
  }, [])

  return (
    <ThemeProvider>
      <LayoutProvider>
        <ActiveThemeProvider>
          <CacheProvider>
            <SidebarProvider>{children}</SidebarProvider>
          </CacheProvider>
        </ActiveThemeProvider>
      </LayoutProvider>
    </ThemeProvider>
  )
}
