/**
 * Lightweight TokenManager used by tests and API client integration points
 * Provides a simple in-memory token store with localStorage synchronization when available.
 */

export const TokenManager = {
  _accessToken: null as string | null,

  initializeTokens(): void {
    try {
      const token = this.getAccessToken()
      if (token) {
        // no-op initialization hook for tests
      }
    } catch {
      // ignore
    }
  },

  setAccessToken(token: string | null): void {
    this._accessToken = token
    try {
      if (typeof window !== "undefined" && window.localStorage) {
        if (token) window.localStorage.setItem("access_token", token)
        else window.localStorage.removeItem("access_token")
      }
    } catch {
      // ignore storage errors in tests
    }
  },

  getAccessToken(): string | null {
    if (this._accessToken) return this._accessToken
    try {
      if (typeof window !== "undefined" && window.localStorage) {
        return window.localStorage.getItem("access_token")
      }
    } catch {
      // ignore
    }
    return null
  },

  clearTokens(): void {
    this._accessToken = null
    try {
      if (typeof window !== "undefined" && window.localStorage) {
        window.localStorage.removeItem("access_token")
        window.localStorage.removeItem("refresh_token")
      }
    } catch {
      // ignore
    }
  },

  decodeToken(_token: string): any {
    // minimal stub for tests
    return {}
  },

  isTokenExpired(_token: string): boolean {
    // tests will mock this; default to false
    return false
  },
}
