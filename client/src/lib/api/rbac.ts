/**
 * RBAC API client
 *
 * Role-Based Access Control API functions for test compatibility
 */

import type { Role, RoleCreate, RoleUpdate } from "./types/authorization"

import {
  createRole,
  deleteRole,
  getAllRoles,
  getRoleHierarchy,
  updateRole,
} from "./endpoints/authorization"

/**
 * RBAC API client for test compatibility
 */
export const rbacApiClient = {
  getRoles: getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  getRoleHierarchy,
}

// Export types for convenience
export type { Role, RoleCreate, RoleUpdate }
