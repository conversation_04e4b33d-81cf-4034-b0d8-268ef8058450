/**
 * Project Management API Endpoints
 *
 * Provides type-safe project management API functions
 */

import type {
  ProjectActivityPaginatedResponse,
  ProjectAdvancedSearch,
  ProjectBulkStatusUpdate,
  ProjectBulkUpdate,
  ProjectCreate,
  ProjectExportParams,
  ProjectImportData,
  ProjectListParams,
  ProjectMember,
  ProjectMemberCreate,
  ProjectMemberPaginatedResponse,
  ProjectMemberUpdate,
  ProjectNotification,
  ProjectPaginatedResponse,
  ProjectRead,
  ProjectSearchParams,
  ProjectStats,
  ProjectStatus,
  ProjectSummaryPaginatedResponse,
  ProjectUpdate,
} from "../types/projects"

import { apiClient } from "../client"

/**
 * Project management endpoints
 */
export const projectsApi = {
  /**
   * Project CRUD operations
   */
  list: async (
    params?: ProjectListParams
  ): Promise<ProjectPaginatedResponse> => {
    return apiClient.get("/projects", { params })
  },

  listSummary: async (
    params?: ProjectListParams
  ): Promise<ProjectSummaryPaginatedResponse> => {
    return apiClient.get("/projects/summary", { params })
  },

  get: async (id: number): Promise<ProjectRead> => {
    return apiClient.get(`/projects/${id}`)
  },

  create: async (data: ProjectCreate): Promise<ProjectRead> => {
    return apiClient.post("/projects", data)
  },

  update: async (id: number, data: ProjectUpdate): Promise<ProjectRead> => {
    return apiClient.put(`/projects/${id}`, data)
  },

  delete: async (id: number): Promise<void> => {
    return apiClient.delete(`/projects/${id}`)
  },

  /**
   * Project search and filtering
   */
  search: async (
    params: ProjectSearchParams
  ): Promise<ProjectPaginatedResponse> => {
    return apiClient.post("/projects/search", params)
  },

  advancedSearch: async (
    criteria: ProjectAdvancedSearch
  ): Promise<ProjectPaginatedResponse> => {
    return apiClient.post("/projects/advanced-search", criteria)
  },

  /**
   * Project statistics
   */
  getStats: async (): Promise<ProjectStats> => {
    return apiClient.get("/projects/stats")
  },

  getProjectStats: async (
    id: number
  ): Promise<{
    member_count: number
    task_count: number
    completed_task_count: number
    progress_percentage: number
    days_since_start?: number
    estimated_days_remaining?: number
  }> => {
    return apiClient.get(`/projects/${id}/stats`)
  },

  /**
   * Project bulk operations
   */
  bulkUpdate: async (
    data: ProjectBulkUpdate
  ): Promise<{ updated: number; failed: number }> => {
    return apiClient.put("/projects/bulk", data)
  },

  bulkStatusUpdate: async (
    data: ProjectBulkStatusUpdate
  ): Promise<{ updated: number; failed: number }> => {
    return apiClient.put("/projects/bulk-status", data)
  },

  bulkDelete: async (
    projectIds: number[]
  ): Promise<{ deleted: number; failed: number }> => {
    return apiClient.post("/projects/bulk-delete", { project_ids: projectIds })
  },

  /**
   * Project status management
   */
  updateStatus: async (
    id: number,
    status: ProjectStatus,
    reason?: string
  ): Promise<ProjectRead> => {
    return apiClient.put(`/projects/${id}/status`, { status, reason })
  },

  archive: async (id: number): Promise<ProjectRead> => {
    return apiClient.post(`/projects/${id}/archive`)
  },

  unarchive: async (id: number): Promise<ProjectRead> => {
    return apiClient.post(`/projects/${id}/unarchive`)
  },

  /**
   * Project member management
   */
  getMembers: async (
    id: number,
    params?: { is_active?: boolean; role_id?: number }
  ): Promise<ProjectMemberPaginatedResponse> => {
    return apiClient.get(`/projects/${id}/members`, { params })
  },

  addMember: async (
    id: number,
    data: ProjectMemberCreate
  ): Promise<ProjectMember> => {
    return apiClient.post(`/projects/${id}/members`, data)
  },

  updateMember: async (
    id: number,
    memberId: number,
    data: ProjectMemberUpdate
  ): Promise<ProjectMember> => {
    return apiClient.put(`/projects/${id}/members/${memberId}`, data)
  },

  removeMember: async (id: number, memberId: number): Promise<void> => {
    return apiClient.delete(`/projects/${id}/members/${memberId}`)
  },

  bulkAddMembers: async (
    id: number,
    members: ProjectMemberCreate[]
  ): Promise<{
    added: ProjectMember[]
    failed: Array<{ member: ProjectMemberCreate; errors: string[] }>
  }> => {
    return apiClient.post(`/projects/${id}/members/bulk`, { members })
  },

  /**
   * Project import/export
   */
  import: async (
    data: ProjectImportData[]
  ): Promise<{
    imported: number
    failed: number
    errors: Array<{
      row: number
      name: string
      errors: string[]
    }>
  }> => {
    return apiClient.post("/projects/import", { projects: data })
  },

  export: async (params: ProjectExportParams): Promise<Blob> => {
    const response = await apiClient.get("/projects/export", {
      params,
      responseType: "blob",
    })
    return response
  },

  /**
   * Project activity and notifications
   */
  getActivity: async (
    id: number,
    params?: { page?: number; size?: number }
  ): Promise<ProjectActivityPaginatedResponse> => {
    return apiClient.get(`/projects/${id}/activity`, { params })
  },

  getNotifications: async (id: number): Promise<ProjectNotification[]> => {
    return apiClient.get(`/projects/${id}/notifications`)
  },

  markNotificationRead: async (
    id: number,
    notificationId: number
  ): Promise<void> => {
    return apiClient.put(`/projects/${id}/notifications/${notificationId}/read`)
  },

  /**
   * Project templates
   */
  getTemplates: async (): Promise<
    Array<{
      id: number
      name: string
      description?: string
      default_phases: Array<{
        name: string
        description?: string
        order: number
      }>
    }>
  > => {
    return apiClient.get("/projects/templates")
  },

  createFromTemplate: async (
    templateId: number,
    data: {
      name: string
      description?: string
      client?: string
      location?: string
    }
  ): Promise<ProjectRead> => {
    return apiClient.post(`/projects/templates/${templateId}/create`, data)
  },

  /**
   * Project collaboration
   */
  invite: async (
    id: number,
    data: {
      email: string
      role_id: number
      message?: string
    }
  ): Promise<{ message: string }> => {
    return apiClient.post(`/projects/${id}/invite`, data)
  },

  acceptInvitation: async (
    token: string
  ): Promise<{ project: ProjectRead; member: ProjectMember }> => {
    return apiClient.post("/projects/accept-invitation", { token })
  },

  /**
   * Project data management
   */
  duplicate: async (
    id: number,
    data: {
      name: string
      include_members?: boolean
      include_tasks?: boolean
    }
  ): Promise<ProjectRead> => {
    return apiClient.post(`/projects/${id}/duplicate`, data)
  },

  backup: async (
    id: number
  ): Promise<{ backup_url: string; expires_at: string }> => {
    return apiClient.post(`/projects/${id}/backup`)
  },

  restore: async (id: number, backupUrl: string): Promise<ProjectRead> => {
    return apiClient.post(`/projects/${id}/restore`, { backup_url: backupUrl })
  },
}

// Export individual functions for convenience
export const {
  list,
  listSummary,
  get,
  create,
  update,
  delete: deleteProject,
  search,
  advancedSearch,
  getStats,
  getProjectStats,
  bulkUpdate,
  bulkStatusUpdate,
  bulkDelete,
  updateStatus,
  archive,
  unarchive,
  getMembers,
  addMember,
  updateMember,
  removeMember,
  bulkAddMembers,
  import: importProjects,
  export: exportProjects,
  getActivity,
  getNotifications,
  markNotificationRead,
  getTemplates,
  createFromTemplate,
  invite,
  acceptInvitation,
  duplicate,
  backup,
  restore,
} = projectsApi

export default projectsApi
