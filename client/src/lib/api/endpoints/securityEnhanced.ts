/**
 * Enhanced Security API Endpoints for FR-1 Security Features
 * 
 * This module extends the existing authentication API with enhanced security features
 * including account lockout protection, email verification workflow, and enhanced
 * password reset functionality.
 */

import { apiClient } from '../client';
import type {
  EmailVerificationRequest,
  EmailVerificationResponse,
  ResendVerificationRequest,
  ResendVerificationResponse
} from '@/components/modules/auth/types/emailVerification';
import type {
  PasswordResetRequest,
  PasswordResetResponse,
  ResetPasswordRequest,
  ResetPasswordResponse
} from '@/components/modules/auth/types/passwordReset';
import type {
  LoginResponseEnhanced,
  RegistrationResponseEnhanced
} from '@/components/modules/auth/types/security';
import type { LoginRequest, RegisterRequest } from '../types/auth';

/**
 * Enhanced authentication endpoints with security features
 */
export const securityEnhancedApi = {
  /**
   * Enhanced authentication with lockout information
   */
  loginEnhanced: async (credentials: LoginRequest): Promise<LoginResponseEnhanced> => {
    try {
      // Use existing login method but expect enhanced response
      const response = await apiClient.post('/api/v1/auth/login', credentials);
      return response as LoginResponseEnhanced;
    } catch (error: any) {
      // Enhanced error handling for lockout information
      if (error?.response?.status === 401 && error?.response?.data?.lockout_info) {
        throw {
          ...error,
          data: {
            ...error.response.data,
            lockout_info: error.response.data.lockout_info
          }
        };
      }
      throw error;
    }
  },

  /**
   * Enhanced registration with email verification workflow
   */
  registerEnhanced: async (userData: RegisterRequest): Promise<RegistrationResponseEnhanced> => {
    const response = await apiClient.post('/api/v1/auth/register', userData);
    return response as RegistrationResponseEnhanced;
  },

  /**
   * Email verification workflow endpoints
   */
  verifyEmail: async (data: EmailVerificationRequest): Promise<EmailVerificationResponse> => {
    return apiClient.post('/api/v1/auth/verify-email', data);
  },

  resendVerificationEmail: async (data: ResendVerificationRequest): Promise<ResendVerificationResponse> => {
    return apiClient.post('/api/v1/auth/resend-verification', data);
  },

  /**
   * Enhanced password reset workflow endpoints
   */
  requestPasswordReset: async (data: PasswordResetRequest): Promise<PasswordResetResponse> => {
    return apiClient.post('/api/v1/auth/request-password-reset', data);
  },

  resetPassword: async (data: ResetPasswordRequest): Promise<ResetPasswordResponse> => {
    return apiClient.post('/api/v1/auth/reset-password', data);
  },

  /**
   * Account security status endpoints
   */
  getAccountSecurityStatus: async (): Promise<{
    is_locked: boolean;
    lockout_expires_at: string | null;
    failed_attempts: number;
    attempts_remaining: number;
    is_email_verified: boolean;
    two_factor_enabled: boolean;
  }> => {
    return apiClient.get('/api/v1/auth/security-status');
  },

  /**
   * Security event logging (client-side events)
   */
  logSecurityEvent: async (event: {
    event_type: string;
    metadata?: Record<string, any>;
  }): Promise<void> => {
    return apiClient.post('/api/v1/auth/security-events', event);
  },

  /**
   * Check account lockout status without authentication
   */
  checkLockoutStatus: async (email: string): Promise<{
    is_locked: boolean;
    lockout_expires_at: string | null;
    attempts_remaining: number;
  }> => {
    return apiClient.post('/api/v1/auth/check-lockout-status', { email });
  }
};

/**
 * Security service class for centralized security operations
 */
export class SecurityService {
  /**
   * Verify email with token
   */
  static async verifyEmail(token: string): Promise<EmailVerificationResponse> {
    return securityEnhancedApi.verifyEmail({ token });
  }

  /**
   * Resend verification email
   */
  static async resendVerification(email: string): Promise<ResendVerificationResponse> {
    return securityEnhancedApi.resendVerificationEmail({ email });
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<PasswordResetResponse> {
    return securityEnhancedApi.requestPasswordReset({ email });
  }

  /**
   * Reset password with token
   */
  static async resetPassword(token: string, new_password: string): Promise<ResetPasswordResponse> {
    return securityEnhancedApi.resetPassword({ token, new_password });
  }

  /**
   * Enhanced login with lockout handling
   */
  static async loginWithLockoutInfo(email: string, password: string): Promise<LoginResponseEnhanced> {
    return securityEnhancedApi.loginEnhanced({ 
      username: email, // Backend expects username field
      password 
    });
  }

  /**
   * Enhanced registration with verification workflow
   */
  static async registerWithVerification(userData: RegisterRequest): Promise<RegistrationResponseEnhanced> {
    return securityEnhancedApi.registerEnhanced(userData);
  }

  /**
   * Get current account security status
   */
  static async getSecurityStatus() {
    return securityEnhancedApi.getAccountSecurityStatus();
  }

  /**
   * Log security event for audit trail
   */
  static async logSecurityEvent(eventType: string, metadata?: Record<string, any>) {
    return securityEnhancedApi.logSecurityEvent({
      event_type: eventType,
      metadata
    });
  }

  /**
   * Check if account is locked without authentication
   */
  static async checkLockoutStatus(email: string) {
    return securityEnhancedApi.checkLockoutStatus(email);
  }
}

// Export individual functions for convenience
export const {
  loginEnhanced,
  registerEnhanced,
  verifyEmail,
  resendVerificationEmail,
  requestPasswordReset,
  resetPassword,
  getAccountSecurityStatus,
  logSecurityEvent,
  checkLockoutStatus
} = securityEnhancedApi;

export default securityEnhancedApi;