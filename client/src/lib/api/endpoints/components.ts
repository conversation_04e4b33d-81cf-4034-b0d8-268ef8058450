/**
 * Component Management API Endpoints
 *
 * Provides type-safe component management API functions
 */

import type {
  ComponentAdvancedSearch,
  ComponentAdvancedSearchResponse,
  ComponentBulkCreate,
  ComponentBulkUpdate,
  ComponentCreate,
  ComponentExportParams,
  ComponentImportData,
  ComponentListParams,
  ComponentPaginatedResponse,
  ComponentRead,
  ComponentSearchResult,
  ComponentStats,
  ComponentSummary,
  ComponentSummaryPaginatedResponse,
  ComponentUpdate,
  ComponentValidationResult,
} from "../types/components"

import { apiClient } from "../client"

/**
 * Component management endpoints
 */
export const componentsApi = {
  /**
   * Component CRUD operations
   */
  list: async (
    params?: ComponentListParams
  ): Promise<ComponentPaginatedResponse> => {
    return apiClient.get("/components", { params })
  },

  listSummary: async (
    params?: ComponentListParams
  ): Promise<ComponentSummaryPaginatedResponse> => {
    return apiClient.get("/components/summary", { params })
  },

  get: async (id: number): Promise<ComponentRead> => {
    return apiClient.get(`/components/${id}`)
  },

  create: async (data: ComponentCreate): Promise<ComponentRead> => {
    return apiClient.post("/components", data)
  },

  update: async (id: number, data: ComponentUpdate): Promise<ComponentRead> => {
    return apiClient.put(`/components/${id}`, data)
  },

  delete: async (id: number): Promise<void> => {
    return apiClient.delete(`/components/${id}`)
  },

  /**
   * Component search and filtering
   */
  search: async (
    criteria: ComponentAdvancedSearch
  ): Promise<ComponentAdvancedSearchResponse> => {
    return apiClient.post("/components/search", criteria)
  },

  quickSearch: async (
    query: string,
    limit?: number
  ): Promise<ComponentSearchResult[]> => {
    return apiClient.get("/components/quick-search", {
      params: { q: query, limit: limit || 10 },
    })
  },

  /**
   * Component bulk operations
   */
  bulkCreate: async (
    data: ComponentBulkCreate
  ): Promise<ComponentValidationResult> => {
    return apiClient.post("/components/bulk", data)
  },

  bulkUpdate: async (
    data: ComponentBulkUpdate
  ): Promise<{ updated: number; failed: number }> => {
    return apiClient.put("/components/bulk", data)
  },

  bulkDelete: async (
    componentIds: number[]
  ): Promise<{ deleted: number; failed: number }> => {
    return apiClient.post("/components/bulk-delete", {
      component_ids: componentIds,
    })
  },

  /**
   * Component validation
   */
  validate: async (
    data: ComponentCreate[]
  ): Promise<ComponentValidationResult> => {
    return apiClient.post("/components/validate", { components: data })
  },

  validateSingle: async (
    data: ComponentCreate
  ): Promise<{ valid: boolean; errors?: string[] }> => {
    return apiClient.post("/components/validate-single", data)
  },

  /**
   * Component import/export
   */
  import: async (
    data: ComponentImportData[]
  ): Promise<ComponentValidationResult> => {
    return apiClient.post("/components/import", { components: data })
  },

  export: async (params: ComponentExportParams): Promise<Blob> => {
    const response = await apiClient.get("/components/export", {
      params,
      responseType: "blob",
    })
    return response
  },

  /**
   * Component statistics
   */
  getStats: async (): Promise<ComponentStats> => {
    return apiClient.get("/components/stats")
  },

  getStatsByCategory: async (): Promise<Record<string, number>> => {
    return apiClient.get("/components/stats/by-category")
  },

  getStatsByManufacturer: async (): Promise<Record<string, number>> => {
    return apiClient.get("/components/stats/by-manufacturer")
  },

  /**
   * Component preferences and favorites
   */
  markAsPreferred: async (id: number): Promise<ComponentRead> => {
    return apiClient.post(`/components/${id}/prefer`)
  },

  unmarkAsPreferred: async (id: number): Promise<ComponentRead> => {
    return apiClient.delete(`/components/${id}/prefer`)
  },

  getPreferred: async (
    params?: ComponentListParams
  ): Promise<ComponentPaginatedResponse> => {
    return apiClient.get("/components/preferred", { params })
  },

  /**
   * Component stock management
   */
  updateStock: async (id: number, status: string): Promise<ComponentRead> => {
    return apiClient.put(`/components/${id}/stock`, { status })
  },

  getByStockStatus: async (
    status: string,
    params?: ComponentListParams
  ): Promise<ComponentPaginatedResponse> => {
    return apiClient.get("/components/by-stock", {
      params: { ...params, status },
    })
  },

  /**
   * Component specifications
   */
  updateSpecifications: async (
    id: number,
    specifications: Record<string, unknown>
  ): Promise<ComponentRead> => {
    return apiClient.put(`/components/${id}/specifications`, { specifications })
  },

  getSpecificationKeys: async (): Promise<string[]> => {
    return apiClient.get("/components/specification-keys")
  },

  /**
   * Component relationships
   */
  getSimilar: async (
    id: number,
    limit?: number
  ): Promise<ComponentSummary[]> => {
    return apiClient.get(`/components/${id}/similar`, {
      params: { limit: limit || 5 },
    })
  },

  getAlternatives: async (id: number): Promise<ComponentSummary[]> => {
    return apiClient.get(`/components/${id}/alternatives`)
  },

  /**
   * Component versioning
   */
  getVersions: async (id: number): Promise<ComponentRead[]> => {
    return apiClient.get(`/components/${id}/versions`)
  },

  createVersion: async (
    id: number,
    data: ComponentUpdate
  ): Promise<ComponentRead> => {
    return apiClient.post(`/components/${id}/versions`, data)
  },

  /**
   * Component attachments and documents
   */
  uploadAttachment: async (
    id: number,
    file: File
  ): Promise<{ url: string; filename: string }> => {
    const formData = new FormData()
    formData.append("file", file)
    return apiClient.post(`/components/${id}/attachments`, formData, {
      headers: { "Content-Type": "multipart/form-data" },
    })
  },

  getAttachments: async (
    id: number
  ): Promise<
    Array<{ id: number; filename: string; url: string; uploaded_at: string }>
  > => {
    return apiClient.get(`/components/${id}/attachments`)
  },

  deleteAttachment: async (id: number, attachmentId: number): Promise<void> => {
    return apiClient.delete(`/components/${id}/attachments/${attachmentId}`)
  },
}

// Export individual functions for convenience
export const {
  list,
  listSummary,
  get,
  create,
  update,
  delete: deleteComponent,
  search,
  quickSearch,
  bulkCreate,
  bulkUpdate,
  bulkDelete,
  validate,
  validateSingle,
  import: importComponents,
  export: exportComponents,
  getStats,
  getStatsByCategory,
  getStatsByManufacturer,
  markAsPreferred,
  unmarkAsPreferred,
  getPreferred,
  updateStock,
  getByStockStatus,
  updateSpecifications,
  getSpecificationKeys,
  getSimilar,
  getAlternatives,
  getVersions,
  createVersion,
  uploadAttachment,
  getAttachments,
  deleteAttachment,
} = componentsApi

export default componentsApi
