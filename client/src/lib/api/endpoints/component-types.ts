/**
 * Component Type Management API Endpoints
 *
 * Provides type-safe component type management API functions
 */

import type {
  ComponentTypeCreate,
  ComponentTypeListParams,
  ComponentTypePaginatedResponse,
  ComponentTypeRead,
  ComponentTypeSpecificationTemplate,
  ComponentTypeStats,
  ComponentTypeSummary,
  ComponentTypeSummaryPaginatedResponse,
  ComponentTypeUpdate,
} from "../types/components"

import { apiClient } from "../client"

/**
 * Component type management endpoints
 */
export const componentTypesApi = {
  /**
   * Type CRUD operations
   */
  list: async (
    params?: ComponentTypeListParams
  ): Promise<ComponentTypePaginatedResponse> => {
    return apiClient.get("/component-types", { params })
  },

  listSummary: async (
    params?: ComponentTypeListParams
  ): Promise<ComponentTypeSummaryPaginatedResponse> => {
    return apiClient.get("/component-types/summary", { params })
  },

  get: async (id: number): Promise<ComponentTypeRead> => {
    return apiClient.get(`/component-types/${id}`)
  },

  create: async (data: ComponentTypeCreate): Promise<ComponentTypeRead> => {
    return apiClient.post("/component-types", data)
  },

  update: async (
    id: number,
    data: ComponentTypeUpdate
  ): Promise<ComponentTypeRead> => {
    return apiClient.put(`/component-types/${id}`, data)
  },

  delete: async (id: number): Promise<void> => {
    return apiClient.delete(`/component-types/${id}`)
  },

  /**
   * Type search and filtering
   */
  search: async (query: string): Promise<ComponentTypeRead[]> => {
    return apiClient.get("/component-types/search", { params: { q: query } })
  },

  getByCategory: async (
    categoryId: number,
    params?: ComponentTypeListParams
  ): Promise<ComponentTypePaginatedResponse> => {
    return apiClient.get(`/component-types/by-category/${categoryId}`, {
      params,
    })
  },

  /**
   * Type statistics
   */
  getStats: async (): Promise<ComponentTypeStats> => {
    return apiClient.get("/component-types/stats")
  },

  getTypeStats: async (
    id: number
  ): Promise<{
    component_count: number
    active_component_count: number
    preferred_component_count: number
    avg_component_price?: number
    specifications_coverage: number
  }> => {
    return apiClient.get(`/component-types/${id}/stats`)
  },

  /**
   * Specification template management
   */
  getSpecificationTemplate: async (
    id: number
  ): Promise<ComponentTypeSpecificationTemplate> => {
    return apiClient.get(`/component-types/${id}/specification-template`)
  },

  updateSpecificationTemplate: async (
    id: number,
    template: ComponentTypeSpecificationTemplate
  ): Promise<ComponentTypeRead> => {
    return apiClient.put(
      `/component-types/${id}/specification-template`,
      template
    )
  },

  deleteSpecificationTemplate: async (
    id: number
  ): Promise<ComponentTypeRead> => {
    return apiClient.delete(`/component-types/${id}/specification-template`)
  },

  getDefaultTemplate: async (
    categoryId: number
  ): Promise<ComponentTypeSpecificationTemplate> => {
    return apiClient.get(`/component-types/default-template/${categoryId}`)
  },

  /**
   * Type bulk operations
   */
  bulkCreate: async (
    types: ComponentTypeCreate[]
  ): Promise<{
    created: ComponentTypeRead[]
    failed: Array<{ type: ComponentTypeCreate; errors: string[] }>
  }> => {
    return apiClient.post("/component-types/bulk", { types })
  },

  bulkUpdate: async (
    updates: Array<{ id: number; data: ComponentTypeUpdate }>
  ): Promise<{
    updated: number
    failed: number
  }> => {
    return apiClient.put("/component-types/bulk", { updates })
  },

  bulkDelete: async (
    typeIds: number[]
  ): Promise<{
    deleted: number
    failed: number
  }> => {
    return apiClient.post("/component-types/bulk-delete", { type_ids: typeIds })
  },

  /**
   * Type validation
   */
  validate: async (
    data: ComponentTypeCreate
  ): Promise<{
    valid: boolean
    errors?: string[]
    warnings?: string[]
  }> => {
    return apiClient.post("/component-types/validate", data)
  },

  validateSpecificationTemplate: async (
    template: ComponentTypeSpecificationTemplate
  ): Promise<{
    valid: boolean
    errors?: string[]
    warnings?: string[]
  }> => {
    return apiClient.post("/component-types/validate-template", template)
  },

  /**
   * Type import/export
   */
  export: async (
    params: {
      format?: "csv" | "xlsx" | "json"
      category_ids?: number[]
      include_templates?: boolean
    } = {}
  ): Promise<Blob> => {
    const response = await apiClient.get("/component-types/export", {
      params,
      responseType: "blob",
    })
    return response
  },

  import: async (
    data: Array<{
      name: string
      description?: string
      category_name: string
      specifications_template?: ComponentTypeSpecificationTemplate
    }>
  ): Promise<{
    imported: number
    failed: number
    errors: Array<{
      row: number
      name: string
      errors: string[]
    }>
  }> => {
    return apiClient.post("/component-types/import", { types: data })
  },

  /**
   * Type activation/deactivation
   */
  activate: async (id: number): Promise<ComponentTypeRead> => {
    return apiClient.post(`/component-types/${id}/activate`)
  },

  deactivate: async (id: number): Promise<ComponentTypeRead> => {
    return apiClient.post(`/component-types/${id}/deactivate`)
  },

  /**
   * Type relationships
   */
  getSimilar: async (
    id: number,
    limit?: number
  ): Promise<ComponentTypeSummary[]> => {
    return apiClient.get(`/component-types/${id}/similar`, {
      params: { limit: limit || 5 },
    })
  },

  getCompatible: async (id: number): Promise<ComponentTypeSummary[]> => {
    return apiClient.get(`/component-types/${id}/compatible`)
  },

  /**
   * Type components
   */
  getComponents: async (
    id: number,
    params?: {
      page?: number
      size?: number
      is_active?: boolean
      is_preferred?: boolean
    }
  ): Promise<import("../types/components").ComponentPaginatedResponse> => {
    return apiClient.get(`/component-types/${id}/components`, { params })
  },

  /**
   * Type specification analysis
   */
  analyzeSpecifications: async (
    id: number
  ): Promise<{
    common_specifications: Record<
      string,
      {
        frequency: number
        sample_values: unknown[]
      }
    >
    missing_specifications: string[]
    specification_coverage: number
  }> => {
    return apiClient.get(`/component-types/${id}/analyze-specifications`)
  },

  getSpecificationSuggestions: async (
    id: number
  ): Promise<{
    suggested_fields: Array<{
      field_name: string
      field_type: string
      description: string
      required: boolean
    }>
  }> => {
    return apiClient.get(`/component-types/${id}/specification-suggestions`)
  },
}

// Export individual functions for convenience
export const {
  list,
  listSummary,
  get,
  create,
  update,
  delete: deleteType,
  search,
  getByCategory,
  getStats,
  getTypeStats,
  getSpecificationTemplate,
  updateSpecificationTemplate,
  deleteSpecificationTemplate,
  getDefaultTemplate,
  bulkCreate,
  bulkUpdate,
  bulkDelete,
  validate,
  validateSpecificationTemplate,
  export: exportTypes,
  import: importTypes,
  activate,
  deactivate,
  getSimilar,
  getCompatible,
  getComponents,
  analyzeSpecifications,
  getSpecificationSuggestions,
} = componentTypesApi

export default componentTypesApi
