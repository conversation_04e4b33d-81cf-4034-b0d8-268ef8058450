/**
 * Health Check API Endpoints
 *
 * Provides type-safe health monitoring API functions
 */

import type { HealthCheckResponse } from "../types/common"

import { apiClient } from "../client"

/**
 * Health check endpoints
 */
export const healthApi = {
  /**
   * Basic health check
   */
  check: async (): Promise<HealthCheckResponse> => {
    return apiClient.get("/health")
  },

  /**
   * Readiness probe - checks if the service is ready to serve traffic
   */
  ready: async (): Promise<HealthCheckResponse> => {
    return apiClient.get("/health/ready")
  },

  /**
   * Liveness probe - checks if the service is alive
   */
  live: async (): Promise<HealthCheckResponse> => {
    return apiClient.get("/health/live")
  },

  /**
   * Detailed health check with component status
   */
  detailed: async (): Promise<HealthCheckResponse> => {
    return apiClient.get("/health/detailed")
  },

  /**
   * Database health check
   */
  database: async (): Promise<{
    status: "pass" | "fail" | "warn"
    message?: string
    connection_pool: {
      active: number
      idle: number
      total: number
    }
    response_time_ms: number
  }> => {
    return apiClient.get("/health/database")
  },

  /**
   * Cache health check (Redis)
   */
  cache: async (): Promise<{
    status: "pass" | "fail" | "warn"
    message?: string
    response_time_ms: number
    memory_usage?: {
      used: number
      max: number
      percentage: number
    }
  }> => {
    return apiClient.get("/health/cache")
  },

  /**
   * External services health check
   */
  external: async (): Promise<{
    status: "pass" | "fail" | "warn"
    services: Record<
      string,
      {
        status: "pass" | "fail" | "warn"
        response_time_ms?: number
        message?: string
      }
    >
  }> => {
    return apiClient.get("/health/external")
  },

  /**
   * System metrics
   */
  metrics: async (): Promise<{
    system: {
      cpu_usage_percent: number
      memory_usage_percent: number
      disk_usage_percent: number
      uptime_seconds: number
    }
    application: {
      active_connections: number
      requests_per_minute: number
      error_rate_percent: number
      average_response_time_ms: number
    }
    database: {
      active_connections: number
      query_time_avg_ms: number
      slow_queries_count: number
    }
  }> => {
    return apiClient.get("/health/metrics")
  },

  /**
   * Performance monitoring
   */
  performance: async (
    timeRange?: "1h" | "24h" | "7d" | "30d"
  ): Promise<{
    time_range: string
    metrics: {
      timestamp: string
      response_time_avg_ms: number
      requests_per_second: number
      error_rate_percent: number
      cpu_usage_percent: number
      memory_usage_percent: number
    }[]
  }> => {
    return apiClient.get("/health/performance", {
      params: { time_range: timeRange || "1h" },
    })
  },

  /**
   * Service dependencies status
   */
  dependencies: async (): Promise<{
    status: "pass" | "fail" | "warn"
    dependencies: Record<
      string,
      {
        name: string
        status: "pass" | "fail" | "warn"
        version?: string
        response_time_ms?: number
        last_check: string
        message?: string
      }
    >
  }> => {
    return apiClient.get("/health/dependencies")
  },

  /**
   * Version and build information
   */
  version: async (): Promise<{
    version: string
    build_date: string
    commit_hash: string
    environment: string
    python_version: string
    dependencies: Record<string, string>
  }> => {
    return apiClient.get("/health/version")
  },

  /**
   * Configuration status
   */
  config: async (): Promise<{
    status: "pass" | "fail" | "warn"
    environment: string
    debug_mode: boolean
    database_url_configured: boolean
    cache_configured: boolean
    email_configured: boolean
    storage_configured: boolean
    security_configured: boolean
  }> => {
    return apiClient.get("/health/config")
  },

  /**
   * Security status
   */
  security: async (): Promise<{
    status: "pass" | "fail" | "warn"
    https_enabled: boolean
    jwt_configured: boolean
    rate_limiting_enabled: boolean
    cors_configured: boolean
    security_headers_enabled: boolean
    last_security_scan?: string
  }> => {
    return apiClient.get("/health/security")
  },

  /**
   * Background jobs status
   */
  jobs: async (): Promise<{
    status: "pass" | "fail" | "warn"
    queues: Record<
      string,
      {
        pending: number
        processing: number
        completed: number
        failed: number
        last_job_at?: string
      }
    >
    workers: {
      active: number
      total: number
    }
  }> => {
    return apiClient.get("/health/jobs")
  },

  /**
   * Storage health check
   */
  storage: async (): Promise<{
    status: "pass" | "fail" | "warn"
    type: string
    available_space_gb?: number
    used_space_gb?: number
    total_space_gb?: number
    response_time_ms: number
  }> => {
    return apiClient.get("/health/storage")
  },
}

// Export individual functions for convenience
export const {
  check,
  ready,
  live,
  detailed,
  database,
  cache,
  external,
  metrics,
  performance,
  dependencies,
  version,
  config,
  security,
  jobs,
  storage,
} = healthApi

export default healthApi
