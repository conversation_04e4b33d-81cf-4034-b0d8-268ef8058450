/**
 * Authorization API endpoints
 *
 * API client functions for RBAC operations including roles, permissions,
 * and user role assignments.
 */

import type {
  Permission,
  PermissionCreate,
  Role,
  RoleCreate,
  RoleUpdate,
  UserRoleAssignment,
  UserRoleAssignmentCreate,
} from "../types/authorization"

import { apiClient } from "../client"

/**
 * Role Management Functions
 */

// Role operations
export async function createRole(roleData: RoleCreate): Promise<Role> {
  return await apiClient.post<Role>("/auth/roles", roleData)
}

export async function getAllRoles(): Promise<Role[]> {
  return await apiClient.get<Role[]>("/auth/roles")
}

export async function getRoleById(roleId: number): Promise<Role> {
  return await apiClient.get<Role>(`/auth/roles/${roleId}`)
}

export async function updateRole(
  roleId: number,
  roleData: RoleUpdate
): Promise<Role> {
  return await apiClient.patch<Role>(`/auth/roles/${roleId}`, roleData)
}

export async function deleteRole(roleId: number): Promise<void> {
  await apiClient.delete(`/auth/roles/${roleId}`)
}

// Permission operations
export async function createPermission(
  permissionData: PermissionCreate
): Promise<Permission> {
  return await apiClient.post<Permission>("/auth/permissions", permissionData)
}

export async function getAllPermissions(): Promise<Permission[]> {
  return await apiClient.get<Permission[]>("/auth/permissions")
}

// Role permission assignment
export async function assignPermissionsToRole(
  roleId: number,
  permissionIds: number[]
): Promise<Role> {
  return await apiClient.patch<Role>(
    `/auth/roles/${roleId}/permissions`,
    permissionIds
  )
}

// User role assignment operations
export async function assignRoleToUser(
  userId: number,
  assignmentData: UserRoleAssignmentCreate
): Promise<UserRoleAssignment> {
  return await apiClient.post<UserRoleAssignment>(
    `/auth/users/${userId}/roles`,
    assignmentData
  )
}

export async function removeRoleFromUser(
  userId: number,
  roleId: number
): Promise<void> {
  await apiClient.delete(`/auth/users/${userId}/roles/${roleId}`)
}

// Permission checking
export async function checkUserPermission(
  userId: number,
  resource: string,
  action: string
): Promise<boolean> {
  return await apiClient.get<boolean>(
    `/auth/users/${userId}/permissions/${resource}/${action}`
  )
}

export async function getUserPermissions(userId: number): Promise<string[]> {
  return await apiClient.get<string[]>(`/auth/users/${userId}/permissions`)
}

// Role hierarchy operations
export async function getRoleHierarchy(): Promise<Role[]> {
  // For now, return all roles as a flat list since hierarchy is not yet implemented
  return getAllRoles()
}

/**
 * Authorization API object for backward compatibility
 */
export const authorizationApi = {
  createRole,
  getAllRoles,
  getRoleById,
  updateRole,
  deleteRole,
  createPermission,
  getAllPermissions,
  assignPermissionsToRole,
  assignRoleToUser,
  removeRoleFromUser,
  checkUserPermission,
  getUserPermissions,
  getRoleHierarchy,
}

/**
 * RBAC API client alias for test compatibility
 */
export const rbacApiClient = {
  getRoles: getAllRoles,
  createRole,
  updateRole,
  deleteRole,
  getRoleHierarchy,
}
