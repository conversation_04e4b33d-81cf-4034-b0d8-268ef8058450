/**
 * Authentication API Endpoints
 *
 * Provides type-safe authentication API functions using the existing APIClient
 */

import type {
  LoginRequest,
  LoginResponse,
  LogoutResponse,
  PasswordChangeRequest,
  PasswordChangeResponse,
  PasswordResetConfirm,
  PasswordResetRequest,
  PasswordResetResponse,
  RegisterRequest,
  RegisterResponse,
  UserActivityLog,
  UserPreference,
  UserPreferenceCreate,
  UserPreferenceUpdate,
  UserRead,
  UserSession,
  UserUpdate,
} from "../types/auth"
import type { PaginatedResponse } from "../types/common"

import { apiClient } from "../client"

/**
 * Authentication endpoints
 */
export const authApi = {
  /**
   * User authentication
   */
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    return apiClient.login(credentials)
  },

  register: async (userData: RegisterRequest): Promise<RegisterResponse> => {
    return apiClient.register(userData)
  },

  logout: async (): Promise<LogoutResponse> => {
    return apiClient.logout()
  },

  /**
   * Current user profile management
   */
  getCurrentUser: async (): Promise<UserRead> => {
    return apiClient.getCurrentUser()
  },

  updateCurrentUser: async (userData: UserUpdate): Promise<UserRead> => {
    return apiClient.put("/auth/me", userData)
  },

  /**
   * Password management
   */
  changePassword: async (
    data: PasswordChangeRequest
  ): Promise<PasswordChangeResponse> => {
    return apiClient.changePassword(data)
  },

  requestPasswordReset: async (
    data: PasswordResetRequest
  ): Promise<PasswordResetResponse> => {
    return apiClient.requestPasswordReset(data)
  },

  confirmPasswordReset: async (
    data: PasswordResetConfirm
  ): Promise<PasswordResetResponse> => {
    return apiClient.confirmPasswordReset(data)
  },

  /**
   * User preferences
   */
  getUserPreferences: async (): Promise<UserPreference[]> => {
    return apiClient.get("/auth/preferences")
  },

  createUserPreference: async (
    data: UserPreferenceCreate
  ): Promise<UserPreference> => {
    return apiClient.post("/auth/preferences", data)
  },

  updateUserPreference: async (
    id: number,
    data: UserPreferenceUpdate
  ): Promise<UserPreference> => {
    return apiClient.put(`/auth/preferences/${id}`, data)
  },

  deleteUserPreference: async (id: number): Promise<void> => {
    return apiClient.delete(`/auth/preferences/${id}`)
  },

  /**
   * User activity and sessions
   */
  getUserActivity: async (params?: {
    page?: number
    size?: number
  }): Promise<PaginatedResponse<UserActivityLog>> => {
    return apiClient.get("/auth/activity", { params })
  },

  getUserSessions: async (): Promise<UserSession[]> => {
    return apiClient.get("/auth/sessions")
  },

  revokeSession: async (sessionId: number): Promise<void> => {
    return apiClient.delete(`/auth/sessions/${sessionId}`)
  },

  revokeAllSessions: async (): Promise<void> => {
    return apiClient.delete("/auth/sessions")
  },

  /**
   * Token management
   */
  refreshToken: async (): Promise<LoginResponse> => {
    return apiClient.post("/auth/refresh")
  },

  verifyToken: async (): Promise<{ valid: boolean; user?: UserRead }> => {
    return apiClient.get("/auth/verify")
  },

  /**
   * Account verification
   */
  sendVerificationEmail: async (): Promise<{ message: string }> => {
    return apiClient.post("/auth/send-verification")
  },

  verifyEmail: async (token: string): Promise<{ message: string }> => {
    return apiClient.post("/auth/verify-email", { token })
  },

  /**
   * Two-factor authentication
   */
  enableTwoFactor: async (): Promise<{
    qr_code: string
    backup_codes: string[]
  }> => {
    return apiClient.post("/auth/2fa/enable")
  },

  confirmTwoFactor: async (code: string): Promise<{ message: string }> => {
    return apiClient.post("/auth/2fa/confirm", { code })
  },

  disableTwoFactor: async (password: string): Promise<{ message: string }> => {
    return apiClient.post("/auth/2fa/disable", { password })
  },

  generateBackupCodes: async (): Promise<{ backup_codes: string[] }> => {
    return apiClient.post("/auth/2fa/backup-codes")
  },

  /**
   * Security settings
   */
  getSecuritySettings: async (): Promise<{
    two_factor_enabled: boolean
    backup_codes_generated: boolean
    trusted_devices: Array<{
      id: string
      name: string
      last_used: string
      created_at: string
    }>
    active_sessions: Array<{
      id: string
      ip_address: string
      user_agent: string
      last_activity: string
      is_current: boolean
    }>
  }> => {
    return apiClient.get("/auth/security")
  },

  addTrustedDevice: async (
    deviceName: string
  ): Promise<{ device_id: string }> => {
    return apiClient.post("/auth/trusted-devices", { name: deviceName })
  },

  removeTrustedDevice: async (deviceId: string): Promise<void> => {
    return apiClient.delete(`/auth/trusted-devices/${deviceId}`)
  },
}

// Export individual functions for convenience
export const {
  login,
  register,
  logout,
  getCurrentUser,
  updateCurrentUser,
  changePassword,
  requestPasswordReset,
  confirmPasswordReset,
  getUserPreferences,
  createUserPreference,
  updateUserPreference,
  deleteUserPreference,
  getUserActivity,
  getUserSessions,
  revokeSession,
  revokeAllSessions,
  refreshToken,
  verifyToken,
  sendVerificationEmail,
  verifyEmail,
  enableTwoFactor,
  confirmTwoFactor,
  disableTwoFactor,
  generateBackupCodes,
  getSecuritySettings,
  addTrustedDevice,
  removeTrustedDevice,
} = authApi

export default authApi
