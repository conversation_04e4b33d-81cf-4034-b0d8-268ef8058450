/**
 * API Endpoints - Zod validation tests (TDD)
 */
import { beforeEach, describe, expect, it, vi } from "vitest"
import { authApi } from "../auth"

// Mock api client to use schema param passthrough
vi.mock("../../client", () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
    _requestWithValidation: vi.fn(),
  },
}))

const mockApiClient = vi.mocked(await import("../../client")).apiClient

describe("Auth endpoints Zod validation", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("validates login response against schema", async () => {
    const mockResponse = {
      access_token: "token123",
      token_type: "Bearer",
      expires_in: 3600,
      user: {
        id: 1,
        name: "User",
        email: "<EMAIL>",
        is_superuser: false,
        is_active: true,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      },
    }

    // Make client.getCurrentUser use our validation helper
    mockApiClient.login = vi
      .fn()
      .mockImplementation(async (_data: any, _cfg?: any, schema?: any) => {
        // Simulate axios-like response object for helper
        const res = { data: mockResponse }
        return await (mockApiClient as any)._requestWithValidation(
          () => Promise.resolve(res),
          schema
        )
      })
    ;(mockApiClient as any)._requestWithValidation.mockResolvedValue(
      mockResponse
    )

    const result = await authApi.login({
      username: "<EMAIL>",
      password: "x",
    })

    // Our endpoint uses client.login directly, so we assert validation helper used with schema
    expect((mockApiClient as any)._requestWithValidation).toHaveBeenCalled()
    expect(result).toEqual(mockResponse)
  })
})
