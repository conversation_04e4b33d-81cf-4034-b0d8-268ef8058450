/**
 * API Endpoints Tests
 *
 * Tests for API endpoint functions with mocked responses
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { authApi } from "../auth"
import { componentsApi } from "../components"
import { healthApi } from "../health"
import { usersApi } from "../users"

// Mock the API client
vi.mock("../../client", () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
    login: vi.fn(),
    register: vi.fn(),
    logout: vi.fn(),
    getCurrentUser: vi.fn(),
    updateCurrentUser: vi.fn(),
    changePassword: vi.fn(),
    requestPasswordReset: vi.fn(),
    confirmPasswordReset: vi.fn(),
  },
}))

// Get the mocked API client
const { apiClient } = await import("../../client")
const mockApiClient = vi.mocked(apiClient)

describe("API Endpoints Tests", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe("Authentication API", () => {
    it("should login successfully", async () => {
      const mockResponse = {
        access_token: "token123",
        token_type: "Bearer",
        expires_in: 3600,
        user: {
          id: 1,
          name: "Test User",
          email: "<EMAIL>",
          is_superuser: false,
          is_active: true,
          created_at: "2023-01-01T00:00:00Z",
          updated_at: "2023-01-01T00:00:00Z",
        },
      }

      mockApiClient.login.mockResolvedValue(mockResponse)

      const result = await authApi.login({
        username: "<EMAIL>",
        password: "password123",
      })

      expect(mockApiClient.login).toHaveBeenCalledWith({
        username: "<EMAIL>",
        password: "password123",
      })
      expect(result).toEqual(mockResponse)
    })

    it("should handle login error", async () => {
      const mockError = new Error("Invalid credentials")
      mockApiClient.login.mockRejectedValue(mockError)

      await expect(
        authApi.login({
          username: "<EMAIL>",
          password: "wrongpassword",
        })
      ).rejects.toThrow("Invalid credentials")
    })

    it("should register successfully", async () => {
      const mockResponse = {
        message: "User registered successfully",
        user: {
          id: 1,
          name: "New User",
          email: "<EMAIL>",
          is_superuser: false,
          is_active: true,
          created_at: "2023-01-01T00:00:00Z",
          updated_at: "2023-01-01T00:00:00Z",
        },
      }

      mockApiClient.register.mockResolvedValue(mockResponse)

      const result = await authApi.register({
        name: "New User",
        email: "<EMAIL>",
        password: "password123",
        confirm_password: "password123",
      })

      expect(mockApiClient.register).toHaveBeenCalledWith({
        name: "New User",
        email: "<EMAIL>",
        password: "password123",
        confirm_password: "password123",
      })
      expect(result).toEqual(mockResponse)
    })

    it("should get current user", async () => {
      const mockUser = {
        id: 1,
        name: "Test User",
        email: "<EMAIL>",
        is_superuser: false,
        is_active: true,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      }

      mockApiClient.getCurrentUser.mockResolvedValue(mockUser)

      const result = await authApi.getCurrentUser()

      expect(mockApiClient.getCurrentUser).toHaveBeenCalled()
      expect(result).toEqual(mockUser)
    })

    it("should change password", async () => {
      const mockResponse = {
        message: "Password changed successfully",
        changed_at: "2023-01-01T00:00:00Z",
      }

      mockApiClient.changePassword.mockResolvedValue(mockResponse)

      const result = await authApi.changePassword({
        current_password: "oldpassword",
        new_password: "newpassword",
        confirm_password: "newpassword",
      })

      expect(mockApiClient.changePassword).toHaveBeenCalledWith({
        current_password: "oldpassword",
        new_password: "newpassword",
        confirm_password: "newpassword",
      })
      expect(result).toEqual(mockResponse)
    })
  })

  describe("Users API", () => {
    it("should list users", async () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            email: "<EMAIL>",
            first_name: "User",
            last_name: "One",
            is_active: true,
            is_verified: true,
            created_at: "2023-01-01T00:00:00Z",
            updated_at: "2023-01-01T00:00:00Z",
          },
        ],
        total: 1,
        page: 1,
        size: 10,
        pages: 1,
        has_next: false,
        has_prev: false,
      }

      mockApiClient.get.mockResolvedValue(mockResponse)

      const result = await usersApi.list({ page: 1, size: 10 })

      expect(mockApiClient.get).toHaveBeenCalledWith("/users", {
        params: { page: 1, size: 10 },
      })
      expect(result).toEqual(mockResponse)
    })

    it("should get user by id", async () => {
      const mockUser = {
        id: 1,
        email: "<EMAIL>",
        first_name: "User",
        last_name: "One",
        is_active: true,
        is_verified: true,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      }

      mockApiClient.get.mockResolvedValue(mockUser)

      const result = await usersApi.get(1)

      expect(mockApiClient.get).toHaveBeenCalledWith("/users/1")
      expect(result).toEqual(mockUser)
    })

    it("should create user", async () => {
      const mockUser = {
        id: 2,
        email: "<EMAIL>",
        first_name: "New",
        last_name: "User",
        is_active: true,
        is_verified: false,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      }

      mockApiClient.post.mockResolvedValue(mockUser)

      const userData = {
        name: "New User",
        email: "<EMAIL>",
        password: "password123",
      }

      const result = await usersApi.create(userData)

      expect(mockApiClient.post).toHaveBeenCalledWith("/users", userData)
      expect(result).toEqual(mockUser)
    })

    it("should update user", async () => {
      const mockUser = {
        id: 1,
        email: "<EMAIL>",
        first_name: "Updated",
        last_name: "User",
        is_active: true,
        is_verified: true,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T01:00:00Z",
      }

      mockApiClient.put.mockResolvedValue(mockUser)

      const updateData = {
        email: "<EMAIL>",
        first_name: "Updated",
      }

      const result = await usersApi.update(1, updateData)

      expect(mockApiClient.put).toHaveBeenCalledWith("/users/1", updateData)
      expect(result).toEqual(mockUser)
    })

    it("should delete user", async () => {
      mockApiClient.delete.mockResolvedValue({
        message: "User deleted successfully",
      })

      const result = await usersApi.delete(1)

      expect(mockApiClient.delete).toHaveBeenCalledWith("/users/1")
      expect(result).toEqual({ message: "User deleted successfully" })
    })
  })

  describe("Components API", () => {
    it("should list components", async () => {
      const mockResponse = {
        items: [
          {
            id: 1,
            name: "Test Component",
            manufacturer: "Test Manufacturer",
            part_number: "TC-001",
            category_id: 1,
            type_id: 1,
            specifications: { voltage: "5V" },
            is_preferred: false,
            stock_status: "in_stock",
            current_stock: 100,
            unit_price: 1.5,
            created_at: "2023-01-01T00:00:00Z",
            updated_at: "2023-01-01T00:00:00Z",
          },
        ],
        total: 1,
        page: 1,
        size: 10,
        pages: 1,
        has_next: false,
        has_prev: false,
      }

      mockApiClient.get.mockResolvedValue(mockResponse)

      const result = await componentsApi.list({ page: 1, size: 10 })

      expect(mockApiClient.get).toHaveBeenCalledWith("/components", {
        params: { page: 1, size: 10 },
      })
      expect(result).toEqual(mockResponse)
    })

    it("should create component", async () => {
      const mockComponent = {
        id: 1,
        name: "New Component",
        manufacturer: "New Manufacturer",
        part_number: "NC-001",
        category_id: 1,
        type_id: 1,
        specifications: { voltage: "3.3V" },
        is_preferred: false,
        stock_status: "in_stock",
        current_stock: 50,
        unit_price: 2.0,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2023-01-01T00:00:00Z",
      }

      mockApiClient.post.mockResolvedValue(mockComponent)

      const componentData = {
        name: "New Component",
        manufacturer: "New Manufacturer",
        model_number: "NC-001",
        category_id: 1,
        component_type_id: 1,
        specifications: {
          electrical: { voltage: "3.3V" },
        },
        stock_status: "available",
        unit_price: 2.0,
      }

      const result = await componentsApi.create(componentData)

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/components",
        componentData
      )
      expect(result).toEqual(mockComponent)
    })

    it("should search components", async () => {
      const mockResponse = {
        results: [
          {
            id: 1,
            name: "Resistor 1k",
            manufacturer: "Test Manufacturer",
            part_number: "R-1K",
            relevance_score: 0.95,
          },
        ],
        total: 1,
        query: "resistor",
        filters_applied: ["category_id"],
      }

      mockApiClient.post.mockResolvedValue(mockResponse)

      const searchCriteria = {
        query: "resistor",
        category_ids: [1],
        sort_by: "name",
        sort_order: "asc" as const,
      }

      const result = await componentsApi.search(searchCriteria)

      expect(mockApiClient.post).toHaveBeenCalledWith(
        "/components/search",
        searchCriteria
      )
      expect(result).toEqual(mockResponse)
    })
  })

  describe("Health API", () => {
    it("should check health", async () => {
      const mockResponse = {
        status: "healthy",
        timestamp: "2023-01-01T00:00:00Z",
        version: "1.0.0",
        checks: {
          database: {
            status: "pass",
            message: "Database is healthy",
            duration_ms: 10,
          },
          cache: {
            status: "pass",
            message: "Cache is healthy",
            duration_ms: 5,
          },
        },
      }

      mockApiClient.get.mockResolvedValue(mockResponse)

      const result = await healthApi.check()

      expect(mockApiClient.get).toHaveBeenCalledWith("/health")
      expect(result).toEqual(mockResponse)
    })

    it("should handle unhealthy status", async () => {
      const mockResponse = {
        status: "unhealthy",
        timestamp: "2023-01-01T00:00:00Z",
        version: "1.0.0",
        checks: {
          database: {
            status: "fail",
            message: "Database connection failed",
            duration_ms: 5000,
          },
        },
      }

      mockApiClient.get.mockResolvedValue(mockResponse)

      const result = await healthApi.check()

      expect(result.status).toBe("unhealthy")
      expect(result.checks.database.status).toBe("fail")
    })
  })

  describe("Error Handling", () => {
    it("should handle network errors", async () => {
      const networkError = new Error("Network Error")
      mockApiClient.get.mockRejectedValue(networkError)

      await expect(usersApi.list()).rejects.toThrow("Network Error")
    })

    it("should handle API errors", async () => {
      const apiError = {
        response: {
          status: 400,
          data: {
            detail: "Validation error",
            code: "VALIDATION_ERROR",
          },
        },
      }
      mockApiClient.post.mockRejectedValue(apiError)

      await expect(
        usersApi.create({
          name: "",
          email: "invalid-email",
          password: "short",
        })
      ).rejects.toEqual(apiError)
    })

    it("should handle 404 errors", async () => {
      const notFoundError = {
        response: {
          status: 404,
          data: {
            detail: "User not found",
            code: "NOT_FOUND",
          },
        },
      }
      mockApiClient.get.mockRejectedValue(notFoundError)

      await expect(usersApi.get(999)).rejects.toEqual(notFoundError)
    })

    it("should handle 401 unauthorized errors", async () => {
      const unauthorizedError = {
        response: {
          status: 401,
          data: {
            detail: "Authentication required",
            code: "UNAUTHORIZED",
          },
        },
      }
      mockApiClient.getCurrentUser.mockRejectedValue(unauthorizedError)

      await expect(authApi.getCurrentUser()).rejects.toEqual(unauthorizedError)
    })
  })
})
