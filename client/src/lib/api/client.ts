/**
 * API Client for Ultimate Electrical Designer
 *
 * Centralized Axios-based HTTP client with request/response interceptors
 * for authentication, error handling, and consistent API communication.
 */

import type {
  LoginRequest,
  LoginResponse,
  LogoutResponse,
  PasswordChangeRequest,
  PasswordChangeResponse,
  PasswordResetConfirm,
  PasswordResetRequest,
  PasswordResetResponse,
  RegisterRequest,
  RegisterResponse,
  UserRead,
} from "./types/auth"

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios"

// API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
const API_VERSION = "v1"
const API_TIMEOUT = 10000 // 10 seconds

// Error response interface
export interface APIError {
  message: string
  code: string
  details?: Record<string, unknown>
}

class APIClient {
  private client: AxiosInstance
  private authToken: string | null = null

  constructor() {
    this.client = axios.create({
      baseURL: `${API_BASE_URL}/api/${API_VERSION}`,
      timeout: API_TIMEOUT,
      headers: {
        "Content-Type": "application/json",
      },
    })

    // If MSW browser worker is initializing, wait for it before first request to avoid race
    if (typeof window !== "undefined" && (window as any).__msw_ready) {
      ;(window as any).__msw_ready.catch(() => void 0)
    }

    this.setupInterceptors()
  }

  /**
   * Set up request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor - add auth token if available
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token to requests if available
        if (this.authToken) {
          config.headers.Authorization = `Bearer ${this.authToken}`
        }

        // Add request ID for tracing
        config.headers["X-Request-ID"] = crypto.randomUUID()

        return config
      },
      (error) => {
        return Promise.reject(this.handleError(error))
      }
    )

    // Response interceptor - handle errors and format responses
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        return response
      },
      (error) => {
        // Handle 401 Unauthorized - clear auth state
        if (error.response?.status === 401) {
          this.clearAuthToken()
          // Emit event for auth store to handle logout
          window.dispatchEvent(new CustomEvent("auth:unauthorized"))
        }

        // Handle network errors
        if (!error.response) {
          const networkError: APIError = {
            message: "Network error - please check your connection",
            code: "NETWORK_ERROR",
            details: { originalError: error.message },
          }
          return Promise.reject(networkError)
        }

        return Promise.reject(this.handleError(error))
      }
    )
  }

  /**
   * Standardize error handling across the application
   */
  private handleError(error: any): APIError {
    // If it's already our standardized error, return it
    if (error.code && error.message) {
      return error as APIError
    }

    // Handle Axios errors
    if (error.response) {
      const { data, status } = error.response

      // Backend validation errors
      if (status === 422 && data.detail) {
        return {
          message: Array.isArray(data.detail)
            ? data.detail.map((d: any) => d.msg).join(", ")
            : data.detail,
          code: "VALIDATION_ERROR",
          details: data.detail,
        }
      }

      // Other HTTP errors
      return {
        message: data.message || data.detail || `HTTP ${status} Error`,
        code: `HTTP_${status}`,
        details: data,
      }
    }

    // Generic error
    return {
      message: error.message || "An unexpected error occurred",
      code: "UNKNOWN_ERROR",
      details: { originalError: error },
    }
  }

  /**
   * Set authentication token for requests
   */
  setAuthToken(token: string): void {
    this.authToken = token
  }

  /**
   * Internal helper to perform a request and validate the response via Zod
   */
  async _requestWithValidation<T>(
    requestFn: () => Promise<AxiosResponse<any, any> | { data: any }>,
    schema?: { parse: (data: unknown) => T }
  ): Promise<T> {
    const response = await requestFn()
    const data = (response as any).data ?? response
    if (!schema) return data as T
    try {
      return schema.parse(data)
    } catch (err: any) {
      const error: APIError = {
        message: "Response validation failed",
        code: "SCHEMA_VALIDATION_ERROR",
        details: { issues: err?.issues, data },
      }
      throw error
    }
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    this.authToken = null
  }

  /**
   * Get current auth token
   */
  getAuthToken(): string | null {
    return this.authToken
  }

  // Auth endpoints
  async login(
    credentials: LoginRequest,
    schema?: { parse: (data: unknown) => LoginResponse }
  ): Promise<LoginResponse> {
    return this._requestWithValidation(
      () => this.client.post("/auth/login", credentials),
      schema
    )
  }

  async register(
    userData: RegisterRequest,
    schema?: { parse: (data: unknown) => RegisterResponse }
  ): Promise<RegisterResponse> {
    return this._requestWithValidation(
      () => this.client.post("/auth/register", userData),
      schema
    )
  }

  async logout(schema?: {
    parse: (data: unknown) => LogoutResponse
  }): Promise<LogoutResponse> {
    return this._requestWithValidation(
      () => this.client.post("/auth/logout"),
      schema
    )
  }

  async getCurrentUser(schema?: {
    parse: (data: unknown) => UserRead
  }): Promise<UserRead> {
    return this._requestWithValidation(
      () => this.client.get("/auth/me"),
      schema
    )
  }

  async updateCurrentUser(
    userData: Partial<UserRead>,
    schema?: { parse: (data: unknown) => UserRead }
  ): Promise<UserRead> {
    return this._requestWithValidation(
      () => this.client.put("/auth/me", userData),
      schema
    )
  }

  async changePassword(
    data: PasswordChangeRequest,
    schema?: { parse: (data: unknown) => PasswordChangeResponse }
  ): Promise<PasswordChangeResponse> {
    return this._requestWithValidation(
      () => this.client.post("/auth/change-password", data),
      schema
    )
  }

  async requestPasswordReset(
    data: PasswordResetRequest,
    schema?: { parse: (data: unknown) => PasswordResetResponse }
  ): Promise<PasswordResetResponse> {
    return this._requestWithValidation(
      () => this.client.post("/auth/password-reset", data),
      schema
    )
  }

  async confirmPasswordReset(
    data: PasswordResetConfirm,
    schema?: { parse: (data: unknown) => PasswordResetResponse }
  ): Promise<PasswordResetResponse> {
    return this._requestWithValidation(
      () => this.client.post("/auth/password-reset/confirm", data),
      schema
    )
  }

  // Generic request methods for other API calls
  async get<T = any>(
    url: string,
    config?: AxiosRequestConfig,
    schema?: { parse: (data: unknown) => T }
  ): Promise<T> {
    return this._requestWithValidation(
      () => this.client.get(url, config),
      schema
    )
  }

  async post<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
    schema?: { parse: (data: unknown) => T }
  ): Promise<T> {
    return this._requestWithValidation(
      () => this.client.post(url, data, config),
      schema
    )
  }

  async put<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
    schema?: { parse: (data: unknown) => T }
  ): Promise<T> {
    return this._requestWithValidation(
      () => this.client.put(url, data, config),
      schema
    )
  }

  async delete<T = any>(
    url: string,
    config?: AxiosRequestConfig,
    schema?: { parse: (data: unknown) => T }
  ): Promise<T> {
    return this._requestWithValidation(
      () => this.client.delete(url, config),
      schema
    )
  }

  async patch<T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig,
    schema?: { parse: (data: unknown) => T }
  ): Promise<T> {
    return this._requestWithValidation(
      () => this.client.patch(url, data, config),
      schema
    )
  }
}

// Create singleton instance
export const apiClient = new APIClient()

// Export for testing and advanced usage
export default apiClient
