// Central API types and schemas used across hooks and stores

// Basic list/query params used by hooks
export type ListQueryParams = {
  page?: number
  page_size?: number
  search?: string
  ordering?: string
  [key: string]: unknown
}

// Generic paginated response
export type PaginatedResponse<T> = {
  items: T[]
  total: number
  page: number
  page_size: number
}

// Connection Quality enum and schema used by networkStatusStore and tests
export enum ConnectionQualityEnum {
  Offline = "Offline",
  Slow = "Slow",
  Moderate = "Moderate",
  Fast = "Fast",
}

// Lightweight enum-like schema to match usage: connectionQualitySchema.enum.X
export const connectionQualitySchema = {
  enum: ConnectionQualityEnum,
} as const

// RBAC minimal types used by useRbac hooks
export type UserRoleCreate = {
  name: string
  description?: string
}

export type UserRoleUpdate = Partial<UserRoleCreate>

export type UserRoleAssignmentCreate = {
  user_id: number
  role_id: number
  context?: string | null
}

export type UserRoleAssignmentUpdate = Partial<UserRoleAssignmentCreate>
