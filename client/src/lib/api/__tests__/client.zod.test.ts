/**
 * API Client Zod Validation - TDD
 */
import { beforeEach, describe, expect, it, vi } from "vitest"
import { z } from "zod"

// Prepare axios mock before importing client
const mockAxios = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
  patch: vi.fn(),
  interceptors: { request: { use: vi.fn() }, response: { use: vi.fn() } },
}

vi.mock("axios", () => ({
  default: { create: vi.fn(() => mockAxios) },
}))

let apiClient: any
beforeEach(async () => {
  // Import after mocking
  const mod = await import("../client")
  apiClient = (mod as any).apiClient
})

describe("APIClient Zod response validation", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it("validates successful response against schema", async () => {
    const schema = z.object({ a: z.number() })
    ;(mockAxios.get as any).mockResolvedValue({ data: { a: 1 } })

    const result = await (apiClient as any)._requestWithValidation(
      () => mockAxios.get("/ok"),
      schema
    )

    expect(result).toEqual({ a: 1 })
  })

  it("throws standardized error when validation fails", async () => {
    const schema = z.object({ a: z.number() })
    ;(mockAxios.get as any).mockResolvedValue({ data: { a: "x" } })

    await expect(
      (apiClient as any)._requestWithValidation(
        () => mockAxios.get("/bad"),
        schema
      )
    ).rejects.toMatchObject({
      code: "SCHEMA_VALIDATION_ERROR",
    })
  })
})
