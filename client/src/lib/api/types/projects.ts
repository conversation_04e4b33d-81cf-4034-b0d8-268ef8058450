/**
 * Project management types
 *
 * These types correspond to the backend schemas defined in:
 * server/src/core/schemas/general/project_schemas.py
 * server/src/core/schemas/general/project_member_schemas.py
 */

import { UserRead } from "./auth"
import { PaginatedResponse, StatsResponse, TimestampMixin } from "./common"

// Project status enum matching backend ProjectStatus
export enum ProjectStatus {
  DRAFT = "draft",
  ACTIVE = "active",
  ON_HOLD = "on_hold",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  ARCHIVED = "archived",
}

// Base project schema matching ProjectBaseSchema
export interface ProjectBase {
  name: string
  description?: string
  status: ProjectStatus
  client?: string
  location?: string
  is_offline: boolean
  database_url?: string
  start_date?: string
  end_date?: string
  budget?: number
  currency?: string
  client_contact?: string
  project_manager_id?: number
  metadata?: Record<string, unknown>
}

// Project creation matching ProjectCreateSchema
export interface ProjectCreate extends ProjectBase {
  name: string
  project_number?: string
}

// Project update matching ProjectUpdateSchema
export interface ProjectUpdate {
  name?: string
  description?: string
  status?: ProjectStatus
  client_name?: string
  client_contact?: string
  location?: string
  start_date?: string
  end_date?: string
  budget?: number
  currency?: string
  project_manager_id?: number
  metadata?: Record<string, unknown>
}

// Project member role types
export interface ProjectUserRole {
  id: number
  name: string
  description?: string
  permissions: string[]
}

// Project member matching ProjectMemberReadSchema
export interface ProjectMember extends TimestampMixin {
  id: number
  project_id: number
  user_id: number
  role_id: number
  is_active: boolean
  expires_at?: string
  user: UserRead
  role: ProjectUserRole
}

// Project member creation matching ProjectMemberCreateSchema
export interface ProjectMemberCreate {
  user_id: number
  role_id: number
  expires_at?: string
}

// Project member update matching ProjectMemberUpdateSchema
export interface ProjectMemberUpdate {
  role_id?: number
  is_active?: boolean
  expires_at?: string
}

// Project read matching ProjectReadSchema
export interface ProjectRead extends ProjectBase, TimestampMixin {
  id: number
  project_number: string
  members: ProjectMember[]
}

// Project summary matching ProjectSummarySchema
export interface ProjectSummary {
  id: number
  name: string
  project_number: string
  status: ProjectStatus
  client?: string
  location?: string
  start_date?: string
  end_date?: string
  member_count: number
  task_count: number
  created_at: string
  updated_at: string
}

// Project list parameters
export interface ProjectListParams {
  page?: number
  size?: number
  search?: string
  status?: ProjectStatus
  client?: string
  location?: string
  project_manager_id?: number
  start_date_after?: string
  start_date_before?: string
  end_date_after?: string
  end_date_before?: string
  is_offline?: boolean
  ordering?: string
}

// Project search and filtering
export interface ProjectSearchParams {
  query?: string
  status?: ProjectStatus[]
  client?: string
  location?: string
  project_manager_id?: number
  budget_min?: number
  budget_max?: number
  start_date_after?: string
  start_date_before?: string
  end_date_after?: string
  end_date_before?: string
  is_offline?: boolean
  has_members?: boolean
}

export interface ProjectAdvancedSearch {
  filters: ProjectSearchParams
  sort_by?:
    | "name"
    | "project_number"
    | "status"
    | "start_date"
    | "end_date"
    | "created_at"
  sort_order?: "asc" | "desc"
  include_archived?: boolean
}

// Project statistics
export interface ProjectStats extends StatsResponse {
  total_projects: number
  active_projects: number
  completed_projects: number
  draft_projects: number
  on_hold_projects: number
  cancelled_projects: number
  archived_projects: number
  by_status: Record<ProjectStatus, number>
  by_client: Record<string, number>
  average_duration_days?: number
  total_budget?: number
}

// Project bulk operations
export interface ProjectBulkUpdate {
  project_ids: number[]
  updates: ProjectUpdate
}

export interface ProjectBulkStatusUpdate {
  project_ids: number[]
  status: ProjectStatus
  reason?: string
}

// Project member management
export interface ProjectMemberListParams {
  project_id: number
  is_active?: boolean
  role_id?: number
}

export interface ProjectMemberBulkAdd {
  project_id: number
  members: ProjectMemberCreate[]
}

export interface ProjectMemberBulkUpdate {
  project_id: number
  member_ids: number[]
  updates: ProjectMemberUpdate
}

// Project templates and phases
export interface ProjectTemplate {
  id: number
  name: string
  description?: string
  default_phases: ProjectPhase[]
  default_roles: ProjectUserRole[]
  metadata?: Record<string, unknown>
}

export interface ProjectPhase {
  id: number
  name: string
  description?: string
  order: number
  estimated_duration_days?: number
  dependencies?: number[]
  deliverables?: string[]
}

// Project import/export
export interface ProjectImportData {
  name: string
  description?: string
  project_number?: string
  client?: string
  location?: string
  start_date?: string
  end_date?: string
  budget?: number
  members?: Array<{
    user_email: string
    role_name: string
  }>
}

export interface ProjectExportParams {
  format: "csv" | "xlsx" | "json"
  include_members?: boolean
  include_tasks?: boolean
  status_filter?: ProjectStatus[]
  date_range?: {
    start: string
    end: string
  }
}

// Project collaboration
export interface ProjectActivity extends TimestampMixin {
  id: number
  project_id: number
  user_id: number
  action: string
  resource_type?: string
  resource_id?: number
  description: string
  metadata?: Record<string, unknown>
}

export interface ProjectNotification {
  id: number
  project_id: number
  user_id: number
  type: string
  title: string
  message: string
  is_read: boolean
  created_at: string
}

// Paginated responses
export interface ProjectPaginatedResponse
  extends PaginatedResponse<ProjectRead> {}
export interface ProjectSummaryPaginatedResponse
  extends PaginatedResponse<ProjectSummary> {}
export interface ProjectMemberPaginatedResponse
  extends PaginatedResponse<ProjectMember> {}
export interface ProjectActivityPaginatedResponse
  extends PaginatedResponse<ProjectActivity> {}
