/**
 * Authorization and RBAC types
 *
 * These types correspond to the backend schemas defined in:
 * server/src/core/schemas/general/user_role_schemas.py
 * server/src/core/schemas/general/permission_schemas.py
 */

import { PaginatedResponse, TimestampMixin } from "./common"

// Role types matching backend schemas
export interface Role extends TimestampMixin {
  id: number
  name: string
  description?: string
  is_system_role: boolean
  is_active: boolean
  permissions?: string
  parent_role_id?: number
  priority: number
  notes?: string
  is_deleted: boolean
  deleted_at?: string | null
  deleted_by_user_id?: number | null
}

export interface RoleCreate {
  name: string
  description?: string
  is_system_role?: boolean
  is_active?: boolean
  permissions?: string
  parent_role_id?: number
  priority?: number
  notes?: string
}

export interface RoleUpdate {
  name?: string
  description?: string
  is_system_role?: boolean
  is_active?: boolean
  permissions?: string
  parent_role_id?: number
  priority?: number
  notes?: string
}

// Permission types matching backend schemas
export interface Permission extends TimestampMixin {
  id: number
  name: string
  resource: string
  action: string
  description?: string
  is_system_permission: boolean
  is_active: boolean
}

export interface PermissionCreate {
  name: string
  resource: string
  action: string
  description?: string
  is_system_permission?: boolean
}

export interface PermissionUpdate {
  name?: string
  description?: string
  is_active?: boolean
}

// Role assignment types matching backend schemas
export interface UserRoleAssignment extends TimestampMixin {
  id: number
  name: string
  user_id: number
  role_id: number
  assigned_by_user_id?: number
  assigned_at: string
  expires_at?: string
  is_active: boolean
  assignment_context?: string
  notes?: string
  is_deleted: boolean
  deleted_at?: string | null
  deleted_by_user_id?: number | null
  is_expired: boolean
}

export interface UserRoleAssignmentCreate {
  name: string
  user_id: number
  role_id: number
  assigned_by_user_id?: number
  expires_at?: string
  is_active?: boolean
  assignment_context?: string
  notes?: string
}

export interface UserRoleAssignmentUpdate {
  expires_at?: string
  is_active?: boolean
  assignment_context?: string
  notes?: string
}

// Role permission types
export interface RolePermission extends TimestampMixin {
  id: number
  name: string
  role_id: number
  permission_id: number
  granted_by_user_id?: number
  granted_at: string
  is_active: boolean
  grant_context?: string
}

// Extended user types with RBAC information
export interface UserWithRoles {
  id: number
  name: string
  email: string
  is_superuser: boolean
  is_active: boolean
  role?: string | null
  roles: Role[]
  permissions: string[]
  last_login?: string | null
  created_at: string
  updated_at: string
}

// Permission check types
export interface PermissionCheck {
  user_id: number
  resource: string
  action: string
  has_permission: boolean
}

// Role hierarchy types
export interface RoleHierarchy {
  id: number
  name: string
  description?: string
  priority: number
  parent_role_id?: number
  child_roles: RoleHierarchy[]
}

// Permission grouping types
export interface PermissionsByResource {
  resource: string
  permissions: Permission[]
}

// User permissions summary
export interface UserPermissionsSummary {
  user_id: number
  permissions: string[]
  roles: string[]
  is_superuser: boolean
}

// Paginated responses
export interface RolePaginatedResponse extends PaginatedResponse<Role> {}
export interface PermissionPaginatedResponse
  extends PaginatedResponse<Permission> {}
export interface UserRoleAssignmentPaginatedResponse
  extends PaginatedResponse<UserRoleAssignment> {}

// Form types for frontend components
export interface RoleFormData {
  name: string
  description?: string
  priority?: number
  notes?: string
}

export interface PermissionFormData {
  name: string
  resource: string
  action: string
  description?: string
}

export interface UserRoleAssignmentFormData {
  user_id: number
  role_id: number
  expires_at?: string
  assignment_context?: string
}

// Authorization state for stores
export interface AuthorizationState {
  roles: Role[]
  permissions: Permission[]
  userPermissions: string[]
  isLoadingRoles: boolean
  isLoadingPermissions: boolean
}

// Authorization actions for stores
export interface AuthorizationActions {
  setRoles: (roles: Role[]) => void
  setPermissions: (permissions: Permission[]) => void
  setUserPermissions: (permissions: string[]) => void
  addRole: (role: Role) => void
  updateRole: (role: Role) => void
  removeRole: (roleId: number) => void
  addPermission: (permission: Permission) => void
  updatePermission: (permission: Permission) => void
  removePermission: (permissionId: number) => void
  setLoadingRoles: (loading: boolean) => void
  setLoadingPermissions: (loading: boolean) => void
  clearAuthorizationData: () => void
}

// Permission checking utility types
export type ResourceAction = `${string}.${string}`

export interface PermissionCheckResult {
  resource: string
  action: string
  allowed: boolean
  reason?: string
}
