import { PaginatedResponse, StatsResponse, TimestampMixin } from "./common"

/**
 * User management types
 *
 * These types correspond to the backend schemas defined in:
 * server/src/core/schemas/general/user_schemas.py
 *
 * Re-exports auth types for user management operations
 */

// Re-export user types from auth module for consistency
export type {
  UserRead,
  UserCreate,
  UserUpdate,
  UserSummary,
  UserPaginatedResponse,
  UserListParams,
  UserPreference,
  UserPreferenceCreate,
  UserPreferenceUpdate,
  UserActivityLog,
  UserSession,
} from "./auth"

// User role types for RBAC
export interface UserRole extends TimestampMixin {
  id: number
  name: string
  description?: string
  permissions: string[]
  is_active: boolean
}

export interface UserRoleCreate {
  name: string
  description?: string
  permissions?: string[]
  is_active?: boolean
}

export interface UserRoleUpdate {
  name?: string
  description?: string
  permissions?: string[]
  is_active?: boolean
}

// User role assignment
export interface UserRoleAssignment extends TimestampMixin {
  id: number
  user_id: number
  role_id: number
  context?: string | null
  is_active: boolean
  expires_at?: string | null
  assigned_by: number
}

export interface UserRoleAssignmentCreate {
  user_id: number
  role_id: number
  context?: string | null
  expires_at?: string | null
}

export interface UserRoleAssignmentUpdate {
  is_active?: boolean
  expires_at?: string | null
}

// User statistics
export interface UserStats extends StatsResponse {
  total_users: number
  active_users: number
  inactive_users: number
  superusers: number
  recent_logins: number
  never_logged_in: number
}

// User search and filtering
export interface UserSearchParams {
  query?: string
  is_active?: boolean
  is_superuser?: boolean
  role?: string
  created_after?: string
  created_before?: string
  last_login_after?: string
  last_login_before?: string
}

export interface UserAdvancedSearch {
  filters: UserSearchParams
  sort_by?: "name" | "email" | "created_at" | "last_login"
  sort_order?: "asc" | "desc"
  include_inactive?: boolean
}

// User bulk operations
export interface UserBulkCreate {
  users: import("./auth").UserCreate[]
  send_welcome_email?: boolean
  default_role?: string
}

export interface UserBulkUpdate {
  user_ids: number[]
  updates: import("./auth").UserUpdate
}

export interface UserBulkDelete {
  user_ids: number[]
  soft_delete?: boolean
}

// User import/export
export interface UserImportData {
  name: string
  email: string
  role?: string
  is_active?: boolean
  metadata?: Record<string, unknown>
}

export interface UserImportResult {
  imported: number
  failed: number
  errors: Array<{
    row: number
    email: string
    errors: string[]
  }>
}

export interface UserExportParams {
  format: "csv" | "xlsx" | "json"
  include_inactive?: boolean
  fields?: string[]
  filters?: UserSearchParams
}

// User profile management
export interface UserProfile {
  id: number
  name: string
  email: string
  avatar_url?: string
  bio?: string
  location?: string
  website?: string
  phone?: string
  timezone?: string
  language?: string
  preferences: Record<string, unknown>
  professional_info?: {
    license?: string
    company?: string
    job_title?: string
    years_experience?: number
    specializations?: string[]
    certifications?: string[]
  }
}

export interface UserProfileUpdate {
  name?: string
  avatar_url?: string
  bio?: string
  location?: string
  website?: string
  phone?: string
  timezone?: string
  language?: string
  professional_info?: {
    license?: string
    company?: string
    job_title?: string
    years_experience?: number
    specializations?: string[]
    certifications?: string[]
  }
}

// User notification preferences
export interface UserNotificationPreferences {
  email_notifications: boolean
  push_notifications: boolean
  sms_notifications: boolean
  notification_types: {
    project_updates: boolean
    task_assignments: boolean
    system_alerts: boolean
    security_alerts: boolean
    marketing: boolean
  }
  frequency: "immediate" | "daily" | "weekly" | "never"
}

// User security settings
export interface UserSecuritySettings {
  two_factor_enabled: boolean
  backup_codes_generated: boolean
  trusted_devices: Array<{
    id: string
    name: string
    last_used: string
    created_at: string
  }>
  active_sessions: Array<{
    id: string
    ip_address: string
    user_agent: string
    last_activity: string
    is_current: boolean
  }>
}

// Paginated responses
export interface UserRolePaginatedResponse
  extends PaginatedResponse<UserRole> {}
export interface UserRoleAssignmentPaginatedResponse
  extends PaginatedResponse<UserRoleAssignment> {}
export interface UserActivityLogPaginatedResponse
  extends PaginatedResponse<import("./auth").UserActivityLog> {}
export interface UserSessionPaginatedResponse
  extends PaginatedResponse<import("./auth").UserSession> {}
