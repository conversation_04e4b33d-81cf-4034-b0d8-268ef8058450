/**
 * Common API types and interfaces
 *
 * Shared types used across all API domains, matching backend base schemas
 * from server/src/core/schemas/base_schemas.py
 */

// Pagination types matching backend PaginationSchema
export interface PaginationInfo {
  page: number
  size: number
  total: number
  pages: number
}

// Generic paginated response matching backend PaginatedResponseSchema
export interface PaginatedResponse<T> {
  items: T[]
  pagination: PaginationInfo
}

// Timestamp mixin matching backend TimestampMixin
export interface TimestampMixin {
  created_at: string
  updated_at: string | null
}

// API error response structure
export interface APIError {
  message: string
  code: string
  details?: Record<string, unknown>
  field_errors?: Record<string, string[]>
}

// Generic list query parameters
export interface ListQueryParams {
  page?: number
  size?: number
  search?: string
  ordering?: string
  [key: string]: unknown
}

// Generic ID parameter for endpoints
export interface IDParam {
  id: number
}

// Generic response wrapper for operations
export interface OperationResponse<T = unknown> {
  success: boolean
  message: string
  data?: T
}

// Bulk operation result
export interface BulkOperationResult<T> {
  successful: T[]
  failed: Array<{
    item: Partial<T>
    errors: string[]
  }>
  total_processed: number
  success_count: number
  failure_count: number
}

// Validation result for bulk operations
export interface ValidationResult<T> {
  valid: T[]
  invalid: Array<{
    item: Partial<T>
    errors: Record<string, string[]>
  }>
  total_validated: number
  valid_count: number
  invalid_count: number
}

// Search and filter types
export interface SearchParams {
  query?: string
  filters?: Record<string, unknown>
  sort_by?: string
  sort_order?: "asc" | "desc"
}

// Generic statistics response
export interface StatsResponse {
  total: number
  active: number
  inactive: number
  [key: string]: number | string | Record<string, number> | undefined
}

// File upload types
export interface FileUpload {
  file: File
  filename: string
  content_type: string
}

export interface FileUploadResponse {
  filename: string
  url: string
  size: number
  content_type: string
  uploaded_at: string
}

// Health check response
export interface HealthCheckResponse {
  status: "healthy" | "unhealthy" | "degraded"
  timestamp: string
  version: string
  checks: Record<
    string,
    {
      status: "pass" | "fail" | "warn"
      message?: string
      duration_ms?: number
    }
  >
}

// Connection quality enum matching frontend usage
export enum ConnectionQuality {
  Offline = "Offline",
  Slow = "Slow",
  Moderate = "Moderate",
  Fast = "Fast",
}

// Generic metadata type
export interface Metadata {
  [key: string]: unknown
}

// Soft delete mixin for entities that support soft deletion
export interface SoftDeleteMixin {
  is_deleted: boolean
  deleted_at: string | null
}

// Version tracking for entities
export interface VersionMixin {
  version: string
  version_notes?: string
}

// Audit trail information
export interface AuditMixin {
  created_by?: number
  updated_by?: number
  created_by_name?: string
  updated_by_name?: string
}

// Generic entity base combining common mixins
export interface BaseEntity extends TimestampMixin {
  id: number
}

// Entity with soft delete support
export interface SoftDeleteEntity extends BaseEntity, SoftDeleteMixin {}

// Entity with version tracking
export interface VersionedEntity extends BaseEntity, VersionMixin {}

// Entity with full audit trail
export interface AuditedEntity extends BaseEntity, AuditMixin {}

// Complete entity with all mixins
export interface FullEntity
  extends BaseEntity,
    SoftDeleteMixin,
    VersionMixin,
    AuditMixin {}
