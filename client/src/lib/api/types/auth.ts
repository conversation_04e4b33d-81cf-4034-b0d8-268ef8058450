/**
 * Authentication and user management types
 *
 * These types correspond to the backend schemas defined in:
 * server/src/core/schemas/general/user_schemas.py
 */

import { PaginatedResponse, TimestampMixin } from "./common"

// Base user information matching UserReadSchema
export interface UserRead extends TimestampMixin {
  id: number
  name: string
  email: string
  is_superuser: boolean
  is_active: boolean
  role?: string | null
  roles?: string[]
  permissions?: string[]
  last_login?: string | null
}

// User creation matching UserCreateSchema
export interface UserCreate {
  name: string
  email: string
  password: string
  is_superuser?: boolean
}

// User update matching UserUpdateSchema
export interface UserUpdate {
  name?: string
  email?: string
  password?: string
}

// User summary matching UserSummarySchema
export interface UserSummary {
  id: number
  name: string
  email: string
  is_active: boolean
  role?: string | null
  last_login?: string | null
  created_at: string
}

// Authentication request matching LoginRequestSchema
export interface LoginRequest {
  username: string // Can be username or email
  password: string
}

// Authentication response matching LoginResponseSchema
export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  user: UserRead
}

// Registration request (extended from backend schema)
export interface RegisterRequest {
  name: string
  email: string
  password: string
  confirm_password: string
  // Professional credentials for electrical designers
  professional_license?: string
  company_name?: string
  job_title?: string
  years_experience?: number
  specializations?: string[]
}

// Registration response
export interface RegisterResponse {
  message: string
  user: UserRead
  requires_verification?: boolean
}

// Logout response matching LogoutResponseSchema
export interface LogoutResponse {
  message: string
  logged_out_at: string
}

// Password change request matching PasswordChangeRequestSchema
export interface PasswordChangeRequest {
  current_password: string
  new_password: string
  confirm_password: string
}

// Password change response matching PasswordChangeResponseSchema
export interface PasswordChangeResponse {
  message: string
  changed_at: string
}

// Password reset request matching PasswordResetRequestSchema
export interface PasswordResetRequest {
  email: string
}

// Password reset confirmation matching PasswordResetConfirmSchema
export interface PasswordResetConfirm {
  token: string
  new_password: string
  confirm_password: string
}

// Password reset response matching PasswordResetResponseSchema
export interface PasswordResetResponse {
  message: string
  reset_at?: string
}

// User preferences matching UserPreferenceReadSchema
export interface UserPreference extends TimestampMixin {
  id: number
  user_id: number
  theme: string
  language: string
  timezone: string
  date_format: string
  time_format: string
  units_system: string
  notifications_enabled: boolean
  auto_save_interval: number
}

// User preference creation matching UserPreferenceCreateSchema
export interface UserPreferenceCreate {
  user_id: number
  theme?: string
  language?: string
  timezone?: string
  date_format?: string
  time_format?: string
  units_system?: string
  notifications_enabled?: boolean
  auto_save_interval?: number
}

// User preference update matching UserPreferenceUpdateSchema
export interface UserPreferenceUpdate {
  theme?: string
  language?: string
  timezone?: string
  date_format?: string
  time_format?: string
  units_system?: string
  notifications_enabled?: boolean
  auto_save_interval?: number
}

// User activity log matching UserActivityLogSchema
export interface UserActivityLog {
  id: number
  user_id: number
  action: string
  resource?: string | null
  ip_address?: string | null
  user_agent?: string | null
  timestamp: string
  success: boolean
  details?: Record<string, unknown> | null
}

// User session matching UserSessionSchema
export interface UserSession extends TimestampMixin {
  id: string
  user_id: number
  ip_address?: string | null
  user_agent?: string | null
  last_activity: string
  expires_at: string
  is_active: boolean
}

// Paginated user responses
export interface UserPaginatedResponse extends PaginatedResponse<UserRead> {}

// Auth state for stores
export interface AuthState {
  user: UserRead | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  lastActivity: string | null
}

// Auth actions for stores
export interface AuthActions {
  setAuth: (user: UserRead, token: string) => void
  updateUser: (user: UserRead) => void
  clearAuth: () => void
  setLoading: (loading: boolean) => void
  initializeAuth: () => void
}

// Form validation types
export interface LoginFormData {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterFormData {
  name: string
  email: string
  password: string
  confirm_password: string
  professional_license?: string
  company_name?: string
  job_title?: string
  years_experience?: number
  specializations?: string[]
  terms_accepted: boolean
}

export interface FormFieldError {
  field: string
  message: string
}

export interface ValidationErrors {
  [key: string]: string | string[]
}

// User list parameters
export interface UserListParams {
  page?: number
  size?: number
  search?: string
  is_active?: boolean
  is_superuser?: boolean
  role?: string
  ordering?: string
}
