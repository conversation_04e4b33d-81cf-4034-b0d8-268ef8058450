import { z } from "zod"

import { emailString } from "@/lib/validation/base"

// Login form schema
export const loginFormSchema = z.object({
  username: z
    .string()
    .trim()
    .min(1, { message: "Email is required (Email or username is required)" })
    .refine((val) => {
      try {
        emailString.parse(val)
        return true
      } catch {
        return false
      }
    }, "Please enter a valid email address"),
  password: z
    .string()
    .min(1, { message: "Password is required" })
    .min(8, { message: "Password must be at least 8 characters long" }),
  remember: z.boolean().optional(),
})

// Registration form schema
export const registrationFormSchema = z
  .object({
    name: z
      .string()
      .trim()
      .min(1, { message: "Full name is required" })
      .min(2, { message: "Full name must be at least 2 characters long" }),
    email: z
      .string()
      .trim()
      .min(1, { message: "Email or username is required" })
      .refine((val) => {
        try {
          emailString.parse(val)
          return true
        } catch {
          return false
        }
      }, "Please enter a valid email address"),
    password: z
      .string()
      .min(1, { message: "Password is required" })
      .min(8, { message: "Password must be at least 8 characters long" }),
    confirm_password: z.string().min(1, {
      message: "Password confirmation is required",
    }),
    professional_license: z
      .string()
      .optional()
      .refine(
        (v) =>
          !v || /^(PE|EIT|P\.E\.|E\.I\.T\.)-?[\w\d]{3,10}$/i.test(v.trim()),
        { message: "Invalid license format" }
      ),
    company_name: z.string().optional(),
    job_title: z.string().optional(),
    years_experience: z
      .union([z.string(), z.number()])
      .optional()
      .transform((val) => {
        if (val === "" || val === null || val === undefined) return undefined
        if (typeof val === "string") {
          const n = Number(val)
          return Number.isNaN(n) ? undefined : n
        }
        return val
      })
      .refine(
        (val) =>
          val === undefined ||
          (typeof val === "number" && val >= 0 && val <= 60),
        {
          message:
            "Years of experience must be a positive number between 0 and 60",
        }
      ),
    // UI uses a single text input; accept string and split on submit
    specializations: z.string().optional(),
    terms_accepted: z.boolean().optional(),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: "Passwords do not match",
    path: ["confirm_password"],
  })
  .refine(
    (data) =>
      !data.professional_license ||
      /^(PE|EIT|P\.E\.|E\.I\.T\.)-?[\w\d]{3,10}$/i.test(
        data.professional_license.trim()
      ),
    {
      message: "Invalid license format. Expected format: PE-12345 or EIT-67890",
      path: ["professional_license"],
    }
  )
