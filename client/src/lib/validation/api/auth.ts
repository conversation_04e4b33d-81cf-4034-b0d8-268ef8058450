/**
 * Zod schemas for Auth API request/response types
 */
import { z } from "zod"

import {
  emailString,
  isoDateString,
  paginatedResponseSchema,
  timestampMixinSchema,
} from "@/lib/validation/base"

export const userReadSchema = timestampMixinSchema.extend({
  id: z.number().int(),
  name: z.string(),
  email: emailString,
  is_superuser: z.boolean(),
  is_active: z.boolean(),
  role: z.string().nullable().optional(),
  roles: z.array(z.string()).optional(),
  permissions: z.array(z.string()).optional(),
  last_login: isoDateString.nullable().optional(),
})

export const loginRequestSchema = z.object({
  username: z.string().min(1),
  password: z.string().min(1),
})

export const loginResponseSchema = z.object({
  access_token: z.string().min(1),
  token_type: z.string().min(1),
  expires_in: z.number().int().positive(),
  user: userReadSchema,
})

export const registerRequestSchema = z.object({
  name: z.string().min(1),
  email: emailString,
  password: z.string().min(8),
  confirm_password: z.string().min(8),
  professional_license: z.string().optional(),
  company_name: z.string().optional(),
  job_title: z.string().optional(),
  years_experience: z.number().int().min(0).optional(),
  specializations: z.array(z.string()).optional(),
})

export const registerResponseSchema = z.object({
  message: z.string(),
  user: userReadSchema,
  requires_verification: z.boolean().optional(),
})

export const logoutResponseSchema = z.object({
  message: z.string(),
  logged_out_at: isoDateString,
})

export const passwordChangeRequestSchema = z.object({
  current_password: z.string().min(1),
  new_password: z.string().min(8),
  confirm_password: z.string().min(8),
})

export const passwordChangeResponseSchema = z.object({
  message: z.string(),
  changed_at: isoDateString,
})

export const passwordResetRequestSchema = z.object({
  email: emailString,
})

export const passwordResetConfirmSchema = z.object({
  token: z.string().min(1),
  new_password: z.string().min(8),
  confirm_password: z.string().min(8),
})

export const passwordResetResponseSchema = z.object({
  message: z.string(),
  reset_at: isoDateString.optional(),
})

export const userPreferenceSchema = timestampMixinSchema.extend({
  id: z.number().int(),
  user_id: z.number().int(),
  theme: z.string(),
  language: z.string(),
  timezone: z.string(),
  date_format: z.string(),
  time_format: z.string(),
  units_system: z.string(),
  notifications_enabled: z.boolean(),
  auto_save_interval: z.number().int(),
})

export const userPreferenceCreateSchema = z.object({
  user_id: z.number().int(),
  theme: z.string().optional(),
  language: z.string().optional(),
  timezone: z.string().optional(),
  date_format: z.string().optional(),
  time_format: z.string().optional(),
  units_system: z.string().optional(),
  notifications_enabled: z.boolean().optional(),
  auto_save_interval: z.number().int().optional(),
})

export const userPreferenceUpdateSchema = z.object({
  theme: z.string().optional(),
  language: z.string().optional(),
  timezone: z.string().optional(),
  date_format: z.string().optional(),
  time_format: z.string().optional(),
  units_system: z.string().optional(),
  notifications_enabled: z.boolean().optional(),
  auto_save_interval: z.number().int().optional(),
})

export const userActivityLogSchema = z.object({
  id: z.number().int(),
  user_id: z.number().int(),
  action: z.string(),
  resource: z.string().nullable().optional(),
  ip_address: z.string().nullable().optional(),
  user_agent: z.string().nullable().optional(),
  timestamp: isoDateString,
  success: z.boolean(),
  details: z.record(z.string(), z.unknown()).nullable().optional(),
})

export const userSessionSchema = timestampMixinSchema.extend({
  id: z.string(),
  user_id: z.number().int(),
  ip_address: z.string().nullable().optional(),
  user_agent: z.string().nullable().optional(),
  last_activity: isoDateString,
  expires_at: isoDateString,
  is_active: z.boolean(),
})

export const userPaginatedResponseSchema =
  paginatedResponseSchema(userReadSchema)

export type LoginResponse = z.infer<typeof loginResponseSchema>
export type UserRead = z.infer<typeof userReadSchema>
