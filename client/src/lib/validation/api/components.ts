/**
 * Component Management API Validation Schemas
 *
 * Comprehensive Zod validation schemas for the electrical component management system.
 * Provides type-safe validation for Components, Categories, and Types across all CRUD operations.
 *
 * Schema Organization:
 * - Base schemas with common field definitions
 * - Create schemas with required fields for new entities
 * - Update schemas with optional fields for modifications
 * - Read schemas with computed fields and relationships
 * - List and pagination schemas for data retrieval
 * - Advanced search and filtering capabilities
 *
 * Features:
 * - Type-safe validation with comprehensive error messages
 * - Hierarchical category support with path computation
 * - Multi-currency pricing validation
 * - Technical specifications and metadata handling
 * - Advanced filtering with range and faceted search
 * - Bulk operations support with validation
 *
 * Technical Implementation:
 * - Zod schema composition and extension patterns
 * - Lazy evaluation for recursive tree structures
 * - Custom validation rules for business logic
 * - Integration with TypeScript for compile-time safety
 *
 * @module ComponentsValidation
 * @version 1.0.0
 * <AUTHOR> Electrical Designer Team
 * @since 2025-01-10
 */
import { z } from "zod"

import {
  isoDateString,
  paginatedResponseSchema,
  timestampMixinSchema,
} from "../base"

/**
 * Physical dimensions schema for electrical components.
 * Supports metric measurements in millimeters for length, width, height, diameter,
 * and weight in kilograms for comprehensive component specifications.
 */
export const componentDimensionsSchema = z.object({
  length_mm: z.number().optional(),
  width_mm: z.number().optional(),
  height_mm: z.number().optional(),
  diameter_mm: z.number().optional(),
  weight_kg: z.number().optional(),
})

/**
 * Technical specifications schema for electrical components.
 * Provides flexible structure for various specification types including
 * electrical, thermal, mechanical, standards compliance, and environmental data.
 */
export const componentSpecificationsSchema = z.object({
  electrical: z.record(z.string(), z.unknown()).optional(),
  thermal: z.record(z.string(), z.unknown()).optional(),
  mechanical: z.record(z.string(), z.unknown()).optional(),
  standards_compliance: z.array(z.string()).optional(),
  environmental: z.record(z.string(), z.unknown()).optional(),
})

/**
 * Base component schema with all core fields for electrical component data.
 * Serves as foundation for create, update, and read schemas with comprehensive
 * field definitions including relationships, pricing, and technical details.
 */
export const componentBaseSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  manufacturer: z.string().optional(),
  model_number: z.string().optional(),
  category_id: z.number().int(),
  component_type_id: z.number().int(),
  specifications: componentSpecificationsSchema.optional(),
  unit_price: z.number().optional(),
  currency: z.string().optional(),
  supplier: z.string().optional(),
  part_number: z.string().optional(),
  weight_kg: z.number().optional(),
  dimensions: componentDimensionsSchema.optional(),
  is_active: z.boolean().optional(),
  is_preferred: z.boolean().optional(),
  stock_status: z.string().optional(),
  version: z.string().optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
})

/**
 * Component creation schema with required fields for new component registration.
 * Enforces mandatory fields: name, category_id, component_type_id for proper
 * component classification and catalog organization.
 */
export const componentCreateSchema = componentBaseSchema.extend({
  name: z.string(),
  category_id: z.number().int(),
  component_type_id: z.number().int(),
})

/**
 * Component update schema with all fields optional for flexible modifications.
 * Allows partial updates to existing components while maintaining data integrity
 * and business rule validation.
 */
export const componentUpdateSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  manufacturer: z.string().optional(),
  model_number: z.string().optional(),
  category_id: z.number().int().optional(),
  component_type_id: z.number().int().optional(),
  specifications: componentSpecificationsSchema.optional(),
  unit_price: z.number().optional(),
  currency: z.string().optional(),
  supplier: z.string().optional(),
  part_number: z.string().optional(),
  weight_kg: z.number().optional(),
  dimensions: componentDimensionsSchema.optional(),
  is_active: z.boolean().optional(),
  is_preferred: z.boolean().optional(),
  stock_status: z.string().optional(),
  version: z.string().optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
})

/**
 * Component read schema with computed fields and relationship data.
 * Includes timestamps, relationship names, and derived fields for complete
 * component information display in user interfaces.
 */
export const componentReadSchema = componentBaseSchema
  .merge(timestampMixinSchema)
  .extend({
    id: z.number().int(),
    full_name: z.string().optional(),
    display_name: z.string().optional(),
    category_name: z.string().optional(),
    type_name: z.string().optional(),
    category_path: z.string().optional(),
  })

// Component summary schema
export const componentSummarySchema = z.object({
  id: z.number().int(),
  name: z.string(),
  manufacturer: z.string(),
  model_number: z.string(),
  component_type_id: z.number().int(),
  category_id: z.number().int(),
  unit_price: z.number().optional(),
  currency: z.string(),
  is_active: z.boolean(),
  is_preferred: z.boolean(),
  stock_status: z.string(),
})

// Filter operators enum schema
export const filterOperatorSchema = z.enum([
  "eq",
  "ne",
  "gt",
  "gte",
  "lt",
  "lte",
  "contains",
  "starts_with",
  "ends_with",
  "in",
  "not_in",
  "between",
  "fuzzy",
  "regex",
  "is_null",
  "is_not_null",
])

// Logical operators enum schema
export const logicalOperatorSchema = z.enum(["and", "or", "not"])

// Range filter schema
export const rangeFilterSchema = z.object({
  min: z.number().optional(),
  max: z.number().optional(),
})

// Advanced filter schema
export const advancedFilterSchema = z.object({
  field: z.string(),
  operator: filterOperatorSchema,
  value: z.unknown().optional(),
  range: rangeFilterSchema.optional(),
})

// Component advanced search schema
export const componentAdvancedSearchSchema = z.object({
  query: z.string().optional(),
  filters: z.array(advancedFilterSchema).optional(),
  specification_filters: z
    .array(
      z.object({
        key: z.string(),
        operator: filterOperatorSchema,
        value: z.unknown().optional(),
        range: rangeFilterSchema.optional(),
      })
    )
    .optional(),
  logical_operator: logicalOperatorSchema.optional(),
  category_ids: z.array(z.number().int()).optional(),
  type_ids: z.array(z.number().int()).optional(),
  manufacturers: z.array(z.string()).optional(),
  is_active: z.boolean().optional(),
  is_preferred: z.boolean().optional(),
  stock_statuses: z.array(z.string()).optional(),
  sort_by: z.string().optional(),
  sort_order: z.enum(["asc", "desc"]).optional(),
})

// Component list parameters schema
export const componentListParamsSchema = z.object({
  page: z.number().int().optional(),
  size: z.number().int().optional(),
  search: z.string().optional(),
  category_id: z.number().int().optional(),
  component_type_id: z.number().int().optional(),
  manufacturer: z.string().optional(),
  is_active: z.boolean().optional(),
  is_preferred: z.boolean().optional(),
  stock_status: z.string().optional(),
  ordering: z.string().optional(),
})

// Bulk operations schemas
export const componentBulkCreateSchema = z.object({
  components: z.array(componentCreateSchema),
  validate_only: z.boolean().optional(),
  skip_duplicates: z.boolean().optional(),
})

// Paginated responses
export const componentPaginatedResponseSchema =
  paginatedResponseSchema(componentReadSchema)
export const componentSummaryPaginatedResponseSchema = paginatedResponseSchema(
  componentSummarySchema
)

/**
 * Base schema for electrical component categories with hierarchical support.
 * Enables multi-level category organization with parent-child relationships
 * for comprehensive component classification and organization.
 */
export const componentCategoryBaseSchema = z.object({
  name: z.string().min(1, "Category name is required").max(255),
  description: z.string().optional(),
  parent_category_id: z.number().int().optional(),
  is_active: z.boolean().optional().default(true),
})

export const componentCategoryCreateSchema = componentCategoryBaseSchema.extend(
  {
    name: z.string().min(1, "Category name is required").max(255),
  }
)

export const componentCategoryUpdateSchema = z.object({
  name: z.string().min(1, "Category name is required").max(255).optional(),
  description: z.string().optional(),
  parent_category_id: z.number().int().optional(),
  is_active: z.boolean().optional(),
})

export const componentCategoryReadSchema = componentCategoryBaseSchema
  .merge(timestampMixinSchema)
  .extend({
    id: z.number().int(),
    full_path: z.string(),
    level: z.number().int(),
    is_root_category: z.boolean(),
    has_children: z.boolean(),
    component_count: z.number().int(),
  })

export const componentCategorySummarySchema = z.object({
  id: z.number().int(),
  name: z.string(),
  description: z.string().optional(),
  parent_category_id: z.number().int().optional(),
  is_active: z.boolean(),
  component_count: z.number().int(),
  child_count: z.number().int(),
})

export const componentCategoryTreeNodeSchema: z.ZodType<{
  id: number
  name: string
  description?: string
  is_active: boolean
  level: number
  component_count: number
  children: Array<{
    id: number
    name: string
    description?: string
    is_active: boolean
    level: number
    component_count: number
    children: any[]
  }>
}> = z.object({
  id: z.number().int(),
  name: z.string(),
  description: z.string().optional(),
  is_active: z.boolean(),
  level: z.number().int(),
  component_count: z.number().int(),
  children: z.array(z.lazy(() => componentCategoryTreeNodeSchema)),
})

export const componentCategoryListParamsSchema = z.object({
  page: z.number().int().optional(),
  size: z.number().int().optional(),
  search: z.string().optional(),
  parent_category_id: z.number().int().optional(),
  is_active: z.boolean().optional(),
  level: z.number().int().optional(),
  ordering: z.string().optional(),
})

/**
 * Base schema for component types with category association and templates.
 * Supports specification templates for standardized component data entry
 * and category-specific type definitions.
 */
export const componentTypeBaseSchema = z.object({
  name: z.string().min(1, "Type name is required").max(255),
  description: z.string().optional(),
  category_id: z.number().int(),
  is_active: z.boolean().optional().default(true),
  specifications_template: z.record(z.string(), z.unknown()).optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
})

export const componentTypeCreateSchema = componentTypeBaseSchema.extend({
  name: z.string().min(1, "Type name is required").max(255),
  category_id: z.number().int(),
})

export const componentTypeUpdateSchema = z.object({
  name: z.string().min(1, "Type name is required").max(255).optional(),
  description: z.string().optional(),
  category_id: z.number().int().optional(),
  is_active: z.boolean().optional(),
  specifications_template: z.record(z.string(), z.unknown()).optional(),
  metadata: z.record(z.string(), z.unknown()).optional(),
})

export const componentTypeReadSchema = componentTypeBaseSchema
  .merge(timestampMixinSchema)
  .extend({
    id: z.number().int(),
    full_name: z.string(),
    category_path: z.string(),
    component_count: z.number().int(),
    has_specifications_template: z.boolean(),
    category_name: z.string().optional(),
  })

export const componentTypeSummarySchema = z.object({
  id: z.number().int(),
  name: z.string(),
  description: z.string().optional(),
  category_id: z.number().int(),
  category_name: z.string(),
  is_active: z.boolean(),
  component_count: z.number().int(),
  has_specifications_template: z.boolean(),
  created_at: isoDateString,
})

export const componentTypeListParamsSchema = z.object({
  page: z.number().int().optional(),
  size: z.number().int().optional(),
  search: z.string().optional(),
  category_id: z.number().int().optional(),
  is_active: z.boolean().optional(),
  has_specifications_template: z.boolean().optional(),
  ordering: z.string().optional(),
})

// Additional paginated responses
export const componentCategoryPaginatedResponseSchema = paginatedResponseSchema(
  componentCategoryReadSchema
)
export const componentCategorySummaryPaginatedResponseSchema =
  paginatedResponseSchema(componentCategorySummarySchema)
export const componentTypePaginatedResponseSchema = paginatedResponseSchema(
  componentTypeReadSchema
)
export const componentTypeSummaryPaginatedResponseSchema =
  paginatedResponseSchema(componentTypeSummarySchema)

// Type exports for easier importing
export type ComponentCreate = z.infer<typeof componentCreateSchema>
export type ComponentRead = z.infer<typeof componentReadSchema>
export type ComponentUpdate = z.infer<typeof componentUpdateSchema>
export type ComponentSummary = z.infer<typeof componentSummarySchema>
export type ComponentAdvancedSearch = z.infer<
  typeof componentAdvancedSearchSchema
>
export type ComponentListParams = z.infer<typeof componentListParamsSchema>

// Component Category types
export type ComponentCategoryCreate = z.infer<
  typeof componentCategoryCreateSchema
>
export type ComponentCategoryRead = z.infer<typeof componentCategoryReadSchema>
export type ComponentCategoryUpdate = z.infer<
  typeof componentCategoryUpdateSchema
>
export type ComponentCategorySummary = z.infer<
  typeof componentCategorySummarySchema
>
export type ComponentCategoryTreeNode = z.infer<
  typeof componentCategoryTreeNodeSchema
>
export type ComponentCategoryListParams = z.infer<
  typeof componentCategoryListParamsSchema
>

// Component Type types
export type ComponentTypeCreate = z.infer<typeof componentTypeCreateSchema>
export type ComponentTypeRead = z.infer<typeof componentTypeReadSchema>
export type ComponentTypeUpdate = z.infer<typeof componentTypeUpdateSchema>
export type ComponentTypeSummary = z.infer<typeof componentTypeSummarySchema>
export type ComponentTypeListParams = z.infer<
  typeof componentTypeListParamsSchema
>
