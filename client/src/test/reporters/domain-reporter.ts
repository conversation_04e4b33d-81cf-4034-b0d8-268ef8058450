import type {
  Reporter,
  SerializedError,
  TestCase,
  TestModule,
  TestRunEndReason,
  TestSuite,
  Vitest,
} from "vitest/node"

import fs from "fs"
import path from "path"

interface CategorizedResults {
  [category: string]: {
    failed: { title: string; file: string }[]
    skipped: { title: string; file: string }[]
  }
}

function isTestCase(test: TestSuite | TestCase): test is TestCase {
  return typeof (test as TestCase).result === "function"
}

export default class DomainReporter implements Reporter {
  private results: CategorizedResults = {}
  private totalTests = 0
  private totalPassed = 0
  private totalFailed = 0
  private totalSkipped = 0

  onInit(_vitest: Vitest) {
    // Initialize reporter
  }

  async onTestRunEnd(
    testModules: readonly TestModule[],
    _unhandledErrors: readonly SerializedError[],
    _reason: TestRunEndReason
  ): Promise<void> {
    for (const mod of testModules) {
      const filePath = mod.moduleId
      const category = this.getCategory(filePath)
      this.results[category] ??= { failed: [], skipped: [] }

      for (const test of mod.children) {
        if (!isTestCase(test)) continue
        const result = test.result()
        if (!result) continue

        this.totalTests++
        if (result.state === "failed") {
          this.totalFailed++
          this.results[category].failed.push({
            title: test.name,
            file: filePath,
          })
        } else if (result.state === "skipped") {
          this.totalSkipped++
          this.results[category].skipped.push({
            title: test.name,
            file: filePath,
          })
        } else {
          this.totalPassed++
        }
      }
    }

    const summary = this.generateSummary()
    const logPath = path.resolve(process.cwd(), "test_output.log")
    fs.writeFileSync(logPath, summary, "utf8")
    console.log(`\n📄 Test summary written to ${logPath}`)
  }

  private getCategory(filePath: string): string {
    if (filePath.includes("/atoms/")) return "Atoms"
    if (filePath.includes("/modules/")) return "Modules"
    if (filePath.includes("/molecules/")) return "Molecules"
    if (filePath.includes("/organisms/")) return "Organisms"
    if (filePath.includes("/templates/")) return "Templates"
    if (filePath.includes("/core/")) return "Core"
    if (filePath.includes("/hooks/")) return "Hooks"
    if (filePath.includes("/lib/")) return "Lib"
    if (filePath.includes("/stores/")) return "Stores"
    if (filePath.includes("/utils/")) return "Utils"
    if (filePath.includes("/integration/")) return "Integration"
    if (filePath.includes("/tests/e2e/")) return "E2E"
    return "Other"
  }

  private generateSummary(): string {
    let out = `===== Condensed Test Summary =====\n\n`
    for (const [cat, { failed, skipped }] of Object.entries(this.results)) {
      out += `## ${cat}\n`
      out += failed.length
        ? `  ❌ Failed Tests (${failed.length}):\n` +
          failed.map((f) => `    - ${f.title} (${f.file})`).join("\n") +
          "\n"
        : `  ✅ No Failed Tests\n`
      out += skipped.length
        ? `  ⚠️ Skipped Tests (${skipped.length}):\n` +
          skipped.map((s) => `    - ${s.title} (${s.file})`).join("\n") +
          "\n"
        : `  ➖ No Skipped Tests\n`
      out += "\n"
    }
    out += `===== Totals =====\n`
    out += `Total Tests: ${this.totalTests}\nPassed: ${this.totalPassed}\nFailed: ${this.totalFailed}\nSkipped: ${this.totalSkipped}\n`
    return out
  }
}
