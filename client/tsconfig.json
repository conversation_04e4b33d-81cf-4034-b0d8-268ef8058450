{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "esModuleInterop": true, "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/lib/*": ["src/lib/*"], "@/types/*": ["src/types/*"], "@/stores/*": ["src/stores/*"], "@tests/*": ["tests/*"]}, "types": ["vitest/globals", "@playwright/test"]}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "vitest.setup.ts", "playwright.setup.ts", "next-env.d.ts", "dist", ".next", "coverage", "out", "public", "**/tests/**", "src/test/reporters"]}