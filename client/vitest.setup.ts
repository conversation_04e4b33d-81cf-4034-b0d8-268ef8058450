import "@testing-library/jest-dom"

import { vi } from "vitest"

// Mock matchMedia with vi.stubGlobal for auto cleanup
vi.stubGlobal(
  "matchMedia",
  vi.fn().mockImplementation((query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }))
)

// Patch HTMLFormElement.prototype.requestSubmit safely
const originalRequestSubmit = HTMLFormElement.prototype.requestSubmit
HTMLFormElement.prototype.requestSubmit = vi.fn(function (
  this: HTMLFormElement,
  submitter?: HTMLElement
) {
  this.submit()
})

// Patch private _doRequestSubmit if it exists
if ("_doRequestSubmit" in HTMLFormElement.prototype) {
  // @ts-ignore
  HTMLFormElement.prototype._doRequestSubmit = vi.fn(function (
    this: HTMLFormElement,
    submitter?: HTMLElement
  ) {
    this.submit()
  })
}

// Mock missing DOM methods for Radix UI compatibility
vi.stubGlobal(
  "ResizeObserver",
  vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))
)

// Use Object.defineProperty with typings for Element prototype mocks
const elementPrototypeMocks: [keyof Element, () => any][] = [
  ["hasPointerCapture", () => false],
  ["setPointerCapture", () => undefined],
  ["releasePointerCapture", () => undefined],
  ["scrollIntoView", () => undefined],
]

elementPrototypeMocks.forEach(([method, impl]) => {
  Object.defineProperty(Element.prototype, method, {
    value: vi.fn(impl),
    writable: true,
    configurable: true,
  })
})

// -------- IndexedDB mocks --------

// In-memory storage for IndexedDB mock
const mockIndexedDBStorage = new Map<string, Map<string, any>>()

const createMockIDBRequest = (result: any = null, error: any = null) => {
  const request = {
    result,
    error,
    readyState: "done" as const,
    source: null,
    transaction: null,
    onsuccess: null as ((event: Event) => void) | null,
    onerror: null as ((event: Event) => void) | null,
    onblocked: null as ((event: Event) => void) | null,
    onupgradeneeded: null as ((event: Event) => void) | null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }

  setTimeout(() => {
    if (request.onsuccess && !error) {
      request.onsuccess(new Event("success"))
    } else if (request.onerror && error) {
      request.onerror(new Event("error"))
    }
  }, 0)

  return request
}

const mockIDBObjectStore = (
  dbName: string = "default-db",
  storeName: string = "default-store"
) => {
  const getStore = () => {
    if (!mockIndexedDBStorage.has(dbName)) {
      mockIndexedDBStorage.set(dbName, new Map())
    }
    return mockIndexedDBStorage.get(dbName)!
  }

  return {
    add: vi.fn().mockImplementation((value: any, key?: any) => {
      const store = getStore()
      const actualKey = key || Date.now().toString()
      if (store.has(actualKey)) {
        return createMockIDBRequest(null, new Error("Key already exists"))
      }
      store.set(actualKey, value)
      return createMockIDBRequest(actualKey)
    }),
    clear: vi.fn().mockImplementation(() => {
      getStore().clear()
      return createMockIDBRequest(undefined)
    }),
    count: vi.fn().mockImplementation(() => {
      return createMockIDBRequest(getStore().size)
    }),
    delete: vi.fn().mockImplementation((key: any) => {
      const store = getStore()
      store.delete(key)
      return createMockIDBRequest(undefined)
    }),
    get: vi.fn().mockImplementation((key: any) => {
      const store = getStore()
      return createMockIDBRequest(store.get(key))
    }),
    getAll: vi.fn().mockImplementation(() => {
      const store = getStore()
      return createMockIDBRequest(Array.from(store.values()))
    }),
    getAllKeys: vi.fn().mockImplementation(() => {
      const store = getStore()
      return createMockIDBRequest(Array.from(store.keys()))
    }),
    getKey: vi.fn().mockImplementation(() => createMockIDBRequest(undefined)),
    put: vi.fn().mockImplementation((value: any, key?: any) => {
      const store = getStore()
      const actualKey = key || Date.now().toString()
      store.set(actualKey, value)
      return createMockIDBRequest(actualKey)
    }),
    openCursor: vi.fn().mockImplementation(() => createMockIDBRequest(null)),
    openKeyCursor: vi.fn().mockImplementation(() => createMockIDBRequest(null)),
    createIndex: vi.fn(),
    deleteIndex: vi.fn(),
    index: vi.fn(),
    indexNames: [],
    keyPath: null,
    name: storeName,
    transaction: null,
    autoIncrement: false,
  }
}

const mockIDBTransaction = (dbName: string = "default-db") => ({
  abort: vi.fn(),
  commit: vi.fn(),
  objectStore: vi
    .fn()
    .mockImplementation((storeName: string) =>
      mockIDBObjectStore(dbName, storeName)
    ),
  db: null,
  durability: "default" as const,
  error: null,
  mode: "readwrite" as const,
  objectStoreNames: ["cache"],
  onabort: null,
  oncomplete: null,
  onerror: null,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
})

const mockIDBDatabase = (dbName: string = "default-db") => ({
  close: vi.fn(),
  createObjectStore: vi
    .fn()
    .mockImplementation((storeName: string) =>
      mockIDBObjectStore(dbName, storeName)
    ),
  deleteObjectStore: vi.fn(),
  transaction: vi
    .fn()
    .mockImplementation((storeNames: string | string[], mode = "readonly") =>
      mockIDBTransaction(dbName)
    ),
  name: dbName,
  objectStoreNames: ["cache"],
  onabort: null,
  onclose: null,
  onerror: null,
  onversionchange: null,
  version: 1,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
})

// Patch window.indexedDB using Object.defineProperty + vi.stubGlobal not viable
Object.defineProperty(window, "indexedDB", {
  writable: true,
  configurable: true,
  value: {
    open: vi.fn().mockImplementation((name: string, version?: number) => {
      const request = createMockIDBRequest(mockIDBDatabase(name))
      setTimeout(() => {
        if (request.onupgradeneeded)
          request.onupgradeneeded(new Event("upgradeneeded"))
        if (request.onsuccess) request.onsuccess(new Event("success"))
      }, 0)
      return request
    }),
    deleteDatabase: vi.fn().mockImplementation((name: string) => {
      mockIndexedDBStorage.delete(name)
      return createMockIDBRequest(undefined)
    }),
    databases: vi.fn().mockResolvedValue([]),
    cmp: vi.fn().mockImplementation((a: any, b: any) => {
      if (a < b) return -1
      if (a > b) return 1
      return 0
    }),
  },
})

// Add required IndexedDB globals for idb library support
vi.stubGlobal(
  "IDBRequest",
  class MockIDBRequest extends EventTarget {
    result: any = null
    error: any = null
    readyState: "pending" | "done" = "done"
    source: any = null
    transaction: any = null
    onsuccess: ((event: Event) => void) | null = null
    onerror: ((event: Event) => void) | null = null
  }
)

vi.stubGlobal(
  "IDBOpenDBRequest",
  class MockIDBOpenDBRequest extends (global.IDBRequest as any) {
    onblocked: ((event: Event) => void) | null = null
    onupgradeneeded: ((event: Event) => void) | null = null
  }
)

vi.stubGlobal(
  "IDBDatabase",
  class MockIDBDatabase extends EventTarget {
    name: string = "test-db"
    version: number = 1
    objectStoreNames: string[] = []
    onabort: ((event: Event) => void) | null = null
    onclose: ((event: Event) => void) | null = null
    onerror: ((event: Event) => void) | null = null
    onversionchange: ((event: Event) => void) | null = null

    close() {}
    createObjectStore() {
      return mockIDBObjectStore()
    }
    deleteObjectStore() {}
    transaction() {
      return mockIDBTransaction()
    }
  }
)

vi.stubGlobal(
  "IDBTransaction",
  class MockIDBTransaction extends EventTarget {
    db: any = null
    durability: string = "default"
    error: any = null
    mode: string = "readonly"
    objectStoreNames: string[] = []
    onabort: ((event: Event) => void) | null = null
    oncomplete: ((event: Event) => void) | null = null
    onerror: ((event: Event) => void) | null = null

    abort() {}
    commit() {}
    objectStore() {
      return mockIDBObjectStore()
    }
  }
)

vi.stubGlobal(
  "IDBObjectStore",
  class MockIDBObjectStore {
    autoIncrement: boolean = false
    indexNames: string[] = []
    keyPath: any = null
    name: string = "test-store"
    transaction: any = null

    constructor(
      dbName: string = "default-db",
      storeName: string = "default-store"
    ) {
      this.name = storeName
    }

    add(value: any, key?: any) {
      const actualKey = key || Date.now().toString()
      return createMockIDBRequest(actualKey)
    }
    clear() {
      return createMockIDBRequest(undefined)
    }
    count() {
      return createMockIDBRequest(0)
    }
    delete() {
      return createMockIDBRequest(undefined)
    }
    get() {
      return createMockIDBRequest(undefined)
    }
    getAll() {
      return createMockIDBRequest([])
    }
    getAllKeys() {
      return createMockIDBRequest([])
    }
    getKey() {
      return createMockIDBRequest(undefined)
    }
    put(value: any, key?: any) {
      const actualKey = key || Date.now().toString()
      return createMockIDBRequest(actualKey)
    }
    openCursor() {
      return createMockIDBRequest(null)
    }
    openKeyCursor() {
      return createMockIDBRequest(null)
    }
    createIndex() {
      return {}
    }
    deleteIndex() {}
    index() {
      return {}
    }
  }
)

vi.stubGlobal(
  "IDBKeyRange",
  class MockIDBKeyRange {
    lower: any
    upper: any
    lowerOpen: boolean = false
    upperOpen: boolean = false

    static bound(
      lower: any,
      upper: any,
      lowerOpen?: boolean,
      upperOpen?: boolean
    ) {
      const range = new MockIDBKeyRange()
      range.lower = lower
      range.upper = upper
      range.lowerOpen = !!lowerOpen
      range.upperOpen = !!upperOpen
      return range
    }

    static only(value: any) {
      const range = new MockIDBKeyRange()
      range.lower = value
      range.upper = value
      return range
    }

    static lowerBound(lower: any, open?: boolean) {
      const range = new MockIDBKeyRange()
      range.lower = lower
      range.lowerOpen = !!open
      return range
    }

    static upperBound(upper: any, open?: boolean) {
      const range = new MockIDBKeyRange()
      range.upper = upper
      range.upperOpen = !!open
      return range
    }

    includes(value: any) {
      return true
    }
  }
)

vi.stubGlobal(
  "IDBIndex",
  class MockIDBIndex {
    keyPath: any = null
    multiEntry: boolean = false
    name: string = "test-index"
    objectStore: any = null
    unique: boolean = false

    count() {
      return createMockIDBRequest(0)
    }
    get() {
      return createMockIDBRequest(undefined)
    }
    getAll() {
      return createMockIDBRequest([])
    }
    getAllKeys() {
      return createMockIDBRequest([])
    }
    getKey() {
      return createMockIDBRequest(undefined)
    }
    openCursor() {
      return createMockIDBRequest(null)
    }
    openKeyCursor() {
      return createMockIDBRequest(null)
    }
  }
)

vi.stubGlobal(
  "IDBCursor",
  class MockIDBCursor {
    direction: string = "next"
    key: any = null
    primaryKey: any = null
    request: any = null
    source: any = null

    advance() {}
    continue() {}
    continuePrimaryKey() {}
    delete() {
      return createMockIDBRequest(undefined)
    }
    update() {
      return createMockIDBRequest(undefined)
    }
  }
)

vi.stubGlobal(
  "IDBCursorWithValue",
  class MockIDBCursorWithValue extends (global.IDBCursor as any) {
    value: any = null
  }
)

// Consolidated idb library mock factory for direct usage
export const createMockIDBDatabase = (
  storage: Map<string, any> = new Map()
) => ({
  get: vi.fn().mockImplementation(async (storeName: string, key: any) => {
    return storage.get(key)
  }),
  put: vi
    .fn()
    .mockImplementation(async (storeName: string, value: any, key?: any) => {
      const actualKey = key || Date.now().toString()
      storage.set(actualKey, value)
      return actualKey
    }),
  delete: vi.fn().mockImplementation(async (storeName: string, key: any) => {
    storage.delete(key)
    return undefined
  }),
  clear: vi.fn().mockImplementation(async (storeName: string) => {
    storage.clear()
    return undefined
  }),
  count: vi.fn().mockImplementation(async (storeName: string) => {
    return storage.size
  }),
  transaction: vi
    .fn()
    .mockImplementation((storeNames: string | string[], mode = "readonly") => ({
      objectStore: vi.fn().mockImplementation((storeName: string) => ({
        get: vi.fn().mockImplementation(async (key: any) => storage.get(key)),
        put: vi.fn().mockImplementation(async (value: any, key?: any) => {
          const actualKey = key || Date.now().toString()
          storage.set(actualKey, value)
          return actualKey
        }),
        delete: vi.fn().mockImplementation(async (key: any) => {
          storage.delete(key)
          return undefined
        }),
        clear: vi.fn().mockImplementation(async () => {
          storage.clear()
          return undefined
        }),
        count: vi.fn().mockImplementation(async () => storage.size),
        add: vi.fn().mockImplementation(async (value: any, key?: any) => {
          const actualKey = key || Date.now().toString()
          if (storage.has(actualKey)) {
            throw new Error("Key already exists")
          }
          storage.set(actualKey, value)
          return actualKey
        }),
        getAll: vi
          .fn()
          .mockImplementation(async () => Array.from(storage.values())),
        getAllKeys: vi
          .fn()
          .mockImplementation(async () => Array.from(storage.keys())),
        index: vi.fn().mockImplementation(() => ({
          get: vi.fn().mockImplementation(async () => undefined),
          getAll: vi.fn().mockImplementation(async () => []),
        })),
      })),
      done: Promise.resolve(),
    })),
  name: "test-db",
  version: 1,
  objectStoreNames: { contains: vi.fn(() => false) },
  createObjectStore: vi.fn().mockImplementation(() => ({
    createIndex: vi.fn(),
  })),
  close: vi.fn(),
})

// Mock idb library with shared factory
vi.mock("idb", () => {
  return {
    openDB: vi
      .fn()
      .mockImplementation((name: string, version?: number, options?: any) => {
        const storage = new Map<string, any>()
        const database = createMockIDBDatabase(storage)

        if (options?.upgrade) {
          try {
            options.upgrade(database, 0, version || 1, null, null)
          } catch {
            // ignore upgrade errors in mock
          }
        }

        return Promise.resolve(database)
      }),
    deleteDB: vi.fn().mockResolvedValue(undefined),
    wrap: vi.fn().mockImplementation((value: any) => value),
    unwrap: vi.fn().mockImplementation((value: any) => value),
  }
})

// Mock Next.js router with stubGlobal for better cleanup
vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  usePathname: () => "/",
  useSearchParams: () => new URLSearchParams(),
  useParams: () => ({}),
}))

// Mock Next.js dynamic imports
vi.mock("next/dynamic", () => ({
  __esModule: true,
  default: (fn: () => Promise<any>) => {
    const Component = () => null
    Component.displayName = "DynamicComponent"
    return Component
  },
}))

// Mock window.location with Object.defineProperty + configurable:true for patching
Object.defineProperty(window, "location", {
  writable: true,
  configurable: true,
  value: {
    href: "http://localhost:3000",
    origin: "http://localhost:3000",
    protocol: "http:",
    host: "localhost:3000",
    hostname: "localhost",
    port: "3000",
    pathname: "/",
    search: "",
    hash: "",
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
    toString: () => "http://localhost:3000",
  },
})
