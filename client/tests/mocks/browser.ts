/**
 * MSW Browser Worker Setup for Playwright E2E Tests
 *
 * Reuses the same handlers as the Node.js MSW server to ensure consistency
 * across unit/integration (Vitest) and browser E2E (Playwright) test runs.
 */

import { setupWorker } from "msw/browser"

import { authHandlers } from "./handlers/auth"
import { componentCategoryHandlers } from "./handlers/componentCategories"
import { componentHandlers } from "./handlers/components"
import { componentTypeHandlers } from "./handlers/componentTypes"
import { healthHandlers } from "./handlers/health"
import { projectHandlers } from "./handlers/projects"
import { userHandlers } from "./handlers/users"

export const worker = setupWorker(
  // Authentication endpoints
  ...authHandlers,
  // User management endpoints
  ...userHandlers,
  // Health check endpoints
  ...healthHandlers,
  // Project management endpoints
  ...projectHandlers,
  // Component management endpoints
  ...componentHandlers,
  // Component category endpoints
  ...componentCategoryHandlers,
  // Component type endpoints
  ...componentTypeHandlers
)

export async function startMockWorker() {
  // Start the worker in quiet mode by default; warn on unhandled to help debugging
  const startPromise = worker.start({
    onUnhandledRequest: "warn",
    serviceWorker: {
      // Next.js serves from /public, so the worker must be available at this path
      url: "/mockServiceWorker.js",
    },
  })
  // Expose a global promise so API client can await MSW readiness before first request
  if (typeof window !== "undefined") {
    ;(window as any).__msw_ready = startPromise
  }
  await startPromise
}
