/**
 * MSW handlers for project API endpoints
 * Supports E2E testing for offline mutation and synchronization
 */

import { http, HttpResponse } from "msw"

import { AuthErrors, getUserFromRequest } from "../fixtures/auth"

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000"
console.log("🔧 MSW: Projects API_BASE configured as:", API_BASE)

// In-memory storage for projects during testing
let projects: any[] = [
  {
    id: 1,
    name: "Sample Project 1",
    description: "A sample project for testing",
    status: "ACTIVE",
    client: "Sample Client",
    location: "Sample Location",
    is_offline: false,
    database_url: "postgresql+asyncpg://postgres:test@localhost:5433/test_db",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    project_number: "PROJ-001",
  },
  {
    id: 2,
    name: "Sample Project 2",
    description: "Another sample project",
    status: "DRAFT",
    client: "Another Client",
    location: "Another Location",
    is_offline: true,
    database_url: "postgresql+asyncpg://postgres:test@localhost:5433/test_db",
    created_at: "2024-01-02T00:00:00Z",
    updated_at: "2024-01-02T00:00:00Z",
    project_number: "PROJ-002",
  },
]

let nextId = 3

// Utility function to create a new project
const createProject = (data: any) => {
  const now = new Date().toISOString()
  return {
    id: nextId++,
    name: data.name,
    description: data.description || null,
    status: data.status || "DRAFT",
    client: data.client || null,
    location: data.location || null,
    is_offline: data.is_offline || false,
    database_url: "postgresql+asyncpg://postgres:test@localhost:5433/test_db",
    project_number:
      data.project_number || `PROJ-${String(nextId - 1).padStart(3, "0")}`,
    created_at: now,
    updated_at: now,
    members: [],
  }
}

// Filter projects based on query parameters
const filterProjects = (searchParams: any) => {
  let filtered = [...projects]

  if (searchParams.search) {
    const searchTerm = searchParams.search.toLowerCase()
    filtered = filtered.filter(
      (project) =>
        project.name.toLowerCase().includes(searchTerm) ||
        (project.description &&
          project.description.toLowerCase().includes(searchTerm)) ||
        (project.client && project.client.toLowerCase().includes(searchTerm))
    )
  }

  if (searchParams.status) {
    filtered = filtered.filter(
      (project) => project.status === searchParams.status
    )
  }

  if (searchParams.is_offline !== undefined) {
    const isOffline = searchParams.is_offline === "true"
    filtered = filtered.filter((project) => project.is_offline === isOffline)
  }

  return filtered
}

export const projectHandlers = [
  // Debug: Log all project requests
  http.all("*", ({ request }) => {
    if (request.url.includes("/api/v1/project")) {
      console.log(
        "🌐 MSW: Project API request detected:",
        request.method,
        request.url
      )
    }
    return
  }),

  // GET /api/v1/projects/ - List projects with filtering and pagination
  http.get(`${API_BASE}/api/v1/projects/`, async ({ request }) => {
    console.log("🔍 MSW: Project list request intercepted!", request.url)

    // Check authentication
    const user = await getUserFromRequest(request)
    if (!user) {
      console.log("❌ MSW: Authentication failed for project list")
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    console.log(
      "✅ MSW: Authentication passed for project list, user:",
      user.email
    )

    const url = new URL(request.url)
    const searchParams = Object.fromEntries(url.searchParams)

    // Apply filters
    const filteredProjects = filterProjects(searchParams)

    // Pagination
    const page = parseInt(searchParams.page, 10) || 1
    const perPage = parseInt(searchParams.size, 10) || 10
    const startIndex = (page - 1) * perPage
    const endIndex = startIndex + perPage
    const paginatedProjects = filteredProjects.slice(startIndex, endIndex)

    const response = {
      items: paginatedProjects,
      total: filteredProjects.length,
      page,
      size: perPage,
      pages: Math.ceil(filteredProjects.length / perPage),
    }

    console.log("📦 MSW: Returning project list response:", {
      total: response.total,
      page: response.page,
      items_count: response.items.length,
    })

    return HttpResponse.json(response)
  }),

  // GET /api/v1/projects/:id - Get project by ID
  http.get(`${API_BASE}/api/v1/projects/:id`, async ({ params, request }) => {
    console.log("🔍 MSW: Get project by ID request:", params.id)

    // Check authentication
    const user = await getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    const { id } = params
    const project = projects.find((p) => p.id === parseInt(id as string))

    if (!project) {
      console.log("❌ MSW: Project not found:", id)
      return HttpResponse.json({ error: "Project not found" }, { status: 404 })
    }

    console.log("✅ MSW: Returning project:", project.name)
    return HttpResponse.json(project)
  }),

  // POST /api/v1/projects/ - Create new project
  http.post(`${API_BASE}/api/v1/projects/`, async ({ request }) => {
    console.log("📝 MSW: Create project request intercepted!")

    // Check authentication
    const user = await getUserFromRequest(request)
    if (!user) {
      console.log("❌ MSW: Authentication failed for project creation")
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    try {
      const body = (await request.json()) as any
      console.log("📝 MSW: Project creation data:", {
        name: body.name,
        is_offline: body.is_offline,
      })

      // Basic validation
      if (!body.name || body.name.trim() === "") {
        console.log("❌ MSW: Project creation failed - missing name")
        return HttpResponse.json(
          {
            error: "Validation error",
            details: ["Name is required"],
          },
          { status: 400 }
        )
      }

      // Create new project
      const newProject = createProject(body)
      projects.push(newProject)

      console.log(
        "✅ MSW: Project created successfully:",
        newProject.name,
        "ID:",
        newProject.id
      )

      return HttpResponse.json(newProject, { status: 201 })
    } catch (error) {
      console.log("❌ MSW: Error creating project:", error)
      return HttpResponse.json(
        {
          error: "Invalid request body",
        },
        { status: 400 }
      )
    }
  }),

  // PUT /api/v1/projects/:id - Update project
  http.put(`${API_BASE}/api/v1/projects/:id`, async ({ params, request }) => {
    console.log("📝 MSW: Update project request intercepted!", params.id)

    // Check authentication
    const user = getUserFromRequest(request)
    if (!user) {
      console.log("❌ MSW: Authentication failed for project update")
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    const { id } = params
    const projectIndex = projects.findIndex(
      (p) => p.id === parseInt(id as string)
    )

    if (projectIndex === -1) {
      console.log("❌ MSW: Project not found for update:", id)
      return HttpResponse.json({ error: "Project not found" }, { status: 404 })
    }

    try {
      const body = (await request.json()) as any
      console.log("📝 MSW: Project update data:", {
        id,
        name: body.name,
        is_offline: body.is_offline,
      })

      // Update project
      const updatedProject = {
        ...projects[projectIndex],
        ...body,
        updated_at: new Date().toISOString(),
      }

      projects[projectIndex] = updatedProject

      console.log("✅ MSW: Project updated successfully:", updatedProject.name)
      return HttpResponse.json(updatedProject)
    } catch (error) {
      console.log("❌ MSW: Error updating project:", error)
      return HttpResponse.json(
        {
          error: "Invalid request body",
        },
        { status: 400 }
      )
    }
  }),

  // DELETE /api/v1/projects/:id - Delete project
  http.delete(`${API_BASE}/api/v1/projects/:id`, ({ params, request }) => {
    console.log("🗑️ MSW: Delete project request intercepted!", params.id)

    // Check authentication
    const user = getUserFromRequest(request)
    if (!user) {
      console.log("❌ MSW: Authentication failed for project deletion")
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    const { id } = params
    const projectIndex = projects.findIndex(
      (p) => p.id === parseInt(id as string)
    )

    if (projectIndex === -1) {
      console.log("❌ MSW: Project not found for deletion:", id)
      return HttpResponse.json({ error: "Project not found" }, { status: 404 })
    }

    const deletedProject = projects[projectIndex]
    projects.splice(projectIndex, 1)

    console.log("✅ MSW: Project deleted successfully:", deletedProject.name)
    return HttpResponse.json({ message: "Project deleted successfully" })
  }),

  // POST /api/v1/projects/sync - Synchronization endpoint with conflict resolution
  http.post(`${API_BASE}/api/v1/projects/sync`, async ({ request }) => {
    console.log("🔄 MSW: Project sync request intercepted!")

    // Check authentication
    const user = getUserFromRequest(request)
    if (!user) {
      console.log("❌ MSW: Authentication failed for project sync")
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    try {
      const body = (await request.json()) as any
      const projectId = body.project_id
      const clientChanges = body.client_changes || []

      console.log(
        "🔄 MSW: Processing sync for project:",
        projectId,
        "with",
        clientChanges.length,
        "client changes"
      )

      // Simulate conflict detection and resolution
      let conflictsDetected = 0
      let conflictsResolved = 0

      // Check for conflicts in client changes
      for (const change of clientChanges) {
        if (change.operation === "update" || change.operation === "delete") {
          // Simulate server-side timestamp check
          const serverProject = projects.find(
            (p) => p.id === parseInt(change.resource_id)
          )
          if (serverProject) {
            const serverModified = new Date(serverProject.updated_at)
            const clientModified = new Date(change.timestamp)

            // Detect conflict if server was modified after client's base timestamp
            if (serverModified > clientModified) {
              conflictsDetected++
              console.log(
                `⚠️ MSW: Conflict detected for project ${change.resource_id}: Server timestamp ${serverModified.toISOString()} > Client timestamp ${clientModified.toISOString()}`
              )

              // Apply Last-Write Wins resolution
              const serverTimestamp = serverModified.getTime()
              const clientTimestamp = clientModified.getTime()

              if (serverTimestamp > clientTimestamp) {
                console.log("🏆 MSW: Server version wins (Last-Write Wins)")
                // Server version is kept, client change is discarded
              } else {
                console.log("🏆 MSW: Client version wins (Last-Write Wins)")
                // Apply client change
                if (change.operation === "update") {
                  Object.assign(serverProject, change.data, {
                    updated_at: new Date().toISOString(),
                  })
                } else if (change.operation === "delete") {
                  const index = projects.findIndex(
                    (p) => p.id === parseInt(change.resource_id)
                  )
                  if (index !== -1) projects.splice(index, 1)
                }
              }
              conflictsResolved++
            }
          }
        }
      }

      // Simulate sync operation results
      const syncResult = {
        project_id: projectId,
        status: "completed",
        local_to_central: {
          created: clientChanges.filter(
            (c: { operation: string }) => c.operation === "create"
          ).length,
          updated: clientChanges.filter(
            (c: { operation: string }) => c.operation === "update"
          ).length,
          deleted: clientChanges.filter(
            (c: { operation: string }) => c.operation === "delete"
          ).length,
          errors: 0,
        },
        central_to_local: {
          created: 0,
          updated: 0,
          deleted: 0,
          errors: 0,
        },
        conflicts_detected: conflictsDetected,
        conflicts_resolved: conflictsResolved,
        conflict_resolution_strategy: "last_write_wins",
        sync_direction: "bidirectional",
        timestamp: new Date().toISOString(),
        message:
          conflictsDetected > 0
            ? `Synchronization completed with ${conflictsResolved} conflicts resolved using Last-Write Wins`
            : "Synchronization completed successfully",
      }

      console.log(
        "✅ MSW: Sync completed successfully with conflict resolution:",
        {
          conflicts: conflictsDetected,
          resolved: conflictsResolved,
        }
      )
      return HttpResponse.json(syncResult)
    } catch (error) {
      console.log("❌ MSW: Error during sync:", error)
      return HttpResponse.json(
        {
          error: "Sync operation failed",
          details: error instanceof Error ? error.message : "Unknown error",
        },
        { status: 500 }
      )
    }
  }),

  // POST /api/v1/projects/conflict-resolution - Explicit conflict resolution endpoint
  http.post(
    `${API_BASE}/api/v1/projects/conflict-resolution`,
    async ({ request }) => {
      console.log("⚔️ MSW: Conflict resolution request intercepted!")

      // Check authentication
      const user = await getUserFromRequest(request)
      if (!user) {
        return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
          status: 401,
        })
      }

      try {
        const body = (await request.json()) as any
        const {
          conflict_type,
          resource_id,
          client_data,
          server_data,
          resolution_strategy,
        } = body

        console.log("⚔️ MSW: Resolving conflict:", {
          conflict_type,
          resource_id,
          resolution_strategy,
        })

        let winner = "server" // Default to server wins
        let winnerData = server_data

        // Apply resolution strategy
        if (resolution_strategy === "last_write_wins") {
          const clientTimestamp = new Date(
            client_data.timestamp || client_data.updated_at
          ).getTime()
          const serverTimestamp = new Date(
            server_data.timestamp || server_data.updated_at
          ).getTime()

          if (clientTimestamp > serverTimestamp) {
            winner = "client"
            winnerData = client_data
            console.log("🏆 MSW: Client wins via Last-Write Wins")
          } else {
            console.log("🏆 MSW: Server wins via Last-Write Wins")
          }
        }

        // Update the project with winner data
        if (conflict_type !== "delete_conflict") {
          const projectIndex = projects.findIndex(
            (p) => p.id === parseInt(resource_id)
          )
          if (projectIndex !== -1) {
            projects[projectIndex] = {
              ...projects[projectIndex],
              ...winnerData,
              updated_at: new Date().toISOString(),
            }
          }
        }

        const resolutionResult = {
          conflict_id: `conflict_${Date.now()}`,
          resource_id,
          conflict_type,
          resolution_strategy,
          winner,
          winner_data: winnerData,
          resolved_at: new Date().toISOString(),
          status: "resolved",
        }

        console.log("✅ MSW: Conflict resolved:", winner, "version selected")
        return HttpResponse.json(resolutionResult)
      } catch (error) {
        console.log("❌ MSW: Error resolving conflict:", error)
        return HttpResponse.json(
          {
            error: "Conflict resolution failed",
            details: error instanceof Error ? error.message : "Unknown error",
          },
          { status: 500 }
        )
      }
    }
  ),

  // GET /api/v1/projects/:id/members - Get project members
  http.get(`${API_BASE}/api/v1/projects/:id/members`, ({ params, request }) => {
    console.log("👥 MSW: Get project members request:", params.id)

    // Check authentication
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    const { id } = params
    const project = projects.find((p) => p.id === parseInt(id as string))

    if (!project) {
      return HttpResponse.json({ error: "Project not found" }, { status: 404 })
    }

    // Return empty members list for now
    return HttpResponse.json([])
  }),

  // Bulk operations for testing
  http.post(`${API_BASE}/api/v1/projects/bulk-delete`, async ({ request }) => {
    console.log("🗑️ MSW: Bulk delete projects request")

    // Check authentication
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, {
        status: 401,
      })
    }

    const body = (await request.json()) as { ids: number[] }
    const deletedCount = body.ids.length

    projects = projects.filter((p) => !body.ids.includes(p.id))

    console.log(`✅ MSW: Bulk deleted ${deletedCount} projects`)
    return HttpResponse.json({
      message: `Successfully deleted ${deletedCount} projects`,
      deleted_count: deletedCount,
    })
  }),
]

// Utility function to reset project data (for testing)
export const resetProjectData = () => {
  projects.length = 0
  projects.push(
    {
      id: 1,
      name: "Sample Project 1",
      description: "A sample project for testing",
      status: "ACTIVE",
      client: "Sample Client",
      location: "Sample Location",
      is_offline: false,
      database_url: "postgresql+asyncpg://postgres:test@localhost:5433/test_db",
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
      project_number: "PROJ-001",
    },
    {
      id: 2,
      name: "Sample Project 2",
      description: "Another sample project",
      status: "DRAFT",
      client: "Another Client",
      location: "Another Location",
      is_offline: true,
      database_url: "postgresql+asyncpg://postgres:test@localhost:5433/test_db",
      created_at: "2024-01-02T00:00:00Z",
      updated_at: "2024-01-02T00:00:00Z",
      project_number: "PROJ-002",
    }
  )
  nextId = 3
  console.log("🔄 MSW: Project data reset")
}
