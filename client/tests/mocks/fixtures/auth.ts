/**
 * Authentication Fixtures for MSW Testing
 *
 * Provides comprehensive authentication mock data and utilities including:
 * - User management with roles and permissions
 * - JWT token generation and validation
 * - Session management and token refresh
 * - Role-based access control
 */

import { jwtVerify, SignJWT } from "jose"

// JWT secret for test tokens
const JWT_SECRET = "test-secret-key-for-msw-mocking"
const JWT_REFRESH_SECRET = "test-refresh-secret-key-for-msw-mocking"

// User types
export interface MockUser {
  id: number
  username: string
  email: string
  first_name: string
  last_name: string
  role: "admin" | "user" | "viewer"
  is_active: boolean
  created_at: string
  last_login?: string
  password_hash: string // In real app this would be hashed
}

export interface AuthToken {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

export interface JWTPayload {
  sub: string // user_id
  email: string
  role: string
  is_active: boolean
  exp: number
  iat: number
}

// Mock user database
export const mockUsers: MockUser[] = [
  {
    id: 1,
    username: "admin",
    email: "<EMAIL>",
    first_name: "Admin",
    last_name: "User",
    role: "admin",
    is_active: true,
    created_at: "2024-01-01T00:00:00Z",
    last_login: "2024-07-17T00:00:00Z",
    password_hash: "hashed_password_admin",
  },
  {
    id: 2,
    username: "testuser",
    email: "<EMAIL>",
    first_name: "Test",
    last_name: "User",
    role: "user",
    is_active: true,
    created_at: "2024-01-10T00:00:00Z",
    last_login: "2024-07-18T00:00:00Z",
    password_hash: "hashed_password_test",
  },
  {
    id: 3,
    username: "user1",
    email: "<EMAIL>",
    first_name: "Regular",
    last_name: "User",
    role: "user",
    is_active: true,
    created_at: "2024-01-15T00:00:00Z",
    last_login: "2024-07-16T00:00:00Z",
    password_hash: "hashed_password_user1",
  },
  {
    id: 4,
    username: "inactiveuser",
    email: "<EMAIL>",
    first_name: "Inactive",
    last_name: "User",
    role: "user",
    is_active: false,
    created_at: "2024-02-01T00:00:00Z",
    password_hash: "hashed_password_inactive",
  },
  {
    id: 5,
    username: "viewer1",
    email: "<EMAIL>",
    first_name: "Read Only",
    last_name: "Viewer",
    role: "viewer",
    is_active: true,
    created_at: "2024-03-01T00:00:00Z",
    last_login: "2024-07-15T00:00:00Z",
    password_hash: "hashed_password_viewer1",
  },
]

// Active sessions (refresh tokens)
const activeSessions = new Map<
  string,
  { userId: number; refreshToken: string; expiresAt: Date }
>()

// Authentication utilities
export const AuthUtils = {
  /**
   * Generate JWT access token
   */
  async generateAccessToken(user: MockUser): Promise<string> {
    const now = Math.floor(Date.now() / 1000)
    const payload: JWTPayload = {
      sub: user.id.toString(),
      email: user.email,
      role: user.role,
      is_active: user.is_active,
      exp: now + 15 * 60, // 15 minutes
      iat: now,
    }

    const secret = new TextEncoder().encode(JWT_SECRET)
    const token = await new SignJWT(payload as any)
      .setProtectedHeader({ alg: "HS256" })
      .setIssuedAt(now)
      .setExpirationTime(payload.exp)
      .sign(secret)
    return token
  },

  /**
   * Generate refresh token
   */
  async generateRefreshToken(user: MockUser): Promise<string> {
    const now = Math.floor(Date.now() / 1000)
    const payload = {
      sub: user.id.toString(),
      type: "refresh",
      exp: now + 7 * 24 * 60 * 60, // 7 days
      iat: now,
    }

    const secret = new TextEncoder().encode(JWT_REFRESH_SECRET)
    const refreshToken = await new SignJWT(payload as any)
      .setProtectedHeader({ alg: "HS256" })
      .setIssuedAt(now)
      .setExpirationTime(payload.exp)
      .sign(secret)

    // Store session
    activeSessions.set(refreshToken, {
      userId: user.id,
      refreshToken,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    })

    return refreshToken
  },

  /**
   * Validate and decode access token
   */
  async validateAccessToken(token: string): Promise<JWTPayload | null> {
    try {
      const secret = new TextEncoder().encode(JWT_SECRET)
      const { payload } = await jwtVerify(token, secret)
      return payload as unknown as JWTPayload
    } catch (error) {
      return null
    }
  },

  /**
   * Validate refresh token
   */
  async validateRefreshToken(
    token: string
  ): Promise<{ userId: number } | null> {
    try {
      const secret = new TextEncoder().encode(JWT_REFRESH_SECRET)
      await jwtVerify(token, secret)
      const session = activeSessions.get(token)

      if (!session || session.expiresAt < new Date()) {
        activeSessions.delete(token)
        return null
      }

      return { userId: session.userId }
    } catch (error) {
      return null
    }
  },

  /**
   * Create complete auth token response
   */
  async createAuthTokens(user: MockUser): Promise<AuthToken> {
    const accessToken = await this.generateAccessToken(user)
    const refreshToken = await this.generateRefreshToken(user)

    return {
      access_token: accessToken,
      refresh_token: refreshToken,
      token_type: "bearer",
      expires_in: 15 * 60, // 15 minutes
    }
  },

  /**
   * Revoke refresh token (logout)
   */
  revokeRefreshToken(token: string): boolean {
    return activeSessions.delete(token)
  },

  /**
   * Clear all sessions for user
   */
  clearUserSessions(userId: number): void {
    for (const [token, session] of activeSessions.entries()) {
      if (session.userId === userId) {
        activeSessions.delete(token)
      }
    }
  },
}

// User management utilities
export const UserUtils = {
  /**
   * Find user by email
   */
  findByEmail(email: string): MockUser | undefined {
    return mockUsers.find((user) => user.email === email)
  },

  /**
   * Find user by username
   */
  findByUsername(username: string): MockUser | undefined {
    return mockUsers.find((user) => user.username === username)
  },

  /**
   * Find user by ID
   */
  findById(id: number): MockUser | undefined {
    return mockUsers.find((user) => user.id === id)
  },

  /**
   * Authenticate user by credentials
   */
  authenticate(username: string, password: string): MockUser | null {
    const user = this.findByUsername(username) || this.findByEmail(username)

    if (!user) return null

    // In real app, compare hashed passwords
    const isValidPassword =
      password === "password123" ||
      (username === "admin" && password === "admin123")

    if (!isValidPassword) return null

    // Update last login
    user.last_login = new Date().toISOString()

    return user
  },

  /**
   * Create new user
   */
  createUser(userData: Partial<MockUser>): MockUser {
    const newId = Math.max(...mockUsers.map((u) => u.id)) + 1

    const newUser: MockUser = {
      id: newId,
      username: userData.username || "",
      email: userData.email || "",
      first_name: userData.first_name || "",
      last_name: userData.last_name || "",
      role: userData.role || "user",
      is_active: userData.is_active ?? true,
      created_at: new Date().toISOString(),
      password_hash: "hashed_password_new",
    }

    mockUsers.push(newUser)
    return newUser
  },

  /**
   * Update user
   */
  updateUser(id: number, updates: Partial<MockUser>): MockUser | null {
    const userIndex = mockUsers.findIndex((user) => user.id === id)

    if (userIndex === -1) return null

    mockUsers[userIndex] = { ...mockUsers[userIndex], ...updates }
    return mockUsers[userIndex]
  },

  /**
   * Delete user
   */
  deleteUser(id: number): boolean {
    const userIndex = mockUsers.findIndex((user) => user.id === id)

    if (userIndex === -1) return false

    // Clear all sessions for user
    AuthUtils.clearUserSessions(id)

    mockUsers.splice(userIndex, 1)
    return true
  },

  /**
   * Get paginated users
   */
  getPaginatedUsers(
    skip: number = 0,
    limit: number = 20,
    filters?: {
      is_active?: boolean
      role?: string
      search?: string
    }
  ) {
    let filteredUsers = [...mockUsers]

    if (filters?.is_active !== undefined) {
      filteredUsers = filteredUsers.filter(
        (user) => user.is_active === filters.is_active
      )
    }

    if (filters?.role) {
      filteredUsers = filteredUsers.filter((user) => user.role === filters.role)
    }

    if (filters?.search) {
      const searchLower = filters.search.toLowerCase()
      filteredUsers = filteredUsers.filter(
        (user) =>
          user.username.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          user.first_name.toLowerCase().includes(searchLower) ||
          user.last_name.toLowerCase().includes(searchLower)
      )
    }

    const total = filteredUsers.length
    const users = filteredUsers.slice(skip, skip + limit)

    return { users, total }
  },
}

// Request utilities
export async function getUserFromRequest(
  request: Request
): Promise<MockUser | null> {
  const authHeader = request.headers.get("Authorization")

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null
  }

  const token = authHeader.substring(7)
  const payload = await AuthUtils.validateAccessToken(token)

  if (!payload) return null

  const user = UserUtils.findById(parseInt(payload.sub))
  return user || null
}

export function isAuthenticatedUser(user: MockUser | null): boolean {
  return !!(user && user.is_active)
}

export function isAdminUser(user: MockUser | null): boolean {
  return !!(user && user.is_active && user.role === "admin")
}

export function hasRole(user: MockUser | null, role: string): boolean {
  return !!(user && user.is_active && user.role === role)
}

// Common error responses
export const AuthErrors = {
  AUTHENTICATION_REQUIRED: {
    error: "AUTHENTICATION_REQUIRED",
    detail: "Authentication credentials were not provided",
    status_code: 401,
  },

  INVALID_CREDENTIALS: {
    error: "INVALID_CREDENTIALS",
    detail: "Invalid username or password",
    status_code: 401,
  },

  TOKEN_EXPIRED: {
    error: "TOKEN_EXPIRED",
    detail: "Authentication token has expired",
    status_code: 401,
  },

  INVALID_TOKEN: {
    error: "INVALID_TOKEN",
    detail: "Invalid authentication token",
    status_code: 401,
  },

  USER_INACTIVE: {
    error: "USER_INACTIVE",
    detail: "User account is inactive",
    status_code: 403,
  },

  ADMIN_REQUIRED: {
    error: "ADMIN_REQUIRED",
    detail: "Admin privileges required for this action",
    status_code: 403,
  },

  INSUFFICIENT_PERMISSIONS: {
    error: "INSUFFICIENT_PERMISSIONS",
    detail: "Insufficient permissions for this action",
    status_code: 403,
  },
}

// Reset function for test isolation
export function resetAuthState(): void {
  // Reset users to initial state
  mockUsers.splice(0, mockUsers.length)
  mockUsers.push(
    {
      id: 1,
      username: "admin",
      email: "<EMAIL>",
      first_name: "Admin",
      last_name: "User",
      role: "admin",
      is_active: true,
      created_at: "2024-01-01T00:00:00Z",
      last_login: "2024-07-17T00:00:00Z",
      password_hash: "hashed_password_admin",
    },
    {
      id: 2,
      username: "testuser",
      email: "<EMAIL>",
      first_name: "Test",
      last_name: "User",
      role: "user",
      is_active: true,
      created_at: "2024-01-10T00:00:00Z",
      last_login: "2024-07-18T00:00:00Z",
      password_hash: "hashed_password_test",
    },
    {
      id: 3,
      username: "user1",
      email: "<EMAIL>",
      first_name: "Regular",
      last_name: "User",
      role: "user",
      is_active: true,
      created_at: "2024-01-15T00:00:00Z",
      last_login: "2024-07-16T00:00:00Z",
      password_hash: "hashed_password_user1",
    },
    {
      id: 4,
      username: "inactiveuser",
      email: "<EMAIL>",
      first_name: "Inactive",
      last_name: "User",
      role: "user",
      is_active: false,
      created_at: "2024-02-01T00:00:00Z",
      password_hash: "hashed_password_inactive",
    },
    {
      id: 5,
      username: "viewer1",
      email: "<EMAIL>",
      first_name: "Read Only",
      last_name: "Viewer",
      role: "viewer",
      is_active: true,
      created_at: "2024-03-01T00:00:00Z",
      last_login: "2024-07-15T00:00:00Z",
      password_hash: "hashed_password_viewer1",
    }
  )

  // Clear all sessions
  activeSessions.clear()
}
