/**
 * Playwright E2E Tests for Registration Flow
 *
 * These tests verify the complete user journey for user registration including:
 * - Registration form rendering and validation
 * - Successful account creation flow
 * - Error handling for duplicate users
 * - Professional credentials validation
 * - Accessibility and user experience
 */

import { expect, test } from "@playwright/test"

// Helper function to fill professional fields if they exist
async function fillProfessionalFields(page: any) {
  const yearsExperienceField = page.locator('input[name="years_experience"]')
  if ((await yearsExperienceField.count()) > 0) {
    await page.fill('input[name="years_experience"]', "5")
  }

  const professionalLicenseField = page.locator(
    'input[name="professional_license"]'
  )
  if ((await professionalLicenseField.count()) > 0) {
    await page.fill('input[name="professional_license"]', "PE-12345")
  }

  const specializationsField = page.locator('input[name="specializations"]')
  if ((await specializationsField.count()) > 0) {
    await page.fill('input[name="specializations"]', "Software Engineering")
  }
}

test.describe("Registration Flow E2E Tests", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to registration page
    await page.goto("/register")
  })

  test("should render registration form with all required elements", async ({
    page,
  }) => {
    // Verify page title and branding (scoped)
    const authHeader = page.locator('[data-testid="auth-container"]')
    await expect(
      page
        .getByTestId("brand-logo")
        .getByText("Ultimate Electrical Designer", { exact: true })
    ).toBeVisible()
    await expect(
      authHeader.getByRole("heading", { name: "Create Your Account" })
    ).toBeVisible()
    await expect(
      authHeader.getByText(
        "Join the Ultimate Electrical Designer platform and start designing professional electrical systems"
      )
    ).toBeVisible()

    // Verify basic form elements are present (current UI fields)
    await expect(page.locator('input[name="name"]').first()).toBeVisible()
    await expect(page.locator('input[name="email"]').first()).toBeVisible()
    await expect(page.locator('input[name="password"]').first()).toBeVisible()
    await expect(
      page.locator('input[name="confirm_password"]').first()
    ).toBeVisible()
    await expect(
      page.locator('button:has-text("Create Account")')
    ).toBeVisible()

    // Verify additional elements
    await expect(
      page.locator("text=Already have an account? Sign in")
    ).toBeVisible()

    // Verify password visibility toggles (use aria-label since buttons aren't direct siblings)
    await expect(
      page.locator('button[aria-label="Toggle password visibility"]')
    ).toHaveCount(2)
  })

  test("should render professional fields when enabled", async ({ page }) => {
    // Professional fields are enabled in the register page (showProfessionalFields=true)
    await expect(
      page.locator('input[name="professional_license"]').first()
    ).toBeVisible()
    await expect(
      page.locator('input[name="years_experience"]').first()
    ).toBeVisible()
    await expect(
      page.locator('input[name="specializations"]').first()
    ).toBeVisible()
  })

  test("should handle successful registration flow", async ({ page }) => {
    // Fill in valid registration data (current UI fields)
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")

    // Fill professional fields if they exist (they are rendered in the form)
    await fillProfessionalFields(page)

    // Submit the form
    await page.click('button:has-text("Create Account")')

    // Wait for navigation to login page (current app behavior)
    await expect(page).toHaveURL("/login", { timeout: 10000 })
  })

  test("should handle duplicate user error", async ({ page }) => {
    // Fill in registration data with existing email (UI does not use username)
    await page.fill('input[name="name"]', "Test User")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")

    // Submit the form
    await page.click('button:has-text("Create Account")')

    // Wait for form processing and error display
    await page.waitForTimeout(2000)

    // Wait for and verify error message (check for any error display)
    await expect(
      page.locator("[role='alert'], .text-destructive").first()
    ).toBeVisible()

    // Ensure we're still on the registration page
    await expect(page).toHaveURL("/register")

    // Verify form is still functional
    await expect(page.locator('input[name="name"]').first()).toBeVisible()
  })

  test("should handle validation errors for empty required fields", async ({
    page,
  }) => {
    // Try to submit empty form
    await page.click('button:has-text("Create Account")')

    // Verify validation errors for required fields (current UI)
    await expect(page.locator("text=Full name is required")).toBeVisible()
    await expect(
      page.locator("text=Email or username is required")
    ).toBeVisible()
    await expect(page.locator("text=Password is required")).toBeVisible()

    // Fill some fields and submit again
    await page.fill('input[name="name"]', "John Doe")
    await page.click('button:has-text("Create Account")')

    // Verify remaining required field errors
    await expect(
      page.locator("text=Email or username is required")
    ).toBeVisible()
    await expect(page.locator("text=Password is required")).toBeVisible()
  })

  test("should validate email format", async ({ page }) => {
    // Fill in invalid email
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "invalid-email")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")

    // Submit the form
    await page.click('button:has-text("Create Account")')

    // Verify email format validation error
    await expect(
      page.locator("text=Please enter a valid email address")
    ).toBeVisible()
  })

  test("should validate password requirements", async ({ page }) => {
    // Fill in weak password
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "weak")
    await page.fill('input[name="confirm_password"]', "weak")

    // Submit the form
    await page.click('button:has-text("Create Account")')

    // Verify password strength validation error
    await expect(
      page.locator("text=Password must be at least 8 characters long")
    ).toBeVisible()
  })

  test("should validate password confirmation match", async ({ page }) => {
    // Fill in mismatched passwords
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "DifferentPass456!")

    // Submit the form
    await page.click('button:has-text("Create Account")')

    // Wait for validation error to appear
    await page.waitForTimeout(1000)

    // Verify password match validation error (check for any validation error)
    await expect(
      page.locator("[role='alert'], .text-destructive, .error").first()
    ).toBeVisible()
  })

  test("should validate professional license format", async ({ page }) => {
    // Fill in valid data but invalid license format
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")

    // Ensure professional fields are visible (page sets showProfessionalFields=true)
    await page.fill('input[name="professional_license"]', "INVALID-LICENSE")

    // Submit the form
    await page.click('button:has-text("Create Account")')

    // Wait for validation error to appear
    await page.waitForTimeout(1000)

    // Verify professional license format validation error (check for any validation error)
    await expect(
      page.locator("[role='alert'], .text-destructive, .error").first()
    ).toBeVisible()
  })

  test("should validate years of experience", async ({ page }) => {
    // Fill in valid data but invalid years experience
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")

    await page.fill('input[name="years_experience"]', "-1")

    // Submit the form
    await page.click('button:has-text("Create Account")')

    // Verify years experience validation error
    await expect(
      page.locator("text=Years of experience must be a positive number")
    ).toBeVisible()
  })

  test("should toggle password visibility for both password fields", async ({
    page,
  }) => {
    const passwordInput = page.locator('input[name="password"]')
    const confirmPasswordInput = page.locator('input[name="confirm_password"]')
    const passwordToggles = page.locator(
      'button[aria-label="Toggle password visibility"]'
    )

    // Initially both passwords should be hidden (type="password")
    await expect(passwordInput).toHaveAttribute("type", "password")
    await expect(confirmPasswordInput).toHaveAttribute("type", "password")

    // Fill in passwords
    await passwordInput.fill("testpassword")
    await confirmPasswordInput.fill("testpassword")

    // Toggle both password fields to show
    await passwordToggles.first().click()
    await passwordToggles.last().click()
    await expect(passwordInput).toHaveAttribute("type", "text")
    await expect(confirmPasswordInput).toHaveAttribute("type", "text")

    // Toggle both back to hidden
    await passwordToggles.first().click()
    await passwordToggles.last().click()
    await expect(passwordInput).toHaveAttribute("type", "password")
    await expect(confirmPasswordInput).toHaveAttribute("type", "password")
  })

  test("should handle sign in navigation", async ({ page }) => {
    // Click sign in button (just the button part, not the whole text)
    await page.click("text=Sign in")

    // Verify navigation to login page
    await expect(page).toHaveURL("/login", { timeout: 10000 })

    // Verify we're on the login page (use heading to avoid strict mode violation)
    await expect(
      page.getByRole("heading", { name: "Sign in to your account" })
    ).toBeVisible()
  })

  test("should be keyboard accessible", async ({ page }) => {
    // Test that key form elements are focusable
    await page.locator('input[name="name"]').first().focus()
    await expect(page.locator('input[name="name"]')).toBeFocused()

    await page.locator('input[name="email"]').first().focus()
    await expect(page.locator('input[name="email"]')).toBeFocused()

    await page.locator('input[name="password"]').first().focus()
    await expect(page.locator('input[name="password"]')).toBeFocused()

    await page
      .locator('button[aria-label="Toggle password visibility"]')
      .first()
      .focus()
    await expect(
      page.locator('button[aria-label="Toggle password visibility"]').first()
    ).toBeFocused()

    await page.locator('input[name="confirm_password"]').first().focus()
    await expect(page.locator('input[name="confirm_password"]')).toBeFocused()

    await page
      .locator('button[aria-label="Toggle password visibility"]')
      .last()
      .focus()
    await expect(
      page.locator('button[aria-label="Toggle password visibility"]').last()
    ).toBeFocused()

    await page.locator("text=Sign in").first().focus()
    await expect(page.locator("text=Sign in")).toBeFocused()

    await page.locator('button:has-text("Create Account")').first().focus()
    await expect(
      page.locator('button:has-text("Create Account")')
    ).toBeFocused()
  })

  test("should support form submission with Enter key", async ({ page }) => {
    // Fill in valid registration data
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")

    // Fill professional fields if they exist
    await fillProfessionalFields(page)

    // Press Enter to submit form
    await page.keyboard.press("Enter")

    // Should navigate to login page
    await expect(page).toHaveURL("/login", { timeout: 10000 })
  })

  test("should show loading state during registration", async ({ page }) => {
    // Fill in valid registration data
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")

    // Fill professional fields if they exist
    await fillProfessionalFields(page)

    // Submit the form and immediately check for loading state
    await page.click('button:has-text("Create Account")')

    // Verify loading state (check for disabled submit button or loading text)
    await expect(
      page
        .locator('button[type="submit"][disabled]')
        .or(page.locator('button:has-text("Creating Account...")'))
    ).toBeVisible({ timeout: 1000 })

    // Wait for navigation to complete
    await expect(page).toHaveURL("/login", { timeout: 10000 })
  })

  test("should handle network errors gracefully", async ({ page }) => {
    // Fill in valid registration data
    await page.fill('input[name="name"]', "John Doe")
    await page.fill('input[name="email"]', "<EMAIL>")
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")

    // Fill professional fields if they exist
    await fillProfessionalFields(page)

    // Simulate network failure by going offline
    await page.context().setOffline(true)

    // Submit the form
    await page.click('button:has-text("Create Account")')

    // Wait for form processing and error display
    await page.waitForTimeout(2000)

    // Should show network error message (check for any error display)
    await expect(
      page.locator("[role='alert'], .text-destructive").first()
    ).toBeVisible()

    // Go back online
    await page.context().setOffline(false)

    // Wait a moment for network to be restored
    await page.waitForTimeout(2000)

    // Navigate to a fresh registration page
    await page.goto("/register")

    // Fill all fields for a successful registration
    await page.fill('input[name="name"]', "John Doe")
    await page.fill(
      'input[name="email"]',
      "<EMAIL>"
    )
    await page.fill('input[name="password"]', "SecurePass123!")
    await page.fill('input[name="confirm_password"]', "SecurePass123!")
    await fillProfessionalFields(page)

    await page.click('button:has-text("Create Account")')

    // Should navigate to login page
    await expect(page).toHaveURL("/login", { timeout: 10000 })
  })
})
