/**
 * Playwright E2E Tests for Login Flow
 *
 * These tests verify the complete user journey for authentication including:
 * - Login form rendering and validation
 * - Successful authentication flow
 * - Error handling for invalid credentials
 * - Session management and redirects
 * - Accessibility and user experience
 */

import { expect, test } from "@playwright/test"

test.describe("Login Flow E2E Tests", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page (assuming we'll create a dedicated login route)
    await page.goto("/login")
  })

  test("should render login form with all required elements", async ({
    page,
  }) => {
    // Verify page title and branding - be specific to the auth header
    const authHeader = page.locator('[data-testid="auth-container"]')
    await expect(
      page
        .getByTestId("brand-logo")
        .getByText("Ultimate Electrical Designer", { exact: true })
    ).toBeVisible()
    await expect(
      authHeader.getByRole("heading", { name: "Sign in to your account" })
    ).toBeVisible()
    await expect(
      authHeader.getByText(
        "Enter your credentials to access the Ultimate Electrical Designer platform"
      )
    ).toBeVisible()

    // Verify form elements are present
    await expect(page.locator('input[name="username"]')).toBeVisible()
    await expect(page.locator('input[name="password"]')).toBeVisible()
    await expect(page.locator('button:has-text("Sign In")')).toBeVisible()

    // Verify additional elements
    await expect(page.locator("text=Forgot password?")).toBeVisible()
    await expect(
      page.locator("text=Don't have an account? Sign up")
    ).toBeVisible()

    // Verify password visibility toggle
    await expect(
      page.locator('button[aria-label="Toggle password visibility"]')
    ).toBeVisible()
  })

  test("should handle successful login flow", async ({ page }) => {
    // Fill in valid credentials (email + password per form validation)
    await page.fill('input[name="username"]', "<EMAIL>")
    await page.fill('input[name="password"]', "password123")

    // Submit the form
    await page.click('button:has-text("Sign In")')

    // Wait for navigation to dashboard
    await expect(page).toHaveURL("/")

    // Verify dashboard elements are present (indicating successful login)
    await expect(
      page.getByRole("heading", { name: "Critical Alerts" })
    ).toBeVisible()
    await expect(page.locator("text=System Health")).toBeVisible()
  })

  test("should handle invalid credentials error", async ({ page }) => {
    // Fill in invalid credentials (must be a valid email per form schema)
    await page.fill('input[name="username"]', "<EMAIL>")
    await page.fill('input[name="password"]', "wrongpassword")

    // Submit the form
    await page.click('button:has-text("Sign In")')

    // Wait for form processing and error display
    await page.waitForTimeout(2000)

    // Wait for and verify error message (check for any error display)
    await expect(
      page.locator("[role='alert'], .text-destructive").first()
    ).toBeVisible()

    // Ensure we're still on the login page
    await expect(page).toHaveURL("/login")

    // Verify form is still functional
    await expect(page.locator('input[name="username"]')).toBeVisible()
    await expect(page.locator('input[name="password"]')).toBeVisible()
  })

  test("should handle validation errors for empty fields", async ({ page }) => {
    // Try to submit empty form
    await page.click('button:has-text("Sign In")')

    // Verify validation errors
    await expect(
      page.locator("text=Email is required (Email or username is required)")
    ).toBeVisible()
    await expect(page.locator("text=Password is required")).toBeVisible()

    // Fill only username and submit (still invalid until it's an email)
    await page.fill('input[name="username"]', "not-an-email")
    await page.click('button:has-text("Sign In")')

    // Verify email format validation
    await expect(
      page.locator("text=Please enter a valid email address")
    ).toBeVisible()
  })

  test("should handle inactive user error", async ({ page }) => {
    // Use credentials for an inactive user (based on mock data)
    await page.fill('input[name="username"]', "<EMAIL>")
    await page.fill('input[name="password"]', "password123")

    // Submit the form
    await page.click('button:has-text("Sign In")')

    // Wait for form processing and error display
    await page.waitForTimeout(2000)

    // Wait for and verify error message (check for any error display)
    await expect(
      page.locator("[role='alert'], .text-destructive").first()
    ).toBeVisible()

    // Ensure we're still on the login page
    await expect(page).toHaveURL("/login")
  })

  test("should toggle password visibility", async ({ page }) => {
    const passwordInput = page.locator('input[name="password"]')
    const toggleButton = page.locator(
      'button[aria-label="Toggle password visibility"]'
    )

    // Initially password should be hidden (type="password")
    await expect(passwordInput).toHaveAttribute("type", "password")

    // Fill in password
    await passwordInput.fill("testpassword")

    // Click toggle to show password
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute("type", "text")

    // Click toggle to hide password again
    await toggleButton.click()
    await expect(passwordInput).toHaveAttribute("type", "password")
  })

  test("should handle forgot password link", async ({ page }) => {
    // Click forgot password link
    await page.click("text=Forgot password?")

    // Verify navigation or modal appears (implementation dependent)
    // This test will need to be updated based on actual forgot password implementation
    // For now, we just ensure the link is clickable
    await expect(page.locator("text=Forgot password?")).toBeVisible()
  })

  test("should handle sign up navigation", async ({ page }) => {
    // Click the specific Sign up button in the form footer
    await page.locator('button:has-text("Sign up")').click()

    // Verify navigation to registration page (assuming /register route)
    await expect(page).toHaveURL("/register")

    // Verify we're on the registration page (use heading to avoid strict mode violation)
    await expect(
      page.getByRole("heading", { name: "Create Your Account" })
    ).toBeVisible()
  })

  test("should be keyboard accessible", async ({ page }) => {
    // Test that key form elements are focusable
    await page.locator('input[name="username"]').first().focus()
    await expect(page.locator('input[name="username"]').first()).toBeFocused()

    await page.locator('input[name="password"]').first().focus()
    await expect(page.locator('input[name="password"]').first()).toBeFocused()

    await page
      .locator('button[aria-label="Toggle password visibility"]')
      .first()
      .focus()
    await expect(
      page.locator('button[aria-label="Toggle password visibility"]').first()
    ).toBeFocused()

    await page.locator('button[type="submit"]').first().focus()
    await expect(page.locator('button[type="submit"]').first()).toBeFocused()

    await page.locator("text=Forgot password?").first().focus()
    await expect(page.locator("text=Forgot password?").first()).toBeFocused()

    await page.locator('button:has-text("Sign up")').first().focus()
    await expect(
      page.locator('button:has-text("Sign up")').first()
    ).toBeFocused()
  })

  test("should support form submission with Enter key", async ({ page }) => {
    // Fill in valid credentials
    await page.fill('input[name="username"]', "<EMAIL>")
    await page.fill('input[name="password"]', "password123")

    // Press Enter to submit form
    await page.keyboard.press("Enter")

    // Wait for navigation to dashboard
    await expect(page).toHaveURL("/")
  })

  test("should show loading state during login", async ({ page }) => {
    // Fill in valid credentials
    await page.fill('input[name="username"]', "<EMAIL>")
    await page.fill('input[name="password"]', "password123")

    // Submit the form and immediately check for loading state
    await page.click('button:has-text("Sign In")')

    // Verify loading state (check for disabled button or loading text)
    await expect(
      page
        .locator('button[type="submit"][disabled]')
        .or(page.locator('button:has-text("Signing in...")'))
    ).toBeVisible({ timeout: 1000 })

    // Wait for navigation to complete
    await expect(page).toHaveURL("/", { timeout: 10000 })
  })

  test("should persist login state across page refreshes", async ({ page }) => {
    // Login successfully
    await page.fill('input[name="username"]', "<EMAIL>")
    await page.fill('input[name="password"]', "password123")
    await page.click('button:has-text("Sign In")')

    // Wait for navigation to dashboard
    await expect(page).toHaveURL("/")

    // Refresh the page
    await page.reload()

    // Should still be on dashboard (not redirected to login)
    await expect(page).toHaveURL("/")
    await expect(
      page.getByRole("heading", { name: "Critical Alerts" })
    ).toBeVisible()
  })

  test("should handle network errors gracefully", async ({ page }) => {
    // Simulate network failure by going offline
    await page.context().setOffline(true)

    // Fill in credentials and submit
    await page.fill('input[name="username"]', "<EMAIL>")
    await page.fill('input[name="password"]', "password123")
    await page.click('button:has-text("Sign In")')

    // Should show network error message (check for any error display)
    await expect(
      page.locator("[role='alert'], .text-destructive").first()
    ).toBeVisible()

    // Go back online
    await page.context().setOffline(false)

    // Wait a moment for network to be restored
    await page.waitForTimeout(2000)

    // Reload the page to ensure clean state
    await page.reload()

    // Fill credentials and submit again
    await page.fill('input[name="username"]', "<EMAIL>")
    await page.fill('input[name="password"]', "password123")
    await page.click('button:has-text("Sign In")')

    // Wait for navigation to dashboard
    await expect(page).toHaveURL("/", { timeout: 15000 })
  })
})
