import path from "path"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vitest/config"

import DomainReporter from "./src/test/reporters/domain-reporter"

/// <reference types="@vitest/browser/providers/playwright" />
/// <reference types="vitest" />

export default defineConfig({
  plugins: [react()],
  server: {
    host: "**************",
  },
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./vitest.setup.ts"],
    include: ["src/**/*.{test,spec}.{ts,tsx}"],
    reporters: ["dot", new DomainReporter()],
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html", "lcov"],
    },
    pool: "threads",
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    __dirname: JSON.stringify(__dirname),
  },
})
