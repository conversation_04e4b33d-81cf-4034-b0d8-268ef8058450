import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';

const compat = new FlatCompat({
    baseDirectory: import.meta.dirname,
    recommendedConfig: js.configs.recommended,
});

const eslintConfig = [
    ...compat.config({
        extends: [
            'eslint:recommended',
            'next',
            'prettier',
        ],
        overrides: [
            {
                files: ['**/*.{ts,tsx}'],
                rules: {
                    'prefer-const': 'off',
                },
            },
        ],
        rules: {
            'no-unused-vars': 'off',
            'prefer-const': 'off',
            'no-undef': 'off',
        },
        ignorePatterns: ['__tests__/**'],
    }),
];

export default eslintConfig;