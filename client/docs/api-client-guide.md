# API Client Guide

This guide provides comprehensive documentation for the frontend API client system, including type-safe endpoints, React Query hooks, and offline capabilities.

## Overview

The API client system provides:

- **Type-safe API endpoints** for all backend operations
- **React Query hooks** for data fetching and mutations
- **Offline support** with automatic synchronization
- **Comprehensive error handling** and validation
- **Optimistic updates** and cache management

## Architecture

```
src/lib/api/
├── types/           # TypeScript interface definitions
├── endpoints/       # API endpoint functions
├── keys.ts         # React Query cache keys
└── client.ts       # Base API client configuration

src/hooks/api/      # React Query hooks
├── useAuth.ts      # Authentication hooks
├── useUsers.ts     # User management hooks
├── useComponents.ts # Component management hooks
├── useProjects.ts  # Project management hooks
└── useTasks.ts     # Task management hooks
```

## Quick Start

### 1. Authentication

```typescript
import { useLogin, useCurrentUser, useLogout } from '@/hooks/api/useAuth'

function LoginForm() {
  const login = useLogin()
  const { data: user } = useCurrentUser()
  const logout = useLogout()

  const handleLogin = async (credentials) => {
    try {
      await login.mutateAsync(credentials)
      // User is automatically logged in and cached
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  return (
    <div>
      {user ? (
        <div>
          <p>Welcome, {user.first_name}!</p>
          <button onClick={() => logout.mutate()}>Logout</button>
        </div>
      ) : (
        <LoginForm onSubmit={handleLogin} />
      )}
    </div>
  )
}
```

### 2. Data Fetching

```typescript
import { useComponents, useComponent } from '@/hooks/api/useComponents'

function ComponentList() {
  const { data: components, isLoading, error } = useComponents({
    page: 1,
    size: 20,
    category_id: 1,
  })

  if (isLoading) return <div>Loading...</div>
  if (error) return <div>Error: {error.message}</div>

  return (
    <div>
      {components?.items.map(component => (
        <ComponentCard key={component.id} component={component} />
      ))}
    </div>
  )
}

function ComponentDetail({ id }: { id: number }) {
  const { data: component, isLoading } = useComponent(id)

  if (isLoading) return <div>Loading component...</div>

  return (
    <div>
      <h1>{component?.name}</h1>
      <p>Manufacturer: {component?.manufacturer}</p>
      <p>Part Number: {component?.part_number}</p>
    </div>
  )
}
```

### 3. Mutations

```typescript
import { useCreateComponent, useUpdateComponent } from '@/hooks/api/useComponents'

function ComponentForm({ component, onSuccess }) {
  const createComponent = useCreateComponent()
  const updateComponent = useUpdateComponent()

  const handleSubmit = async (data) => {
    try {
      if (component?.id) {
        await updateComponent.mutateAsync({ id: component.id, data })
      } else {
        await createComponent.mutateAsync(data)
      }
      onSuccess()
    } catch (error) {
      console.error('Save failed:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      <button 
        type="submit" 
        disabled={createComponent.isPending || updateComponent.isPending}
      >
        {component?.id ? 'Update' : 'Create'} Component
      </button>
    </form>
  )
}
```

## API Endpoints

### Authentication Endpoints

```typescript
import { authApi } from '@/lib/api/endpoints'

// Login
const response = await authApi.login({
  email: '<EMAIL>',
  password: 'password123'
})

// Register
const user = await authApi.register({
  email: '<EMAIL>',
  password: 'password123',
  first_name: 'John',
  last_name: 'Doe'
})

// Get current user
const currentUser = await authApi.getCurrentUser()

// Change password
await authApi.changePassword({
  current_password: 'oldpassword',
  new_password: 'newpassword',
  confirm_password: 'newpassword'
})
```

### Component Endpoints

```typescript
import { componentsApi } from '@/lib/api/endpoints'

// List components with filters
const components = await componentsApi.list({
  page: 1,
  size: 20,
  category_id: 1,
  manufacturer: 'Texas Instruments',
  is_preferred: true
})

// Get component by ID
const component = await componentsApi.get(123)

// Create component
const newComponent = await componentsApi.create({
  name: 'LM358 Op-Amp',
  manufacturer: 'Texas Instruments',
  part_number: 'LM358N',
  category_id: 1,
  type_id: 2,
  specifications: {
    voltage_supply: '3V to 32V',
    channels: 2,
    package: 'DIP-8'
  }
})

// Advanced search
const searchResults = await componentsApi.search({
  query: 'operational amplifier',
  filters: {
    category_ids: [1, 2],
    specifications: {
      voltage: ['3.3V', '5V'],
      package: ['DIP-8', 'SOIC-8']
    }
  },
  limit: 50
})
```

### Project Endpoints

```typescript
import { projectsApi } from '@/lib/api/endpoints'

// List projects
const projects = await projectsApi.list({
  status: 'active',
  client: 'Acme Corp'
})

// Create project
const project = await projectsApi.create({
  name: 'IoT Sensor Network',
  description: 'Smart building sensor deployment',
  client: 'Acme Corp',
  start_date: '2023-01-01',
  end_date: '2023-12-31',
  budget: 50000
})

// Add team member
await projectsApi.addMember(project.id, {
  user_id: 123,
  role_id: 2,
  permissions: ['read', 'write'],
  hourly_rate: 75.00
})
```

## React Query Hooks

### Query Hooks

All query hooks support standard React Query options:

```typescript
const { data, isLoading, error, refetch } = useComponents({
  page: 1,
  size: 20
}, {
  staleTime: 5 * 60 * 1000, // 5 minutes
  refetchOnWindowFocus: false,
  enabled: !!categoryId
})
```

### Mutation Hooks

Mutation hooks automatically handle cache invalidation:

```typescript
const createComponent = useCreateComponent()

// The mutation automatically:
// - Invalidates component list queries
// - Updates component statistics
// - Handles optimistic updates
await createComponent.mutateAsync(componentData)
```

### Pagination

```typescript
function ComponentList() {
  const [page, setPage] = useState(1)
  const { data, isLoading } = useComponents({ page, size: 20 })

  return (
    <div>
      {data?.items.map(component => (
        <ComponentCard key={component.id} component={component} />
      ))}
      
      <Pagination
        current={page}
        total={data?.pages || 1}
        onChange={setPage}
      />
    </div>
  )
}
```

## Offline Support

The system includes comprehensive offline support:

### Automatic Offline Detection

```typescript
import { useOfflineMutation } from '@/hooks/useOfflineMutation'

function CreateComponentOffline() {
  const createComponent = useOfflineMutation({
    endpoint: '/components',
    method: 'POST',
    mutationKey: 'createComponent'
  })

  // Works online and offline
  const handleCreate = (data) => {
    createComponent.mutate(data)
  }
}
```

### Sync Status

```typescript
import { useSyncStatus } from '@/hooks/useSyncStatus'

function SyncIndicator() {
  const { isOnline, pendingMutations, isSyncing } = useSyncStatus()

  return (
    <div>
      <span>Status: {isOnline ? 'Online' : 'Offline'}</span>
      {pendingMutations > 0 && (
        <span>Pending: {pendingMutations} operations</span>
      )}
      {isSyncing && <span>Syncing...</span>}
    </div>
  )
}
```

## Error Handling

### Global Error Handling

```typescript
import { QueryClient } from '@tanstack/react-query'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false
        }
        return failureCount < 3
      }
    }
  }
})
```

### Component-Level Error Handling

```typescript
function ComponentList() {
  const { data, error, isError } = useComponents()

  if (isError) {
    return (
      <ErrorBoundary error={error}>
        <div>Failed to load components</div>
        <button onClick={() => refetch()}>Retry</button>
      </ErrorBoundary>
    )
  }

  return <div>{/* Component list */}</div>
}
```

## Performance Optimization

### Query Key Management

```typescript
import { QueryKeys } from '@/lib/api/keys'

// Invalidate all component queries
queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })

// Invalidate specific component
queryClient.invalidateQueries({ queryKey: QueryKeys.components.detail(123) })

// Prefetch related data
queryClient.prefetchQuery({
  queryKey: QueryKeys.components.similar(123),
  queryFn: () => componentsApi.getSimilar(123)
})
```

### Optimistic Updates

```typescript
const updateComponent = useMutation({
  mutationFn: ({ id, data }) => componentsApi.update(id, data),
  onMutate: async ({ id, data }) => {
    // Cancel outgoing refetches
    await queryClient.cancelQueries({ queryKey: QueryKeys.components.detail(id) })

    // Snapshot previous value
    const previousComponent = queryClient.getQueryData(QueryKeys.components.detail(id))

    // Optimistically update
    queryClient.setQueryData(QueryKeys.components.detail(id), old => ({
      ...old,
      ...data
    }))

    return { previousComponent }
  },
  onError: (err, variables, context) => {
    // Rollback on error
    queryClient.setQueryData(
      QueryKeys.components.detail(variables.id),
      context.previousComponent
    )
  }
})
```

## Best Practices

### 1. Use TypeScript

Always use the provided TypeScript interfaces:

```typescript
import type { ComponentCreate, ComponentUpdate } from '@/lib/api/types'

const createData: ComponentCreate = {
  name: 'Component Name',
  manufacturer: 'Manufacturer',
  // TypeScript ensures all required fields are present
}
```

### 2. Handle Loading States

```typescript
function ComponentDetail({ id }: { id: number }) {
  const { data: component, isLoading, error } = useComponent(id)

  if (isLoading) return <ComponentSkeleton />
  if (error) return <ErrorMessage error={error} />
  if (!component) return <NotFound />

  return <ComponentView component={component} />
}
```

### 3. Batch Operations

Use bulk operations for better performance:

```typescript
const bulkUpdate = useBulkUpdateComponents()

const handleBulkUpdate = (componentIds: number[], updates: ComponentUpdate) => {
  bulkUpdate.mutate({
    updates: componentIds.map(id => ({ id, data: updates }))
  })
}
```

### 4. Cache Management

```typescript
// Prefetch data for better UX
const prefetchComponent = (id: number) => {
  queryClient.prefetchQuery({
    queryKey: QueryKeys.components.detail(id),
    queryFn: () => componentsApi.get(id),
    staleTime: 10 * 60 * 1000 // 10 minutes
  })
}

// Clear cache when needed
const clearComponentCache = () => {
  queryClient.removeQueries({ queryKey: QueryKeys.components.all })
}
```

## Migration Guide

See [Migration Guide](./migration-guide.md) for upgrading from the previous API client implementation.

## Troubleshooting

### Common Issues

1. **Type errors**: Ensure you're importing types from `@/lib/api/types`
2. **Cache not updating**: Check that mutation hooks are properly invalidating queries
3. **Offline sync issues**: Verify IndexedDB permissions and storage quota
4. **Performance issues**: Use React Query DevTools to inspect cache behavior

### Debug Tools

```typescript
// Enable React Query DevTools in development
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

function App() {
  return (
    <div>
      {/* Your app */}
      <ReactQueryDevtools initialIsOpen={false} />
    </div>
  )
}
```
