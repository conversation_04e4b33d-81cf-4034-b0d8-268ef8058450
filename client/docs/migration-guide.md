# API Client Migration Guide

This guide helps you migrate from the previous API client implementation to the new comprehensive, type-safe API client system.

## Overview of Changes

The new API client system provides:

- **Enhanced Type Safety**: Comprehensive TypeScript interfaces for all API operations
- **React Query Integration**: Built-in caching, background updates, and optimistic updates
- **Offline Support**: Automatic offline operation queuing and synchronization
- **Better Error Handling**: Standardized error handling across all endpoints
- **Improved Performance**: Intelligent caching and query optimization

## Breaking Changes

### 1. Import Paths

**Before:**
```typescript
import { apiClient } from '@/lib/api'
import { useAuth } from '@/hooks/useAuth'
```

**After:**
```typescript
import { authApi } from '@/lib/api/endpoints'
import { useLogin, useCurrentUser } from '@/hooks/api/useAuth'
```

### 2. API Client Usage

**Before:**
```typescript
// Direct API client calls
const response = await apiClient.post('/auth/login', credentials)
const user = await apiClient.get('/auth/me')
```

**After:**
```typescript
// Type-safe endpoint functions
const response = await authApi.login(credentials)
const user = await authApi.getCurrentUser()
```

### 3. React Query Hooks

**Before:**
```typescript
const { data: user } = useQuery(['auth', 'me'], () => 
  apiClient.get('/auth/me')
)

const loginMutation = useMutation((credentials) =>
  apiClient.post('/auth/login', credentials)
)
```

**After:**
```typescript
const { data: user } = useCurrentUser()
const loginMutation = useLogin()
```

## Step-by-Step Migration

### Step 1: Update Imports

Replace old imports with new structured imports:

```typescript
// Old
import { apiClient } from '@/lib/api'
import { useAuth, useUsers, useComponents } from '@/hooks'

// New
import { authApi, usersApi, componentsApi } from '@/lib/api/endpoints'
import { useLogin, useCurrentUser } from '@/hooks/api/useAuth'
import { useUsers, useCreateUser } from '@/hooks/api/useUsers'
import { useComponents, useCreateComponent } from '@/hooks/api/useComponents'
```

### Step 2: Replace Direct API Calls

**Authentication:**
```typescript
// Old
const login = async (credentials) => {
  const response = await apiClient.post('/auth/login', credentials)
  return response.data
}

// New
const login = async (credentials) => {
  return await authApi.login(credentials)
}
```

**User Management:**
```typescript
// Old
const getUsers = async (params) => {
  const response = await apiClient.get('/users', { params })
  return response.data
}

// New
const getUsers = async (params) => {
  return await usersApi.list(params)
}
```

**Component Management:**
```typescript
// Old
const createComponent = async (data) => {
  const response = await apiClient.post('/components', data)
  return response.data
}

// New
const createComponent = async (data) => {
  return await componentsApi.create(data)
}
```

### Step 3: Update React Query Usage

**Data Fetching:**
```typescript
// Old
function UserList() {
  const { data: users, isLoading } = useQuery(
    ['users', { page: 1, size: 20 }],
    () => apiClient.get('/users', { params: { page: 1, size: 20 } })
  )

  return (
    <div>
      {users?.data?.items.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  )
}

// New
function UserList() {
  const { data: users, isLoading } = useUsers({ page: 1, size: 20 })

  return (
    <div>
      {users?.items.map(user => (
        <UserCard key={user.id} user={user} />
      ))}
    </div>
  )
}
```

**Mutations:**
```typescript
// Old
function CreateUserForm() {
  const queryClient = useQueryClient()
  const createUser = useMutation(
    (userData) => apiClient.post('/users', userData),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['users'])
      }
    }
  )

  const handleSubmit = (data) => {
    createUser.mutate(data)
  }

  return <form onSubmit={handleSubmit}>...</form>
}

// New
function CreateUserForm() {
  const createUser = useCreateUser()

  const handleSubmit = (data) => {
    createUser.mutate(data)
    // Cache invalidation is handled automatically
  }

  return <form onSubmit={handleSubmit}>...</form>
}
```

### Step 4: Add Type Safety

**Before (no types):**
```typescript
const createComponent = async (data) => {
  return await apiClient.post('/components', data)
}
```

**After (with types):**
```typescript
import type { ComponentCreate, ComponentRead } from '@/lib/api/types'

const createComponent = async (data: ComponentCreate): Promise<ComponentRead> => {
  return await componentsApi.create(data)
}
```

### Step 5: Update Error Handling

**Before:**
```typescript
try {
  const response = await apiClient.post('/users', userData)
  return response.data
} catch (error) {
  if (error.response?.status === 400) {
    // Handle validation error
  }
  throw error
}
```

**After:**
```typescript
const createUser = useCreateUser()

createUser.mutate(userData, {
  onError: (error) => {
    if (error.response?.status === 400) {
      // Handle validation error
      // Error structure is now typed
    }
  }
})
```

## Component-Specific Migrations

### Authentication Components

**Before:**
```typescript
function LoginForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState(null)

  const handleLogin = async (credentials) => {
    setIsLoading(true)
    setError(null)
    try {
      const response = await apiClient.post('/auth/login', credentials)
      // Handle success
    } catch (err) {
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleLogin}>
      {error && <div className="error">{error}</div>}
      <button disabled={isLoading}>
        {isLoading ? 'Logging in...' : 'Login'}
      </button>
    </form>
  )
}
```

**After:**
```typescript
function LoginForm() {
  const login = useLogin()

  const handleLogin = (credentials) => {
    login.mutate(credentials)
  }

  return (
    <form onSubmit={handleLogin}>
      {login.error && (
        <div className="error">{login.error.message}</div>
      )}
      <button disabled={login.isPending}>
        {login.isPending ? 'Logging in...' : 'Login'}
      </button>
    </form>
  )
}
```

### Data Lists

**Before:**
```typescript
function ComponentList() {
  const [components, setComponents] = useState([])
  const [loading, setLoading] = useState(true)
  const [page, setPage] = useState(1)

  useEffect(() => {
    const fetchComponents = async () => {
      setLoading(true)
      try {
        const response = await apiClient.get('/components', {
          params: { page, size: 20 }
        })
        setComponents(response.data.items)
      } catch (error) {
        console.error('Failed to fetch components:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchComponents()
  }, [page])

  return (
    <div>
      {loading ? (
        <div>Loading...</div>
      ) : (
        components.map(component => (
          <ComponentCard key={component.id} component={component} />
        ))
      )}
    </div>
  )
}
```

**After:**
```typescript
function ComponentList() {
  const [page, setPage] = useState(1)
  const { data: components, isLoading } = useComponents({ page, size: 20 })

  return (
    <div>
      {isLoading ? (
        <div>Loading...</div>
      ) : (
        components?.items.map(component => (
          <ComponentCard key={component.id} component={component} />
        ))
      )}
    </div>
  )
}
```

## New Features Available

### 1. Offline Support

```typescript
// Automatically works offline
function CreateComponentOffline() {
  const createComponent = useCreateComponent()

  const handleCreate = (data) => {
    // Works online and offline
    // Automatically syncs when back online
    createComponent.mutate(data)
  }

  return <ComponentForm onSubmit={handleCreate} />
}
```

### 2. Optimistic Updates

```typescript
function UpdateComponent({ component }) {
  const updateComponent = useUpdateComponent()

  const handleUpdate = (data) => {
    // UI updates immediately, rolls back on error
    updateComponent.mutate({ id: component.id, data })
  }

  return <ComponentForm initialData={component} onSubmit={handleUpdate} />
}
```

### 3. Advanced Search

```typescript
function ComponentSearch() {
  const searchComponents = useSearchComponents()

  const handleSearch = (criteria) => {
    searchComponents.mutate({
      query: criteria.query,
      filters: {
        category_ids: criteria.categories,
        specifications: criteria.specs
      }
    })
  }

  return (
    <div>
      <SearchForm onSubmit={handleSearch} />
      {searchComponents.data?.results.map(component => (
        <ComponentCard key={component.id} component={component} />
      ))}
    </div>
  )
}
```

### 4. Bulk Operations

```typescript
function BulkComponentActions() {
  const bulkUpdate = useBulkUpdateComponents()
  const bulkDelete = useBulkDeleteComponents()

  const handleBulkUpdate = (componentIds, updates) => {
    bulkUpdate.mutate({
      updates: componentIds.map(id => ({ id, data: updates }))
    })
  }

  const handleBulkDelete = (componentIds) => {
    bulkDelete.mutate(componentIds)
  }

  return (
    <div>
      <button onClick={() => handleBulkUpdate(selectedIds, updateData)}>
        Update Selected
      </button>
      <button onClick={() => handleBulkDelete(selectedIds)}>
        Delete Selected
      </button>
    </div>
  )
}
```

## Testing Updates

### Before:
```typescript
// Manual mocking
jest.mock('@/lib/api', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
  }
}))
```

### After:
```typescript
// Use provided test utilities
import { renderWithQueryClient } from '@/test-utils'
import { server } from '@/mocks/server'

// MSW handlers are already set up for all endpoints
test('should create component', async () => {
  const { user } = renderWithQueryClient(<CreateComponentForm />)
  
  await user.type(screen.getByLabelText('Name'), 'Test Component')
  await user.click(screen.getByRole('button', { name: 'Create' }))
  
  expect(await screen.findByText('Component created')).toBeInTheDocument()
})
```

## Performance Improvements

The new system provides several performance benefits:

1. **Automatic Caching**: Data is cached and reused across components
2. **Background Updates**: Data is refreshed in the background
3. **Request Deduplication**: Identical requests are automatically deduplicated
4. **Optimistic Updates**: UI updates immediately for better perceived performance
5. **Intelligent Refetching**: Only refetches when necessary

## Troubleshooting

### Common Migration Issues

1. **Type Errors**: 
   - Ensure you're importing types from `@/lib/api/types`
   - Check that your data structures match the new interfaces

2. **Missing Data**:
   - The response structure may have changed (e.g., `response.data.items` → `response.items`)
   - Check the new API documentation for correct response formats

3. **Cache Issues**:
   - Clear your browser cache and restart the development server
   - Check React Query DevTools to inspect cache state

4. **Hook Dependencies**:
   - Some hooks now have different dependency arrays
   - Check the new hook documentation for correct usage

### Getting Help

- Check the [API Client Guide](./api-client-guide.md) for detailed usage examples
- Use React Query DevTools to debug cache and query issues
- Review the TypeScript errors carefully - they often point to the exact issue

## Rollback Plan

If you need to rollback temporarily:

1. Keep the old API client code in a separate branch
2. Use feature flags to toggle between old and new implementations
3. Migrate components gradually rather than all at once

```typescript
// Feature flag approach
const useNewApiClient = process.env.REACT_APP_NEW_API_CLIENT === 'true'

function ComponentList() {
  if (useNewApiClient) {
    return <NewComponentList />
  }
  return <OldComponentList />
}
```

This allows for a gradual migration and easy rollback if issues arise.
