<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/design/">
        <link rel="shortcut icon" href="../img/favicon.ico">
        <title>Design - Ultimate Electrical Designer Docs</title>
        <link href="../css/bootstrap.min.css" rel="stylesheet">
        <link href="../css/fontawesome.min.css" rel="stylesheet">
        <link href="../css/brands.min.css" rel="stylesheet">
        <link href="../css/solid.min.css" rel="stylesheet">
        <link href="../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href=".." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="./" class="nav-link active" aria-current="page">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../tasks/" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                            <li class="nav-item">
                                <a rel="prev" href="../requirements/" class="nav-link">
                                    <i class="fa fa-arrow-left"></i> Previous
                                </a>
                            </li>
                            <li class="nav-item">
                                <a rel="next" href="../tasks/" class="nav-link">
                                    Next <i class="fa fa-arrow-right"></i>
                                </a>
                            </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#technical-design-specification" class="nav-link">Technical Design Specification</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#ultimate-electrical-designer" class="nav-link">Ultimate Electrical Designer</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#architecture-overview" class="nav-link">Architecture Overview</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#system-architecture" class="nav-link">System Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#shared-local-database-architecture" class="nav-link">Shared Local Database Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#backend-synchronization-service-architecture" class="nav-link">Backend Synchronization Service Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#frontend-caching-offline-handling-architecture" class="nav-link">Frontend Caching &amp; Offline Handling Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#component-interactions" class="nav-link">Component Interactions</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#database-schema-design" class="nav-link">Database Schema Design</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#project-task-management-architecture" class="nav-link">Project Task Management Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#frontend-component-architecture" class="nav-link">Frontend Component Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#data-validation-integrity-architecture" class="nav-link">Data Validation &amp; Integrity Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#security-architecture" class="nav-link">Security Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#performance-architecture" class="nav-link">Performance Architecture</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#integration-patterns" class="nav-link">Integration Patterns</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="technical-design-specification">Technical Design Specification<a class="headerlink" href="#technical-design-specification" title="Permanent link">&para;</a></h1>
<h2 id="ultimate-electrical-designer">Ultimate Electrical Designer<a class="headerlink" href="#ultimate-electrical-designer" title="Permanent link">&para;</a></h2>
<p><strong>Document Version:</strong> 1.0 <strong>Last Updated:</strong> July 2025 <strong>References:</strong> <a href="../product/">product.md</a>,
<a href="../structure/">structure.md</a>, <a href="../tech/">tech.md</a>, <a href="../rules/">rules.md</a>, <a href="../requirements/">requirements.md</a></p>
<hr />
<h2 id="architecture-overview">Architecture Overview<a class="headerlink" href="#architecture-overview" title="Permanent link">&para;</a></h2>
<p>The Ultimate Electrical Designer implements a modern, scalable architecture designed for professional electrical
engineering applications. The system follows a 5-layer backend architecture with a React-based frontend, unified error
handling, and comprehensive security framework.</p>
<hr />
<h2 id="system-architecture">System Architecture<a class="headerlink" href="#system-architecture" title="Permanent link">&para;</a></h2>
<h3 id="high-level-architecture-diagram">High-Level Architecture Diagram<a class="headerlink" href="#high-level-architecture-diagram" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
├─────────────────────────────────────────────────────────────────┤
│  Next.js React Frontend (TypeScript)                           │
│  ├── App Router (Pages &amp; Layouts)                              │
│  ├── Component Library (Radix UI + Tailwind)                   │
│  ├── State Management (React Query + Zustand)                  │
│  └── Real-time Communication (WebSocket)                       │
└─────────────────────────────────────────────────────────────────┘
                                │
                        HTTPS/WebSocket
                                │
┌─────────────────────────────────────────────────────────────────┐
│                     API Gateway Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  FastAPI Application (Python)                                  │
│  ├── Authentication Middleware                                 │
│  ├── Security Middleware                                       │
│  ├── Performance Monitoring                                    │
│  └── CORS &amp; Rate Limiting                                      │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    5-Layer Backend Architecture                 │
├─────────────────────────────────────────────────────────────────┤
│  Layer 1: API Routes (FastAPI Endpoints)                       │
│  ├── Authentication Routes (/api/v1/auth)                      │
│  ├── Component Routes (/api/v1/components)                     │
│  ├── Calculation Routes (/api/v1/calculations) [Planned]       │
│  ├── Project Routes (/api/v1/projects)                         │
│  └── Task Management Routes (/api/v1/tasks)                    │
├─────────────────────────────────────────────────────────────────┤
│  Layer 2: Business Services                                    │
│  ├── Authentication Service                                    │
│  ├── Component Management Service                              │
│  ├── Calculation Engine Service                                │
│  ├── Project Management Service                                │
│  └── Task Management Service                                   │
├─────────────────────────────────────────────────────────────────┤
│  Layer 3: Data Repositories                                    │
│  ├── User Repository                                           │
│  ├── Component Repository                                      │
│  ├── Calculation Repository                                    │
│  ├── Project Repository                                        │
│  └── Task Repository                                           │
├─────────────────────────────────────────────────────────────────┤
│  Layer 4: Data Models (SQLAlchemy)                             │
│  ├── User Models                                               │
│  ├── Component Models                                          │
│  ├── Calculation Models                                        │
│  ├── Project Models                                            │
│  └── Task Models                                               │
├─────────────────────────────────────────────────────────────────┤
│  Layer 5: Validation Schemas (Pydantic)                        │
│  ├── Request/Response Schemas                                  │
│  ├── Data Transfer Objects                                     │
│  └── Validation Rules                                          │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      Data Layer                                │
├─────────────────────────────────────────────────────────────────┤
│  PostgreSQL (Production / Shared Local)                      │
│  ├── User Management Tables                                    │
│  ├── Component Library Tables                                  │
│  ├── Calculation Results Tables                                │
│  ├── Project Data Tables                                       │
│  └── Task Management Tables                                    │
└─────────────────────────────────────────────────────────────────┘
</code></pre></div>
<hr />
<h2 id="shared-local-database-architecture">Shared Local Database Architecture<a class="headerlink" href="#shared-local-database-architecture" title="Permanent link">&para;</a></h2>
<p>The Ultimate Electrical Designer supports a flexible and robust data management strategy that enables both fully online
and locally-hosted collaborative workflows. This is achieved through a <strong>shared local database architecture</strong>, where a
project's data can be hosted on a local PostgreSQL server, which in turn synchronizes with a central, cloud-hosted
PostgreSQL database. This ensures data consistency and allows teams to work efficiently on a local network with the
safety of a cloud backup and the option for broader collaboration.</p>
<hr />
<h3 id="1-high-level-architecture-diagram-shared-local-database">1. High-Level Architecture Diagram (Shared Local Database)<a class="headerlink" href="#1-high-level-architecture-diagram-shared-local-database" title="Permanent link">&para;</a></h3>
<p>The following diagram illustrates the data flow when operating in a shared local database mode.</p>
<div class="highlight"><pre><span></span><code>graph TD
    subgraph Local Network
        C1[Client App 1] --&gt; SLP
        C2[Client App 2] --&gt; SLP
        C3[Client App 3] --&gt; SLP
    end

    subgraph Backend Services
        API(API Gateway / FastAPI)
    end

    subgraph Cloud Infrastructure
        CP(Central PostgreSQL)
    end

    SLP(Shared Local PostgreSQL) -- Bi-directional Sync --&gt; API
    API -- Bi-directional Sync --&gt; CP

    style SLP fill:#d6eaff
    style CP fill:#d6eaff
</code></pre></div>
<h3 id="2-synchronization-strategy-bi-directional-with-last-write-wins">2. Synchronization Strategy: Bi-Directional with "Last-Write Wins"<a class="headerlink" href="#2-synchronization-strategy-bi-directional-with-last-write-wins" title="Permanent link">&para;</a></h3>
<p>A <strong>bi-directional synchronization</strong> strategy ensures data consistency between the shared local PostgreSQL and the
central online PostgreSQL. This strategy relies on timestamps and a <strong>"last-write wins"</strong> conflict resolution rule.</p>
<h4 id="mechanism">Mechanism<a class="headerlink" href="#mechanism" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Change Data Capture (CDC)</strong>: Changes in both the local and central PostgreSQL databases will be tracked. This can be
  achieved through logical replication, custom triggers, or a dedicated CDC tool like Debezium.</li>
<li><strong>Synchronization Triggers</strong>: Synchronization can be initiated through various mechanisms, such as periodic schedules
  (e.g., every 5 minutes), on-demand user actions, or webhooks from the central server signaling a change.</li>
<li><strong>Conflict Resolution</strong>: The <code>last-write wins</code> policy is applied. When a synchronization event occurs, the record with
  the most recent <code>last_modified_at</code> timestamp will be considered the authoritative version. Conflicting changes are
  logged for audit purposes.</li>
</ul>
<h4 id="synchronization-flow">Synchronization Flow<a class="headerlink" href="#synchronization-flow" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Initiate Sync</strong>: The <code>SynchronizationService</code> in the backend is triggered.</li>
<li><strong>Exchange Changes</strong>: Both the local and central databases exchange data that has changed since the last successful
   sync timestamp.</li>
<li><strong>Apply Changes &amp; Resolve Conflicts</strong>: Each database applies the incoming changes, respecting the "last-write wins"
   rule for any conflicts.</li>
<li><strong>Log &amp; Notify</strong>: All sync operations and conflict resolutions are logged. Notifications can be sent for significant
   events.</li>
</ol>
<h3 id="3-client-side-data-access">3. Client-Side Data Access<a class="headerlink" href="#3-client-side-data-access" title="Permanent link">&para;</a></h3>
<p>Client applications (frontend or other services) will primarily interact with the backend API. The backend is
responsible for managing the connection to the appropriate database (local or central) based on the project's
configuration. This abstraction ensures that the client application does not need to manage database connections
directly.</p>
<p>If a true "offline from local network" capability is required for individual clients, a lightweight caching mechanism
(e.g., using IndexedDB) can be implemented to store transient data, which is then synchronized with the shared local
PostgreSQL server upon reconnection to the local network.</p>
<h3 id="4-architectural-pattern-refinement-5-layer-adaptation">4. Architectural Pattern Refinement (5-Layer Adaptation)<a class="headerlink" href="#4-architectural-pattern-refinement-5-layer-adaptation" title="Permanent link">&para;</a></h3>
<p>The 5-layer backend architecture is adapted to support this new model, primarily by enhancing the
<code>SynchronizationService</code> and making the data access layer (Repositories) more dynamic.</p>
<ul>
<li><strong><code>SynchronizationService</code></strong>: This service is now responsible for the full bi-directional sync between a designated
  shared local PostgreSQL instance and the central PostgreSQL database.</li>
<li><strong>Repository Layer</strong>: Repositories will be configured dynamically to connect to either the central PostgreSQL or a
  specific shared local PostgreSQL based on the active project's settings. This is managed through the dependency
  injection system.</li>
<li><strong>Database Connection Management</strong>: The connection management system will be enhanced to handle a pool of connections
  to the central database as well as dynamic connections to various local PostgreSQL instances as required by active
  projects.</li>
</ul>
<hr />
<h2 id="backend-synchronization-service-architecture">Backend Synchronization Service Architecture<a class="headerlink" href="#backend-synchronization-service-architecture" title="Permanent link">&para;</a></h2>
<p>The Ultimate Electrical Designer implements a sophisticated <strong>Backend Synchronization Service</strong> that enables seamless
bi-directional data synchronization between shared local PostgreSQL databases and the central cloud PostgreSQL database.
This service is a critical component of the 5-layer backend architecture, designed to handle complex synchronization
scenarios while maintaining data integrity and providing robust conflict resolution.</p>
<h3 id="architecture-overview_1">Architecture Overview<a class="headerlink" href="#architecture-overview_1" title="Permanent link">&para;</a></h3>
<p>The <strong>SynchronizationService</strong> operates within <strong>Layer 2: Business Services</strong> of the 5-layer backend architecture and
coordinates with all other layers to provide comprehensive synchronization capabilities.</p>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Layer 1: API Routes&quot;
        SR[Sync Routes&lt;br&gt;/api/v1/sync]
        PR[Project Routes&lt;br&gt;/api/v1/projects]
    end

    subgraph &quot;Layer 2: Business Services&quot;
        SS[SynchronizationService&lt;br&gt;Core Orchestrator]
        PS[ProjectService&lt;br&gt;Business Logic]
    end

    subgraph &quot;Layer 3: Data Repositories&quot;
        SLR[SyncLog Repository&lt;br&gt;Audit Trail]
        PR2[Project Repository&lt;br&gt;CRUD Operations]
    end

    subgraph &quot;Layer 4: Data Models&quot;
        SLM[SynchronizationLog Model&lt;br&gt;Change Tracking]
        PM[Project Model&lt;br&gt;Entity Data]
    end

    subgraph &quot;Layer 5: Validation Schemas&quot;
        SSS[Sync Schemas&lt;br&gt;Request/Response]
        PSS[Project Schemas&lt;br&gt;Data Transfer]
    end

    subgraph &quot;Data Layer&quot;
        LDB[(Local PostgreSQL&lt;br&gt;Shared Database)]
        CDB[(Central PostgreSQL&lt;br&gt;Cloud Database)]
    end

    SR --&gt; SS
    PR --&gt; PS
    SS --&gt; SLR
    SS --&gt; PR2
    PS --&gt; PR2
    SLR --&gt; SLM
    PR2 --&gt; PM
    SS --&gt; SSS
    PS --&gt; PSS
    SLR --&gt; LDB
    SLR --&gt; CDB
    PR2 --&gt; LDB
    PR2 --&gt; CDB

    style SS fill:#ff9999,stroke:#333,stroke-width:3px
    style SLR fill:#99ccff,stroke:#333,stroke-width:2px
</code></pre></div>
<h3 id="core-components">Core Components<a class="headerlink" href="#core-components" title="Permanent link">&para;</a></h3>
<h4 id="1-synchronizationservice-core-orchestrator">1. SynchronizationService (Core Orchestrator)<a class="headerlink" href="#1-synchronizationservice-core-orchestrator" title="Permanent link">&para;</a></h4>
<p><strong>Location:</strong> <code>server/src/core/services/general/synchronization_service.py</code> <strong>Responsibilities:</strong></p>
<ul>
<li>Orchestrate bi-directional synchronization between local and central databases</li>
<li>Implement Change Data Capture (CDC) mechanisms</li>
<li>Handle conflict detection and resolution</li>
<li>Manage synchronization logging and audit trails</li>
<li>Coordinate with other business services for data operations</li>
</ul>
<p><strong>Key Methods:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">SynchronizationService</span><span class="p">:</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">synchronize_project</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SynchronizationResult</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Main synchronization orchestrator for a specific project.</span>

<span class="sd">        Flow:</span>
<span class="sd">        1. Retrieve last sync timestamp for the project</span>
<span class="sd">        2. Get local changes since last sync (Change Data Capture)</span>
<span class="sd">        3. Get central changes since last sync (Change Data Capture)</span>
<span class="sd">        4. Apply bidirectional changes with conflict resolution</span>
<span class="sd">        5. Update sync timestamps and log operations</span>

<span class="sd">        Returns:</span>
<span class="sd">            SynchronizationResult with detailed sync statistics</span>
<span class="sd">        &quot;&quot;&quot;</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_detect_conflicts</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">local_changes</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ChangeRecord</span><span class="p">],</span>
        <span class="n">central_changes</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ChangeRecord</span><span class="p">]</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">ConflictRecord</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Detect conflicts between local and central changes.</span>

<span class="sd">        Conflict Detection Logic:</span>
<span class="sd">        - Same entity modified in both databases</span>
<span class="sd">        - Timestamps overlap or concurrent modifications</span>
<span class="sd">        - Delete vs Update conflicts</span>
<span class="sd">        - Schema version mismatches</span>
<span class="sd">        &quot;&quot;&quot;</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_resolve_conflict</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">conflict</span><span class="p">:</span> <span class="n">ConflictRecord</span><span class="p">,</span>
        <span class="n">strategy</span><span class="p">:</span> <span class="n">ConflictResolutionStrategy</span> <span class="o">=</span> <span class="n">ConflictResolutionStrategy</span><span class="o">.</span><span class="n">LAST_WRITE_WINS</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ConflictResolution</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Resolve detected conflicts using specified strategy.</span>

<span class="sd">        Last-Write Wins Implementation:</span>
<span class="sd">        1. Compare updated_at timestamps</span>
<span class="sd">        2. Choose record with latest timestamp</span>
<span class="sd">        3. Log conflict resolution details</span>
<span class="sd">        4. Apply winning changes to both databases</span>
<span class="sd">        &quot;&quot;&quot;</span>
</code></pre></div>
<h4 id="2-change-data-capture-cdc-mechanism">2. Change Data Capture (CDC) Mechanism<a class="headerlink" href="#2-change-data-capture-cdc-mechanism" title="Permanent link">&para;</a></h4>
<p>The synchronization service implements a <strong>timestamp-based CDC mechanism</strong> that efficiently identifies and captures
changes across all synchronized entities.</p>
<p><strong>CDC Implementation:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_get_local_changes</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">since_timestamp</span><span class="p">:</span> <span class="n">datetime</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">ChangeRecord</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Capture local changes since last synchronization.</span>

<span class="sd">    Detection Strategy:</span>
<span class="sd">    - Query entities with updated_at &gt; since_timestamp</span>
<span class="sd">    - Include created_at for new entities</span>
<span class="sd">    - Track deleted entities through soft-delete flags</span>
<span class="sd">    - Capture related entity changes (cascading updates)</span>

<span class="sd">    Returns:</span>
<span class="sd">        List of ChangeRecord objects with operation type and data</span>
<span class="sd">    &quot;&quot;&quot;</span>

<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_get_central_changes</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">since_timestamp</span><span class="p">:</span> <span class="n">datetime</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">ChangeRecord</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Capture central database changes since last synchronization.</span>

<span class="sd">    Optimizations:</span>
<span class="sd">    - Efficient timestamp-based queries with proper indexing</span>
<span class="sd">    - Batch processing for large change sets</span>
<span class="sd">    - Incremental loading to manage memory usage</span>
<span class="sd">    - Change deduplication and ordering</span>
<span class="sd">    &quot;&quot;&quot;</span>
</code></pre></div>
<h4 id="3-conflict-detection-and-resolution-framework">3. Conflict Detection and Resolution Framework<a class="headerlink" href="#3-conflict-detection-and-resolution-framework" title="Permanent link">&para;</a></h4>
<p>The service implements a comprehensive conflict detection and resolution system with support for multiple strategies,
currently featuring <strong>Last-Write Wins</strong> as the primary strategy.</p>
<p><strong>Conflict Types Handled:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">ConflictType</span><span class="p">(</span><span class="n">Enum</span><span class="p">):</span>
    <span class="n">UPDATE_UPDATE</span> <span class="o">=</span> <span class="s2">&quot;update_update&quot;</span>      <span class="c1"># Same entity updated in both DBs</span>
    <span class="n">CREATE_CREATE</span> <span class="o">=</span> <span class="s2">&quot;create_create&quot;</span>      <span class="c1"># Same entity created in both DBs</span>
    <span class="n">UPDATE_DELETE</span> <span class="o">=</span> <span class="s2">&quot;update_delete&quot;</span>      <span class="c1"># Entity updated locally, deleted centrally</span>
    <span class="n">DELETE_UPDATE</span> <span class="o">=</span> <span class="s2">&quot;delete_update&quot;</span>      <span class="c1"># Entity deleted locally, updated centrally</span>
    <span class="n">SCHEMA_MISMATCH</span> <span class="o">=</span> <span class="s2">&quot;schema_mismatch&quot;</span>  <span class="c1"># Different schema versions</span>
</code></pre></div>
<p><strong>Last-Write Wins Resolution:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_apply_last_write_wins</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">conflict</span><span class="p">:</span> <span class="n">ConflictRecord</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ConflictResolution</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Apply Last-Write Wins conflict resolution strategy.</span>

<span class="sd">    Algorithm:</span>
<span class="sd">    1. Extract timestamps from local and central versions</span>
<span class="sd">    2. Compare timestamps with microsecond precision</span>
<span class="sd">    3. Handle edge cases (identical timestamps, null timestamps)</span>
<span class="sd">    4. Apply winning version to both databases</span>
<span class="sd">    5. Create detailed resolution audit log</span>

<span class="sd">    Edge Cases:</span>
<span class="sd">    - Identical timestamps: Use tie-breaker (server ID, creation order)</span>
<span class="sd">    - Missing timestamps: Treat as oldest version</span>
<span class="sd">    - Clock skew: Apply tolerance window for near-simultaneous updates</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">local_timestamp</span> <span class="o">=</span> <span class="n">conflict</span><span class="o">.</span><span class="n">local_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;updated_at&#39;</span><span class="p">)</span>
    <span class="n">central_timestamp</span> <span class="o">=</span> <span class="n">conflict</span><span class="o">.</span><span class="n">central_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;updated_at&#39;</span><span class="p">)</span>

    <span class="k">if</span> <span class="ow">not</span> <span class="n">local_timestamp</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">central_timestamp</span><span class="p">:</span>
        <span class="c1"># Handle missing timestamp edge case</span>
        <span class="n">winner</span> <span class="o">=</span> <span class="s1">&#39;central&#39;</span> <span class="k">if</span> <span class="n">central_timestamp</span> <span class="k">else</span> <span class="s1">&#39;local&#39;</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="c1"># Compare with microsecond precision</span>
        <span class="n">local_dt</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">fromisoformat</span><span class="p">(</span><span class="n">local_timestamp</span><span class="p">)</span>
        <span class="n">central_dt</span> <span class="o">=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">fromisoformat</span><span class="p">(</span><span class="n">central_timestamp</span><span class="p">)</span>
        <span class="n">winner</span> <span class="o">=</span> <span class="s1">&#39;central&#39;</span> <span class="k">if</span> <span class="n">central_dt</span> <span class="o">&gt;</span> <span class="n">local_dt</span> <span class="k">else</span> <span class="s1">&#39;local&#39;</span>

    <span class="c1"># Apply winning version and log resolution</span>
    <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_apply_resolution</span><span class="p">(</span><span class="n">conflict</span><span class="p">,</span> <span class="n">winner</span><span class="p">)</span>
</code></pre></div>
<h4 id="4-bi-directional-synchronization-flow">4. Bi-Directional Synchronization Flow<a class="headerlink" href="#4-bi-directional-synchronization-flow" title="Permanent link">&para;</a></h4>
<p>The service orchestrates a comprehensive bi-directional synchronization process that ensures data consistency across
both database systems.</p>
<p><strong>Synchronization Sequence Diagram:</strong></p>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant API as Sync API
    participant SS as SynchronizationService
    participant LDB as Local PostgreSQL
    participant CDB as Central PostgreSQL
    participant SL as SyncLog Repository

    API-&gt;&gt;SS: synchronize_project(project_id)
    SS-&gt;&gt;SL: get_last_sync_timestamp(project_id)
    SL--&gt;&gt;SS: last_sync_timestamp

    par Local Changes
        SS-&gt;&gt;LDB: query_changes_since(timestamp)
        LDB--&gt;&gt;SS: local_changes[]
    and Central Changes
        SS-&gt;&gt;CDB: query_changes_since(timestamp)
        CDB--&gt;&gt;SS: central_changes[]
    end

    SS-&gt;&gt;SS: detect_conflicts(local_changes, central_changes)

    loop For each conflict
        SS-&gt;&gt;SS: resolve_conflict(conflict, LAST_WRITE_WINS)
        SS-&gt;&gt;SL: log_conflict_resolution(conflict, resolution)
    end

    par Apply Local to Central
        SS-&gt;&gt;CDB: apply_changes(local_changes)
        CDB--&gt;&gt;SS: local_to_central_result
    and Apply Central to Local
        SS-&gt;&gt;LDB: apply_changes(central_changes)
        LDB--&gt;&gt;SS: central_to_local_result
    end

    SS-&gt;&gt;SL: update_sync_timestamp(project_id, now())
    SS-&gt;&gt;SL: log_sync_operation(sync_result)
    SS--&gt;&gt;API: SynchronizationResult
</code></pre></div>
<h4 id="5-synchronizationlog-functionality">5. SynchronizationLog Functionality<a class="headerlink" href="#5-synchronizationlog-functionality" title="Permanent link">&para;</a></h4>
<p>The service maintains comprehensive audit trails and synchronization history through the <strong>SynchronizationLog</strong> system.</p>
<p><strong>SynchronizationLog Model Structure:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">SynchronizationLog</span><span class="p">(</span><span class="n">Base</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Comprehensive audit trail for all synchronization operations.</span>

<span class="sd">    Tracks:</span>
<span class="sd">    - Sync operation metadata (timestamps, duration, status)</span>
<span class="sd">    - Change statistics (created, updated, deleted counts)</span>
<span class="sd">    - Conflict resolution details (detected, resolved, strategies)</span>
<span class="sd">    - Performance metrics (processing time, data volume)</span>
<span class="sd">    - Error information (failures, retries, recovery)</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="nb">id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">primary_key</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">ForeignKey</span><span class="p">(</span><span class="s1">&#39;project.id&#39;</span><span class="p">))</span>

    <span class="c1"># Operation Metadata</span>
    <span class="n">sync_direction</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">20</span><span class="p">))</span>  <span class="c1"># &#39;bidirectional&#39;, &#39;local_to_central&#39;, &#39;central_to_local&#39;</span>
    <span class="n">operation_type</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">50</span><span class="p">))</span>  <span class="c1"># &#39;scheduled&#39;, &#39;manual&#39;, &#39;conflict_resolution&#39;</span>

    <span class="c1"># Timestamps</span>
    <span class="n">started_at</span><span class="p">:</span> <span class="n">datetime</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
    <span class="n">completed_at</span><span class="p">:</span> <span class="n">datetime</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
    <span class="n">duration_ms</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">)</span>

    <span class="c1"># Change Statistics</span>
    <span class="n">local_to_central</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">JSON</span><span class="p">)</span>     <span class="c1"># {&#39;created&#39;: 5, &#39;updated&#39;: 3, &#39;deleted&#39;: 1, &#39;errors&#39;: 0}</span>
    <span class="n">central_to_local</span><span class="p">:</span> <span class="nb">dict</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">JSON</span><span class="p">)</span>     <span class="c1"># {&#39;created&#39;: 2, &#39;updated&#39;: 8, &#39;deleted&#39;: 0, &#39;errors&#39;: 0}</span>

    <span class="c1"># Conflict Resolution</span>
    <span class="n">conflicts_detected</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
    <span class="n">conflicts_resolved</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
    <span class="n">conflict_resolution_strategy</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">50</span><span class="p">))</span>

    <span class="c1"># Status and Error Handling</span>
    <span class="n">status</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">20</span><span class="p">))</span>          <span class="c1"># &#39;completed&#39;, &#39;failed&#39;, &#39;partial&#39;</span>
    <span class="n">error_message</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Text</span><span class="p">,</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">retry_count</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>
</code></pre></div>
<p><strong>Key Sync Logging Methods:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">log_sync_operation</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">sync_result</span><span class="p">:</span> <span class="n">SynchronizationResult</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SynchronizationLog</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Create comprehensive sync operation log entry.</span>

<span class="sd">    Captures:</span>
<span class="sd">    - Complete operation timeline and performance metrics</span>
<span class="sd">    - Detailed change statistics for both directions</span>
<span class="sd">    - Conflict resolution summary and strategy effectiveness</span>
<span class="sd">    - Error details and recovery information</span>
<span class="sd">    - Data volume and processing efficiency metrics</span>
<span class="sd">    &quot;&quot;&quot;</span>

<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_sync_history</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">limit</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">50</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">SynchronizationLog</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Retrieve synchronization history for analysis and debugging.</span>

<span class="sd">    Uses:</span>
<span class="sd">    - Performance monitoring and optimization</span>
<span class="sd">    - Conflict pattern analysis</span>
<span class="sd">    - Troubleshooting sync issues</span>
<span class="sd">    - Audit compliance and reporting</span>
<span class="sd">    &quot;&quot;&quot;</span>
</code></pre></div>
<h3 id="integration-with-5-layer-architecture">Integration with 5-Layer Architecture<a class="headerlink" href="#integration-with-5-layer-architecture" title="Permanent link">&para;</a></h3>
<p>The Backend Synchronization Service is seamlessly integrated within the existing 5-layer architecture:</p>
<p><strong>Layer 1 Integration (API Routes):</strong></p>
<div class="highlight"><pre><span></span><code><span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/projects/</span><span class="si">{project_id}</span><span class="s2">/sync&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">synchronize_project</span><span class="p">(</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">sync_service</span><span class="p">:</span> <span class="n">SynchronizationService</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_sync_service</span><span class="p">),</span>
    <span class="n">current_user</span><span class="p">:</span> <span class="n">User</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_user</span><span class="p">)</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SynchronizationResultSchema</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Trigger manual project synchronization.</span>

<span class="sd">    Validates:</span>
<span class="sd">    - User permissions for project access</span>
<span class="sd">    - Project exists and is sync-enabled</span>
<span class="sd">    - No concurrent sync operations</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="k">await</span> <span class="n">sync_service</span><span class="o">.</span><span class="n">synchronize_project</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>
</code></pre></div>
<p><strong>Layer 3 Integration (Data Repositories):</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">ProjectRepository</span><span class="p">:</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_changes_since</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
        <span class="n">since_timestamp</span><span class="p">:</span> <span class="n">datetime</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">ChangeRecord</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Repository method supporting CDC for synchronization.</span>

<span class="sd">        Optimized for:</span>
<span class="sd">        - Efficient timestamp-based queries</span>
<span class="sd">        - Proper indexing strategy</span>
<span class="sd">        - Change record formatting</span>
<span class="sd">        - Batch processing support</span>
<span class="sd">        &quot;&quot;&quot;</span>
</code></pre></div>
<p><strong>Layer 5 Integration (Validation Schemas):</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">SynchronizationResultSchema</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Response schema for synchronization operations.</span>

<span class="sd">    Provides:</span>
<span class="sd">    - Type-safe API responses</span>
<span class="sd">    - Comprehensive result validation</span>
<span class="sd">    - Client-side TypeScript generation</span>
<span class="sd">    - API documentation integration</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">status</span><span class="p">:</span> <span class="n">SyncStatus</span>
    <span class="n">local_to_central</span><span class="p">:</span> <span class="n">ChangeStatistics</span>
    <span class="n">central_to_local</span><span class="p">:</span> <span class="n">ChangeStatistics</span>
    <span class="n">conflicts_detected</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">conflicts_resolved</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">conflict_resolution_strategy</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">sync_direction</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">started_at</span><span class="p">:</span> <span class="n">datetime</span>
    <span class="n">completed_at</span><span class="p">:</span> <span class="n">datetime</span>
    <span class="n">duration_ms</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">message</span><span class="p">:</span> <span class="nb">str</span>
</code></pre></div>
<h3 id="performance-and-scalability-considerations">Performance and Scalability Considerations<a class="headerlink" href="#performance-and-scalability-considerations" title="Permanent link">&para;</a></h3>
<p>The Backend Synchronization Service is designed with enterprise-grade performance and scalability requirements:</p>
<p><strong>Optimization Strategies:</strong></p>
<ul>
<li><strong>Batched Processing</strong>: Large change sets processed in configurable batches</li>
<li><strong>Concurrent Operations</strong>: Parallel processing of local-to-central and central-to-local sync</li>
<li><strong>Efficient Queries</strong>: Optimized timestamp-based queries with proper indexing</li>
<li><strong>Memory Management</strong>: Streaming large datasets to prevent memory exhaustion</li>
<li><strong>Connection Pooling</strong>: Efficient database connection reuse across sync operations</li>
<li><strong>Async Processing</strong>: Non-blocking operations for improved throughput</li>
</ul>
<p><strong>Scalability Features:</strong></p>
<ul>
<li><strong>Horizontal Scaling</strong>: Multiple sync service instances with coordination</li>
<li><strong>Database Partitioning</strong>: Project-based data partitioning for large deployments</li>
<li><strong>Caching Strategy</strong>: Intelligent caching of sync metadata and conflict patterns</li>
<li><strong>Monitoring Integration</strong>: Comprehensive metrics and alerting for production monitoring</li>
</ul>
<hr />
<h2 id="frontend-caching-offline-handling-architecture">Frontend Caching &amp; Offline Handling Architecture<a class="headerlink" href="#frontend-caching-offline-handling-architecture" title="Permanent link">&para;</a></h2>
<p>The Ultimate Electrical Designer implements a sophisticated <strong>Frontend Caching &amp; Offline Handling system</strong> that provides
seamless offline capabilities and intelligent client-side data management. This system enables users to continue working
productively even when disconnected from the server, with automatic synchronization when connectivity is restored.</p>
<h3 id="architecture-overview_2">Architecture Overview<a class="headerlink" href="#architecture-overview_2" title="Permanent link">&para;</a></h3>
<p>The frontend offline system integrates multiple advanced technologies to create a robust, user-friendly offline
experience:</p>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;React Frontend Layer&quot;
        UI[User Interface Components&lt;br&gt;Projects, Forms, Views]
        OM[useOfflineMutation Hook&lt;br&gt;Offline Operation Handler]
    end

    subgraph &quot;State Management Layer&quot;
        RQ[React Query&lt;br&gt;Server State Manager]
        ZU[Zustand&lt;br&gt;Client State Manager]
    end

    subgraph &quot;Persistence Layer&quot;
        IDP[IndexedDBPersister&lt;br&gt;React Query Persister]
        MO[Mutation Outbox&lt;br&gt;IndexedDB Store]
        QC[Query Cache&lt;br&gt;IndexedDB Store]
    end

    subgraph &quot;Synchronization Layer&quot;
        SM[SyncManager&lt;br&gt;Network &amp; Sync Orchestrator]
        NM[NetworkMonitor&lt;br&gt;Connection Quality Detection]
        CR[ConflictResolver&lt;br&gt;Client-Side Resolution]
    end

    subgraph &quot;Storage Layer&quot;
        IDB[(IndexedDB&lt;br&gt;Browser Database)]
        LS[LocalStorage&lt;br&gt;Preferences &amp; Settings]
    end

    subgraph &quot;Network Layer&quot;
        API[Backend API&lt;br&gt;Sync Endpoints]
        WS[WebSocket&lt;br&gt;Real-time Updates]
    end

    UI --&gt; OM
    UI --&gt; RQ
    UI --&gt; ZU
    OM --&gt; MO
    RQ --&gt; IDP
    RQ --&gt; QC
    IDP --&gt; IDB
    MO --&gt; IDB
    QC --&gt; IDB
    SM --&gt; MO
    SM --&gt; NM
    SM --&gt; CR
    SM --&gt; API
    NM --&gt; SM
    CR --&gt; RQ
    ZU --&gt; LS
    API --&gt; WS

    style SM fill:#ff9999,stroke:#333,stroke-width:3px
    style OM fill:#99ccff,stroke:#333,stroke-width:3px
    style IDP fill:#99ff99,stroke:#333,stroke-width:2px
</code></pre></div>
<h3 id="core-components_1">Core Components<a class="headerlink" href="#core-components_1" title="Permanent link">&para;</a></h3>
<h4 id="1-indexeddbpersister-react-query-integration">1. IndexedDBPersister (React Query Integration)<a class="headerlink" href="#1-indexeddbpersister-react-query-integration" title="Permanent link">&para;</a></h4>
<p><strong>Location:</strong> <code>client/src/lib/cache/indexeddb-persister.ts</code> <strong>Purpose:</strong> Provides seamless persistence of React Query
server state to IndexedDB, enabling data survival across browser sessions and page reloads.</p>
<p><strong>Key Features:</strong></p>
<ul>
<li><strong>Comprehensive State Persistence</strong>: Stores complete React Query client state including queries, mutations, and
  metadata</li>
<li><strong>Efficient Serialization</strong>: Optimized JSON serialization with data deduplication</li>
<li><strong>Automatic Restoration</strong>: Seamless state restoration on application initialization</li>
<li><strong>Performance Optimized</strong>: Batched operations and intelligent caching strategies</li>
</ul>
<p><strong>Implementation Details:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">IndexedDBPersister</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">Persister</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">dbName</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;query-cache&quot;</span><span class="p">;</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">version</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">1</span><span class="p">;</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">db</span><span class="o">:</span><span class="w"> </span><span class="kt">IDBDatabase</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span><span class="p">;</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">persistClient</span><span class="p">(</span><span class="nx">client</span><span class="o">:</span><span class="w"> </span><span class="kt">PersistedClient</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Persist React Query client state to IndexedDB.</span>
<span class="cm">     *</span>
<span class="cm">     * Process:</span>
<span class="cm">     * 1. Serialize client state with metadata</span>
<span class="cm">     * 2. Store in query-cache object store</span>
<span class="cm">     * 3. Handle storage quota limits gracefully</span>
<span class="cm">     * 4. Maintain integrity with atomic transactions</span>
<span class="cm">     */</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">db</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getDatabase</span><span class="p">();</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">transaction</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">db</span><span class="p">.</span><span class="nx">transaction</span><span class="p">([</span><span class="s2">&quot;query-cache&quot;</span><span class="p">],</span><span class="w"> </span><span class="s2">&quot;readwrite&quot;</span><span class="p">);</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">store</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">transaction</span><span class="p">.</span><span class="nx">objectStore</span><span class="p">(</span><span class="s2">&quot;query-cache&quot;</span><span class="p">);</span>

<span class="w">    </span><span class="c1">// Enhanced serialization with compression</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">serializedData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">stringify</span><span class="p">({</span>
<span class="w">      </span><span class="nx">clientState</span><span class="o">:</span><span class="w"> </span><span class="kt">client.clientState</span><span class="p">,</span>
<span class="w">      </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">Date.now</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">version</span><span class="o">:</span><span class="w"> </span><span class="kt">this.version</span><span class="p">,</span>
<span class="w">      </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">queryCount</span><span class="o">:</span><span class="w"> </span><span class="kt">Object.keys</span><span class="p">(</span><span class="nx">client</span><span class="p">.</span><span class="nx">clientState</span><span class="p">.</span><span class="nx">queries</span><span class="p">).</span><span class="nx">length</span><span class="p">,</span>
<span class="w">        </span><span class="nx">mutationCount</span><span class="o">:</span><span class="w"> </span><span class="kt">Object.keys</span><span class="p">(</span><span class="nx">client</span><span class="p">.</span><span class="nx">clientState</span><span class="p">.</span><span class="nx">mutations</span><span class="p">).</span><span class="nx">length</span><span class="p">,</span>
<span class="w">      </span><span class="p">},</span>
<span class="w">    </span><span class="p">});</span>

<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">store</span><span class="p">.</span><span class="nx">put</span><span class="p">(</span><span class="nx">serializedData</span><span class="p">,</span><span class="w"> </span><span class="s2">&quot;react-query-state&quot;</span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">restoreClient</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">PersistedClient</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">undefined</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Restore React Query client state from IndexedDB.</span>
<span class="cm">     *</span>
<span class="cm">     * Features:</span>
<span class="cm">     * - Version compatibility checking</span>
<span class="cm">     * - Graceful degradation for corrupted data</span>
<span class="cm">     * - Selective restoration based on data freshness</span>
<span class="cm">     * - Automatic cleanup of stale entries</span>
<span class="cm">     */</span>

<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">db</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getDatabase</span><span class="p">();</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">transaction</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">db</span><span class="p">.</span><span class="nx">transaction</span><span class="p">([</span><span class="s2">&quot;query-cache&quot;</span><span class="p">],</span><span class="w"> </span><span class="s2">&quot;readonly&quot;</span><span class="p">);</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">store</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">transaction</span><span class="p">.</span><span class="nx">objectStore</span><span class="p">(</span><span class="s2">&quot;query-cache&quot;</span><span class="p">);</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">data</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">store</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="s2">&quot;react-query-state&quot;</span><span class="p">);</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">data</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">parsed</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">parse</span><span class="p">(</span><span class="nx">data</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Version and freshness validation</span>
<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">isValidAndFresh</span><span class="p">(</span><span class="nx">parsed</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="nx">clientState</span><span class="o">:</span><span class="w"> </span><span class="kt">parsed.clientState</span><span class="p">,</span>
<span class="w">            </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">parsed.timestamp</span><span class="p">,</span>
<span class="w">          </span><span class="p">};</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">warn</span><span class="p">(</span><span class="s2">&quot;Failed to restore client state:&quot;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">      </span><span class="c1">// Graceful degradation - continue without cached state</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="kc">undefined</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="2-useofflinemutation-hook-outbox-pattern">2. useOfflineMutation Hook (Outbox Pattern)<a class="headerlink" href="#2-useofflinemutation-hook-outbox-pattern" title="Permanent link">&para;</a></h4>
<p><strong>Location:</strong> <code>client/src/hooks/cache/useOfflineMutation.ts</code> <strong>Purpose:</strong> Provides seamless offline mutation capability
using the outbox pattern, queuing operations when offline and automatically synchronizing when online.</p>
<p><strong>Outbox Pattern Implementation:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">useOfflineMutation</span><span class="o">&lt;</span><span class="nx">TData</span><span class="p">,</span><span class="w"> </span><span class="nx">TError</span><span class="p">,</span><span class="w"> </span><span class="nx">TVariables</span><span class="o">&gt;</span><span class="p">(</span>
<span class="w">  </span><span class="nx">options</span><span class="o">:</span><span class="w"> </span><span class="kt">OfflineMutationOptions</span><span class="o">&lt;</span><span class="nx">TData</span><span class="p">,</span><span class="w"> </span><span class="nx">TError</span><span class="p">,</span><span class="w"> </span><span class="nx">TVariables</span><span class="o">&gt;</span>
<span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Enhanced offline mutation hook with comprehensive error handling.</span>
<span class="cm">   *</span>
<span class="cm">   * Features:</span>
<span class="cm">   * - Automatic offline detection and outbox queuing</span>
<span class="cm">   * - Optimistic UI updates with rollback capability</span>
<span class="cm">   * - Conflict resolution integration</span>
<span class="cm">   * - Retry logic with exponential backoff</span>
<span class="cm">   * - Progress tracking and user feedback</span>
<span class="cm">   */</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="p">[</span><span class="nx">isOnline</span><span class="p">,</span><span class="w"> </span><span class="nx">setIsOnline</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useState</span><span class="p">(</span><span class="nx">navigator</span><span class="p">.</span><span class="nx">onLine</span><span class="p">);</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="p">[</span><span class="nx">syncManager</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useState</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">SyncManager</span><span class="p">.</span><span class="nx">getInstance</span><span class="p">());</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">useMutation</span><span class="o">&lt;</span><span class="nx">TData</span><span class="p">,</span><span class="w"> </span><span class="nx">TError</span><span class="p">,</span><span class="w"> </span><span class="nx">TVariables</span><span class="o">&gt;</span><span class="p">({</span>
<span class="w">    </span><span class="nx">mutationFn</span><span class="o">:</span><span class="w"> </span><span class="kt">async</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">isOnline</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Direct API call when online</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">options</span><span class="p">.</span><span class="nx">mutationFn</span><span class="p">(</span><span class="nx">variables</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Queue in outbox when offline</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">outboxEntry</span><span class="o">:</span><span class="w"> </span><span class="kt">OutboxEntry</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">generateId</span><span class="p">(),</span>
<span class="w">          </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="kt">options.endpoint</span><span class="p">,</span>
<span class="w">          </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="kt">options.method</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="s2">&quot;POST&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nx">variables</span><span class="p">,</span>
<span class="w">          </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">().</span><span class="nx">toISOString</span><span class="p">(),</span>
<span class="w">          </span><span class="nx">retryCount</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">          </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;pending&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="p">};</span>

<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">addToOutbox</span><span class="p">(</span><span class="nx">outboxEntry</span><span class="p">);</span>

<span class="w">        </span><span class="c1">// Return optimistic result</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="nx">options</span><span class="p">.</span><span class="nx">optimisticResponse</span><span class="o">?</span><span class="p">.(</span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="w"> </span><span class="kr">as</span><span class="w"> </span><span class="nx">TData</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>

<span class="w">    </span><span class="nx">onSuccess</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">data</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Handle optimistic updates and cache invalidation</span>
<span class="w">      </span><span class="nx">options</span><span class="p">.</span><span class="nx">onSuccess</span><span class="o">?</span><span class="p">.(</span><span class="nx">data</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">);</span>
<span class="w">    </span><span class="p">},</span>

<span class="w">    </span><span class="nx">onError</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Enhanced error handling with offline context</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">isOnline</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Queue failed operations for retry when online</span>
<span class="w">        </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s2">&quot;Operation queued for retry when online&quot;</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">      </span><span class="nx">options</span><span class="p">.</span><span class="nx">onError</span><span class="o">?</span><span class="p">.(</span><span class="nx">error</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">);</span>
<span class="w">    </span><span class="p">},</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">}</span>
</code></pre></div>
<p><strong>Outbox Data Structure:</strong></p>
<div class="highlight"><pre><span></span><code><span class="kd">interface</span><span class="w"> </span><span class="nx">OutboxEntry</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;GET&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;POST&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;PUT&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;DELETE&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;PATCH&quot;</span><span class="p">;</span>
<span class="w">  </span><span class="nx">variables</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">;</span>
<span class="w">  </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">retryCount</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">  </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;pending&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;processing&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;completed&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;failed&quot;</span><span class="p">;</span>

<span class="w">  </span><span class="c1">// Enhanced metadata</span>
<span class="w">  </span><span class="nx">metadata</span><span class="o">?:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">entityType</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">    </span><span class="nx">entityId?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="w">    </span><span class="nx">operation</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;create&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;update&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;delete&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="nx">priority</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;high&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;medium&quot;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s2">&quot;low&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="nx">dependencies?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">[];</span><span class="w"> </span><span class="c1">// Other outbox entry IDs this depends on</span>
<span class="w">  </span><span class="p">};</span>

<span class="w">  </span><span class="c1">// Conflict resolution data</span>
<span class="w">  </span><span class="nx">baseVersion?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">optimisticResult?</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">;</span>

<span class="w">  </span><span class="c1">// Error information</span>
<span class="w">  </span><span class="nx">lastError?</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">;</span>
<span class="w">  </span><span class="nx">maxRetries?</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="3-syncmanager-network-synchronization-orchestration">3. SyncManager (Network &amp; Synchronization Orchestration)<a class="headerlink" href="#3-syncmanager-network-synchronization-orchestration" title="Permanent link">&para;</a></h4>
<p><strong>Location:</strong> <code>client/src/lib/sync/sync-manager.ts</code> <strong>Purpose:</strong> Central orchestrator for all offline synchronization
activities, network monitoring, and conflict resolution coordination.</p>
<p><strong>Core Responsibilities:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">SyncManager</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">static</span><span class="w"> </span><span class="nx">instance</span><span class="o">:</span><span class="w"> </span><span class="kt">SyncManager</span><span class="p">;</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">networkMonitor</span><span class="o">:</span><span class="w"> </span><span class="kt">NetworkMonitor</span><span class="p">;</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">isProcessing</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">processingQueue</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Set</span><span class="o">&lt;</span><span class="kt">string</span><span class="o">&gt;</span><span class="p">();</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="kr">constructor</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">networkMonitor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">NetworkMonitor</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupEventListeners</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">processOutbox</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">SyncResult</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Process all pending mutations in the outbox.</span>
<span class="cm">     *</span>
<span class="cm">     * Processing Strategy:</span>
<span class="cm">     * 1. Retrieve all pending entries from IndexedDB</span>
<span class="cm">     * 2. Sort by priority and timestamp</span>
<span class="cm">     * 3. Process in batches with dependency resolution</span>
<span class="cm">     * 4. Handle conflicts using client-side resolution</span>
<span class="cm">     * 5. Update outbox status and clean up completed entries</span>
<span class="cm">     *</span>
<span class="cm">     * Returns:</span>
<span class="cm">     *   Comprehensive sync result with statistics</span>
<span class="cm">     */</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">isProcessing</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;already_processing&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">message</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;Sync already in progress&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="p">};</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">isProcessing</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">true</span><span class="p">;</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">();</span>

<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Get pending mutations with dependency ordering</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">pendingMutations</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getOutboxEntries</span><span class="p">();</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">sortedMutations</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">sortByDependencies</span><span class="p">(</span><span class="nx">pendingMutations</span><span class="p">);</span>

<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">results</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">processed</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">        </span><span class="nx">completed</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">        </span><span class="nx">failed</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">        </span><span class="nx">conflicts</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">      </span><span class="p">};</span>

<span class="w">      </span><span class="c1">// Process in batches to avoid overwhelming the server</span>
<span class="w">      </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="nx">batch</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">createBatches</span><span class="p">(</span><span class="nx">sortedMutations</span><span class="p">,</span><span class="w"> </span><span class="mf">5</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="nb">Promise</span><span class="p">.</span><span class="nx">allSettled</span><span class="p">(</span><span class="nx">batch</span><span class="p">.</span><span class="nx">map</span><span class="p">((</span><span class="nx">entry</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">processSingleEntry</span><span class="p">(</span><span class="nx">entry</span><span class="p">,</span><span class="w"> </span><span class="nx">results</span><span class="p">)));</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;completed&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">duration</span><span class="o">:</span><span class="w"> </span><span class="kt">Date.now</span><span class="p">()</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">startTime</span><span class="p">,</span>
<span class="w">        </span><span class="p">...</span><span class="nx">results</span><span class="p">,</span>
<span class="w">      </span><span class="p">};</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">finally</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">isProcessing</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="nx">processSingleEntry</span><span class="p">(</span><span class="nx">entry</span><span class="o">:</span><span class="w"> </span><span class="kt">OutboxEntry</span><span class="p">,</span><span class="w"> </span><span class="nx">results</span><span class="o">:</span><span class="w"> </span><span class="kt">SyncResults</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Process individual outbox entry with comprehensive error handling.</span>
<span class="cm">     *</span>
<span class="cm">     * Processing Flow:</span>
<span class="cm">     * 1. Validate entry and check dependencies</span>
<span class="cm">     * 2. Execute API call with retry logic</span>
<span class="cm">     * 3. Handle conflicts with resolution strategies</span>
<span class="cm">     * 4. Update local cache with server response</span>
<span class="cm">     * 5. Mark entry as completed or retry if failed</span>
<span class="cm">     */</span>

<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">results</span><span class="p">.</span><span class="nx">processed</span><span class="o">++</span><span class="p">;</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">processingQueue</span><span class="p">.</span><span class="nx">add</span><span class="p">(</span><span class="nx">entry</span><span class="p">.</span><span class="nx">id</span><span class="p">);</span>

<span class="w">      </span><span class="c1">// Execute API call</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">executeApiCall</span><span class="p">(</span><span class="nx">entry</span><span class="p">);</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">response</span><span class="p">.</span><span class="nx">status</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s2">&quot;conflict&quot;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Handle conflict using resolution strategy</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="nx">resolution</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">resolveConflict</span><span class="p">(</span><span class="nx">entry</span><span class="p">,</span><span class="w"> </span><span class="nx">response</span><span class="p">);</span>
<span class="w">        </span><span class="nx">results</span><span class="p">.</span><span class="nx">conflicts</span><span class="o">++</span><span class="p">;</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">resolution</span><span class="p">.</span><span class="nx">success</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">updateLocalCache</span><span class="p">(</span><span class="nx">resolution</span><span class="p">.</span><span class="nx">resolvedData</span><span class="p">);</span>
<span class="w">          </span><span class="nx">results</span><span class="p">.</span><span class="nx">completed</span><span class="o">++</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Successful operation</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">updateLocalCache</span><span class="p">(</span><span class="nx">response</span><span class="p">.</span><span class="nx">data</span><span class="p">);</span>
<span class="w">        </span><span class="nx">results</span><span class="p">.</span><span class="nx">completed</span><span class="o">++</span><span class="p">;</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">      </span><span class="c1">// Clean up completed entry</span>
<span class="w">      </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">removeFromOutbox</span><span class="p">(</span><span class="nx">entry</span><span class="p">.</span><span class="nx">id</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">results</span><span class="p">.</span><span class="nx">failed</span><span class="o">++</span><span class="p">;</span>

<span class="w">      </span><span class="c1">// Update retry count and schedule retry if applicable</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">entry</span><span class="p">.</span><span class="nx">retryCount</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="p">(</span><span class="nx">entry</span><span class="p">.</span><span class="nx">maxRetries</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="mf">3</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">entry</span><span class="p">.</span><span class="nx">retryCount</span><span class="o">++</span><span class="p">;</span>
<span class="w">        </span><span class="nx">entry</span><span class="p">.</span><span class="nx">lastError</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="ne">Error</span><span class="w"> </span><span class="o">?</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="w"> </span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;Unknown error&quot;</span><span class="p">;</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">updateOutboxEntry</span><span class="p">(</span><span class="nx">entry</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="c1">// Max retries exceeded - log and remove</span>
<span class="w">        </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="sb">`Max retries exceeded for outbox entry </span><span class="si">${</span><span class="nx">entry</span><span class="p">.</span><span class="nx">id</span><span class="si">}</span><span class="sb">:`</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>
<span class="w">        </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">removeFromOutbox</span><span class="p">(</span><span class="nx">entry</span><span class="p">.</span><span class="nx">id</span><span class="p">);</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">finally</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">processingQueue</span><span class="p">.</span><span class="ow">delete</span><span class="p">(</span><span class="nx">entry</span><span class="p">.</span><span class="nx">id</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="4-networkmonitor-connection-quality-detection">4. NetworkMonitor (Connection Quality Detection)<a class="headerlink" href="#4-networkmonitor-connection-quality-detection" title="Permanent link">&para;</a></h4>
<p><strong>Location:</strong> <code>client/src/lib/sync/network-monitor.ts</code> <strong>Purpose:</strong> Intelligent network status monitoring with
connection quality assessment and adaptive behavior.</p>
<p><strong>Advanced Network Detection:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">NetworkMonitor</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">connectionQuality</span><span class="o">:</span><span class="w"> </span><span class="kt">ConnectionQuality</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s2">&quot;unknown&quot;</span><span class="p">;</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">listeners</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Set</span><span class="o">&lt;</span><span class="nx">NetworkStatusListener</span><span class="o">&gt;</span><span class="p">();</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">lastOnlineCheck</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">();</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">QUALITY_CHECK_INTERVAL</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">30000</span><span class="p">;</span><span class="w"> </span><span class="c1">// 30 seconds</span>

<span class="w">  </span><span class="kr">constructor</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">setupEventListeners</span><span class="p">();</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">startQualityMonitoring</span><span class="p">();</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">detectConnectionQuality</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">ConnectionQuality</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Assess network connection quality through multiple indicators.</span>
<span class="cm">     *</span>
<span class="cm">     * Assessment Factors:</span>
<span class="cm">     * - Network Information API (connection type, downlink speed)</span>
<span class="cm">     * - Response time to lightweight API endpoint</span>
<span class="cm">     * - Historical success/failure rates</span>
<span class="cm">     * - Browser-reported connection status</span>
<span class="cm">     *</span>
<span class="cm">     * Returns:</span>
<span class="cm">     *   &#39;fast&#39; | &#39;slow&#39; | &#39;unstable&#39; | &#39;offline&#39; | &#39;unknown&#39;</span>
<span class="cm">     */</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">navigator</span><span class="p">.</span><span class="nx">onLine</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="s2">&quot;offline&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Use Network Information API if available</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">connection</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">(</span><span class="nx">navigator</span><span class="w"> </span><span class="kr">as</span><span class="w"> </span><span class="nx">any</span><span class="p">).</span><span class="nx">connection</span><span class="p">;</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">connection</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="kd">const</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">effectiveType</span><span class="p">,</span><span class="w"> </span><span class="nx">downlink</span><span class="p">,</span><span class="w"> </span><span class="nx">rtt</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">connection</span><span class="p">;</span>

<span class="w">        </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">effectiveType</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s2">&quot;4g&quot;</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">downlink</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">1.5</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="s2">&quot;fast&quot;</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">effectiveType</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s2">&quot;3g&quot;</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="p">(</span><span class="nx">downlink</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">0.5</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">rtt</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">300</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="s2">&quot;slow&quot;</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">rtt</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="mf">500</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nx">downlink</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">0.2</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="k">return</span><span class="w"> </span><span class="s2">&quot;unstable&quot;</span><span class="p">;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>

<span class="w">      </span><span class="c1">// Fallback: Measure response time to API health check</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">startTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">();</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">fetch</span><span class="p">(</span><span class="s2">&quot;/api/v1/health/ping&quot;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;HEAD&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">signal</span><span class="o">:</span><span class="w"> </span><span class="kt">AbortSignal.timeout</span><span class="p">(</span><span class="mf">5000</span><span class="p">),</span>
<span class="w">      </span><span class="p">});</span>

<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">responseTime</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">startTime</span><span class="p">;</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">response</span><span class="p">.</span><span class="nx">ok</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">responseTime</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">200</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="s2">&quot;fast&quot;</span><span class="p">;</span>
<span class="w">      </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">response</span><span class="p">.</span><span class="nx">ok</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">responseTime</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">1000</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="s2">&quot;slow&quot;</span><span class="p">;</span>
<span class="w">      </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="s2">&quot;unstable&quot;</span><span class="p">;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="s2">&quot;unstable&quot;</span><span class="p">;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">getAdaptiveSyncStrategy</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nx">SyncStrategy</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="cm">/**</span>
<span class="cm">     * Determine optimal sync strategy based on connection quality.</span>
<span class="cm">     *</span>
<span class="cm">     * Strategies:</span>
<span class="cm">     * - Fast: Immediate sync, large batches, real-time updates</span>
<span class="cm">     * - Slow: Batched sync, compressed payloads, reduced frequency</span>
<span class="cm">     * - Unstable: Conservative sync, small batches, aggressive retries</span>
<span class="cm">     * - Offline: Queue all operations, no network activity</span>
<span class="cm">     */</span>

<span class="w">    </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">connectionQuality</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s2">&quot;fast&quot;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">batchSize</span><span class="o">:</span><span class="w"> </span><span class="kt">10</span><span class="p">,</span>
<span class="w">          </span><span class="nx">syncInterval</span><span class="o">:</span><span class="w"> </span><span class="kt">5000</span><span class="p">,</span>
<span class="w">          </span><span class="nx">retryStrategy</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;immediate&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nx">compression</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s2">&quot;slow&quot;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">batchSize</span><span class="o">:</span><span class="w"> </span><span class="kt">3</span><span class="p">,</span>
<span class="w">          </span><span class="nx">syncInterval</span><span class="o">:</span><span class="w"> </span><span class="kt">15000</span><span class="p">,</span>
<span class="w">          </span><span class="nx">retryStrategy</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;exponential&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nx">compression</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s2">&quot;unstable&quot;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">batchSize</span><span class="o">:</span><span class="w"> </span><span class="kt">1</span><span class="p">,</span>
<span class="w">          </span><span class="nx">syncInterval</span><span class="o">:</span><span class="w"> </span><span class="kt">30000</span><span class="p">,</span>
<span class="w">          </span><span class="nx">retryStrategy</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;exponential_with_jitter&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nx">compression</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s2">&quot;offline&quot;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">batchSize</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">          </span><span class="nx">syncInterval</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">          </span><span class="nx">retryStrategy</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;none&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nx">compression</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">      </span><span class="nx">default</span><span class="o">:</span>
<span class="w">        </span><span class="kt">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">batchSize</span><span class="o">:</span><span class="w"> </span><span class="kt">5</span><span class="p">,</span>
<span class="w">          </span><span class="nx">syncInterval</span><span class="o">:</span><span class="w"> </span><span class="kt">10000</span><span class="p">,</span>
<span class="w">          </span><span class="nx">retryStrategy</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;linear&quot;</span><span class="p">,</span>
<span class="w">          </span><span class="nx">compression</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="p">,</span>
<span class="w">        </span><span class="p">};</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="5-client-side-cache-integration">5. Client-Side Cache Integration<a class="headerlink" href="#5-client-side-cache-integration" title="Permanent link">&para;</a></h4>
<p>The system provides comprehensive integration with React Query for optimal server state management:</p>
<p><strong>Cache Provider Setup:</strong></p>
<div class="highlight"><pre><span></span><code><span class="c1">// client/src/components/providers/cache-provider.tsx</span>
<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">CacheProvider</span><span class="p">({</span><span class="w"> </span><span class="nx">children</span><span class="w"> </span><span class="p">}</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">children</span><span class="o">:</span><span class="w"> </span><span class="kt">React.ReactNode</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="cm">/**</span>
<span class="cm">   * Global cache provider with IndexedDB persistence and offline capabilities.</span>
<span class="cm">   *</span>
<span class="cm">   * Features:</span>
<span class="cm">   * - Automatic state persistence across browser sessions</span>
<span class="cm">   * - Intelligent cache invalidation strategies</span>
<span class="cm">   * - Background sync with conflict resolution</span>
<span class="cm">   * - Performance optimization with query deduplication</span>
<span class="cm">   */</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="p">[</span><span class="nx">queryClient</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useState</span><span class="p">(</span>
<span class="w">    </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">      </span><span class="ow">new</span><span class="w"> </span><span class="nx">QueryClient</span><span class="p">({</span>
<span class="w">        </span><span class="nx">defaultOptions</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">queries</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Extended stale time for offline capability</span>
<span class="w">            </span><span class="nx">staleTime</span><span class="o">:</span><span class="w"> </span><span class="kt">1000</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">60</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">5</span><span class="p">,</span><span class="w"> </span><span class="c1">// 5 minutes</span>
<span class="w">            </span><span class="nx">cacheTime</span><span class="o">:</span><span class="w"> </span><span class="kt">1000</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">60</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">30</span><span class="p">,</span><span class="w"> </span><span class="c1">// 30 minutes</span>

<span class="w">            </span><span class="c1">// Retry with exponential backoff</span>
<span class="w">            </span><span class="nx">retry</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">failureCount</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="c1">// Don&#39;t retry on 4xx errors (client errors)</span>
<span class="w">              </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="w"> </span><span class="ow">instanceof</span><span class="w"> </span><span class="ne">Error</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">.</span><span class="nx">includes</span><span class="p">(</span><span class="s2">&quot;4&quot;</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="k">return</span><span class="w"> </span><span class="kc">false</span><span class="p">;</span>
<span class="w">              </span><span class="p">}</span>
<span class="w">              </span><span class="k">return</span><span class="w"> </span><span class="nx">failureCount</span><span class="w"> </span><span class="o">&lt;</span><span class="w"> </span><span class="mf">3</span><span class="p">;</span>
<span class="w">            </span><span class="p">},</span>

<span class="w">            </span><span class="c1">// Custom retry delay with network-aware backoff</span>
<span class="w">            </span><span class="nx">retryDelay</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">attemptIndex</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nb">Math</span><span class="p">.</span><span class="nx">min</span><span class="p">(</span><span class="mf">1000</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">2</span><span class="w"> </span><span class="o">**</span><span class="w"> </span><span class="nx">attemptIndex</span><span class="p">,</span><span class="w"> </span><span class="mf">30000</span><span class="p">),</span>
<span class="w">          </span><span class="p">},</span>
<span class="w">          </span><span class="nx">mutations</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">            </span><span class="c1">// Enhanced mutation error handling</span>
<span class="w">            </span><span class="nx">onError</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">              </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s2">&quot;Mutation failed:&quot;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">);</span>

<span class="w">              </span><span class="c1">// Automatic retry for network errors</span>
<span class="w">              </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="o">!</span><span class="nx">navigator</span><span class="p">.</span><span class="nx">onLine</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="nx">error</span><span class="p">.</span><span class="nx">message</span><span class="p">.</span><span class="nx">includes</span><span class="p">(</span><span class="s2">&quot;NetworkError&quot;</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">                </span><span class="c1">// Will be handled by offline mutation system</span>
<span class="w">                </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s2">&quot;Queueing mutation for offline retry&quot;</span><span class="p">);</span>
<span class="w">              </span><span class="p">}</span>
<span class="w">            </span><span class="p">},</span>
<span class="w">          </span><span class="p">},</span>
<span class="w">        </span><span class="p">},</span>
<span class="w">      </span><span class="p">})</span>
<span class="w">  </span><span class="p">);</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="p">[</span><span class="nx">persister</span><span class="p">]</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useState</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">IndexedDBPersister</span><span class="p">());</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="o">&lt;</span><span class="nx">QueryClientProvider</span><span class="w"> </span><span class="nx">client</span><span class="o">=</span><span class="p">{</span><span class="nx">queryClient</span><span class="p">}</span><span class="o">&gt;</span>
<span class="w">      </span><span class="o">&lt;</span><span class="nx">PersistQueryClientProvider</span>
<span class="w">        </span><span class="nx">client</span><span class="o">=</span><span class="p">{</span><span class="nx">queryClient</span><span class="p">}</span>
<span class="w">        </span><span class="nx">persistOptions</span><span class="o">=</span><span class="p">{{</span>
<span class="w">          </span><span class="nx">persister</span><span class="p">,</span>
<span class="w">          </span><span class="nx">maxAge</span><span class="o">:</span><span class="w"> </span><span class="kt">1000</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">60</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">60</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">24</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">7</span><span class="p">,</span><span class="w"> </span><span class="c1">// 1 week</span>
<span class="w">          </span><span class="nx">buster</span><span class="o">:</span><span class="w"> </span><span class="kt">process.env.NEXT_PUBLIC_APP_VERSION</span><span class="p">,</span><span class="w"> </span><span class="c1">// Cache bust on version change</span>
<span class="w">        </span><span class="p">}}</span>
<span class="w">      </span><span class="o">&gt;</span>
<span class="w">        </span><span class="p">{</span><span class="nx">children</span><span class="p">}</span>
<span class="w">      </span><span class="o">&lt;</span><span class="err">/PersistQueryClientProvider&gt;</span>
<span class="w">    </span><span class="o">&lt;</span><span class="err">/QueryClientProvider&gt;</span>
<span class="w">  </span><span class="p">);</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="data-flow-and-user-experience">Data Flow and User Experience<a class="headerlink" href="#data-flow-and-user-experience" title="Permanent link">&para;</a></h3>
<p><strong>Offline-to-Online User Journey:</strong></p>
<div class="highlight"><pre><span></span><code>sequenceDiagram
    participant U as User
    participant UI as React UI
    participant OM as useOfflineMutation
    participant O as Outbox (IndexedDB)
    participant SM as SyncManager
    participant NM as NetworkMonitor
    participant API as Backend API
    participant RQ as React Query

    U-&gt;&gt;UI: Create Project (Offline)
    UI-&gt;&gt;OM: Execute Mutation
    OM-&gt;&gt;O: Add to Outbox
    OM-&gt;&gt;UI: Optimistic Update
    UI-&gt;&gt;U: Show Success (Offline Badge)

    Note over NM: Network comes online

    NM-&gt;&gt;SM: Trigger Online Event
    SM-&gt;&gt;O: Process Outbox
    SM-&gt;&gt;API: Sync Project Creation
    API-&gt;&gt;SM: Success Response
    SM-&gt;&gt;RQ: Update Cache
    RQ-&gt;&gt;UI: Remove Offline Badge
    UI-&gt;&gt;U: Sync Complete
</code></pre></div>
<h3 id="integration-with-react-query-ecosystem">Integration with React Query Ecosystem<a class="headerlink" href="#integration-with-react-query-ecosystem" title="Permanent link">&para;</a></h3>
<p>The offline system seamlessly integrates with the React Query ecosystem:</p>
<p><strong>Query Invalidation and Updates:</strong></p>
<div class="highlight"><pre><span></span><code><span class="c1">// Automatic cache updates after sync</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">syncManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">SyncManager</span><span class="p">.</span><span class="nx">getInstance</span><span class="p">();</span>

<span class="nx">syncManager</span><span class="p">.</span><span class="nx">onSyncComplete</span><span class="p">((</span><span class="nx">result</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Invalidate affected queries</span>
<span class="w">  </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">invalidateQueries</span><span class="p">({</span>
<span class="w">    </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;projects&quot;</span><span class="p">],</span>
<span class="w">  </span><span class="p">});</span>

<span class="w">  </span><span class="c1">// Update specific cached data</span>
<span class="w">  </span><span class="nx">result</span><span class="p">.</span><span class="nx">completedOperations</span><span class="p">.</span><span class="nx">forEach</span><span class="p">((</span><span class="nx">operation</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">operation</span><span class="p">.</span><span class="kr">type</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s2">&quot;create&quot;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">setQueryData</span><span class="p">([</span><span class="s2">&quot;projects&quot;</span><span class="p">,</span><span class="w"> </span><span class="nx">operation</span><span class="p">.</span><span class="nx">entityId</span><span class="p">],</span><span class="w"> </span><span class="nx">operation</span><span class="p">.</span><span class="nx">result</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">});</span>
<span class="p">});</span>
</code></pre></div>
<p><strong>Optimistic Updates with Rollback:</strong></p>
<div class="highlight"><pre><span></span><code><span class="kd">const</span><span class="w"> </span><span class="nx">updateProject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useOfflineMutation</span><span class="p">({</span>
<span class="w">  </span><span class="nx">mutationFn</span><span class="o">:</span><span class="w"> </span><span class="kt">updateProjectApi</span><span class="p">,</span>
<span class="w">  </span><span class="nx">onMutate</span><span class="o">:</span><span class="w"> </span><span class="kt">async</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Cancel any outgoing refetches</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">cancelQueries</span><span class="p">({</span><span class="w"> </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;projects&quot;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">]</span><span class="w"> </span><span class="p">});</span>

<span class="w">    </span><span class="c1">// Snapshot previous value</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">previousProject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">getQueryData</span><span class="p">([</span><span class="s2">&quot;projects&quot;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">]);</span>

<span class="w">    </span><span class="c1">// Optimistic update</span>
<span class="w">    </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">setQueryData</span><span class="p">([</span><span class="s2">&quot;projects&quot;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">],</span><span class="w"> </span><span class="p">(</span><span class="nx">old</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">      </span><span class="p">...</span><span class="nx">old</span><span class="p">,</span>
<span class="w">      </span><span class="p">...</span><span class="nx">variables</span><span class="p">,</span>
<span class="w">      </span><span class="nx">_optimistic</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">    </span><span class="p">}));</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">previousProject</span><span class="w"> </span><span class="p">};</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nx">onError</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">err</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Rollback on error</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">context</span><span class="o">?</span><span class="p">.</span><span class="nx">previousProject</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">setQueryData</span><span class="p">([</span><span class="s2">&quot;projects&quot;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">],</span><span class="w"> </span><span class="nx">context</span><span class="p">.</span><span class="nx">previousProject</span><span class="p">);</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nx">onSettled</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Always refetch after mutation</span>
<span class="w">    </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">invalidateQueries</span><span class="p">({</span><span class="w"> </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s2">&quot;projects&quot;</span><span class="p">]</span><span class="w"> </span><span class="p">});</span>
<span class="w">  </span><span class="p">},</span>
<span class="p">});</span>
</code></pre></div>
<h3 id="performance-and-user-experience-optimizations">Performance and User Experience Optimizations<a class="headerlink" href="#performance-and-user-experience-optimizations" title="Permanent link">&para;</a></h3>
<p><strong>Intelligent Caching Strategy:</strong></p>
<ul>
<li><strong>Layered Caching</strong>: Memory → IndexedDB → Network with intelligent fallbacks</li>
<li><strong>Selective Persistence</strong>: Only cache frequently accessed and offline-critical data</li>
<li><strong>Compression</strong>: LZ-string compression for large cached datasets</li>
<li><strong>Cleanup</strong>: Automatic cleanup of stale cache entries with configurable TTL</li>
</ul>
<p><strong>User Experience Enhancements:</strong></p>
<ul>
<li><strong>Offline Indicators</strong>: Visual indicators for offline status and pending sync</li>
<li><strong>Progress Tracking</strong>: Real-time sync progress with detailed status updates</li>
<li><strong>Conflict Notifications</strong>: User-friendly conflict resolution with clear explanations</li>
<li><strong>Background Sync</strong>: Non-intrusive background synchronization with minimal user interruption</li>
<li><strong>Graceful Degradation</strong>: Seamless experience transition between online and offline modes</li>
</ul>
<hr />
<h2 id="component-interactions">Component Interactions<a class="headerlink" href="#component-interactions" title="Permanent link">&para;</a></h2>
<h3 id="authentication-flow">Authentication Flow<a class="headerlink" href="#authentication-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>┌─────────────┐    1. Login Request    ┌─────────────────┐
│   Client    │ ────────────────────► │   Auth Routes   │
│  (React)    │                       │   (FastAPI)     │
└─────────────┘                       └─────────────────┘
       │                                       │
       │                              2. Validate Credentials
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │  Auth Service   │
       │                               │   (Business)    │
       │                               └─────────────────┘
       │                                       │
       │                              3. Query User Data
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │ User Repository │
       │                               │  (Data Access)  │
       │                               └─────────────────┘
       │                                       │
       │                              4. Database Query
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │   Database      │
       │                               │ (PostgreSQL)    │
       │                               └─────────────────┘
       │
       │ 5. JWT Token Response
       ◄────────────────────────────────────────────────────
</code></pre></div>
<h3 id="component-management-flow">Component Management Flow<a class="headerlink" href="#component-management-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>┌─────────────┐  1. Component Request  ┌─────────────────┐
│   Client    │ ────────────────────► │Component Routes │
│  (React)    │                       │   (FastAPI)     │
└─────────────┘                       └─────────────────┘
       │                                       │
       │                              2. Business Logic
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │Component Service│
       │                               │   (Business)    │
       │                               └─────────────────┘
       │                                       │
       │                              3. Data Operations
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │Component Repo   │
       │                               │  (Data Access)  │
       │                               └─────────────────┘
       │                                       │
       │                              4. CRUD Operations
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │   Database      │
       │                               │ (PostgreSQL)    │
       │                               └─────────────────┘
       │
       │ 5. Component Data Response
       ◄────────────────────────────────────────────────────
</code></pre></div>
<hr />
<h2 id="database-schema-design">Database Schema Design<a class="headerlink" href="#database-schema-design" title="Permanent link">&para;</a></h2>
<h3 id="user-management">User Management<a class="headerlink" href="#user-management" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Table:</strong> User</li>
<li><strong>Email Field Enhancement</strong>: Case-insensitive uniqueness constraint with application-level normalization</li>
<li><strong>Database Constraints</strong>: Foreign key constraints enabled for referential integrity</li>
<li>
<p><strong>Validation Layer</strong>: Comprehensive string length validation with boundary condition testing</p>
</li>
<li>
<p><strong>Table:</strong> UserPreference</p>
</li>
<li><strong>Table:</strong> UserRole</li>
<li><strong>Table:</strong> UserRoleAssignment</li>
</ul>
<h3 id="component-library">Component Library<a class="headerlink" href="#component-library" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>Table:</strong> Component</p>
</li>
<li>
<p><strong>Table:</strong> ComponentCategory</p>
</li>
<li><strong>Table:</strong> ComponentType</li>
</ul>
<h3 id="electrical-system-design">Electrical System Design<a class="headerlink" href="#electrical-system-design" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>Table:</strong> Project</p>
</li>
<li>
<p><strong>Table:</strong> ElectricalSystem</p>
</li>
<li><strong>Table:</strong> Circuit</li>
<li><strong>Table:</strong> Load</li>
</ul>
<h3 id="cable-management">Cable Management<a class="headerlink" href="#cable-management" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Table:</strong> CableType</li>
</ul>
<h3 id="calculations">Calculations<a class="headerlink" href="#calculations" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>Table:</strong> CalculationTemplate</p>
</li>
<li>
<p><strong>Table:</strong> Calculation</p>
</li>
</ul>
<h3 id="task-management">Task Management<a class="headerlink" href="#task-management" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Table:</strong> Task</li>
<li><strong>Core Entity</strong>: Primary task entity with title, description, status, priority</li>
<li><strong>Temporal Fields</strong>: due_date, estimated_hours for project planning</li>
<li><strong>Project Integration</strong>: Foreign key relationship to Project table</li>
<li><strong>User Tracking</strong>: created_by_user_id for audit trail</li>
<li>
<p><strong>Enum Fields</strong>: TaskStatus and TaskPriority with proper validation</p>
</li>
<li>
<p><strong>Table:</strong> TaskAssignment</p>
</li>
<li><strong>Relationship Entity</strong>: Many-to-many relationship between Task and User</li>
<li><strong>Assignment Metadata</strong>: assigned_at timestamp, assigned_by_user_id tracking</li>
<li><strong>Status Management</strong>: is_active flag for assignment lifecycle</li>
<li><strong>Soft Delete</strong>: Maintains assignment history for audit purposes</li>
</ul>
<h3 id="activity-logging-and-audit">Activity Logging and Audit<a class="headerlink" href="#activity-logging-and-audit" title="Permanent link">&para;</a></h3>
<ul>
<li>
<p><strong>Table:</strong> ActivityLog</p>
</li>
<li>
<p><strong>Table:</strong> AuditTrail</p>
</li>
</ul>
<hr />
<h2 id="project-task-management-architecture">Project Task Management Architecture<a class="headerlink" href="#project-task-management-architecture" title="Permanent link">&para;</a></h2>
<p>The Ultimate Electrical Designer implements a comprehensive <strong>Project Task Management system</strong> that enables project
managers and team members to efficiently organize, track, and collaborate on electrical design projects. This system
integrates seamlessly with the existing 5-layer architecture and provides robust task lifecycle management with
real-time collaboration capabilities.</p>
<h3 id="task-management-system-overview">Task Management System Overview<a class="headerlink" href="#task-management-system-overview" title="Permanent link">&para;</a></h3>
<p>The Task Management system is built on the foundation of the existing 5-layer architecture, extending project management
capabilities with sophisticated task tracking, assignment management, and progress monitoring features.</p>
<div class="highlight"><pre><span></span><code>graph TB
    subgraph &quot;Layer 1: API Routes&quot;
        TR[Task Routes&lt;br&gt;/api/v1/tasks]
        PR[Project Routes&lt;br&gt;/api/v1/projects]
    end

    subgraph &quot;Layer 2: Business Services&quot;
        TMS[TaskManagerService&lt;br&gt;Core Business Logic]
        PS[ProjectService&lt;br&gt;Project Integration]
    end

    subgraph &quot;Layer 3: Data Repositories&quot;
        TR2[Task Repository&lt;br&gt;CRUD Operations]
        TAR[TaskAssignment Repository&lt;br&gt;Assignment Management]
        PR2[Project Repository&lt;br&gt;Project Integration]
    end

    subgraph &quot;Layer 4: Data Models&quot;
        TM[Task Model&lt;br&gt;Core Entity]
        TAM[TaskAssignment Model&lt;br&gt;User-Task Relations]
        PM[Project Model&lt;br&gt;Parent Entity]
    end

    subgraph &quot;Layer 5: Validation Schemas&quot;
        TCS[Task Create Schema&lt;br&gt;Input Validation]
        TRS[Task Read Schema&lt;br&gt;Response Format]
        TUS[Task Update Schema&lt;br&gt;Modification Validation]
    end

    subgraph &quot;Data Layer&quot;
        PG[(PostgreSQL&lt;br&gt;Task Tables)]
    end

    TR --&gt; TMS
    PR --&gt; PS
    TMS --&gt; TR2
    TMS --&gt; TAR
    PS --&gt; PR2
    TR2 --&gt; TM
    TAR --&gt; TAM
    PR2 --&gt; PM
    TMS --&gt; TCS
    TMS --&gt; TRS
    TMS --&gt; TUS
    TR2 --&gt; PG
    TAR --&gt; PG
    PR2 --&gt; PG

    style TMS fill:#ff9999,stroke:#333,stroke-width:3px
    style TR2 fill:#99ccff,stroke:#333,stroke-width:2px
    style TAR fill:#99ccff,stroke:#333,stroke-width:2px
</code></pre></div>
<h3 id="core-components_2">Core Components<a class="headerlink" href="#core-components_2" title="Permanent link">&para;</a></h3>
<h4 id="1-task-management-api-endpoints">1. Task Management API Endpoints<a class="headerlink" href="#1-task-management-api-endpoints" title="Permanent link">&para;</a></h4>
<p><strong>Location:</strong> <code>server/src/api/v1/task_routes.py</code> <strong>Purpose:</strong> RESTful API endpoints for comprehensive task management
operations</p>
<p><strong>Key Endpoints:</strong></p>
<div class="highlight"><pre><span></span><code><span class="c1"># Task CRUD Operations</span>
<span class="n">GET</span>    <span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">tasks</span>                    <span class="c1"># List tasks with filtering and pagination</span>
<span class="n">POST</span>   <span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">tasks</span>                    <span class="c1"># Create new task</span>
<span class="n">GET</span>    <span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">tasks</span><span class="o">/</span><span class="p">{</span><span class="n">task_id</span><span class="p">}</span>          <span class="c1"># Get specific task details</span>
<span class="n">PUT</span>    <span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">tasks</span><span class="o">/</span><span class="p">{</span><span class="n">task_id</span><span class="p">}</span>          <span class="c1"># Update task information</span>
<span class="n">DELETE</span> <span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">tasks</span><span class="o">/</span><span class="p">{</span><span class="n">task_id</span><span class="p">}</span>          <span class="c1"># Delete task (soft delete)</span>

<span class="c1"># Task Assignment Management</span>
<span class="n">POST</span>   <span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">tasks</span><span class="o">/</span><span class="p">{</span><span class="n">task_id</span><span class="p">}</span><span class="o">/</span><span class="n">assign</span>   <span class="c1"># Assign users to task</span>
<span class="n">DELETE</span> <span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">tasks</span><span class="o">/</span><span class="p">{</span><span class="n">task_id</span><span class="p">}</span><span class="o">/</span><span class="n">unassign</span> <span class="c1"># Remove user assignments</span>

<span class="c1"># Project Integration</span>
<span class="n">GET</span>    <span class="o">/</span><span class="n">api</span><span class="o">/</span><span class="n">v1</span><span class="o">/</span><span class="n">projects</span><span class="o">/</span><span class="p">{</span><span class="n">project_id</span><span class="p">}</span><span class="o">/</span><span class="n">tasks</span>  <span class="c1"># Get all tasks for project</span>
</code></pre></div>
<p><strong>Advanced Filtering Capabilities:</strong></p>
<div class="highlight"><pre><span></span><code><span class="c1"># GET /api/v1/tasks with query parameters</span>
<span class="p">{</span>
    <span class="s2">&quot;status&quot;</span><span class="p">:</span> <span class="s2">&quot;Not Started&quot;</span><span class="p">,</span>           <span class="c1"># Filter by task status</span>
    <span class="s2">&quot;priority&quot;</span><span class="p">:</span> <span class="s2">&quot;High&quot;</span><span class="p">,</span>                <span class="c1"># Filter by priority level</span>
    <span class="s2">&quot;assigned_user_id&quot;</span><span class="p">:</span> <span class="mi">123</span><span class="p">,</span>           <span class="c1"># Filter by assigned user</span>
    <span class="s2">&quot;project_id&quot;</span><span class="p">:</span> <span class="mi">456</span><span class="p">,</span>                 <span class="c1"># Filter by project</span>
    <span class="s2">&quot;due_date_from&quot;</span><span class="p">:</span> <span class="s2">&quot;2025-01-01&quot;</span><span class="p">,</span>     <span class="c1"># Date range filtering</span>
    <span class="s2">&quot;due_date_to&quot;</span><span class="p">:</span> <span class="s2">&quot;2025-12-31&quot;</span><span class="p">,</span>
    <span class="s2">&quot;page&quot;</span><span class="p">:</span> <span class="mi">1</span><span class="p">,</span>                         <span class="c1"># Pagination support</span>
    <span class="s2">&quot;page_size&quot;</span><span class="p">:</span> <span class="mi">20</span><span class="p">,</span>
    <span class="s2">&quot;sort_by&quot;</span><span class="p">:</span> <span class="s2">&quot;due_date&quot;</span><span class="p">,</span>             <span class="c1"># Sorting options</span>
    <span class="s2">&quot;sort_order&quot;</span><span class="p">:</span> <span class="s2">&quot;asc&quot;</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="2-taskmanagerservice-business-logic-layer">2. TaskManagerService (Business Logic Layer)<a class="headerlink" href="#2-taskmanagerservice-business-logic-layer" title="Permanent link">&para;</a></h4>
<p><strong>Location:</strong> <code>server/src/core/services/general/task_manager_service.py</code> <strong>Purpose:</strong> Centralized business logic for
task management operations with comprehensive validation and workflow management</p>
<p><strong>Core Responsibilities:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">TaskManagerService</span><span class="p">:</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_task</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">task_data</span><span class="p">:</span> <span class="n">TaskCreateSchema</span><span class="p">,</span>
        <span class="n">current_user</span><span class="p">:</span> <span class="n">User</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Task</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Create new task with comprehensive validation.</span>

<span class="sd">        Features:</span>
<span class="sd">        - Project membership validation</span>
<span class="sd">        - Due date validation and business rules</span>
<span class="sd">        - Automatic status initialization</span>
<span class="sd">        - Activity logging and audit trail</span>
<span class="sd">        - Integration with project workflow</span>
<span class="sd">        &quot;&quot;&quot;</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">assign_users_to_task</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">task_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
        <span class="n">user_ids</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">int</span><span class="p">],</span>
        <span class="n">current_user</span><span class="p">:</span> <span class="n">User</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Task</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Assign multiple users to task with validation.</span>

<span class="sd">        Features:</span>
<span class="sd">        - Duplicate assignment prevention</span>
<span class="sd">        - User permission validation</span>
<span class="sd">        - Project membership verification</span>
<span class="sd">        - Real-time notification integration</span>
<span class="sd">        - Assignment history tracking</span>
<span class="sd">        &quot;&quot;&quot;</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_tasks_for_project</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span>
        <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
        <span class="n">pagination_params</span><span class="p">:</span> <span class="n">PaginationParams</span><span class="p">,</span>
        <span class="n">sort_params</span><span class="p">:</span> <span class="n">SortParams</span><span class="p">,</span>
        <span class="n">filters</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">]</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">PaginatedResponse</span><span class="p">[</span><span class="n">Task</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Retrieve filtered and paginated tasks for project.</span>

<span class="sd">        Features:</span>
<span class="sd">        - Advanced filtering with enum conversion</span>
<span class="sd">        - Efficient pagination with performance optimization</span>
<span class="sd">        - Multi-criteria sorting capabilities</span>
<span class="sd">        - Assignment relationship loading</span>
<span class="sd">        - Permission-based filtering</span>
<span class="sd">        &quot;&quot;&quot;</span>
</code></pre></div>
<h4 id="3-data-models-and-schema-design">3. Data Models and Schema Design<a class="headerlink" href="#3-data-models-and-schema-design" title="Permanent link">&para;</a></h4>
<p><strong>Task Model Structure:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">Task</span><span class="p">(</span><span class="n">Base</span><span class="p">,</span> <span class="n">SoftDeleteColumns</span><span class="p">,</span> <span class="n">TimestampColumns</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Core task entity with comprehensive metadata and relationships.</span>

<span class="sd">    Features:</span>
<span class="sd">    - Soft delete capability for data retention</span>
<span class="sd">    - Automatic timestamp tracking</span>
<span class="sd">    - Rich enum-based status and priority system</span>
<span class="sd">    - Flexible assignment relationship management</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="nb">id</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">primary_key</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">title</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">255</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
    <span class="n">description</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">Text</span><span class="p">)</span>

    <span class="c1"># Status and Priority (Enum-based)</span>
    <span class="n">status</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">TaskStatus</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">EnumType</span><span class="p">(</span><span class="n">TaskStatus</span><span class="p">),</span>
        <span class="n">default</span><span class="o">=</span><span class="n">TaskStatus</span><span class="o">.</span><span class="n">NOT_STARTED</span>
    <span class="p">)</span>
    <span class="n">priority</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">TaskPriority</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">EnumType</span><span class="p">(</span><span class="n">TaskPriority</span><span class="p">),</span>
        <span class="n">default</span><span class="o">=</span><span class="n">TaskPriority</span><span class="o">.</span><span class="n">MEDIUM</span>
    <span class="p">)</span>

    <span class="c1"># Temporal Management</span>
    <span class="n">due_date</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">]]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">))</span>
    <span class="n">estimated_hours</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">Optional</span><span class="p">[</span><span class="n">Decimal</span><span class="p">]]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">Numeric</span><span class="p">(</span><span class="n">precision</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">scale</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
    <span class="p">)</span>

    <span class="c1"># Project Integration</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">Integer</span><span class="p">,</span>
        <span class="n">ForeignKey</span><span class="p">(</span><span class="s2">&quot;project.id&quot;</span><span class="p">,</span> <span class="n">ondelete</span><span class="o">=</span><span class="s2">&quot;CASCADE&quot;</span><span class="p">)</span>
    <span class="p">)</span>

    <span class="c1"># User Management</span>
    <span class="n">created_by_user_id</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">Integer</span><span class="p">,</span>
        <span class="n">ForeignKey</span><span class="p">(</span><span class="s2">&quot;user.id&quot;</span><span class="p">)</span>
    <span class="p">)</span>

    <span class="c1"># Relationships</span>
    <span class="n">project</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="s2">&quot;Project&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">relationship</span><span class="p">(</span><span class="s2">&quot;Project&quot;</span><span class="p">,</span> <span class="n">back_populates</span><span class="o">=</span><span class="s2">&quot;tasks&quot;</span><span class="p">)</span>
    <span class="n">created_by</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="s2">&quot;User&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">relationship</span><span class="p">(</span><span class="s2">&quot;User&quot;</span><span class="p">)</span>
    <span class="n">assignments</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">List</span><span class="p">[</span><span class="s2">&quot;TaskAssignment&quot;</span><span class="p">]]</span> <span class="o">=</span> <span class="n">relationship</span><span class="p">(</span>
        <span class="s2">&quot;TaskAssignment&quot;</span><span class="p">,</span>
        <span class="n">back_populates</span><span class="o">=</span><span class="s2">&quot;task&quot;</span><span class="p">,</span>
        <span class="n">cascade</span><span class="o">=</span><span class="s2">&quot;all, delete-orphan&quot;</span>
    <span class="p">)</span>
</code></pre></div>
<p><strong>TaskAssignment Model Structure:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">TaskAssignment</span><span class="p">(</span><span class="n">Base</span><span class="p">,</span> <span class="n">SoftDeleteColumns</span><span class="p">,</span> <span class="n">TimestampColumns</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    User-Task assignment relationship with metadata tracking.</span>

<span class="sd">    Features:</span>
<span class="sd">    - Many-to-many relationship between users and tasks</span>
<span class="sd">    - Assignment metadata (assigned date, role, etc.)</span>
<span class="sd">    - Soft delete for assignment history</span>
<span class="sd">    - Active status tracking</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="nb">id</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">primary_key</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="c1"># Foreign Key Relationships</span>
    <span class="n">task_id</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">Integer</span><span class="p">,</span>
        <span class="n">ForeignKey</span><span class="p">(</span><span class="s2">&quot;task.id&quot;</span><span class="p">,</span> <span class="n">ondelete</span><span class="o">=</span><span class="s2">&quot;CASCADE&quot;</span><span class="p">)</span>
    <span class="p">)</span>
    <span class="n">user_id</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">Integer</span><span class="p">,</span>
        <span class="n">ForeignKey</span><span class="p">(</span><span class="s2">&quot;user.id&quot;</span><span class="p">,</span> <span class="n">ondelete</span><span class="o">=</span><span class="s2">&quot;CASCADE&quot;</span><span class="p">)</span>
    <span class="p">)</span>
    <span class="n">assigned_by_user_id</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">Integer</span><span class="p">,</span>
        <span class="n">ForeignKey</span><span class="p">(</span><span class="s2">&quot;user.id&quot;</span><span class="p">)</span>
    <span class="p">)</span>

    <span class="c1"># Assignment Metadata</span>
    <span class="n">assigned_at</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="n">datetime</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span>
        <span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span>
        <span class="n">default</span><span class="o">=</span><span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">()</span>
    <span class="p">)</span>
    <span class="n">is_active</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="nb">bool</span><span class="p">]</span> <span class="o">=</span> <span class="n">mapped_column</span><span class="p">(</span><span class="n">Boolean</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>

    <span class="c1"># Relationships</span>
    <span class="n">task</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="s2">&quot;Task&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">relationship</span><span class="p">(</span><span class="s2">&quot;Task&quot;</span><span class="p">,</span> <span class="n">back_populates</span><span class="o">=</span><span class="s2">&quot;assignments&quot;</span><span class="p">)</span>
    <span class="n">user</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="s2">&quot;User&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">relationship</span><span class="p">(</span><span class="s2">&quot;User&quot;</span><span class="p">,</span> <span class="n">foreign_keys</span><span class="o">=</span><span class="p">[</span><span class="n">user_id</span><span class="p">])</span>
    <span class="n">assigned_by</span><span class="p">:</span> <span class="n">Mapped</span><span class="p">[</span><span class="s2">&quot;User&quot;</span><span class="p">]</span> <span class="o">=</span> <span class="n">relationship</span><span class="p">(</span><span class="s2">&quot;User&quot;</span><span class="p">,</span> <span class="n">foreign_keys</span><span class="o">=</span><span class="p">[</span><span class="n">assigned_by_user_id</span><span class="p">])</span>
</code></pre></div>
<h4 id="4-validation-schemas-pydantic">4. Validation Schemas (Pydantic)<a class="headerlink" href="#4-validation-schemas-pydantic" title="Permanent link">&para;</a></h4>
<p><strong>Request/Response Schema Design:</strong></p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">TaskCreateSchema</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Input validation for task creation with comprehensive business rules.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">title</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">min_length</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_length</span><span class="o">=</span><span class="mi">255</span><span class="p">)</span>
    <span class="n">description</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">max_length</span><span class="o">=</span><span class="mi">2000</span><span class="p">)</span>
    <span class="n">status</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TaskStatus</span><span class="p">]</span> <span class="o">=</span> <span class="n">TaskStatus</span><span class="o">.</span><span class="n">NOT_STARTED</span>
    <span class="n">priority</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">TaskPriority</span><span class="p">]</span> <span class="o">=</span> <span class="n">TaskPriority</span><span class="o">.</span><span class="n">MEDIUM</span>
    <span class="n">due_date</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">estimated_hours</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Decimal</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">0</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mf">999.99</span><span class="p">)</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">gt</span><span class="o">=</span><span class="mi">0</span><span class="p">)</span>

    <span class="nd">@validator</span><span class="p">(</span><span class="s1">&#39;due_date&#39;</span><span class="p">)</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">validate_due_date</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">):</span>
        <span class="k">if</span> <span class="n">v</span> <span class="ow">and</span> <span class="n">v</span> <span class="o">&lt;=</span> <span class="n">datetime</span><span class="o">.</span><span class="n">now</span><span class="p">(</span><span class="n">timezone</span><span class="o">.</span><span class="n">utc</span><span class="p">):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Due date must be in the future&#39;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">v</span>

<span class="k">class</span><span class="w"> </span><span class="nc">TaskReadSchema</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Response schema with comprehensive task information and relationships.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="nb">id</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">title</span><span class="p">:</span> <span class="nb">str</span>
    <span class="n">description</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">str</span><span class="p">]</span>
    <span class="n">status</span><span class="p">:</span> <span class="n">TaskStatus</span>
    <span class="n">priority</span><span class="p">:</span> <span class="n">TaskPriority</span>
    <span class="n">due_date</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">]</span>
    <span class="n">estimated_hours</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">Decimal</span><span class="p">]</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">created_by_user_id</span><span class="p">:</span> <span class="nb">int</span>
    <span class="n">created_at</span><span class="p">:</span> <span class="n">datetime</span>
    <span class="n">updated_at</span><span class="p">:</span> <span class="n">datetime</span>

    <span class="c1"># Relationship Data</span>
    <span class="n">assignments</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">TaskAssignmentReadSchema</span><span class="p">]</span> <span class="o">=</span> <span class="p">[]</span>
    <span class="n">project</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">ProjectSummarySchema</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="n">created_by</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">UserSummarySchema</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">class</span><span class="w"> </span><span class="nc">Config</span><span class="p">:</span>
        <span class="n">from_attributes</span> <span class="o">=</span> <span class="kc">True</span>

<span class="k">class</span><span class="w"> </span><span class="nc">TaskAssignmentCreateSchema</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Schema for creating task assignments with validation.</span>
<span class="sd">    &quot;&quot;&quot;</span>
    <span class="n">user_ids</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">min_items</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">max_items</span><span class="o">=</span><span class="mi">10</span><span class="p">)</span>

    <span class="nd">@validator</span><span class="p">(</span><span class="s1">&#39;user_ids&#39;</span><span class="p">)</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">validate_unique_users</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">):</span>
        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">v</span><span class="p">)</span> <span class="o">!=</span> <span class="nb">len</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">v</span><span class="p">)):</span>
            <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s1">&#39;Duplicate user IDs not allowed&#39;</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">v</span>
</code></pre></div>
<h4 id="5-generic-pagination-schema-architecture">5. Generic Pagination Schema Architecture<a class="headerlink" href="#5-generic-pagination-schema-architecture" title="Permanent link">&para;</a></h4>
<p><strong>Type-Safe Pagination System:</strong></p>
<p>The application implements a generic pagination schema system that provides type safety and consistency across all
paginated endpoints.</p>
<div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">TypeVar</span><span class="p">,</span> <span class="n">Generic</span><span class="p">,</span> <span class="n">List</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pydantic</span><span class="w"> </span><span class="kn">import</span> <span class="n">BaseModel</span><span class="p">,</span> <span class="n">Field</span>

<span class="c1"># Type variable for generic pagination</span>
<span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;T&quot;</span><span class="p">)</span>

<span class="k">class</span><span class="w"> </span><span class="nc">PaginationSchema</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Schema for pagination parameters.&quot;&quot;&quot;</span>

    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number (1-based)&quot;</span><span class="p">)</span>
    <span class="n">size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Items per page&quot;</span><span class="p">)</span>
    <span class="n">total</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Total number of items&quot;</span><span class="p">)</span>
    <span class="n">pages</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Total number of pages&quot;</span><span class="p">)</span>

<span class="k">class</span><span class="w"> </span><span class="nc">PaginatedResponseSchema</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">,</span> <span class="n">Generic</span><span class="p">[</span><span class="n">T</span><span class="p">]):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Generic paginated response schema.&quot;&quot;&quot;</span>

    <span class="n">items</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">T</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;List of items&quot;</span><span class="p">)</span>
    <span class="n">pagination</span><span class="p">:</span> <span class="n">PaginationSchema</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Pagination information&quot;</span><span class="p">)</span>

<span class="c1"># Usage Examples:</span>
<span class="k">class</span><span class="w"> </span><span class="nc">TaskListResponseSchema</span><span class="p">(</span><span class="n">PaginatedResponseSchema</span><span class="p">[</span><span class="n">TaskReadSchema</span><span class="p">]):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Paginated response schema for tasks.&quot;&quot;&quot;</span>
    <span class="k">pass</span>

<span class="k">class</span><span class="w"> </span><span class="nc">ComponentListResponseSchema</span><span class="p">(</span><span class="n">PaginatedResponseSchema</span><span class="p">[</span><span class="n">ComponentReadSchema</span><span class="p">]):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Paginated response schema for components.&quot;&quot;&quot;</span>
    <span class="k">pass</span>
</code></pre></div>
<p><strong>Key Benefits:</strong></p>
<ol>
<li><strong>Type Safety</strong>: Full MyPy compliance with generic type parameters</li>
<li><strong>Consistency</strong>: Unified pagination structure across all endpoints</li>
<li><strong>Maintainability</strong>: Single source of truth for pagination logic</li>
<li><strong>Extensibility</strong>: Easy to add new paginated endpoints with proper typing</li>
</ol>
<p><strong>Migration from Legacy Patterns:</strong></p>
<p>The generic pagination system replaces previous non-generic implementations, ensuring:</p>
<ul>
<li>Zero MyPy type errors</li>
<li>Consistent API response structure</li>
<li>Improved developer experience with proper IDE support</li>
<li>Future-proof architecture for new endpoints</li>
</ul>
<hr />
<h2 id="frontend-component-architecture">Frontend Component Architecture<a class="headerlink" href="#frontend-component-architecture" title="Permanent link">&para;</a></h2>
<h3 id="atomic-design-system">Atomic Design System<a class="headerlink" href="#atomic-design-system" title="Permanent link">&para;</a></h3>
<p>The Ultimate Electrical Designer implements <strong>Atomic Design methodology</strong> across all feature modules, providing a
consistent, scalable, and maintainable component architecture. This system ensures architectural consistency and
reusability across the entire application.</p>
<h4 id="atomic-design-hierarchy">Atomic Design Hierarchy<a class="headerlink" href="#atomic-design-hierarchy" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code>App (layout.tsx)
├── Header
│   ├── Navigation
│   ├── UserMenu
│   └── ThemeToggle
├── Main Content
│   ├── Dashboard
│   │   ├── ProjectSummary
│   │   ├── RecentCalculations
│   │   └── QuickActions
│   ├── Components Module (Atomic Design)
│   │   ├── Atoms/
│   │   │   ├── ComponentBadge
│   │   │   ├── StatusIndicator
│   │   │   ├── ActionButton
│   │   │   └── SearchInput
│   │   ├── Molecules/
│   │   │   ├── ComponentCard
│   │   │   ├── FilterPanel
│   │   │   ├── SearchBar
│   │   │   └── BulkActionBar
│   │   ├── Organisms/
│   │   │   ├── ComponentList
│   │   │   ├── ComponentForm
│   │   │   └── ComponentTable
│   │   └── Templates/
│   │       ├── ComponentListPage
│   │       └── ComponentDetailPage
│   ├── Projects Module (Atomic Design)
│   │   ├── Atoms/
│   │   │   ├── StatusBadge
│   │   │   ├── PriorityBadge
│   │   │   ├── ActionButton
│   │   │   ├── FormInput
│   │   │   ├── LoadingSpinner
│   │   │   └── EmptyState
│   │   ├── Molecules/
│   │   │   ├── SearchBar
│   │   │   ├── BulkActionBar
│   │   │   ├── FilterPanel
│   │   │   ├── MemberCard
│   │   │   └── MemberForm
│   │   ├── Organisms/
│   │   │   ├── ProjectList
│   │   │   ├── TeamManagement
│   │   │   └── ProjectForm
│   │   └── Pages/
│   │       ├── /projects (list)
│   │       ├── /projects/new (creation)
│   │       └── /projects/[id] (detail)
│   └── Calculations Module
│       ├── CalculationWizard
│       ├── ResultsDisplay
│       ├── ReportGenerator
│       └── ParameterForm
└── Footer
    ├── StatusBar
    └── HelpLinks
</code></pre></div>
<h4 id="atomic-design-principles">Atomic Design Principles<a class="headerlink" href="#atomic-design-principles" title="Permanent link">&para;</a></h4>
<p><strong>Atoms</strong>: Basic building blocks that can't be broken down further</p>
<ul>
<li>Single responsibility (one UI element, one purpose)</li>
<li>Highly reusable across multiple contexts</li>
<li>No business logic, pure presentation</li>
<li>Examples: <code>StatusBadge</code>, <code>ActionButton</code>, <code>FormInput</code></li>
</ul>
<p><strong>Molecules</strong>: Simple combinations of atoms with a specific function</p>
<ul>
<li>Combine multiple atoms for a focused purpose</li>
<li>Reusable components with defined interfaces</li>
<li>May contain simple state management</li>
<li>Examples: <code>SearchBar</code>, <code>MemberCard</code>, <code>FilterPanel</code></li>
</ul>
<p><strong>Organisms</strong>: Complex components combining molecules and atoms</p>
<ul>
<li>Represent distinct sections of an interface</li>
<li>Can contain business logic and domain integration</li>
<li>Connected to application state and APIs</li>
<li>Examples: <code>ProjectList</code>, <code>TeamManagement</code>, <code>ComponentTable</code></li>
</ul>
<p><strong>Templates</strong>: Page-level layouts defining content structure</p>
<ul>
<li>Define content areas and responsive behavior</li>
<li>No specific content, just layout structure</li>
<li>Consistent spacing and accessibility patterns</li>
</ul>
<p><strong>Pages</strong>: Specific instances of templates with real content</p>
<ul>
<li>Complete user interfaces for specific routes</li>
<li>Integration with routing and data fetching</li>
<li>Examples: <code>/projects</code>, <code>/projects/new</code>, <code>/projects/[id]</code></li>
</ul>
<p>For full specifications, UI contracts, examples, and migration steps, refer to the canonical
<a href="../atomic-design-system/ATOMIC_DESIGN_GUIDE/">Atomic Design System Guide</a>.</p>
<h3 id="domain-integration">Domain Integration<a class="headerlink" href="#domain-integration" title="Permanent link">&para;</a></h3>
<p>Each atomic design module maintains <strong>domain-aware integration</strong>:</p>
<div class="highlight"><pre><span></span><code><span class="c1">// Domain-aware hooks integration</span>
<span class="kd">const</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">useProjectHooks</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useProjectHooks</span><span class="p">();</span><span class="w"> </span><span class="c1">// Application services</span>
<span class="kd">const</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">useProjectTeam</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useProjectTeam</span><span class="p">();</span><span class="w"> </span><span class="c1">// Team management</span>
<span class="kd">const</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">useProjectStatus</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useProjectStatus</span><span class="p">();</span><span class="w"> </span><span class="c1">// Status management</span>

<span class="c1">// State management integration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">projects</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useProjectStore</span><span class="p">((</span><span class="nx">state</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">state</span><span class="p">.</span><span class="nx">projects</span><span class="p">);</span><span class="w"> </span><span class="c1">// Zustand</span>
<span class="kd">const</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">data</span><span class="p">,</span><span class="w"> </span><span class="nx">isLoading</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useProjectsQuery</span><span class="p">();</span><span class="w"> </span><span class="c1">// React Query</span>
</code></pre></div>
<h3 id="component-composition-patterns">Component Composition Patterns<a class="headerlink" href="#component-composition-patterns" title="Permanent link">&para;</a></h3>
<p><strong>Compound Components</strong>: Complex components built from atomic parts</p>
<div class="highlight"><pre><span></span><code><span class="o">&lt;</span><span class="nx">ProjectList</span><span class="o">&gt;</span>
<span class="w">  </span><span class="o">&lt;</span><span class="nx">ProjectList</span><span class="p">.</span><span class="nx">Header</span><span class="o">&gt;</span>
<span class="w">    </span><span class="o">&lt;</span><span class="nx">SearchBar</span><span class="w"> </span><span class="o">/&gt;</span>
<span class="w">    </span><span class="o">&lt;</span><span class="nx">FilterPanel</span><span class="w"> </span><span class="o">/&gt;</span>
<span class="w">    </span><span class="o">&lt;</span><span class="nx">BulkActionBar</span><span class="w"> </span><span class="o">/&gt;</span>
<span class="w">  </span><span class="o">&lt;</span><span class="err">/ProjectList.Header&gt;</span>
<span class="w">  </span><span class="o">&lt;</span><span class="nx">ProjectList</span><span class="p">.</span><span class="nx">Content</span><span class="o">&gt;</span>
<span class="w">    </span><span class="p">{</span><span class="nx">projects</span><span class="p">.</span><span class="nx">map</span><span class="p">((</span><span class="nx">project</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">(</span>
<span class="w">      </span><span class="o">&lt;</span><span class="nx">ProjectCard</span><span class="w"> </span><span class="nx">key</span><span class="o">=</span><span class="p">{</span><span class="nx">project</span><span class="p">.</span><span class="nx">id</span><span class="p">}</span><span class="w"> </span><span class="nx">project</span><span class="o">=</span><span class="p">{</span><span class="nx">project</span><span class="p">}</span><span class="w"> </span><span class="o">/&gt;</span>
<span class="w">    </span><span class="p">))}</span>
<span class="w">  </span><span class="o">&lt;</span><span class="err">/ProjectList.Content&gt;</span>
<span class="o">&lt;</span><span class="err">/ProjectList&gt;</span>
</code></pre></div>
<p><strong>Render Props</strong>: Flexible component composition</p>
<div class="highlight"><pre><span></span><code><span class="o">&lt;</span><span class="nx">DataTable</span>
<span class="w">  </span><span class="nx">data</span><span class="o">=</span><span class="p">{</span><span class="nx">projects</span><span class="p">}</span>
<span class="w">  </span><span class="nx">renderRow</span><span class="o">=</span><span class="p">{(</span><span class="nx">project</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="o">&lt;</span><span class="nx">ProjectRow</span><span class="w"> </span><span class="nx">project</span><span class="o">=</span><span class="p">{</span><span class="nx">project</span><span class="p">}</span><span class="w"> </span><span class="o">/&gt;</span><span class="p">}</span>
<span class="w">  </span><span class="nx">renderHeader</span><span class="o">=</span><span class="p">{()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="o">&lt;</span><span class="nx">ProjectHeader</span><span class="w"> </span><span class="o">/&gt;</span><span class="p">}</span>
<span class="w">  </span><span class="nx">renderEmpty</span><span class="o">=</span><span class="p">{()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="o">&lt;</span><span class="nx">EmptyState</span><span class="w"> </span><span class="o">/&gt;</span><span class="p">}</span>
<span class="err">/&gt;</span>
</code></pre></div>
<h3 id="accessibility-performance">Accessibility &amp; Performance<a class="headerlink" href="#accessibility-performance" title="Permanent link">&para;</a></h3>
<p><strong>Accessibility Integration</strong>:</p>
<ul>
<li>WCAG 2.1 AA compliance at atomic level</li>
<li>Semantic HTML structure in all atoms</li>
<li>Keyboard navigation support</li>
<li>Screen reader compatibility</li>
<li>Focus management</li>
</ul>
<p><strong>Performance Optimization</strong>:</p>
<ul>
<li>React.memo for atoms and molecules</li>
<li>useMemo for expensive calculations</li>
<li>useCallback for event handlers</li>
<li>Code splitting at organism level</li>
<li>Lazy loading for large components</li>
</ul>
<hr />
<h2 id="data-validation-integrity-architecture">Data Validation &amp; Integrity Architecture<a class="headerlink" href="#data-validation-integrity-architecture" title="Permanent link">&para;</a></h2>
<h3 id="email-normalization-system">Email Normalization System<a class="headerlink" href="#email-normalization-system" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Schema Layer (Pydantic) - Email Normalization</span>
<span class="nd">@field_validator</span><span class="p">(</span><span class="s2">&quot;email&quot;</span><span class="p">)</span>
<span class="nd">@classmethod</span>
<span class="k">def</span><span class="w"> </span><span class="nf">normalize_email</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Normalize email to lowercase for consistency.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">v</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span> <span class="k">if</span> <span class="n">v</span> <span class="k">else</span> <span class="n">v</span>
</code></pre></div>
<div class="highlight"><pre><span></span><code><span class="c1"># Repository Layer - Case-Insensitive Lookup</span>
<span class="k">def</span><span class="w"> </span><span class="nf">get_by_email</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">email</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">User</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Get user by email address (case-insensitive).&quot;&quot;&quot;</span>
    <span class="n">normalized_email</span> <span class="o">=</span> <span class="n">email</span><span class="o">.</span><span class="n">lower</span><span class="p">()</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
    <span class="n">stmt</span> <span class="o">=</span> <span class="n">select</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="p">)</span><span class="o">.</span><span class="n">where</span><span class="p">(</span>
        <span class="n">and_</span><span class="p">(</span>
            <span class="n">func</span><span class="o">.</span><span class="n">lower</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">email</span><span class="p">)</span> <span class="o">==</span> <span class="n">normalized_email</span><span class="p">,</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">model</span><span class="o">.</span><span class="n">is_active</span> <span class="o">==</span> <span class="kc">True</span><span class="p">,</span>
        <span class="p">)</span>
    <span class="p">)</span>
    <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">db_session</span><span class="o">.</span><span class="n">scalar</span><span class="p">(</span><span class="n">stmt</span><span class="p">)</span>
</code></pre></div>
<div class="highlight"><pre><span></span><code><span class="c1"># Service Layer - Enhanced Validation</span>
<span class="k">def</span><span class="w"> </span><span class="nf">_validate_user_creation</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">user_data</span><span class="p">:</span> <span class="n">UserCreateSchema</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="kc">None</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Perform business validation for user creation.&quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">user_repo</span><span class="o">.</span><span class="n">check_email_exists</span><span class="p">(</span><span class="n">user_data</span><span class="o">.</span><span class="n">email</span><span class="p">):</span>
        <span class="k">raise</span> <span class="n">InvalidInputError</span><span class="p">(</span>
            <span class="sa">f</span><span class="s2">&quot;Email address &#39;</span><span class="si">{</span><span class="n">user_data</span><span class="o">.</span><span class="n">email</span><span class="si">}</span><span class="s2">&#39; is already registered. &quot;</span>
            <span class="s2">&quot;Please use a different email or sign in to your existing account.&quot;</span>
        <span class="p">)</span>
</code></pre></div>
<h3 id="string-length-validation-system">String Length Validation System<a class="headerlink" href="#string-length-validation-system" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Schema-Level Validation with Specific Error Messages</span>
<span class="nd">@field_validator</span><span class="p">(</span><span class="s2">&quot;name&quot;</span><span class="p">)</span>
<span class="nd">@classmethod</span>
<span class="k">def</span><span class="w"> </span><span class="nf">validate_name</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">:</span> <span class="nb">str</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="nb">str</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Validate and normalize name field.&quot;&quot;&quot;</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">v</span> <span class="ow">or</span> <span class="ow">not</span> <span class="n">v</span><span class="o">.</span><span class="n">strip</span><span class="p">():</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Name cannot be empty or just whitespace&quot;</span><span class="p">)</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">v</span><span class="o">.</span><span class="n">strip</span><span class="p">()</span>
    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">name</span><span class="p">)</span> <span class="o">&lt;</span> <span class="mi">3</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Name must be at least 3 characters long&quot;</span><span class="p">)</span>
    <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">name</span><span class="p">)</span> <span class="o">&gt;</span> <span class="mi">50</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="s2">&quot;Name cannot exceed 50 characters&quot;</span><span class="p">)</span>
    <span class="k">return</span> <span class="n">name</span>
</code></pre></div>
<h3 id="database-constraint-enforcement">Database Constraint Enforcement<a class="headerlink" href="#database-constraint-enforcement" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Test Environment - PostgreSQL Configuration</span>
<span class="c1"># PostgreSQL automatically enforces foreign key constraints</span>
<span class="c1"># No additional configuration needed for constraint enforcement</span>
</code></pre></div>
<hr />
<h2 id="security-architecture">Security Architecture<a class="headerlink" href="#security-architecture" title="Permanent link">&para;</a></h2>
<h3 id="authentication-authorization-flow">Authentication &amp; Authorization Flow<a class="headerlink" href="#authentication-authorization-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>┌─────────────┐    1. Login Request    ┌─────────────────┐
│   Client    │ ────────────────────► │Security Middleware│
└─────────────┘                       └─────────────────┘
       │                                       │
       │                              2. Validate JWT
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │Security Validator│
       │                               └─────────────────┘
       │                                       │
       │                              3. Check Permissions
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │   Auth Service  │
       │                               └─────────────────┘
       │                                       │
       │                              4. Route to Handler
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │  API Endpoint   │
       │                               └─────────────────┘
</code></pre></div>
<h3 id="security-layers">Security Layers<a class="headerlink" href="#security-layers" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Transport Security</strong>: TLS 1.3 encryption for all communications</li>
<li><strong>Authentication</strong>: JWT tokens with RS256 signing</li>
<li><strong>Authorization</strong>: Role-based access control (RBAC)</li>
<li><strong>Input Validation</strong>: Pydantic schemas for all API inputs</li>
<li><strong>Output Encoding</strong>: XSS prevention through proper encoding</li>
<li><strong>SQL Injection Prevention</strong>: Parameterized queries only</li>
<li><strong>Rate Limiting</strong>: API endpoint protection against abuse</li>
<li><strong>CORS Configuration</strong>: Controlled cross-origin access</li>
</ol>
<hr />
<h2 id="performance-architecture">Performance Architecture<a class="headerlink" href="#performance-architecture" title="Permanent link">&para;</a></h2>
<h3 id="caching-strategy">Caching Strategy<a class="headerlink" href="#caching-strategy" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>┌─────────────┐    Request    ┌─────────────────┐
│   Client    │ ──────────► │   API Gateway   │
└─────────────┘              └─────────────────┘
                                      │
                              Check Cache
                                      ▼
                              ┌─────────────────┐
                              │  Redis Cache    │
                              │  (Session Data) │
                              └─────────────────┘
                                      │
                              Cache Miss
                                      ▼
                              ┌─────────────────┐
                              │   Database      │
                              │  (PostgreSQL)   │
                              └─────────────────┘
</code></pre></div>
<h3 id="performance-optimization">Performance Optimization<a class="headerlink" href="#performance-optimization" title="Permanent link">&para;</a></h3>
<ol>
<li>
<p><strong>Database Optimization</strong>:</p>
</li>
<li>
<p>Strategic indexing for common queries</p>
</li>
<li>Connection pooling with configurable limits</li>
<li>Query optimization with EXPLAIN analysis</li>
<li>
<p>Read replicas for reporting queries</p>
</li>
<li>
<p><strong>Application Optimization</strong>:</p>
</li>
<li>
<p>Async/await for non-blocking operations</p>
</li>
<li>Lazy loading for large datasets</li>
<li>Pagination for list endpoints</li>
<li>
<p>Background tasks for heavy calculations</p>
</li>
<li>
<p><strong>Frontend Optimization</strong>:</p>
</li>
<li>Code splitting with dynamic imports</li>
<li>Image optimization with Next.js</li>
<li>Bundle analysis and tree shaking</li>
<li>Service worker for offline capabilities</li>
</ol>
<hr />
<h2 id="integration-patterns">Integration Patterns<a class="headerlink" href="#integration-patterns" title="Permanent link">&para;</a></h2>
<h3 id="real-time-collaboration">Real-time Collaboration<a class="headerlink" href="#real-time-collaboration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// WebSocket connection management</span>
<span class="kd">class</span><span class="w"> </span><span class="nx">CollaborationService</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">ws</span><span class="o">:</span><span class="w"> </span><span class="kt">WebSocket</span><span class="p">;</span>

<span class="w">  </span><span class="nx">connect</span><span class="p">(</span><span class="nx">projectId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">ws</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">WebSocket</span><span class="p">(</span><span class="sb">`wss://api.example.com/ws/project/</span><span class="si">${</span><span class="nx">projectId</span><span class="si">}</span><span class="sb">`</span><span class="p">);</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">ws</span><span class="p">.</span><span class="nx">onmessage</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">handleMessage</span><span class="p">;</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="nx">sendUpdate</span><span class="p">(</span><span class="nx">update</span><span class="o">:</span><span class="w"> </span><span class="kt">ProjectUpdate</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">ws</span><span class="p">.</span><span class="nx">send</span><span class="p">(</span>
<span class="w">      </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">stringify</span><span class="p">({</span>
<span class="w">        </span><span class="kr">type</span><span class="o">:</span><span class="w"> </span><span class="s2">&quot;project_update&quot;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">data</span><span class="o">:</span><span class="w"> </span><span class="kt">update</span><span class="p">,</span>
<span class="w">        </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">().</span><span class="nx">toISOString</span><span class="p">(),</span>
<span class="w">      </span><span class="p">})</span>
<span class="w">    </span><span class="p">);</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">handleMessage</span><span class="p">(</span><span class="nx">event</span><span class="o">:</span><span class="w"> </span><span class="kt">MessageEvent</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">message</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">parse</span><span class="p">(</span><span class="nx">event</span><span class="p">.</span><span class="nx">data</span><span class="p">);</span>
<span class="w">    </span><span class="c1">// Handle real-time updates</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="data-flow-patterns">Data Flow Patterns<a class="headerlink" href="#data-flow-patterns" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Unidirectional Data Flow</strong>: React Query for server state, Zustand for client state</li>
<li><strong>Optimistic Updates</strong>: Immediate UI updates with rollback on failure</li>
<li><strong>Background Sync</strong>: Automatic data synchronization with conflict resolution</li>
<li><strong>Offline Support</strong>: Local storage with sync on reconnection</li>
</ol>
<hr />
<p>This technical design specification provides the comprehensive architectural foundation for implementing the Ultimate
Electrical Designer platform, ensuring scalability, maintainability, and alignment with engineering-grade standards.</p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../js/base.js"></script>
        <script src="../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
