<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/deployment/POSTGRESQL_INSTALLER_PLAN/">
        <link rel="shortcut icon" href="../../img/favicon.ico">
        <title>PostgreSQL Silent Installer Plan - Ultimate Electrical Designer Docs</title>
        <link href="../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../css/brands.min.css" rel="stylesheet">
        <link href="../../css/solid.min.css" rel="stylesheet">
        <link href="../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../tasks/" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#postgresql-silent-installer-plan" class="nav-link">PostgreSQL Silent Installer Plan</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#1-objective" class="nav-link">1. Objective</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#2-chosen-approach-command-line-installation-script" class="nav-link">2. Chosen Approach: Command-Line Installation Script</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#3-installer-components" class="nav-link">3. Installer Components</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#4-installation-steps-to-be-performed-by-the-script" class="nav-link">4. Installation Steps (to be performed by the script)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#5-configuration-file-setupcfg" class="nav-link">5. Configuration File (setup.cfg)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#6-security-considerations" class="nav-link">6. Security Considerations</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="postgresql-silent-installer-plan">PostgreSQL Silent Installer Plan<a class="headerlink" href="#postgresql-silent-installer-plan" title="Permanent link">&para;</a></h1>
<h2 id="1-objective">1. Objective<a class="headerlink" href="#1-objective" title="Permanent link">&para;</a></h2>
<p>To create a scripted, silent installer for PostgreSQL on Windows. This installer will automate the entire setup process, including installation, configuration, and user creation, requiring minimal user interaction. This is a critical component for deploying the shared local database server as defined in the project's architecture.</p>
<h2 id="2-chosen-approach-command-line-installation-script">2. Chosen Approach: Command-Line Installation Script<a class="headerlink" href="#2-chosen-approach-command-line-installation-script" title="Permanent link">&para;</a></h2>
<p>The chosen method is to use a PowerShell or Batch script that executes the official PostgreSQL installer from EnterpriseDB with a set of command-line arguments to achieve a silent, unattended installation.</p>
<p><strong>Rationale:</strong>
-   <strong>No External Dependencies:</strong> This approach does not require the end-user to install additional package managers like Chocolatey.
-   <strong>Official Installer:</strong> It leverages the official, trusted installer, ensuring a standard and reliable setup.
-   <strong>Customizable:</strong> Command-line arguments provide sufficient control over the installation process, including the installation directory, data directory, password, and port.</p>
<h2 id="3-installer-components">3. Installer Components<a class="headerlink" href="#3-installer-components" title="Permanent link">&para;</a></h2>
<p>The final installation package will consist of:
1.  <strong>The official PostgreSQL Windows installer (<code>.exe</code>)</strong>: This will be bundled with our application's deployment assets.
2.  <strong><code>install_postgres.ps1</code> (PowerShell Script)</strong>: The main script that orchestrates the installation.
3.  <strong><code>setup.cfg</code></strong>: A configuration file containing parameters like the desired installation path, data directory, password, etc. This allows for easy customization without modifying the script itself.
4.  <strong><code>init_db.sql</code></strong>: An SQL script to be run after installation to create the application-specific user and database.</p>
<h2 id="4-installation-steps-to-be-performed-by-the-script">4. Installation Steps (to be performed by the script)<a class="headerlink" href="#4-installation-steps-to-be-performed-by-the-script" title="Permanent link">&para;</a></h2>
<h3 id="step-1-pre-flight-checks">Step 1: Pre-flight Checks<a class="headerlink" href="#step-1-pre-flight-checks" title="Permanent link">&para;</a></h3>
<ul>
<li>The script will first check for existing PostgreSQL installations to avoid conflicts.</li>
<li>It will verify that it is being run with administrator privileges.</li>
<li>It will read the installation parameters from <code>setup.cfg</code>.</li>
</ul>
<h3 id="step-2-silent-installation">Step 2: Silent Installation<a class="headerlink" href="#step-2-silent-installation" title="Permanent link">&para;</a></h3>
<ul>
<li>The script will execute the PostgreSQL installer executable with the necessary command-line arguments. An example command would be:
    <div class="highlight"><pre><span></span><code><span class="p">.\\</span><span class="n">postgresql</span><span class="p">-</span><span class="n">16</span><span class="p">.</span><span class="n">3</span><span class="p">-</span><span class="n">1-windows-x64</span><span class="p">.</span><span class="n">exe</span> <span class="p">-</span><span class="n">-mode</span> <span class="n">unattended</span> <span class="p">-</span><span class="n">-unattendedmodeui</span> <span class="n">none</span> <span class="p">-</span><span class="n">-datadir</span> <span class="s2">&quot;C:\\PostgreSQL\\data&quot;</span> <span class="p">-</span><span class="n">-superpassword</span> <span class="s2">&quot;your_secure_password&quot;</span> <span class="p">-</span><span class="n">-serverport</span> <span class="n">5432</span>
</code></pre></div></li>
<li>The script will wait for the installation process to complete and check the exit code for success.</li>
</ul>
<h3 id="step-3-post-installation-configuration">Step 3: Post-Installation Configuration<a class="headerlink" href="#step-3-post-installation-configuration" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Update <code>pg_hba.conf</code></strong>: The script will programmatically add a line to <code>pg_hba.conf</code> to allow connections from other machines on the local network. For example, adding <code>host all all ***********/24 md5</code>.</li>
<li><strong>Update <code>postgresql.conf</code></strong>: The script will modify <code>postgresql.conf</code> to set <code>listen_addresses = '*'</code>, allowing it to accept connections on all network interfaces.</li>
</ul>
<h3 id="step-4-database-and-user-initialization">Step 4: Database and User Initialization<a class="headerlink" href="#step-4-database-and-user-initialization" title="Permanent link">&para;</a></h3>
<ul>
<li>The script will use <code>psql.exe</code> (now in the system's PATH) to execute the <code>init_db.sql</code> script.</li>
<li>This SQL script will:<ul>
<li>Create a new user role for the application (e.g., <code>ued_app_user</code>).</li>
<li>Create the application database (e.g., <code>ultimate_electrical_designer_local</code>).</li>
<li>Grant the necessary privileges on the database to the new user.</li>
</ul>
</li>
</ul>
<h3 id="step-5-firewall-configuration">Step 5: Firewall Configuration<a class="headerlink" href="#step-5-firewall-configuration" title="Permanent link">&para;</a></h3>
<ul>
<li>The script will add a new inbound rule to the Windows Firewall to allow traffic on the PostgreSQL port (e.g., 5432).</li>
</ul>
<h3 id="step-6-service-management">Step 6: Service Management<a class="headerlink" href="#step-6-service-management" title="Permanent link">&para;</a></h3>
<ul>
<li>The script will ensure the PostgreSQL service is running and set to start automatically on system boot.</li>
</ul>
<h2 id="5-configuration-file-setupcfg">5. Configuration File (<code>setup.cfg</code>)<a class="headerlink" href="#5-configuration-file-setupcfg" title="Permanent link">&para;</a></h2>
<p>This file will provide the parameters for the installation script.</p>
<div class="highlight"><pre><span></span><code><span class="k">[PostgreSQL]</span>
<span class="na">InstallerPath</span><span class="o">=</span><span class="s">./postgresql-16.3-1-windows-x64.exe</span>
<span class="na">InstallDir</span><span class="o">=</span><span class="s">C:\Program Files\PostgreSQL\16</span>
<span class="na">DataDir</span><span class="o">=</span><span class="s">C:\PostgreSQL\data</span>
<span class="na">Password</span><span class="o">=</span><span class="s">a_very_secure_default_password</span>
<span class="na">Port</span><span class="o">=</span><span class="s">5432</span>

<span class="k">[Database]</span>
<span class="na">AppName</span><span class="o">=</span><span class="s">ultimate_electrical_designer</span>
<span class="na">DBName</span><span class="o">=</span><span class="s">ued_local</span>
<span class="na">DBUser</span><span class="o">=</span><span class="s">ued_app_user</span>
<span class="na">DBPassword</span><span class="o">=</span><span class="s">another_secure_password</span>

<span class="k">[Network]</span>
<span class="na">AllowedSubnet</span><span class="o">=</span><span class="s">***********/24</span>
</code></pre></div>
<h2 id="6-security-considerations">6. Security Considerations<a class="headerlink" href="#6-security-considerations" title="Permanent link">&para;</a></h2>
<ul>
<li><strong>Default Passwords</strong>: The script will use strong, randomly generated default passwords and strongly recommend the user to change them immediately after installation. The script will output the generated passwords to a secure file on the desktop.</li>
<li><strong>Firewall Rules</strong>: The firewall rule will be as specific as possible, only allowing connections from the specified local subnet.</li>
</ul>
<p>This plan provides a clear path to creating a robust, automated installer for the shared local PostgreSQL database, which is a foundational piece of the new architecture.</p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../js/base.js"></script>
        <script src="../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
