<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="Technical documentation for the Ultimate Electrical Designer project">
        
        <link rel="canonical" href="https://example.com/">
        <link rel="shortcut icon" href="img/favicon.ico">
        <title>Ultimate Electrical Designer Docs</title>
        <link href="css/bootstrap.min.css" rel="stylesheet">
        <link href="css/fontawesome.min.css" rel="stylesheet">
        <link href="css/brands.min.css" rel="stylesheet">
        <link href="css/solid.min.css" rel="stylesheet">
        <link href="css/v4-font-face.min.css" rel="stylesheet">
        <link href="css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body class="homepage">
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href=".">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="." class="nav-link active" aria-current="page">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="tasks/" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                            <li class="nav-item">
                                <a rel="prev" class="nav-link disabled">
                                    <i class="fa fa-arrow-left"></i> Previous
                                </a>
                            </li>
                            <li class="nav-item">
                                <a rel="next" href="product/" class="nav-link">
                                    Next <i class="fa fa-arrow-right"></i>
                                </a>
                            </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#readme" class="nav-link">README</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#project-overview" class="nav-link">Project Overview</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#project-guidelines-reference-authoritative-source" class="nav-link">Project Guidelines Reference (AUTHORITATIVE SOURCE)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#development-commands" class="nav-link">Development Commands</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#key-architectural-patterns-mandatory-adoption" class="nav-link">Key Architectural Patterns (MANDATORY ADOPTION)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#testing-strategy-mandatory-adherence" class="nav-link">Testing Strategy (MANDATORY ADHERENCE)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#development-standards-strictly-enforced" class="nav-link">Development Standards (STRICTLY ENFORCED)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#module-relationships-understand-and-respect" class="nav-link">Module Relationships (UNDERSTAND AND RESPECT)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#performance-optimization" class="nav-link">Performance Optimization</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="readme">README<a class="headerlink" href="#readme" title="Permanent link">&para;</a></h1>
<p>This file provides <strong>strict and non-negotiable guidance</strong> when working with code in this repository. All instructions
and references herein are <strong>mandatory</strong> and supersede any internal assumptions.</p>
<h2 id="project-overview">Project Overview<a class="headerlink" href="#project-overview" title="Permanent link">&para;</a></h2>
<p>The Ultimate Electrical Designer is a comprehensive electrical design platform built with FastAPI (Python) backend and
Next.js (React) frontend. It follows a <strong>5-layer architecture pattern</strong> with <strong>engineering-grade quality standards</strong> for
professional electrical system design.</p>
<h2 id="project-guidelines-reference-authoritative-source">Project Guidelines Reference (AUTHORITATIVE SOURCE)<a class="headerlink" href="#project-guidelines-reference-authoritative-source" title="Permanent link">&para;</a></h2>
<p>This project follows comprehensive guidelines documented in the <code>docs/</code> folder. <strong>These documents are the single source
of truth for all project decisions, standards, and methodologies.</strong> Always reference these documents <strong>first and
thoroughly</strong> for any architectural decisions, coding standards, or project requirements.</p>
<ul>
<li><strong>docs/structure.md</strong>: Project structure and architectural patterns</li>
<li><strong>docs/tech.md</strong>: Technology stack specification and tool versions</li>
<li><strong>docs/rules.md</strong>: <strong>CRITICAL: Development standards and quality gates (Mandatory Adherence)</strong></li>
<li><strong>docs/TESTING.md</strong>: <strong>CRITICAL: Complete testing strategy and implementation guide (Mandatory Adherence)</strong></li>
<li><strong>docs/requirements.md</strong>: Functional and non-functional requirements</li>
<li><strong>docs/design.md</strong>: Technical architecture and design decisions</li>
<li><strong>docs/tasks.md</strong>: Implementation task breakdown</li>
</ul>
<p><strong>ABSOLUTELY CRITICAL</strong>:</p>
<ul>
<li><strong>NEVER DEVIATE</strong> from the standards, policies, and methodologies documented in <code>docs/rules.md</code>.</li>
<li><strong>ZERO EXCEPTIONS</strong> will be made for linting errors, type safety violations, test failures, or any form of technical
  debt.</li>
<li>All code must reflect <strong>immaculate attention to detail</strong> and adhere to professional electrical design standards.</li>
</ul>
<hr />
<h2 id="development-commands">Development Commands<a class="headerlink" href="#development-commands" title="Permanent link">&para;</a></h2>
<h3 id="backend-server">Backend (Server)<a class="headerlink" href="#backend-server" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Run uv commands from server/</span>
<span class="nb">cd</span><span class="w"> </span>/mnt/d/Projects/ued/server/
<span class="c1"># Start the backend server</span>
uv<span class="w"> </span>run<span class="w"> </span>uvicorn<span class="w"> </span>src.main:app<span class="w"> </span>--reload<span class="w"> </span>--host<span class="w"> </span><span class="m">0</span>.0.0.0<span class="w"> </span>--port<span class="w"> </span><span class="m">8000</span>
<span class="c1"># Code quality (MUST PASS WITHOUT ERRORS OR WARNINGS)</span>
uv<span class="w"> </span>run<span class="w"> </span>mypy<span class="w"> </span>src/<span class="w"> </span>--show-error-codes
uv<span class="w"> </span>run<span class="w"> </span>ruff<span class="w"> </span>format<span class="w"> </span>.
uv<span class="w"> </span>run<span class="w"> </span>ruff<span class="w"> </span>format<span class="w"> </span>.<span class="w"> </span>--check
uv<span class="w"> </span>run<span class="w"> </span>bandit<span class="w"> </span>-r<span class="w"> </span>src/<span class="w"> </span>-f<span class="w"> </span>json<span class="w"> </span>-o<span class="w"> </span>bandit-report.json
<span class="c1"># Testing (MUST PASS WITH REQUIRED COVERAGE)</span>
uv<span class="w"> </span>run<span class="w"> </span>pytest<span class="w"> </span>--html<span class="o">=</span>test-report-all.html<span class="w"> </span>--self-contained-html
uv<span class="w"> </span>run<span class="w"> </span>pytest<span class="w"> </span>-v<span class="w"> </span>-m<span class="w"> </span><span class="s2">&quot;not integration and not performance&quot;</span><span class="w"> </span>--html<span class="o">=</span>test-report-unit.html<span class="w"> </span>--self-contained-html
uv<span class="w"> </span>run<span class="w"> </span>pytest<span class="w"> </span>-v<span class="w"> </span>-m<span class="w"> </span>tests/integration<span class="w"> </span>--html<span class="o">=</span>test-report-integration.html<span class="w"> </span>--self-contained-html
uv<span class="w"> </span>run<span class="w"> </span>pytest<span class="w"> </span>-v<span class="w"> </span>-m<span class="w"> </span>tests/performance<span class="w"> </span>--html<span class="o">=</span>test-report-performance.html<span class="w"> </span>--self-contained-html
uv<span class="w"> </span>run<span class="w"> </span>pytest<span class="w"> </span>tests/<span class="w"> </span>--cov<span class="o">=</span>src<span class="w"> </span>--cov-report<span class="o">=</span>term-missing<span class="w"> </span>--cov-report<span class="o">=</span>xml<span class="w"> </span>--html<span class="o">=</span>test-report-cov.html<span class="w"> </span>--self-contained-html

<span class="c1"># Database</span>
<span class="c1"># Run alembic commands from server/src/</span>
<span class="nb">cd</span><span class="w"> </span>/mnt/d/Projects/ued/server/src/
uv<span class="w"> </span>run<span class="w"> </span>alembic<span class="w"> </span>check
uv<span class="w"> </span>run<span class="w"> </span>alembic<span class="w"> </span>version
uv<span class="w"> </span>run<span class="w"> </span>alembic<span class="w"> </span>current
uv<span class="w"> </span>run<span class="w"> </span>alembic<span class="w"> </span>upgrade<span class="w"> </span>head

<span class="c1"># Run uv commands from server/</span>
<span class="nb">cd</span><span class="w"> </span>/mnt/d/Projects/ued/server/
<span class="c1"># Wipe database</span>
uv<span class="w"> </span>run<span class="w"> </span>python<span class="w"> </span>main.py<span class="w"> </span>wipe-database<span class="w"> </span>--confirm<span class="w">       </span><span class="c1"># WARNING: This will DELETE your development database and re-migrate.</span>
<span class="c1"># Seed database</span>
uv<span class="w"> </span>run<span class="w"> </span>python<span class="w"> </span>main.py<span class="w"> </span>seed-general-data<span class="w">         </span><span class="c1"># Seed database with Phase 1 data (src/core/models/general/)</span>

<span class="c1"># Documentation</span>
uv<span class="w"> </span>run<span class="w"> </span>mkdocs<span class="w"> </span>build<span class="w"> </span>--clean<span class="w"> </span>--site-dir<span class="w"> </span>../docs/api/python
</code></pre></div>
<h3 id="frontend-client">Frontend (Client)<a class="headerlink" href="#frontend-client" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Run pnpm commands from client/</span>
<span class="nb">cd</span><span class="w"> </span>/mnt/d/Projects/ued/client/
<span class="c1"># Start the frontend server</span>
pnpm<span class="w"> </span>run<span class="w"> </span>dev

<span class="c1"># Code quality (MUST PASS WITHOUT ERRORS OR WARNINGS)</span>
pnpm<span class="w"> </span>tsc<span class="w"> </span>--noEmit
pnpm<span class="w"> </span>next<span class="w"> </span>lint<span class="w"> </span>--fix

<span class="c1"># Testing (MUST PASS WITH REQUIRED COVERAGE)</span>
pnpm<span class="w"> </span>vitest<span class="w"> </span><span class="o">[</span>source<span class="o">]</span><span class="w"> </span>--run
pnpm<span class="w"> </span>vitest<span class="w"> </span><span class="o">[</span>source<span class="o">]</span><span class="w"> </span>--coverage<span class="w"> </span>--run
pnpm<span class="w"> </span>playwright<span class="w"> </span><span class="nb">test</span><span class="w"> </span>tests/e2e/<span class="o">[</span>source<span class="o">]</span>
</code></pre></div>
<hr />
<h2 id="key-architectural-patterns-mandatory-adoption">Key Architectural Patterns (MANDATORY ADOPTION)<a class="headerlink" href="#key-architectural-patterns-mandatory-adoption" title="Permanent link">&para;</a></h2>
<h3 id="crud-endpoint-factory-pattern">CRUD Endpoint Factory Pattern<a class="headerlink" href="#crud-endpoint-factory-pattern" title="Permanent link">&para;</a></h3>
<p><strong>CRITICAL</strong>: Use the CRUD endpoint factory for all new entities to avoid boilerplate code. This is a unified pattern
and its usage is <strong>mandatory</strong> unless explicitly documented otherwise.</p>
<div class="highlight"><pre><span></span><code><span class="c1"># For new entities, use the factory pattern</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">src.core.utils.crud_endpoint_factory</span><span class="w"> </span><span class="kn">import</span> <span class="n">create_simple_crud_router</span>

<span class="c1"># Create CRUD router</span>
<span class="n">crud_router</span> <span class="o">=</span> <span class="n">create_simple_crud_router</span><span class="p">(</span>
    <span class="n">entity_name</span><span class="o">=</span><span class="s2">&quot;your_entity&quot;</span><span class="p">,</span>
    <span class="n">entity_name_plural</span><span class="o">=</span><span class="s2">&quot;your_entities&quot;</span><span class="p">,</span>
    <span class="n">create_schema</span><span class="o">=</span><span class="n">YourCreateSchema</span><span class="p">,</span>
    <span class="n">read_schema</span><span class="o">=</span><span class="n">YourReadSchema</span><span class="p">,</span>
    <span class="n">update_schema</span><span class="o">=</span><span class="n">YourUpdateSchema</span><span class="p">,</span>
    <span class="n">list_response_schema</span><span class="o">=</span><span class="n">YourListResponseSchema</span><span class="p">,</span>
    <span class="n">service_class</span><span class="o">=</span><span class="n">YourService</span><span class="p">,</span>
    <span class="n">service_dependency</span><span class="o">=</span><span class="n">get_your_service</span><span class="p">,</span>
    <span class="n">id_type</span><span class="o">=</span><span class="nb">int</span><span class="p">,</span>
    <span class="n">searchable_fields</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="s2">&quot;description&quot;</span><span class="p">],</span>
    <span class="n">sortable_fields</span><span class="o">=</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">,</span> <span class="s2">&quot;created_at&quot;</span><span class="p">,</span> <span class="s2">&quot;updated_at&quot;</span><span class="p">],</span>
<span class="p">)</span>
</code></pre></div>
<hr />
<h2 id="testing-strategy-mandatory-adherence">Testing Strategy (MANDATORY ADHERENCE)<a class="headerlink" href="#testing-strategy-mandatory-adherence" title="Permanent link">&para;</a></h2>
<p>All tests must be implemented according to the specified strategy and pass with <strong>100% pass rate</strong>. Test coverage must
meet documented targets (refer to <code>docs/rules.md</code>).</p>
<h3 id="backend-testing">Backend Testing<a class="headerlink" href="#backend-testing" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Unit Tests</strong>: Individual component testing with pytest.</li>
<li><strong>Integration Tests</strong>: Multi-component workflow testing.</li>
<li><strong>API Tests</strong>: Complete endpoint testing with TestClient.</li>
<li><strong>Performance Tests</strong>: Load testing with specific markers.</li>
<li><strong>Security Tests</strong>: Security validation testing.</li>
</ul>
<h3 id="frontend-testing">Frontend Testing<a class="headerlink" href="#frontend-testing" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Unit Tests</strong>: Component testing with Vitest + React Testing Library.</li>
<li><strong>Integration Tests</strong>: Feature workflow testing.</li>
<li><strong>E2E Tests</strong>: Full user workflow testing with Playwright.</li>
<li><strong>MSW Mocking</strong>: Mock Service Worker for API mocking (for robust frontend testing).</li>
</ul>
<p>To provide comprehensive context and facilitate understanding of our client-side testing strategy and its robust current
state, please refer to the <strong>Test Suite Resolution</strong> document. This detailed report outlines the systematic methodology,
key technical breakthroughs, and the patterns established during the recent effort to achieve a near-perfect pass rate
across our client-side test suite.</p>
<hr />
<h2 id="development-standards-strictly-enforced">Development Standards (STRICTLY ENFORCED)<a class="headerlink" href="#development-standards-strictly-enforced" title="Permanent link">&para;</a></h2>
<p><strong>Adherence to these standards is paramount and non-negotiable for every single line of code.</strong></p>
<ol>
<li><strong>Robust design principles:</strong> Apply <strong>SOLID</strong> principles for structural design, ensuring maintainability and
   flexibility through focused responsibilities, extensibility, and proper abstraction. Complement these with practices
   like <strong>DRY</strong>, <strong>KISS</strong>, and <strong>TDD</strong> to streamline implementation, reduce complexity, and enhance overall code
   quality. <strong>No exceptions.</strong></li>
<li><strong>5-Phase Methodology:</strong> Adopt a systematic 5-phase approach for each feature or task. This structured,
   quality-driven development process is <strong>mandatory</strong> for all work.</li>
<li><strong>Discovery &amp; Analysis:</strong> Understand the current state of the system, identify requirements, and define the scope
      of the main task.</li>
<li><strong>Task Planning:</strong> Break down tasks into smaller, manageable units (max 30-minute work batches) to ensure
      efficient progress.</li>
<li><strong>Implementation:</strong> Execute changes with <strong>engineering-grade quality</strong>, focusing on unified patterns and
      professional electrical design standards.</li>
<li><strong>Verification:</strong> Ensure all requirements are met through comprehensive testing and <strong>100% compliance
      verification</strong> against all documented quality gates.</li>
<li><strong>Documentation &amp; Handover:</strong> Prepare comprehensive documentation and create a handover package for future
      development and AI agent transfer.</li>
<li><strong>Unified Patterns:</strong> Apply consistent "unified patterns" for calculations, service layers, and repositories. Utilize
   decorators for error handling, performance monitoring, and memory optimization as specified in <code>docs/design.md</code>.
   <strong>Consistency is key.</strong></li>
<li><strong>Quality &amp; Standards Focus:</strong> Ensure <strong>immaculate attention to detail</strong>. Adhere to professional electrical design
   standards (IEC/EN). Maintain <strong>complete type safety</strong> with MyPy validation (100% compliance) and comprehensive
   testing (including real database connections where appropriate).</li>
<li><strong>Key Success Metrics:</strong> Define success through high unified patterns compliance (≥90%), extensive test coverage
   (≥85% overall, 100% for critical logic), 100% test pass rates, and zero remaining placeholder implementations.
   <strong>These are minimum success criteria.</strong></li>
</ol>
<hr />
<h2 id="module-relationships-understand-and-respect">Module Relationships (UNDERSTAND AND RESPECT)<a class="headerlink" href="#module-relationships-understand-and-respect" title="Permanent link">&para;</a></h2>
<h3 id="backend-layer-dependencies">Backend Layer Dependencies<a class="headerlink" href="#backend-layer-dependencies" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>API Layer</strong> → <strong>Services Layer</strong> → <strong>Repositories Layer</strong> → <strong>Models Layer</strong></li>
<li><strong>Unified Error Handling</strong> spans all layers.</li>
<li><strong>Security Validation</strong> integrated at API and Service layers.</li>
<li><strong>Performance Monitoring</strong> integrated at all layers.</li>
</ol>
<h3 id="frontend-module-dependencies">Frontend Module Dependencies<a class="headerlink" href="#frontend-module-dependencies" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>App Router</strong> → <strong>Modules</strong> → <strong>Components</strong> → <strong>UI Primitives</strong></li>
<li><strong>State Management</strong> (Zustand) for client state.</li>
<li><strong>React Query</strong> for server state management.</li>
<li><strong>API Client</strong> for backend communication.</li>
</ol>
<h3 id="cross-system-integration">Cross-System Integration<a class="headerlink" href="#cross-system-integration" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Authentication Flow</strong>: Frontend auth module ↔ Backend auth services</li>
<li><strong>Component Management</strong>: Frontend component module ↔ Backend component services</li>
<li><strong>Real-time Updates</strong>: WebSocket connections for live collaboration</li>
<li><strong>File Uploads</strong>: Direct integration for component specifications and drawings</li>
</ul>
<hr />
<h2 id="performance-optimization">Performance Optimization<a class="headerlink" href="#performance-optimization" title="Permanent link">&para;</a></h2>
<ul>
<li><strong>Backend</strong>: Use performance monitoring decorators on services. Implement efficient queries and caching strategies.</li>
<li><strong>Frontend</strong>: Implement React Query for server state caching, code splitting, and image optimization.</li>
<li><strong>Database</strong>: Leverage existing indexing and ensure query optimization.</li>
<li><strong>Caching</strong>: Utilize the built-in caching middleware.</li>
</ul>
<hr /></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = ".",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="js/base.js"></script>
        <script src="search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>

<!--
MkDocs version : 1.6.1
Build Date UTC : 2025-08-08 18:50:50.209799+00:00
-->
