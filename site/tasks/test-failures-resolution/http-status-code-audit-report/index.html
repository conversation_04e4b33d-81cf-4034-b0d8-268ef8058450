<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/tasks/test-failures-resolution/http-status-code-audit-report/">
        <link rel="shortcut icon" href="../../../img/favicon.ico">
        <title>HTTP Status Code Audit Report - Ultimate Electrical Designer Docs</title>
        <link href="../../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../../css/brands.min.css" rel="stylesheet">
        <link href="../../../css/solid.min.css" rel="stylesheet">
        <link href="../../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../../../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#http-status-code-audit-report" class="nav-link">HTTP Status Code Audit Report</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#ultimate-electrical-designer-task-221-completion-report" class="nav-link">Ultimate Electrical Designer - Task 2.2.1 Completion Report</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#executive-summary" class="nav-link">Executive Summary</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#key-findings" class="nav-link">Key Findings</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#status-code-contract-violations" class="nav-link">Status Code Contract Violations</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#unified-error-handler-effectiveness" class="nav-link">Unified Error Handler Effectiveness</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#recommended-fixes" class="nav-link">Recommended Fixes</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#implementation-plan" class="nav-link">Implementation Plan</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#success-criteria" class="nav-link">Success Criteria</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#conclusion" class="nav-link">Conclusion</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="http-status-code-audit-report">HTTP Status Code Audit Report<a class="headerlink" href="#http-status-code-audit-report" title="Permanent link">&para;</a></h1>
<h2 id="ultimate-electrical-designer-task-221-completion-report">Ultimate Electrical Designer - Task 2.2.1 Completion Report<a class="headerlink" href="#ultimate-electrical-designer-task-221-completion-report" title="Permanent link">&para;</a></h2>
<p><strong>Document Version:</strong> 1.0<br />
<strong>Date:</strong> August 7, 2025<br />
<strong>Task:</strong> 2.2.1 - Audit HTTP Status Code Mappings  </p>
<hr />
<h2 id="executive-summary">Executive Summary<a class="headerlink" href="#executive-summary" title="Permanent link">&para;</a></h2>
<p>Comprehensive audit of HTTP status code mappings reveals <strong>inconsistent error handling patterns</strong> where manual exception handlers in API routes are <strong>overriding the unified error handler</strong>, causing incorrect status codes. The unified error handler has proper mappings, but manual handlers are preventing them from being used.</p>
<h2 id="key-findings">Key Findings<a class="headerlink" href="#key-findings" title="Permanent link">&para;</a></h2>
<h3 id="unified-error-handler-correct-mappings">✅ <strong>Unified Error Handler - Correct Mappings</strong><a class="headerlink" href="#unified-error-handler-correct-mappings" title="Permanent link">&para;</a></h3>
<p>The unified error handler has <strong>proper status code mappings</strong>:</p>
<table>
<thead>
<tr>
<th><strong>Exception Type</strong></th>
<th><strong>Status Code</strong></th>
<th><strong>Category</strong></th>
<th><strong>Mapping Location</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><code>NotFoundError</code></td>
<td><strong>404</strong></td>
<td>ClientError</td>
<td><code>exceptions.py:56</code></td>
</tr>
<tr>
<td><code>DuplicateEntryError</code></td>
<td><strong>409</strong></td>
<td>ClientError</td>
<td><code>exceptions.py:186</code></td>
</tr>
<tr>
<td><code>DataValidationError</code></td>
<td><strong>422</strong></td>
<td>Validation</td>
<td><code>exceptions.py:128</code></td>
</tr>
<tr>
<td><code>InvalidInputError</code></td>
<td><strong>400</strong></td>
<td>Validation</td>
<td><code>exceptions.py:147</code></td>
</tr>
<tr>
<td><code>SecurityError</code></td>
<td><strong>403</strong></td>
<td>SecurityError</td>
<td><code>exceptions.py:396</code></td>
</tr>
<tr>
<td><code>DatabaseError</code></td>
<td><strong>500</strong></td>
<td>DatabaseError</td>
<td><code>exceptions.py:207</code></td>
</tr>
<tr>
<td><code>IntegrityError</code> → <code>DuplicateEntryError</code></td>
<td><strong>409</strong></td>
<td>Translation</td>
<td><code>unified_error_handler.py:244-248</code></td>
</tr>
</tbody>
</table>
<h3 id="manual-exception-handlers-incorrect-overrides">❌ <strong>Manual Exception Handlers - Incorrect Overrides</strong><a class="headerlink" href="#manual-exception-handlers-incorrect-overrides" title="Permanent link">&para;</a></h3>
<p><strong>Problem</strong>: Manual exception handlers in API routes are <strong>catching exceptions before</strong> the unified error handler can process them, resulting in incorrect status codes.</p>
<h4 id="issue-1-user-routes-500-instead-of-409"><strong>Issue 1: User Routes - 500 Instead of 409</strong><a class="headerlink" href="#issue-1-user-routes-500-instead-of-409" title="Permanent link">&para;</a></h4>
<p><strong>File</strong>: <code>server/src/api/v1/user_routes.py</code><br />
<strong>Function</strong>: <code>update_current_user_profile</code> (lines 230-235)</p>
<div class="highlight"><pre><span></span><code><span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
    <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Failed to update profile for user </span><span class="si">{</span><span class="n">user_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_500_INTERNAL_SERVER_ERROR</span><span class="p">,</span>  <span class="c1"># ❌ WRONG</span>
        <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;Failed to update user profile&quot;</span><span class="p">,</span>
    <span class="p">)</span>
</code></pre></div>
<p><strong>Root Cause</strong>: 
- <code>IntegrityError</code> (unique constraint violation) should be <strong>409 Conflict</strong>
- Manual handler catches it and converts to <strong>500 Internal Server Error</strong>
- Unified error handler never gets to process the <code>IntegrityError</code></p>
<p><strong>Test Evidence</strong>:
<div class="highlight"><pre><span></span><code>duplicate key value violates unique constraint &quot;uq_user_name&quot;
DETAIL: Key (name)=(Updated User Name) already exists.
→ Returns: 500 Internal Server Error
→ Should Return: 409 Conflict
</code></pre></div></p>
<h4 id="issue-2-inconsistent-duplicate-handling"><strong>Issue 2: Inconsistent Duplicate Handling</strong><a class="headerlink" href="#issue-2-inconsistent-duplicate-handling" title="Permanent link">&para;</a></h4>
<p><strong>File</strong>: <code>server/src/api/v1/auth_routes.py</code><br />
<strong>Function</strong>: <code>register</code> (lines 170-184)</p>
<div class="highlight"><pre><span></span><code><span class="c1"># Check if it&#39;s a duplicate user error</span>
<span class="k">if</span> <span class="s2">&quot;already exists&quot;</span> <span class="ow">in</span> <span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="o">.</span><span class="n">lower</span><span class="p">():</span>
    <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="n">status</span><span class="o">.</span><span class="n">HTTP_409_CONFLICT</span><span class="p">,</span>  <span class="c1"># ✅ CORRECT but inconsistent</span>
        <span class="n">detail</span><span class="o">=</span><span class="s2">&quot;A user with this email or username already exists&quot;</span><span class="p">,</span>
    <span class="p">)</span>
</code></pre></div>
<p><strong>Issue</strong>: 
- Auth routes have <strong>manual duplicate detection</strong> that returns 409
- User routes have <strong>manual catch-all</strong> that returns 500
- <strong>Inconsistent patterns</strong> across API endpoints</p>
<h3 id="database-error-translation-analysis"><strong>Database Error Translation Analysis</strong><a class="headerlink" href="#database-error-translation-analysis" title="Permanent link">&para;</a></h3>
<p>The unified error handler has <strong>correct translation logic</strong>:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># unified_error_handler.py:244-248</span>
<span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">exception</span><span class="p">,</span> <span class="n">IntegrityError</span><span class="p">):</span>
    <span class="n">app_exception</span> <span class="o">=</span> <span class="n">DuplicateEntryError</span><span class="p">(</span>
        <span class="n">message</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Duplicate entry detected: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">exception</span><span class="p">)[:</span><span class="mi">200</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
        <span class="n">original_exception</span><span class="o">=</span><span class="n">exception</span><span class="p">,</span>
    <span class="p">)</span>
</code></pre></div>
<p><strong>Translation Chain</strong>:
1. <code>IntegrityError</code> (SQLAlchemy) → <code>DuplicateEntryError</code> (App Exception)
2. <code>DuplicateEntryError</code> → <strong>409 Conflict</strong> (HTTP Status)</p>
<p><strong>Problem</strong>: Manual handlers <strong>intercept before translation</strong> occurs.</p>
<h2 id="status-code-contract-violations">Status Code Contract Violations<a class="headerlink" href="#status-code-contract-violations" title="Permanent link">&para;</a></h2>
<h3 id="current-issues"><strong>Current Issues</strong><a class="headerlink" href="#current-issues" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th><strong>Endpoint</strong></th>
<th><strong>Scenario</strong></th>
<th><strong>Current Status</strong></th>
<th><strong>Expected Status</strong></th>
<th><strong>Impact</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><code>PUT /api/v1/users/me</code></td>
<td>Duplicate name</td>
<td><strong>500</strong></td>
<td><strong>409</strong></td>
<td>❌ <strong>CRITICAL</strong></td>
</tr>
<tr>
<td><code>PUT /api/v1/users/me</code></td>
<td>User not found</td>
<td><strong>500</strong></td>
<td><strong>404</strong></td>
<td>❌ <strong>HIGH</strong></td>
</tr>
<tr>
<td><code>GET /api/v1/users/summary</code></td>
<td>Internal error</td>
<td><strong>500</strong></td>
<td><strong>500</strong></td>
<td>✅ <strong>CORRECT</strong></td>
</tr>
<tr>
<td><code>POST /api/v1/auth/register</code></td>
<td>Duplicate email</td>
<td><strong>409</strong></td>
<td><strong>409</strong></td>
<td>✅ <strong>CORRECT</strong></td>
</tr>
</tbody>
</table>
<h3 id="test-failures-analysis"><strong>Test Failures Analysis</strong><a class="headerlink" href="#test-failures-analysis" title="Permanent link">&para;</a></h3>
<p>From user routes test failures:</p>
<div class="highlight"><pre><span></span><code>FAILED test_update_current_user_profile - assert 500 == 200
FAILED test_update_current_user_profile_forbidden_fields - assert 500 == 200  
FAILED test_get_users_summary_admin - assert 500 == 200
FAILED test_get_users_summary_with_limit - assert 500 == 200
</code></pre></div>
<p><strong>Root Causes</strong>:
1. <strong>Duplicate constraint violations</strong> → Should be 409, getting 500
2. <strong>Manual exception handlers</strong> overriding unified error handler
3. <strong>Test data isolation issues</strong> causing duplicate entries</p>
<h2 id="unified-error-handler-effectiveness">Unified Error Handler Effectiveness<a class="headerlink" href="#unified-error-handler-effectiveness" title="Permanent link">&para;</a></h2>
<h3 id="strengths">✅ <strong>Strengths</strong><a class="headerlink" href="#strengths" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Comprehensive Exception Mapping</strong>: All major exception types have proper status codes</li>
<li><strong>Database Error Translation</strong>: <code>IntegrityError</code> → <code>DuplicateEntryError</code> → 409</li>
<li><strong>Consistent Error Response Format</strong>: Standardized <code>ErrorResponseSchema</code></li>
<li><strong>Proper Logging and Monitoring</strong>: Error tracking and performance metrics</li>
</ol>
<h3 id="weaknesses">❌ <strong>Weaknesses</strong><a class="headerlink" href="#weaknesses" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Manual Handler Interference</strong>: API routes bypass unified handler with manual <code>try/catch</code></li>
<li><strong>Inconsistent Usage</strong>: Some endpoints use unified handler, others use manual handling</li>
<li><strong>Error Handler Precedence</strong>: Manual handlers execute before unified handler decorators</li>
</ol>
<h2 id="recommended-fixes">Recommended Fixes<a class="headerlink" href="#recommended-fixes" title="Permanent link">&para;</a></h2>
<h3 id="priority-1-remove-manual-exception-handlers"><strong>Priority 1: Remove Manual Exception Handlers</strong><a class="headerlink" href="#priority-1-remove-manual-exception-handlers" title="Permanent link">&para;</a></h3>
<p><strong>Target Files</strong>:
- <code>server/src/api/v1/user_routes.py</code> - Remove manual handlers in update functions
- <code>server/src/api/v1/auth_routes.py</code> - Standardize duplicate handling</p>
<p><strong>Strategy</strong>: Let <code>@handle_api_errors</code> decorator handle all exceptions through unified error handler</p>
<h3 id="priority-2-fix-test-data-isolation"><strong>Priority 2: Fix Test Data Isolation</strong><a class="headerlink" href="#priority-2-fix-test-data-isolation" title="Permanent link">&para;</a></h3>
<p><strong>Issue</strong>: Tests creating duplicate data causing constraint violations
<strong>Solution</strong>: Improve test data cleanup and unique value generation</p>
<h3 id="priority-3-validate-error-handler-chain"><strong>Priority 3: Validate Error Handler Chain</strong><a class="headerlink" href="#priority-3-validate-error-handler-chain" title="Permanent link">&para;</a></h3>
<p><strong>Ensure</strong>: <code>@handle_api_errors</code> decorator is properly applied to all API endpoints</p>
<h2 id="implementation-plan">Implementation Plan<a class="headerlink" href="#implementation-plan" title="Permanent link">&para;</a></h2>
<h3 id="task-222-fix-user-not-found-status-code-issue"><strong>Task 2.2.2: Fix User Not Found Status Code Issue</strong><a class="headerlink" href="#task-222-fix-user-not-found-status-code-issue" title="Permanent link">&para;</a></h3>
<ul>
<li>Remove manual exception handlers from user routes</li>
<li>Let unified error handler process <code>NotFoundError</code> → 404</li>
</ul>
<h3 id="task-223-fix-database-error-status-codes"><strong>Task 2.2.3: Fix Database Error Status Codes</strong><a class="headerlink" href="#task-223-fix-database-error-status-codes" title="Permanent link">&para;</a></h3>
<ul>
<li>Remove manual exception handlers that convert database errors to 500</li>
<li>Let unified error handler translate <code>IntegrityError</code> → 409</li>
</ul>
<h3 id="task-224-create-status-code-validation-tests"><strong>Task 2.2.4: Create Status Code Validation Tests</strong><a class="headerlink" href="#task-224-create-status-code-validation-tests" title="Permanent link">&para;</a></h3>
<ul>
<li>Add comprehensive tests for all error scenarios</li>
<li>Validate proper status codes for each exception type</li>
</ul>
<h2 id="success-criteria">Success Criteria<a class="headerlink" href="#success-criteria" title="Permanent link">&para;</a></h2>
<h3 id="quality-gates"><strong>Quality Gates</strong><a class="headerlink" href="#quality-gates" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th><strong>Criteria</strong></th>
<th><strong>Target</strong></th>
<th><strong>Validation Method</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Duplicate Entry Errors</strong></td>
<td>409 Conflict</td>
<td>Integration tests with constraint violations</td>
</tr>
<tr>
<td><strong>Not Found Errors</strong></td>
<td>404 Not Found</td>
<td>Tests with non-existent resource IDs</td>
</tr>
<tr>
<td><strong>Validation Errors</strong></td>
<td>400/422</td>
<td>Tests with invalid input data</td>
</tr>
<tr>
<td><strong>Security Errors</strong></td>
<td>403 Forbidden</td>
<td>Tests with insufficient permissions</td>
</tr>
<tr>
<td><strong>Internal Errors</strong></td>
<td>500 Internal Server Error</td>
<td>Tests with unexpected exceptions</td>
</tr>
</tbody>
</table>
<h3 id="test-coverage"><strong>Test Coverage</strong><a class="headerlink" href="#test-coverage" title="Permanent link">&para;</a></h3>
<ul>
<li>✅ All API endpoints return correct status codes</li>
<li>✅ Error response format is consistent</li>
<li>✅ Manual exception handlers removed</li>
<li>✅ Unified error handler handles all exceptions</li>
</ul>
<hr />
<h2 id="conclusion">Conclusion<a class="headerlink" href="#conclusion" title="Permanent link">&para;</a></h2>
<p>The unified error handler has <strong>correct status code mappings</strong>, but <strong>manual exception handlers</strong> in API routes are preventing proper error processing. The solution is to <strong>remove manual handlers</strong> and let the unified error handler do its job.</p>
<p><strong>Next Steps</strong>: Implement fixes in Tasks 2.2.2, 2.2.3, and 2.2.4 to achieve consistent HTTP status code compliance.</p>
<p><strong>Quality Gate</strong>: ✅ <strong>PASSED</strong> - Complete understanding of status code issues documented</p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../../js/base.js"></script>
        <script src="../../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
