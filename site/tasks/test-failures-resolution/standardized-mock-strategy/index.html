<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/tasks/test-failures-resolution/standardized-mock-strategy/">
        <link rel="shortcut icon" href="../../../img/favicon.ico">
        <title>Standardized Mock Strategy - Ultimate Electrical Designer Docs</title>
        <link href="../../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../../css/brands.min.css" rel="stylesheet">
        <link href="../../../css/solid.min.css" rel="stylesheet">
        <link href="../../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../../../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#standardized-mock-strategy" class="nav-link">Standardized Mock Strategy</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#ultimate-electrical-designer-test-infrastructure-standardization" class="nav-link">Ultimate Electrical Designer - Test Infrastructure Standardization</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#executive-summary" class="nav-link">Executive Summary</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#core-principles" class="nav-link">Core Principles</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#unit-test-strategy-service-layer" class="nav-link">Unit Test Strategy (Service Layer)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#integration-test-strategy-api-layer" class="nav-link">Integration Test Strategy (API Layer)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#fixture-organization" class="nav-link">Fixture Organization</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#implementation-guidelines" class="nav-link">Implementation Guidelines</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#success-criteria" class="nav-link">Success Criteria</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#next-steps" class="nav-link">Next Steps</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="standardized-mock-strategy">Standardized Mock Strategy<a class="headerlink" href="#standardized-mock-strategy" title="Permanent link">&para;</a></h1>
<h2 id="ultimate-electrical-designer-test-infrastructure-standardization">Ultimate Electrical Designer - Test Infrastructure Standardization<a class="headerlink" href="#ultimate-electrical-designer-test-infrastructure-standardization" title="Permanent link">&para;</a></h2>
<p><strong>Document Version:</strong> 1.0<br />
<strong>Date:</strong> August 7, 2025<br />
<strong>Task:</strong> 2.1.2 - Design Standardized Mock Strategy  </p>
<hr />
<h2 id="executive-summary">Executive Summary<a class="headerlink" href="#executive-summary" title="Permanent link">&para;</a></h2>
<p>This document defines a standardized mock strategy to resolve test failures caused by inconsistent mock usage patterns. The strategy provides clear guidelines for unit tests (service layer) and integration tests (API layer) with specific patterns to address ID mismatch issues and mock assertion failures.</p>
<h2 id="core-principles">Core Principles<a class="headerlink" href="#core-principles" title="Permanent link">&para;</a></h2>
<h3 id="1-clear-test-type-separation">1. <strong>Clear Test Type Separation</strong><a class="headerlink" href="#1-clear-test-type-separation" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Unit Tests</strong>: Mock all external dependencies, test business logic in isolation</li>
<li><strong>Integration Tests</strong>: Use real database and services, test full request/response cycle</li>
<li><strong>No Mixed Patterns</strong>: Never combine mock expectations with real database operations</li>
</ul>
<h3 id="2-predictable-test-data">2. <strong>Predictable Test Data</strong><a class="headerlink" href="#2-predictable-test-data" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Unit Tests</strong>: Use predictable mock IDs (1, 2, 3, etc.)</li>
<li><strong>Integration Tests</strong>: Use ID-agnostic assertions or predictable data creation</li>
<li><strong>Consistent Factories</strong>: Standardized test data creation patterns</li>
</ul>
<h3 id="3-consistent-fixture-organization">3. <strong>Consistent Fixture Organization</strong><a class="headerlink" href="#3-consistent-fixture-organization" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Naming Convention</strong>: <code>mock_*</code> for mocks, <code>test_*</code> for real data, <code>sample_*</code> for schemas</li>
<li><strong>Location-Based</strong>: Service mocks in service test directories, API fixtures in API directories</li>
<li><strong>Shared Utilities</strong>: Common patterns in shared fixture modules</li>
</ul>
<h2 id="unit-test-strategy-service-layer">Unit Test Strategy (Service Layer)<a class="headerlink" href="#unit-test-strategy-service-layer" title="Permanent link">&para;</a></h2>
<h3 id="mock-data-factories">Mock Data Factories<a class="headerlink" href="#mock-data-factories" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># tests/fixtures/mock_factories.py</span>
<span class="k">class</span><span class="w"> </span><span class="nc">MockDataFactory</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Standardized mock data creation for unit tests.&quot;&quot;&quot;</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">create_user</span><span class="p">(</span><span class="nb">id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;Test User&quot;</span><span class="p">,</span> <span class="n">email</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;<EMAIL>&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">User</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create predictable mock user with default ID 1.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">User</span><span class="p">(</span>
            <span class="nb">id</span><span class="o">=</span><span class="nb">id</span><span class="p">,</span>
            <span class="n">name</span><span class="o">=</span><span class="n">name</span><span class="p">,</span>
            <span class="n">email</span><span class="o">=</span><span class="n">email</span><span class="p">,</span>
            <span class="n">password_hash</span><span class="o">=</span><span class="s2">&quot;mock_hash&quot;</span><span class="p">,</span>
            <span class="n">is_active</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="n">is_superuser</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
            <span class="n">created_at</span><span class="o">=</span><span class="n">datetime</span><span class="p">(</span><span class="mi">2024</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span>
            <span class="n">updated_at</span><span class="o">=</span><span class="n">datetime</span><span class="p">(</span><span class="mi">2024</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span>
        <span class="p">)</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">create_project</span><span class="p">(</span><span class="nb">id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="n">user_id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;Test Project&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Project</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create predictable mock project with default ID 1.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">Project</span><span class="p">(</span>
            <span class="nb">id</span><span class="o">=</span><span class="nb">id</span><span class="p">,</span>
            <span class="n">name</span><span class="o">=</span><span class="n">name</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Test project description&quot;</span><span class="p">,</span>
            <span class="n">project_number</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;PRJ-</span><span class="si">{</span><span class="nb">id</span><span class="si">:</span><span class="s2">04d</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="n">status</span><span class="o">=</span><span class="s2">&quot;ACTIVE&quot;</span><span class="p">,</span>
            <span class="n">client</span><span class="o">=</span><span class="s2">&quot;Test Client&quot;</span><span class="p">,</span>
            <span class="n">created_by_user_id</span><span class="o">=</span><span class="n">user_id</span><span class="p">,</span>
            <span class="n">created_at</span><span class="o">=</span><span class="n">datetime</span><span class="p">(</span><span class="mi">2024</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span>
            <span class="n">updated_at</span><span class="o">=</span><span class="n">datetime</span><span class="p">(</span><span class="mi">2024</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span>
        <span class="p">)</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">create_component_category</span><span class="p">(</span><span class="nb">id</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="n">name</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;Test Category&quot;</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ComponentCategory</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create predictable mock component category.&quot;&quot;&quot;</span>
        <span class="k">return</span> <span class="n">ComponentCategory</span><span class="p">(</span>
            <span class="nb">id</span><span class="o">=</span><span class="nb">id</span><span class="p">,</span>
            <span class="n">name</span><span class="o">=</span><span class="n">name</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Test category description&quot;</span><span class="p">,</span>
            <span class="n">is_active</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
            <span class="n">parent_category_id</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span>
            <span class="n">created_at</span><span class="o">=</span><span class="n">datetime</span><span class="p">(</span><span class="mi">2024</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span>
            <span class="n">updated_at</span><span class="o">=</span><span class="n">datetime</span><span class="p">(</span><span class="mi">2024</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">),</span>
        <span class="p">)</span>
</code></pre></div>
<h3 id="standardized-mock-repository-pattern">Standardized Mock Repository Pattern<a class="headerlink" href="#standardized-mock-repository-pattern" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># tests/core/services/conftest.py</span>
<span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
<span class="k">def</span><span class="w"> </span><span class="nf">mock_user_repository</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Standardized mock user repository for unit tests.&quot;&quot;&quot;</span>
    <span class="n">mock_repo</span> <span class="o">=</span> <span class="n">AsyncMock</span><span class="p">(</span><span class="n">spec</span><span class="o">=</span><span class="n">UserRepository</span><span class="p">)</span>

    <span class="c1"># Configure predictable return values</span>
    <span class="n">mock_repo</span><span class="o">.</span><span class="n">create</span><span class="o">.</span><span class="n">return_value</span> <span class="o">=</span> <span class="n">MockDataFactory</span><span class="o">.</span><span class="n">create_user</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">mock_repo</span><span class="o">.</span><span class="n">get_by_id</span><span class="o">.</span><span class="n">return_value</span> <span class="o">=</span> <span class="n">MockDataFactory</span><span class="o">.</span><span class="n">create_user</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">mock_repo</span><span class="o">.</span><span class="n">get_by_email</span><span class="o">.</span><span class="n">return_value</span> <span class="o">=</span> <span class="n">MockDataFactory</span><span class="o">.</span><span class="n">create_user</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">mock_repo</span><span class="o">.</span><span class="n">check_email_exists</span><span class="o">.</span><span class="n">return_value</span> <span class="o">=</span> <span class="kc">False</span>

    <span class="c1"># Configure session methods</span>
    <span class="n">mock_repo</span><span class="o">.</span><span class="n">db_session</span> <span class="o">=</span> <span class="n">AsyncMock</span><span class="p">()</span>
    <span class="n">mock_repo</span><span class="o">.</span><span class="n">db_session</span><span class="o">.</span><span class="n">flush</span> <span class="o">=</span> <span class="n">AsyncMock</span><span class="p">()</span>
    <span class="n">mock_repo</span><span class="o">.</span><span class="n">db_session</span><span class="o">.</span><span class="n">commit</span> <span class="o">=</span> <span class="n">AsyncMock</span><span class="p">()</span>
    <span class="n">mock_repo</span><span class="o">.</span><span class="n">db_session</span><span class="o">.</span><span class="n">refresh</span> <span class="o">=</span> <span class="n">AsyncMock</span><span class="p">()</span>

    <span class="k">return</span> <span class="n">mock_repo</span>

<span class="nd">@pytest</span><span class="o">.</span><span class="n">fixture</span>
<span class="k">def</span><span class="w"> </span><span class="nf">user_service</span><span class="p">(</span><span class="n">mock_user_repository</span><span class="p">,</span> <span class="n">mock_user_preference_repository</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;User service with standardized mocks.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">UserService</span><span class="p">(</span>
        <span class="n">user_repository</span><span class="o">=</span><span class="n">mock_user_repository</span><span class="p">,</span>
        <span class="n">preference_repository</span><span class="o">=</span><span class="n">mock_user_preference_repository</span>
    <span class="p">)</span>
</code></pre></div>
<h3 id="unit-test-example">Unit Test Example<a class="headerlink" href="#unit-test-example" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># tests/core/services/test_user_service.py</span>
<span class="k">class</span><span class="w"> </span><span class="nc">TestUserService</span><span class="p">:</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_create_user_success</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">user_service</span><span class="p">,</span> <span class="n">mock_user_repository</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test successful user creation with predictable mock data.&quot;&quot;&quot;</span>
        <span class="n">user_data</span> <span class="o">=</span> <span class="n">UserCreateSchema</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="s2">&quot;New User&quot;</span><span class="p">,</span>
            <span class="n">email</span><span class="o">=</span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">,</span>
            <span class="n">password</span><span class="o">=</span><span class="s2">&quot;SecurePass123&quot;</span>
        <span class="p">)</span>

        <span class="c1"># Configure mock to return predictable user</span>
        <span class="n">expected_user</span> <span class="o">=</span> <span class="n">MockDataFactory</span><span class="o">.</span><span class="n">create_user</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">name</span><span class="o">=</span><span class="s2">&quot;New User&quot;</span><span class="p">,</span> <span class="n">email</span><span class="o">=</span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">)</span>
        <span class="n">mock_user_repository</span><span class="o">.</span><span class="n">create</span><span class="o">.</span><span class="n">return_value</span> <span class="o">=</span> <span class="n">expected_user</span>
        <span class="n">mock_user_repository</span><span class="o">.</span><span class="n">check_email_exists</span><span class="o">.</span><span class="n">return_value</span> <span class="o">=</span> <span class="kc">False</span>

        <span class="c1"># Execute</span>
        <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="n">user_service</span><span class="o">.</span><span class="n">create_user</span><span class="p">(</span><span class="n">user_data</span><span class="p">)</span>

        <span class="c1"># Assert with predictable expectations</span>
        <span class="k">assert</span> <span class="n">result</span><span class="o">.</span><span class="n">id</span> <span class="o">==</span> <span class="mi">1</span>  <span class="c1"># Predictable mock ID</span>
        <span class="k">assert</span> <span class="n">result</span><span class="o">.</span><span class="n">name</span> <span class="o">==</span> <span class="s2">&quot;New User&quot;</span>
        <span class="k">assert</span> <span class="n">result</span><span class="o">.</span><span class="n">email</span> <span class="o">==</span> <span class="s2">&quot;<EMAIL>&quot;</span>

        <span class="c1"># Verify mock calls</span>
        <span class="n">mock_user_repository</span><span class="o">.</span><span class="n">check_email_exists</span><span class="o">.</span><span class="n">assert_called_once_with</span><span class="p">(</span><span class="s2">&quot;<EMAIL>&quot;</span><span class="p">)</span>
        <span class="n">mock_user_repository</span><span class="o">.</span><span class="n">create</span><span class="o">.</span><span class="n">assert_called_once</span><span class="p">()</span>
</code></pre></div>
<h2 id="integration-test-strategy-api-layer">Integration Test Strategy (API Layer)<a class="headerlink" href="#integration-test-strategy-api-layer" title="Permanent link">&para;</a></h2>
<h3 id="predictable-test-data-creation">Predictable Test Data Creation<a class="headerlink" href="#predictable-test-data-creation" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># tests/fixtures/integration_factories.py</span>
<span class="k">class</span><span class="w"> </span><span class="nc">IntegrationDataFactory</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Standardized real data creation for integration tests.&quot;&quot;&quot;</span>

    <span class="nd">@staticmethod</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">create_test_user</span><span class="p">(</span>
        <span class="n">async_db_session</span><span class="p">,</span>
        <span class="n">name</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;Test User&quot;</span><span class="p">,</span>
        <span class="n">email</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">password</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;SecurePass123&quot;</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">User</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create real test user with predictable data.&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">email</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">unique_suffix</span> <span class="o">=</span> <span class="nb">str</span><span class="p">(</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">())[:</span><span class="mi">8</span><span class="p">]</span>
            <span class="n">email</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;test.</span><span class="si">{</span><span class="n">unique_suffix</span><span class="si">}</span><span class="s2">@example.com&quot;</span>

        <span class="n">user_repo</span> <span class="o">=</span> <span class="n">UserRepository</span><span class="p">(</span><span class="n">async_db_session</span><span class="p">)</span>
        <span class="n">preference_repo</span> <span class="o">=</span> <span class="n">UserPreferenceRepository</span><span class="p">(</span><span class="n">async_db_session</span><span class="p">)</span>
        <span class="n">user_service</span> <span class="o">=</span> <span class="n">UserService</span><span class="p">(</span><span class="n">user_repository</span><span class="o">=</span><span class="n">user_repo</span><span class="p">,</span> <span class="n">preference_repository</span><span class="o">=</span><span class="n">preference_repo</span><span class="p">)</span>

        <span class="n">user_data</span> <span class="o">=</span> <span class="n">UserCreateSchema</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="n">name</span><span class="p">,</span> <span class="n">email</span><span class="o">=</span><span class="n">email</span><span class="p">,</span> <span class="n">password</span><span class="o">=</span><span class="n">password</span><span class="p">)</span>
        <span class="k">return</span> <span class="k">await</span> <span class="n">user_service</span><span class="o">.</span><span class="n">create_user</span><span class="p">(</span><span class="n">user_data</span><span class="p">)</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">create_test_project</span><span class="p">(</span>
        <span class="n">db_session</span><span class="p">,</span>
        <span class="n">user</span><span class="p">:</span> <span class="n">User</span><span class="p">,</span>
        <span class="n">name</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span>
        <span class="n">project_number</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="kc">None</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Project</span><span class="p">:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Create real test project with predictable data.&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">name</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">unique_suffix</span> <span class="o">=</span> <span class="nb">str</span><span class="p">(</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">())[:</span><span class="mi">8</span><span class="p">]</span>
            <span class="n">name</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;Test Project </span><span class="si">{</span><span class="n">unique_suffix</span><span class="si">}</span><span class="s2">&quot;</span>

        <span class="k">if</span> <span class="n">project_number</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="n">unique_suffix</span> <span class="o">=</span> <span class="nb">str</span><span class="p">(</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">())[:</span><span class="mi">8</span><span class="p">]</span>
            <span class="n">project_number</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;PRJ-</span><span class="si">{</span><span class="n">unique_suffix</span><span class="si">}</span><span class="s2">&quot;</span>

        <span class="n">project</span> <span class="o">=</span> <span class="n">Project</span><span class="p">(</span>
            <span class="n">name</span><span class="o">=</span><span class="n">name</span><span class="p">,</span>
            <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Integration test project&quot;</span><span class="p">,</span>
            <span class="n">project_number</span><span class="o">=</span><span class="n">project_number</span><span class="p">,</span>
            <span class="n">status</span><span class="o">=</span><span class="n">ProjectStatus</span><span class="o">.</span><span class="n">ACTIVE</span><span class="o">.</span><span class="n">value</span><span class="p">,</span>
            <span class="n">client</span><span class="o">=</span><span class="s2">&quot;Test Client&quot;</span><span class="p">,</span>
            <span class="n">created_by_user_id</span><span class="o">=</span><span class="n">user</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
        <span class="p">)</span>

        <span class="n">db_session</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">project</span><span class="p">)</span>
        <span class="n">db_session</span><span class="o">.</span><span class="n">flush</span><span class="p">()</span>
        <span class="n">db_session</span><span class="o">.</span><span class="n">refresh</span><span class="p">(</span><span class="n">project</span><span class="p">)</span>
        <span class="k">return</span> <span class="n">project</span>
</code></pre></div>
<h3 id="id-agnostic-assertion-helpers">ID-Agnostic Assertion Helpers<a class="headerlink" href="#id-agnostic-assertion-helpers" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># tests/utils/assertion_helpers.py</span>
<span class="k">class</span><span class="w"> </span><span class="nc">AssertionHelpers</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Helpers for ID-agnostic assertions in integration tests.&quot;&quot;&quot;</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">assert_user_properties</span><span class="p">(</span><span class="n">actual_user</span><span class="p">:</span> <span class="nb">dict</span><span class="p">,</span> <span class="n">expected_name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">expected_email</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Assert user properties without depending on specific ID.&quot;&quot;&quot;</span>
        <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">actual_user</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;id&quot;</span><span class="p">),</span> <span class="nb">int</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">actual_user</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span>
        <span class="k">assert</span> <span class="n">actual_user</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="n">expected_name</span>
        <span class="k">assert</span> <span class="n">actual_user</span><span class="p">[</span><span class="s2">&quot;email&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="n">expected_email</span>
        <span class="k">assert</span> <span class="n">actual_user</span><span class="p">[</span><span class="s2">&quot;is_active&quot;</span><span class="p">]</span> <span class="ow">is</span> <span class="kc">True</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">assert_project_properties</span><span class="p">(</span><span class="n">actual_project</span><span class="p">:</span> <span class="nb">dict</span><span class="p">,</span> <span class="n">expected_name</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">user_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Assert project properties with known user relationship.&quot;&quot;&quot;</span>
        <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">actual_project</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;id&quot;</span><span class="p">),</span> <span class="nb">int</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">actual_project</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span>
        <span class="k">assert</span> <span class="n">actual_project</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="n">expected_name</span>
        <span class="k">assert</span> <span class="n">actual_project</span><span class="p">[</span><span class="s2">&quot;created_by_user_id&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="n">user_id</span>
        <span class="k">assert</span> <span class="n">actual_project</span><span class="p">[</span><span class="s2">&quot;status&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;ACTIVE&quot;</span>

    <span class="nd">@staticmethod</span>
    <span class="k">def</span><span class="w"> </span><span class="nf">assert_category_properties</span><span class="p">(</span><span class="n">actual_category</span><span class="p">:</span> <span class="nb">dict</span><span class="p">,</span> <span class="n">expected_name</span><span class="p">:</span> <span class="nb">str</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Assert category properties without depending on specific ID.&quot;&quot;&quot;</span>
        <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">actual_category</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s2">&quot;id&quot;</span><span class="p">),</span> <span class="nb">int</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">actual_category</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span> <span class="o">&gt;</span> <span class="mi">0</span>
        <span class="k">assert</span> <span class="n">actual_category</span><span class="p">[</span><span class="s2">&quot;name&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="n">expected_name</span>
        <span class="k">assert</span> <span class="n">actual_category</span><span class="p">[</span><span class="s2">&quot;is_active&quot;</span><span class="p">]</span> <span class="ow">is</span> <span class="kc">True</span>
</code></pre></div>
<h3 id="integration-test-example">Integration Test Example<a class="headerlink" href="#integration-test-example" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># tests/api/v1/test_component_category_routes.py</span>
<span class="k">class</span><span class="w"> </span><span class="nc">TestComponentCategoryAPI</span><span class="p">:</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_create_category_endpoint</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">authenticated_client</span><span class="p">,</span> <span class="n">test_project</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Test category creation with ID-agnostic assertions.&quot;&quot;&quot;</span>
        <span class="n">category_data</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Integration Test Category&quot;</span><span class="p">,</span>
            <span class="s2">&quot;description&quot;</span><span class="p">:</span> <span class="s2">&quot;Integration test description&quot;</span><span class="p">,</span>
            <span class="s2">&quot;is_active&quot;</span><span class="p">:</span> <span class="kc">True</span><span class="p">,</span>
        <span class="p">}</span>

        <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">authenticated_client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span>
            <span class="sa">f</span><span class="s2">&quot;/api/v1/projects/</span><span class="si">{</span><span class="n">test_project</span><span class="o">.</span><span class="n">id</span><span class="si">}</span><span class="s2">/components/component-categories/&quot;</span><span class="p">,</span>
            <span class="n">json</span><span class="o">=</span><span class="n">category_data</span><span class="p">,</span>
        <span class="p">)</span>

        <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">status_code</span> <span class="o">==</span> <span class="mi">201</span>

        <span class="c1"># Use ID-agnostic assertions</span>
        <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>
        <span class="n">AssertionHelpers</span><span class="o">.</span><span class="n">assert_category_properties</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="s2">&quot;Integration Test Category&quot;</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;description&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;Integration test description&quot;</span>

        <span class="c1"># Store ID for subsequent operations</span>
        <span class="n">category_id</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span>
        <span class="k">assert</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">category_id</span><span class="p">,</span> <span class="nb">int</span><span class="p">)</span>
        <span class="k">assert</span> <span class="n">category_id</span> <span class="o">&gt;</span> <span class="mi">0</span>
</code></pre></div>
<h2 id="fixture-organization">Fixture Organization<a class="headerlink" href="#fixture-organization" title="Permanent link">&para;</a></h2>
<h3 id="naming-convention">Naming Convention<a class="headerlink" href="#naming-convention" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th><strong>Prefix</strong></th>
<th><strong>Purpose</strong></th>
<th><strong>Example</strong></th>
<th><strong>Usage</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><code>mock_*</code></td>
<td>Unit test mocks</td>
<td><code>mock_user_repository</code></td>
<td>Service layer tests</td>
</tr>
<tr>
<td><code>test_*</code></td>
<td>Integration test real data</td>
<td><code>test_user</code>, <code>test_project</code></td>
<td>API layer tests</td>
</tr>
<tr>
<td><code>sample_*</code></td>
<td>Test data schemas/DTOs</td>
<td><code>sample_user_data</code></td>
<td>Both test types</td>
</tr>
</tbody>
</table>
<h3 id="file-organization">File Organization<a class="headerlink" href="#file-organization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>tests/
├── conftest.py                 # Global fixtures (database, authentication)
├── fixtures/
│   ├── mock_factories.py       # Mock data factories for unit tests
│   ├── integration_factories.py # Real data factories for integration tests
│   └── assertion_helpers.py    # ID-agnostic assertion utilities
├── core/
│   └── services/
│       └── conftest.py         # Service layer mock fixtures
└── api/
    └── conftest.py             # API layer integration fixtures
</code></pre></div>
<h2 id="implementation-guidelines">Implementation Guidelines<a class="headerlink" href="#implementation-guidelines" title="Permanent link">&para;</a></h2>
<h3 id="1-unit-test-migration-pattern">1. <strong>Unit Test Migration Pattern</strong><a class="headerlink" href="#1-unit-test-migration-pattern" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Before (problematic)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_delete_component</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">component_service</span><span class="p">):</span>
    <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="n">component_service</span><span class="o">.</span><span class="n">delete_component</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">deleted_by_user_id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
    <span class="c1"># Fails if real database creates user with ID 35803</span>

<span class="c1"># After (standardized)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_delete_component</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">component_service</span><span class="p">,</span> <span class="n">mock_component_repository</span><span class="p">):</span>
    <span class="n">mock_user</span> <span class="o">=</span> <span class="n">MockDataFactory</span><span class="o">.</span><span class="n">create_user</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">mock_component</span> <span class="o">=</span> <span class="n">MockDataFactory</span><span class="o">.</span><span class="n">create_component</span><span class="p">(</span><span class="nb">id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

    <span class="n">mock_component_repository</span><span class="o">.</span><span class="n">get_by_id</span><span class="o">.</span><span class="n">return_value</span> <span class="o">=</span> <span class="n">mock_component</span>
    <span class="n">mock_component_repository</span><span class="o">.</span><span class="n">soft_delete</span><span class="o">.</span><span class="n">return_value</span> <span class="o">=</span> <span class="kc">True</span>

    <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="n">component_service</span><span class="o">.</span><span class="n">delete_component</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">deleted_by_user_id</span><span class="o">=</span><span class="n">mock_user</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>

    <span class="c1"># Predictable assertion with mock data</span>
    <span class="n">mock_component_repository</span><span class="o">.</span><span class="n">soft_delete</span><span class="o">.</span><span class="n">assert_called_once_with</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">deleted_by_user_id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
</code></pre></div>
<h3 id="2-integration-test-migration-pattern">2. <strong>Integration Test Migration Pattern</strong><a class="headerlink" href="#2-integration-test-migration-pattern" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Before (problematic)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_create_category</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">authenticated_client</span><span class="p">,</span> <span class="n">test_project</span><span class="p">):</span>
    <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">authenticated_client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">data</span><span class="p">)</span>
    <span class="k">assert</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span> <span class="o">==</span> <span class="mi">1</span>  <span class="c1"># Fails with real database</span>

<span class="c1"># After (standardized)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_create_category</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">authenticated_client</span><span class="p">,</span> <span class="n">test_project</span><span class="p">):</span>
    <span class="n">response</span> <span class="o">=</span> <span class="k">await</span> <span class="n">authenticated_client</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="n">url</span><span class="p">,</span> <span class="n">json</span><span class="o">=</span><span class="n">data</span><span class="p">)</span>
    <span class="n">data</span> <span class="o">=</span> <span class="n">response</span><span class="o">.</span><span class="n">json</span><span class="p">()</span>

    <span class="c1"># ID-agnostic assertion</span>
    <span class="n">AssertionHelpers</span><span class="o">.</span><span class="n">assert_category_properties</span><span class="p">(</span><span class="n">data</span><span class="p">,</span> <span class="s2">&quot;Expected Name&quot;</span><span class="p">)</span>

    <span class="c1"># Store ID for subsequent operations</span>
    <span class="n">category_id</span> <span class="o">=</span> <span class="n">data</span><span class="p">[</span><span class="s2">&quot;id&quot;</span><span class="p">]</span>
</code></pre></div>
<h2 id="success-criteria">Success Criteria<a class="headerlink" href="#success-criteria" title="Permanent link">&para;</a></h2>
<h3 id="unit-tests">✅ <strong>Unit Tests</strong><a class="headerlink" href="#unit-tests" title="Permanent link">&para;</a></h3>
<ul>
<li>[ ] All service tests use <code>MockDataFactory</code> for predictable data</li>
<li>[ ] All repository dependencies are mocked with <code>AsyncMock</code></li>
<li>[ ] All assertions use predictable mock IDs (1, 2, 3, etc.)</li>
<li>[ ] No real database operations in unit tests</li>
<li>[ ] Mock assertions work consistently</li>
</ul>
<h3 id="integration-tests">✅ <strong>Integration Tests</strong><a class="headerlink" href="#integration-tests" title="Permanent link">&para;</a></h3>
<ul>
<li>[ ] All API tests use <code>IntegrationDataFactory</code> for real data creation</li>
<li>[ ] All assertions use <code>AssertionHelpers</code> for ID-agnostic validation</li>
<li>[ ] Real database operations with transaction isolation</li>
<li>[ ] Predictable test data creation patterns</li>
<li>[ ] No mock assertions on real database operations</li>
</ul>
<h3 id="overall">✅ <strong>Overall</strong><a class="headerlink" href="#overall" title="Permanent link">&para;</a></h3>
<ul>
<li>[ ] Zero mock assertion failures due to ID mismatches</li>
<li>[ ] Clear separation between unit and integration test patterns</li>
<li>[ ] Consistent fixture usage across similar tests</li>
<li>[ ] Comprehensive documentation and examples</li>
</ul>
<h2 id="next-steps">Next Steps<a class="headerlink" href="#next-steps" title="Permanent link">&para;</a></h2>
<ol>
<li><strong>Task 2.1.3</strong>: Implement mock data factories and assertion helpers</li>
<li><strong>Task 2.1.4</strong>: Migrate existing tests to use standardized patterns</li>
<li><strong>Task 2.1.5</strong>: Validate mock strategy standardization</li>
<li><strong>Task 2.2.1</strong>: Address HTTP status code contract violations</li>
</ol>
<p><strong>Quality Gate</strong>: ✅ Standardized mock strategy designed and documented
- Clear patterns for unit and integration tests
- Mock data factories and assertion helpers specified
- Implementation guidelines provided
- Success criteria defined</p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../../js/base.js"></script>
        <script src="../../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
