<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/tasks/test-failures-resolution/code-quality-verification-report/">
        <link rel="shortcut icon" href="../../../img/favicon.ico">
        <title>FINAL CODE QUALITY VERIFICATION REPORT - Ultimate Electrical Designer Docs</title>
        <link href="../../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../../css/brands.min.css" rel="stylesheet">
        <link href="../../../css/solid.min.css" rel="stylesheet">
        <link href="../../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../../../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#final-code-quality-verification-report" class="nav-link">FINAL CODE QUALITY VERIFICATION REPORT</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#implementation-phase-completion-assessment" class="nav-link">Implementation Phase Completion Assessment</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#executive-summary" class="nav-link">EXECUTIVE SUMMARY</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#critical-successes-confirmed" class="nav-link">CRITICAL SUCCESSES CONFIRMED</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#remaining-critical-issues" class="nav-link">REMAINING CRITICAL ISSUES</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#compliance-assessment-final-status" class="nav-link">COMPLIANCE ASSESSMENT - FINAL STATUS</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#final-assessment" class="nav-link">FINAL ASSESSMENT</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#final-recommendation" class="nav-link">FINAL RECOMMENDATION</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#conclusion" class="nav-link">CONCLUSION</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="final-code-quality-verification-report"><strong>FINAL CODE QUALITY VERIFICATION REPORT</strong><a class="headerlink" href="#final-code-quality-verification-report" title="Permanent link">&para;</a></h1>
<h2 id="implementation-phase-completion-assessment"><strong>Implementation Phase Completion Assessment</strong><a class="headerlink" href="#implementation-phase-completion-assessment" title="Permanent link">&para;</a></h2>
<p><strong>Date:</strong> August 7, 2025<br />
<strong>Agent:</strong> Code Quality Agent<br />
<strong>Status:</strong> <strong>PARTIAL COMPLIANCE - CRITICAL VIOLATIONS RESOLVED BUT SIGNIFICANT ISSUES REMAIN</strong></p>
<hr />
<h2 id="executive-summary"><strong>EXECUTIVE SUMMARY</strong><a class="headerlink" href="#executive-summary" title="Permanent link">&para;</a></h2>
<p>The final verification reveals <strong>mixed results</strong> with significant progress on critical issues but <strong>failure to achieve
100% compliance</strong> required for Implementation Phase completion. While the most critical Zero Tolerance Policy violations
have been successfully resolved, substantial issues remain that prevent the project from proceeding to the Documentation
phase.</p>
<p><strong>Current Test Results:</strong></p>
<ul>
<li><strong>Test Pass Rate:</strong> 84.7% (996 passed, 156 failed, 22 errors, 2 skipped out of 1176 tests)</li>
<li><strong>Previous Rate:</strong> 83.4% (981 passed, 162 failed, 31 errors)</li>
<li><strong>Net Improvement:</strong> +1.3% pass rate, -6 failed tests, -9 errors</li>
</ul>
<hr />
<h2 id="critical-successes-confirmed"><strong>CRITICAL SUCCESSES CONFIRMED</strong><a class="headerlink" href="#critical-successes-confirmed" title="Permanent link">&para;</a></h2>
<h3 id="1-exceptiongroup-errors-eliminated"><strong>✅ 1. ExceptionGroup Errors ELIMINATED</strong><a class="headerlink" href="#1-exceptiongroup-errors-eliminated" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ✅ <strong>FULLY RESOLVED</strong><br />
<strong>Evidence:</strong> The previously critical test <code>test_move_category_endpoint</code> now <strong>PASSES</strong> consistently without any
ExceptionGroup errors.<br />
<strong>Impact:</strong> Complete resolution of the highest priority Zero Tolerance Policy violation.</p>
<h3 id="2-task-route-foreign-key-violations-resolved"><strong>✅ 2. Task Route Foreign Key Violations RESOLVED</strong><a class="headerlink" href="#2-task-route-foreign-key-violations-resolved" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ✅ <strong>FULLY RESOLVED</strong><br />
<strong>Evidence:</strong> No task route tests are failing with foreign key constraint violations. The "tasks_project_id_fkey" errors
have been eliminated.<br />
<strong>Impact:</strong> Major improvement in data integrity compliance.</p>
<h3 id="3-database-constraint-violations-significantly-reduced"><strong>✅ 3. Database Constraint Violations SIGNIFICANTLY REDUCED</strong><a class="headerlink" href="#3-database-constraint-violations-significantly-reduced" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ✅ <strong>MAJOR IMPROVEMENT</strong><br />
<strong>Evidence:</strong> The systematic "duplicate key value violates unique constraint 'uq_user_name'" errors for "Benchmark User
000000" are no longer present.<br />
<strong>Impact:</strong> Test data isolation has been substantially improved.</p>
<hr />
<h2 id="remaining-critical-issues"><strong>REMAINING CRITICAL ISSUES</strong><a class="headerlink" href="#remaining-critical-issues" title="Permanent link">&para;</a></h2>
<h3 id="1-persistent-asyncawait-pattern-violations-high-severity"><strong>❌ 1. Persistent Async/Await Pattern Violations (HIGH SEVERITY)</strong><a class="headerlink" href="#1-persistent-asyncawait-pattern-violations-high-severity" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ❌ <strong>UNRESOLVED</strong> - Multiple coroutine object errors remain<br />
<strong>Count:</strong> 20+ tests failing with "AttributeError: 'coroutine' object has no attribute 'X'"</p>
<p><strong>Affected Areas:</strong></p>
<ul>
<li><strong>Performance Tests:</strong> Component performance, email lookup benchmarks, database performance</li>
<li><strong>Integration Tests:</strong> Validation integration tests</li>
<li><strong>Pattern:</strong> Repository methods returning coroutines instead of actual values</li>
</ul>
<p><strong>Examples:</strong></p>
<div class="highlight"><pre><span></span><code><span class="c1"># FAILING PATTERN (current)</span>
<span class="n">found_user</span> <span class="o">=</span> <span class="n">user_repository</span><span class="o">.</span><span class="n">get_by_email</span><span class="p">(</span><span class="n">email</span><span class="p">)</span>  <span class="c1"># Returns coroutine</span>
<span class="k">assert</span> <span class="n">found_user</span><span class="o">.</span><span class="n">email</span> <span class="o">==</span> <span class="n">expected_email</span>  <span class="c1"># AttributeError</span>

<span class="c1"># REQUIRED PATTERN</span>
<span class="n">found_user</span> <span class="o">=</span> <span class="k">await</span> <span class="n">user_repository</span><span class="o">.</span><span class="n">get_by_email</span><span class="p">(</span><span class="n">email</span><span class="p">)</span>  <span class="c1"># Properly awaited</span>
<span class="k">assert</span> <span class="n">found_user</span><span class="o">.</span><span class="n">email</span> <span class="o">==</span> <span class="n">expected_email</span>  <span class="c1"># Works correctly</span>
</code></pre></div>
<h3 id="2-database-schema-issues-high-severity"><strong>❌ 2. Database Schema Issues (HIGH SEVERITY)</strong><a class="headerlink" href="#2-database-schema-issues-high-severity" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ❌ <strong>NEW CRITICAL ISSUE</strong> - SQL schema inconsistencies<br />
<strong>Count:</strong> 6+ tests failing with "column does not exist" errors</p>
<p><strong>Examples:</strong></p>
<ul>
<li><code>column "email" does not exist</code> in user table queries</li>
<li><code>column "id" does not exist</code> in user table queries</li>
<li>Raw SQL queries using incorrect table/column names</li>
</ul>
<p><strong>Root Cause:</strong> Mismatch between test SQL queries and actual database schema.</p>
<h3 id="3-performance-test-data-isolation-issues-medium-severity"><strong>❌ 3. Performance Test Data Isolation Issues (MEDIUM SEVERITY)</strong><a class="headerlink" href="#3-performance-test-data-isolation-issues-medium-severity" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ❌ <strong>PARTIALLY RESOLVED</strong> - Some unique constraint violations remain<br />
<strong>Pattern:</strong> Component performance tests still failing with duplicate manufacturer/model combinations</p>
<p><strong>Examples:</strong></p>
<ul>
<li><code>duplicate key value violates unique constraint "uq_component_manufacturer_model"</code></li>
<li>Test data factories not generating sufficiently unique identifiers</li>
</ul>
<h3 id="4-transaction-state-management-issues-medium-severity"><strong>❌ 4. Transaction State Management Issues (MEDIUM SEVERITY)</strong><a class="headerlink" href="#4-transaction-state-management-issues-medium-severity" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ❌ <strong>UNRESOLVED</strong> - Same as previous reports<br />
<strong>Pattern:</strong> "current transaction is aborted, commands ignored until end of transaction block"<br />
<strong>Affected:</strong> Cleanup utilities tests</p>
<h3 id="5-project-member-test-infrastructure-issues-medium-severity"><strong>❌ 5. Project Member Test Infrastructure Issues (MEDIUM SEVERITY)</strong><a class="headerlink" href="#5-project-member-test-infrastructure-issues-medium-severity" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ❌ <strong>UNRESOLVED</strong> - Project setup failures<br />
<strong>Pattern:</strong> <code>Project with ID 'XXX' not found</code> errors<br />
<strong>Count:</strong> 8 ERROR status tests</p>
<h3 id="6-validation-logic-mismatches-low-severity"><strong>❌ 6. Validation Logic Mismatches (LOW SEVERITY)</strong><a class="headerlink" href="#6-validation-logic-mismatches-low-severity" title="Permanent link">&para;</a></h3>
<p><strong>Status:</strong> ❌ <strong>UNRESOLVED</strong> - Business logic inconsistencies<br />
<strong>Examples:</strong></p>
<ul>
<li>Compatibility matrix scoring discrepancies</li>
<li>Schema validation expectation mismatches</li>
<li>Legacy migration validation failures</li>
</ul>
<hr />
<h2 id="compliance-assessment-final-status"><strong>COMPLIANCE ASSESSMENT - FINAL STATUS</strong><a class="headerlink" href="#compliance-assessment-final-status" title="Permanent link">&para;</a></h2>
<h3 id="zero-tolerance-policies-status"><strong>Zero Tolerance Policies Status</strong><a class="headerlink" href="#zero-tolerance-policies-status" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Policy</th>
<th>Status</th>
<th>Details</th>
<th>Change from Previous</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>100% Test Pass Rate</strong></td>
<td>❌ <strong>FAILED</strong></td>
<td>84.7% (15.3% below requirement)</td>
<td>⬆️ +1.3% improvement</td>
</tr>
<tr>
<td><strong>Zero ExceptionGroup Errors</strong></td>
<td>✅ <strong>RESOLVED</strong></td>
<td>Zero ExceptionGroup errors</td>
<td>✅ <strong>FIXED</strong></td>
</tr>
<tr>
<td><strong>Zero Database Constraint Violations</strong></td>
<td>⚠️ <strong>PARTIALLY RESOLVED</strong></td>
<td>Some unique constraints remain</td>
<td>🔄 <strong>MAJOR PROGRESS</strong></td>
</tr>
<tr>
<td><strong>Complete Type Safety</strong></td>
<td>❌ <strong>FAILED</strong></td>
<td>20+ coroutine object errors</td>
<td>⚪ <strong>NO CHANGE</strong></td>
</tr>
</tbody>
</table>
<h3 id="quality-standards-status"><strong>Quality Standards Status</strong><a class="headerlink" href="#quality-standards-status" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th>Standard</th>
<th>Status</th>
<th>Details</th>
<th>Change from Previous</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Real Database Testing</strong></td>
<td>❌ <strong>FAILED</strong></td>
<td>Schema inconsistencies</td>
<td>⬇️ <strong>REGRESSION</strong></td>
</tr>
<tr>
<td><strong>Test Data Isolation</strong></td>
<td>⚠️ <strong>PARTIALLY IMPROVED</strong></td>
<td>Most issues resolved</td>
<td>✅ <strong>MAJOR IMPROVEMENT</strong></td>
</tr>
<tr>
<td><strong>Async Testing Patterns</strong></td>
<td>❌ <strong>FAILED</strong></td>
<td>20+ missing await statements</td>
<td>⚪ <strong>NO CHANGE</strong></td>
</tr>
<tr>
<td><strong>Foreign Key Integrity</strong></td>
<td>✅ <strong>IMPROVED</strong></td>
<td>Task FK violations resolved</td>
<td>✅ <strong>MAJOR IMPROVEMENT</strong></td>
</tr>
</tbody>
</table>
<hr />
<h2 id="final-assessment"><strong>FINAL ASSESSMENT</strong><a class="headerlink" href="#final-assessment" title="Permanent link">&para;</a></h2>
<h3 id="achievements"><strong>✅ ACHIEVEMENTS</strong><a class="headerlink" href="#achievements" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>ExceptionGroup errors completely eliminated</strong> - Critical success</li>
<li><strong>Task route foreign key violations resolved</strong> - Major infrastructure improvement</li>
<li><strong>Test data isolation substantially improved</strong> - Quality enhancement</li>
<li><strong>Overall test pass rate improved</strong> - Positive trend</li>
</ol>
<h3 id="blocking-issues-for-100-compliance"><strong>❌ BLOCKING ISSUES FOR 100% COMPLIANCE</strong><a class="headerlink" href="#blocking-issues-for-100-compliance" title="Permanent link">&para;</a></h3>
<h4 id="priority-1-critical-asyncawait-violations-blocking"><strong>Priority 1: Critical Async/Await Violations (BLOCKING)</strong><a class="headerlink" href="#priority-1-critical-asyncawait-violations-blocking" title="Permanent link">&para;</a></h4>
<p><strong>Impact:</strong> 20+ test failures due to missing await statements<br />
<strong>Requirement:</strong> All async repository methods must be properly awaited<br />
<strong>Status:</strong> <strong>ZERO TOLERANCE POLICY VIOLATION</strong></p>
<h4 id="priority-2-database-schema-inconsistencies-blocking"><strong>Priority 2: Database Schema Inconsistencies (BLOCKING)</strong><a class="headerlink" href="#priority-2-database-schema-inconsistencies-blocking" title="Permanent link">&para;</a></h4>
<p><strong>Impact:</strong> 6+ test failures due to SQL schema mismatches<br />
<strong>Requirement:</strong> All database queries must use correct schema<br />
<strong>Status:</strong> <strong>INFRASTRUCTURE FAILURE</strong></p>
<h4 id="priority-3-performance-test-data-isolation-high"><strong>Priority 3: Performance Test Data Isolation (HIGH)</strong><a class="headerlink" href="#priority-3-performance-test-data-isolation-high" title="Permanent link">&para;</a></h4>
<p><strong>Impact:</strong> Unique constraint violations in performance tests<br />
<strong>Requirement:</strong> Complete test data isolation<br />
<strong>Status:</strong> <strong>QUALITY STANDARD VIOLATION</strong></p>
<hr />
<h2 id="final-recommendation"><strong>FINAL RECOMMENDATION</strong><a class="headerlink" href="#final-recommendation" title="Permanent link">&para;</a></h2>
<p><strong>❌ THE IMPLEMENTATION PHASE CANNOT BE DECLARED COMPLETE</strong></p>
<p>Despite significant progress on critical issues, the project <strong>FAILS</strong> to meet the mandatory <strong>100% test pass rate</strong> and
<strong>Zero Tolerance Policy</strong> requirements for Implementation Phase completion.</p>
<h3 id="required-actions-before-documentation-phase"><strong>Required Actions Before Documentation Phase:</strong><a class="headerlink" href="#required-actions-before-documentation-phase" title="Permanent link">&para;</a></h3>
<h4 id="immediate-critical"><strong>Immediate (Critical):</strong><a class="headerlink" href="#immediate-critical" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Fix all async/await pattern violations</strong> - Add missing await statements to 20+ failing tests</li>
<li><strong>Resolve database schema inconsistencies</strong> - Fix SQL queries to match actual schema</li>
<li><strong>Complete performance test data isolation</strong> - Ensure unique identifiers in all test data</li>
</ol>
<h4 id="high-priority"><strong>High Priority:</strong><a class="headerlink" href="#high-priority" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Fix project member test infrastructure</strong> - Ensure proper project setup</li>
<li><strong>Resolve transaction state management</strong> - Implement proper rollback handling</li>
</ol>
<h4 id="medium-priority"><strong>Medium Priority:</strong><a class="headerlink" href="#medium-priority" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Address validation logic mismatches</strong> - Align test expectations with implementation</li>
</ol>
<h3 id="estimated-effort"><strong>Estimated Effort:</strong><a class="headerlink" href="#estimated-effort" title="Permanent link">&para;</a></h3>
<p><strong>2-3 additional development cycles</strong> to achieve full 100% compliance</p>
<h3 id="next-steps"><strong>Next Steps:</strong><a class="headerlink" href="#next-steps" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Return to Backend/Frontend Agent</strong> for critical async/await and schema fixes</li>
<li><strong>Implement systematic async pattern review</strong> across all test files</li>
<li><strong>Conduct database schema audit</strong> and fix SQL inconsistencies</li>
<li><strong>Re-verification required</strong> after fixes are implemented</li>
</ol>
<hr />
<h2 id="conclusion"><strong>CONCLUSION</strong><a class="headerlink" href="#conclusion" title="Permanent link">&para;</a></h2>
<p>The Implementation Phase has achieved <strong>substantial progress</strong> with critical ExceptionGroup errors resolved and major
infrastructure improvements. However, <strong>100% compliance remains unachieved</strong> due to persistent async/await violations
and new database schema issues.</p>
<p><strong>The project demonstrates strong momentum</strong> but requires <strong>focused effort on async patterns and database consistency</strong>
to achieve the mandatory quality standards for proceeding to the Documentation phase.</p>
<hr />
<p><em>This final verification confirms significant progress but mandates additional corrective actions before Implementation
Phase completion.</em></p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../../js/base.js"></script>
        <script src="../../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
