<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/tasks/test-failures-resolution/middleware-safe-error-handling-design/">
        <link rel="shortcut icon" href="../../../img/favicon.ico">
        <title>Middleware-Safe Error Handling Design - Ultimate Electrical Designer Docs</title>
        <link href="../../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../../css/brands.min.css" rel="stylesheet">
        <link href="../../../css/solid.min.css" rel="stylesheet">
        <link href="../../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../../../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#middleware-safe-error-handling-design" class="nav-link">Middleware-Safe Error Handling Design</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#ultimate-electrical-designer-error-handler-redesign" class="nav-link">Ultimate Electrical Designer - Error Handler Redesign</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#problem-statement" class="nav-link">Problem Statement</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#root-cause-analysis" class="nav-link">Root Cause Analysis</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#solution-design-exception-boundary-pattern" class="nav-link">Solution Design: Exception Boundary Pattern</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#implementation-strategy" class="nav-link">Implementation Strategy</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#success-criteria" class="nav-link">Success Criteria</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#risk-mitigation" class="nav-link">Risk Mitigation</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#next-steps" class="nav-link">Next Steps</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="middleware-safe-error-handling-design">Middleware-Safe Error Handling Design<a class="headerlink" href="#middleware-safe-error-handling-design" title="Permanent link">&para;</a></h1>
<h2 id="ultimate-electrical-designer-error-handler-redesign">Ultimate Electrical Designer - Error Handler Redesign<a class="headerlink" href="#ultimate-electrical-designer-error-handler-redesign" title="Permanent link">&para;</a></h2>
<p><strong>Document Version:</strong> 1.0<br />
<strong>Date:</strong> August 7, 2025<br />
<strong>Task:</strong> 1.1.2 - Design Middleware-Safe Error Handling Pattern  </p>
<hr />
<h2 id="problem-statement">Problem Statement<a class="headerlink" href="#problem-statement" title="Permanent link">&para;</a></h2>
<p>The current unified error handler raises <code>HTTPException</code> directly within async middleware contexts, causing ExceptionGroup errors in Starlette's anyio TaskGroup. This breaks the middleware stack and prevents proper error propagation.</p>
<h2 id="root-cause-analysis">Root Cause Analysis<a class="headerlink" href="#root-cause-analysis" title="Permanent link">&para;</a></h2>
<h3 id="current-problematic-flow">Current Problematic Flow<a class="headerlink" href="#current-problematic-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>Service Exception → Error Handler → HTTPException (line 910) → anyio TaskGroup → ExceptionGroup
</code></pre></div>
<h3 id="middleware-stack-context">Middleware Stack Context<a class="headerlink" href="#middleware-stack-context" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>Starlette BaseMiddleware (anyio.create_task_group)
├── SecurityMiddleware (@handle_security_errors)
├── ContextMiddleware  
├── LoggingMiddleware
└── API Routes
</code></pre></div>
<h2 id="solution-design-exception-boundary-pattern">Solution Design: Exception Boundary Pattern<a class="headerlink" href="#solution-design-exception-boundary-pattern" title="Permanent link">&para;</a></h2>
<h3 id="core-principle">Core Principle<a class="headerlink" href="#core-principle" title="Permanent link">&para;</a></h3>
<p><strong>Never raise HTTPException within anyio TaskGroup context</strong>. Instead, use a two-phase error handling approach:</p>
<ol>
<li><strong>Phase 1</strong>: Catch and convert exceptions to application-specific exceptions</li>
<li><strong>Phase 2</strong>: Convert application exceptions to HTTPException at the FastAPI level</li>
</ol>
<h3 id="new-error-handling-pattern">New Error Handling Pattern<a class="headerlink" href="#new-error-handling-pattern" title="Permanent link">&para;</a></h3>
<h4 id="1-middleware-safe-exception-types">1. Middleware-Safe Exception Types<a class="headerlink" href="#1-middleware-safe-exception-types" title="Permanent link">&para;</a></h4>
<p>Create middleware-safe exception types that can be safely raised within async contexts:</p>
<div class="highlight"><pre><span></span><code><span class="k">class</span><span class="w"> </span><span class="nc">MiddlewareSafeException</span><span class="p">(</span><span class="n">BaseApplicationException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Base exception that can be safely raised in middleware context.&quot;&quot;&quot;</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">message</span><span class="p">:</span> <span class="nb">str</span><span class="p">,</span> <span class="n">status_code</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="mi">500</span><span class="p">,</span> <span class="n">detail</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="kc">None</span><span class="p">):</span>
        <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="fm">__init__</span><span class="p">(</span><span class="n">message</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">status_code</span> <span class="o">=</span> <span class="n">status_code</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">detail</span> <span class="o">=</span> <span class="n">detail</span> <span class="ow">or</span> <span class="n">message</span>

<span class="k">class</span><span class="w"> </span><span class="nc">SecurityMiddlewareException</span><span class="p">(</span><span class="n">MiddlewareSafeException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Security-related middleware exception.&quot;&quot;&quot;</span>
    <span class="k">pass</span>

<span class="k">class</span><span class="w"> </span><span class="nc">DatabaseMiddlewareException</span><span class="p">(</span><span class="n">MiddlewareSafeException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Database-related middleware exception.&quot;&quot;&quot;</span>
    <span class="k">pass</span>
</code></pre></div>
<h4 id="2-middleware-safe-error-handler-decorators">2. Middleware-Safe Error Handler Decorators<a class="headerlink" href="#2-middleware-safe-error-handler-decorators" title="Permanent link">&para;</a></h4>
<p>Redesign decorators to raise middleware-safe exceptions instead of HTTPException:</p>
<div class="highlight"><pre><span></span><code><span class="k">def</span><span class="w"> </span><span class="nf">handle_security_errors_safe</span><span class="p">(</span><span class="n">security_operation</span><span class="p">:</span> <span class="nb">str</span> <span class="o">=</span> <span class="s2">&quot;security_check&quot;</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Middleware-safe version of handle_security_errors.&quot;&quot;&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">decorator</span><span class="p">(</span><span class="n">func</span><span class="p">:</span> <span class="n">Callable</span><span class="p">[</span><span class="o">...</span><span class="p">,</span> <span class="n">Any</span><span class="p">])</span> <span class="o">-&gt;</span> <span class="n">Callable</span><span class="p">[</span><span class="o">...</span><span class="p">,</span> <span class="n">Any</span><span class="p">]:</span>
        <span class="nd">@wraps</span><span class="p">(</span><span class="n">func</span><span class="p">)</span>
        <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">async_wrapper</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">:</span> <span class="n">Any</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">:</span> <span class="n">Any</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Any</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">result</span> <span class="o">=</span> <span class="n">func</span><span class="p">(</span><span class="o">*</span><span class="n">args</span><span class="p">,</span> <span class="o">**</span><span class="n">kwargs</span><span class="p">)</span>
                <span class="k">if</span> <span class="n">inspect</span><span class="o">.</span><span class="n">iscoroutine</span><span class="p">(</span><span class="n">result</span><span class="p">):</span>
                    <span class="k">return</span> <span class="k">await</span> <span class="n">result</span>
                <span class="k">return</span> <span class="n">result</span>
            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                <span class="c1"># Pass through existing middleware-safe exceptions</span>
                <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">e</span><span class="p">,</span> <span class="n">MiddlewareSafeException</span><span class="p">):</span>
                    <span class="k">raise</span>

                <span class="c1"># Pass through certain exceptions</span>
                <span class="k">if</span> <span class="nb">isinstance</span><span class="p">(</span><span class="n">e</span><span class="p">,</span> <span class="p">(</span><span class="n">DataValidationError</span><span class="p">,</span> <span class="n">NotFoundError</span><span class="p">)):</span>
                    <span class="k">raise</span>

                <span class="c1"># Convert other exceptions to middleware-safe exceptions</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">exception</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Security operation &#39;</span><span class="si">{</span><span class="n">security_operation</span><span class="si">}</span><span class="s2">&#39; failed&quot;</span><span class="p">)</span>

                <span class="n">result</span> <span class="o">=</span> <span class="n">unified_error_handler</span><span class="o">.</span><span class="n">handle_exception</span><span class="p">(</span>
                    <span class="n">e</span><span class="p">,</span> <span class="n">ErrorContext</span><span class="o">.</span><span class="n">SECURITY</span><span class="p">,</span>
                    <span class="n">additional_context</span><span class="o">=</span><span class="p">{</span>
                        <span class="s2">&quot;security_operation&quot;</span><span class="p">:</span> <span class="n">security_operation</span><span class="p">,</span>
                        <span class="s2">&quot;function&quot;</span><span class="p">:</span> <span class="n">func</span><span class="o">.</span><span class="vm">__name__</span><span class="p">,</span>
                    <span class="p">}</span>
                <span class="p">)</span>

                <span class="c1"># Raise middleware-safe exception instead of HTTPException</span>
                <span class="k">raise</span> <span class="n">SecurityMiddlewareException</span><span class="p">(</span>
                    <span class="n">message</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Security operation &#39;</span><span class="si">{</span><span class="n">security_operation</span><span class="si">}</span><span class="s2">&#39; failed: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
                    <span class="n">status_code</span><span class="o">=</span><span class="n">result</span><span class="o">.</span><span class="n">http_status_code</span><span class="p">,</span>
                    <span class="n">detail</span><span class="o">=</span><span class="n">result</span><span class="o">.</span><span class="n">error_response</span><span class="o">.</span><span class="n">detail</span>
                <span class="p">)</span>

        <span class="k">return</span> <span class="n">async_wrapper</span>
    <span class="k">return</span> <span class="n">decorator</span>
</code></pre></div>
<h4 id="3-fastapi-exception-handler">3. FastAPI Exception Handler<a class="headerlink" href="#3-fastapi-exception-handler" title="Permanent link">&para;</a></h4>
<p>Add a global exception handler at the FastAPI level to convert middleware-safe exceptions to HTTPException:</p>
<div class="highlight"><pre><span></span><code><span class="nd">@app</span><span class="o">.</span><span class="n">exception_handler</span><span class="p">(</span><span class="n">MiddlewareSafeException</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">middleware_safe_exception_handler</span><span class="p">(</span><span class="n">request</span><span class="p">:</span> <span class="n">Request</span><span class="p">,</span> <span class="n">exc</span><span class="p">:</span> <span class="n">MiddlewareSafeException</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Convert middleware-safe exceptions to proper HTTP responses.&quot;&quot;&quot;</span>
    <span class="k">return</span> <span class="n">JSONResponse</span><span class="p">(</span>
        <span class="n">status_code</span><span class="o">=</span><span class="n">exc</span><span class="o">.</span><span class="n">status_code</span><span class="p">,</span>
        <span class="n">content</span><span class="o">=</span><span class="p">{</span><span class="s2">&quot;detail&quot;</span><span class="p">:</span> <span class="n">exc</span><span class="o">.</span><span class="n">detail</span><span class="p">}</span>
    <span class="p">)</span>
</code></pre></div>
<h3 id="exception-boundary-definition">Exception Boundary Definition<a class="headerlink" href="#exception-boundary-definition" title="Permanent link">&para;</a></h3>
<h4 id="safe-zone-no-httpexception">Safe Zone (No HTTPException)<a class="headerlink" href="#safe-zone-no-httpexception" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Middleware Layer</strong>: All middleware decorators and dispatch methods</li>
<li><strong>Service Layer</strong>: Business logic and service methods  </li>
<li><strong>Repository Layer</strong>: Database access methods</li>
<li><strong>anyio TaskGroup Context</strong>: Any code running within Starlette middleware</li>
</ul>
<h4 id="conversion-zone-httpexception-allowed">Conversion Zone (HTTPException Allowed)<a class="headerlink" href="#conversion-zone-httpexception-allowed" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>FastAPI Exception Handlers</strong>: Global exception handlers</li>
<li><strong>API Route Handlers</strong>: Direct endpoint implementations</li>
<li><strong>FastAPI Dependency Injection</strong>: Dependency providers</li>
</ul>
<h3 id="middleware-layer-interaction-diagram">Middleware Layer Interaction Diagram<a class="headerlink" href="#middleware-layer-interaction-diagram" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Application                     │
├─────────────────────────────────────────────────────────────┤
│  Exception Handlers (HTTPException Conversion Zone)        │
│  ├── MiddlewareSafeException → HTTPException               │
│  └── Other Application Exceptions → HTTPException          │
└─────────────────────────────────────────────────────────────┘
                              │
                    MiddlewareSafeException
                              │
┌─────────────────────────────────────────────────────────────┐
│                 Starlette Middleware Stack                 │
│                  (Safe Zone - No HTTPException)            │
├─────────────────────────────────────────────────────────────┤
│  anyio.create_task_group() Context                        │
│  ├── SecurityMiddleware (@handle_security_errors_safe)     │
│  ├── ContextMiddleware                                     │
│  ├── LoggingMiddleware                                     │
│  └── Other Middleware                                      │
└─────────────────────────────────────────────────────────────┘
                              │
                    Service/Repository Exceptions
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layers                       │
├─────────────────────────────────────────────────────────────┤
│  API Routes → Services → Repositories → Models             │
│  (Application Exceptions Only)                             │
└─────────────────────────────────────────────────────────────┘
</code></pre></div>
<h2 id="implementation-strategy">Implementation Strategy<a class="headerlink" href="#implementation-strategy" title="Permanent link">&para;</a></h2>
<h3 id="phase-1-create-middleware-safe-exception-types">Phase 1: Create Middleware-Safe Exception Types<a class="headerlink" href="#phase-1-create-middleware-safe-exception-types" title="Permanent link">&para;</a></h3>
<ol>
<li>Define <code>MiddlewareSafeException</code> base class</li>
<li>Create specific exception types for each context</li>
<li>Ensure proper inheritance from <code>BaseApplicationException</code></li>
</ol>
<h3 id="phase-2-update-error-handler-decorators">Phase 2: Update Error Handler Decorators<a class="headerlink" href="#phase-2-update-error-handler-decorators" title="Permanent link">&para;</a></h3>
<ol>
<li>Create <code>handle_security_errors_safe</code> decorator</li>
<li>Create <code>handle_database_errors_safe</code> decorator  </li>
<li>Create <code>handle_api_errors_safe</code> decorator</li>
<li>Maintain backward compatibility with existing decorators</li>
</ol>
<h3 id="phase-3-add-fastapi-exception-handlers">Phase 3: Add FastAPI Exception Handlers<a class="headerlink" href="#phase-3-add-fastapi-exception-handlers" title="Permanent link">&para;</a></h3>
<ol>
<li>Register global exception handler for <code>MiddlewareSafeException</code></li>
<li>Ensure proper HTTP status code mapping</li>
<li>Maintain error response format consistency</li>
</ol>
<h3 id="phase-4-update-middleware-usage">Phase 4: Update Middleware Usage<a class="headerlink" href="#phase-4-update-middleware-usage" title="Permanent link">&para;</a></h3>
<ol>
<li>Replace <code>@handle_security_errors</code> with <code>@handle_security_errors_safe</code> in middleware</li>
<li>Update other middleware decorators as needed</li>
<li>Test middleware stack integration</li>
</ol>
<h2 id="success-criteria">Success Criteria<a class="headerlink" href="#success-criteria" title="Permanent link">&para;</a></h2>
<h3 id="functional-requirements">Functional Requirements<a class="headerlink" href="#functional-requirements" title="Permanent link">&para;</a></h3>
<ul>
<li>✅ No ExceptionGroup errors in middleware stack</li>
<li>✅ Proper HTTP status codes in API responses</li>
<li>✅ Consistent error response format</li>
<li>✅ Backward compatibility with existing error handling</li>
</ul>
<h3 id="technical-requirements">Technical Requirements<a class="headerlink" href="#technical-requirements" title="Permanent link">&para;</a></h3>
<ul>
<li>✅ Exception boundary clearly defined and enforced</li>
<li>✅ Middleware-safe exceptions never raise HTTPException in async context</li>
<li>✅ FastAPI exception handlers properly convert to HTTP responses</li>
<li>✅ Error logging and tracking maintained</li>
</ul>
<h3 id="quality-requirements">Quality Requirements<a class="headerlink" href="#quality-requirements" title="Permanent link">&para;</a></h3>
<ul>
<li>✅ Zero tolerance policy compliance</li>
<li>✅ 5-layer architecture integrity maintained</li>
<li>✅ Professional error handling standards</li>
<li>✅ Comprehensive test coverage</li>
</ul>
<h2 id="risk-mitigation">Risk Mitigation<a class="headerlink" href="#risk-mitigation" title="Permanent link">&para;</a></h2>
<h3 id="high-risk-areas">High-Risk Areas<a class="headerlink" href="#high-risk-areas" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Breaking Changes</strong>: New exception types might break existing code</li>
<li><strong>Mitigation</strong>: Maintain backward compatibility, gradual migration</li>
<li><strong>Performance Impact</strong>: Additional exception handling layers</li>
<li><strong>Mitigation</strong>: Minimal overhead, benchmark performance</li>
<li><strong>Error Message Consistency</strong>: Different exception paths might produce inconsistent messages</li>
<li><strong>Mitigation</strong>: Centralized error message formatting</li>
</ol>
<h3 id="testing-strategy">Testing Strategy<a class="headerlink" href="#testing-strategy" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Unit Tests</strong>: Test each decorator in isolation</li>
<li><strong>Integration Tests</strong>: Test full middleware stack with error scenarios</li>
<li><strong>Performance Tests</strong>: Ensure no significant performance degradation</li>
<li><strong>Compatibility Tests</strong>: Verify existing error handling still works</li>
</ol>
<h2 id="next-steps">Next Steps<a class="headerlink" href="#next-steps" title="Permanent link">&para;</a></h2>
<p>This design provides the foundation for implementing middleware-safe error handling. The next task (1.1.3) will implement this design in the unified error handler code.</p>
<p><strong>Quality Gate</strong>: ✅ Design approved for middleware compatibility
- Exception boundary clearly defined
- Middleware-safe pattern specified
- Implementation strategy outlined
- Risk mitigation planned</p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../../js/base.js"></script>
        <script src="../../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
