<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/tasks/test-failures-resolution/user-routes-refactoring-summary/">
        <link rel="shortcut icon" href="../../../img/favicon.ico">
        <title>User Routes Unit Tests Refactoring Summary - Ultimate Electrical Designer Docs</title>
        <link href="../../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../../css/brands.min.css" rel="stylesheet">
        <link href="../../../css/solid.min.css" rel="stylesheet">
        <link href="../../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../../../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#user-routes-unit-tests-refactoring-summary" class="nav-link">User Routes Unit Tests Refactoring Summary</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#ultimate-electrical-designer-task-214-completion-report" class="nav-link">Ultimate Electrical Designer - Task 2.1.4 Completion Report</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#executive-summary" class="nav-link">Executive Summary</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#analysis-results" class="nav-link">Analysis Results</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#issues-identified-for-phase-22" class="nav-link">Issues Identified for Phase 2.2</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#task-214-conclusion" class="nav-link">Task 2.1.4 Conclusion</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#phase-21-summary" class="nav-link">Phase 2.1 Summary</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#recommendations-for-phase-22" class="nav-link">Recommendations for Phase 2.2</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#next-steps" class="nav-link">Next Steps</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#conclusion" class="nav-link">Conclusion</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="user-routes-unit-tests-refactoring-summary">User Routes Unit Tests Refactoring Summary<a class="headerlink" href="#user-routes-unit-tests-refactoring-summary" title="Permanent link">&para;</a></h1>
<h2 id="ultimate-electrical-designer-task-214-completion-report">Ultimate Electrical Designer - Task 2.1.4 Completion Report<a class="headerlink" href="#ultimate-electrical-designer-task-214-completion-report" title="Permanent link">&para;</a></h2>
<p><strong>Document Version:</strong> 1.0<br />
<strong>Date:</strong> August 7, 2025<br />
<strong>Task:</strong> 2.1.4 - Refactor User Routes Unit Tests  </p>
<hr />
<h2 id="executive-summary">Executive Summary<a class="headerlink" href="#executive-summary" title="Permanent link">&para;</a></h2>
<p>After comprehensive analysis, <strong>Task 2.1.4 requires no refactoring work</strong> because the user routes tests are <strong>pure integration tests</strong> that do not use mocks. The test failures identified are <strong>API-level issues</strong> that belong in <strong>Phase 2.2: HTTP Status Code Standardization</strong>, not Phase 2.1: Mock Strategy Standardization.</p>
<h2 id="analysis-results">Analysis Results<a class="headerlink" href="#analysis-results" title="Permanent link">&para;</a></h2>
<h3 id="user-routes-test-classification"><strong>User Routes Test Classification</strong><a class="headerlink" href="#user-routes-test-classification" title="Permanent link">&para;</a></h3>
<h4 id="integration-tests-not-unit-tests">✅ <strong>Integration Tests (Not Unit Tests)</strong><a class="headerlink" href="#integration-tests-not-unit-tests" title="Permanent link">&para;</a></h4>
<p>The user routes tests in <code>server/tests/api/v1/test_user_routes.py</code> are <strong>integration tests</strong> that:
- Use real database operations with transaction isolation
- Use real authentication with JWT tokens
- Test full HTTP request/response cycles
- <strong>Do not use any mocks, MagicMock, or AsyncMock</strong>
- Test the complete API stack end-to-end</p>
<h4 id="no-mock-usage-found"><strong>No Mock Usage Found</strong><a class="headerlink" href="#no-mock-usage-found" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Search results for mock patterns in test_user_routes.py:</span>
<span class="c1"># - No MagicMock usage</span>
<span class="c1"># - No AsyncMock usage  </span>
<span class="c1"># - No mock assertions</span>
<span class="c1"># - No dependency injection overrides</span>
<span class="c1"># - No service layer mocking</span>
</code></pre></div>
<h3 id="test-failures-analysis"><strong>Test Failures Analysis</strong><a class="headerlink" href="#test-failures-analysis" title="Permanent link">&para;</a></h3>
<p>The user routes tests have <strong>6 failures out of 20 tests</strong>, but these are <strong>API-level issues</strong>, not mock strategy issues:</p>
<h4 id="1-http-500-internal-server-errors-4-tests"><strong>1. HTTP 500 Internal Server Errors (4 tests)</strong><a class="headerlink" href="#1-http-500-internal-server-errors-4-tests" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code>FAILED test_update_current_user_profile - assert 500 == 200
FAILED test_update_current_user_profile_forbidden_fields - assert 500 == 200  
FAILED test_get_users_summary_admin - assert 500 == 200
FAILED test_get_users_summary_with_limit - assert 500 == 200
</code></pre></div>
<p><strong>Root Cause</strong>: API endpoints are returning 500 errors instead of expected responses</p>
<h4 id="2-exceptiongroup-errors-1-test"><strong>2. ExceptionGroup Errors (1 test)</strong><a class="headerlink" href="#2-exceptiongroup-errors-1-test" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code>FAILED test_user_crud_operations_admin - SecurityMiddlewareException: 
API operation &#39;create_user&#39; failed: Email address &#39;<EMAIL>&#39; is already registered.
</code></pre></div>
<p><strong>Root Cause</strong>: 
- ExceptionGroup errors still occurring (Phase 1 issue)
- Test data isolation problems (email conflicts)</p>
<h4 id="3-error-handler-issues-1-test"><strong>3. Error Handler Issues (1 test)</strong><a class="headerlink" href="#3-error-handler-issues-1-test" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code>FAILED test_user_validation_errors - AssertionError: Expected validation error, 
got An error occurred while processing the error response.
</code></pre></div>
<p><strong>Root Cause</strong>: Error handling chain is failing to process error responses properly</p>
<h3 id="comparison-with-other-api-test-files"><strong>Comparison with Other API Test Files</strong><a class="headerlink" href="#comparison-with-other-api-test-files" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th><strong>Test File</strong></th>
<th><strong>Mock Usage</strong></th>
<th><strong>Test Type</strong></th>
<th><strong>Refactoring Needed</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><code>test_component_routes.py</code></td>
<td>✅ <strong>Extensive mocks</strong></td>
<td>Integration with service mocks</td>
<td>✅ <strong>COMPLETED</strong></td>
</tr>
<tr>
<td><code>test_user_routes.py</code></td>
<td>❌ <strong>No mocks</strong></td>
<td>Pure integration</td>
<td>❌ <strong>NOT NEEDED</strong></td>
</tr>
<tr>
<td><code>test_auth_routes.py</code></td>
<td>⚠️ <strong>Minimal mocks</strong></td>
<td>Pure integration</td>
<td>❌ <strong>NOT NEEDED</strong></td>
</tr>
<tr>
<td><code>test_component_category_routes.py</code></td>
<td>⚠️ <strong>Import only</strong></td>
<td>Pure integration</td>
<td>❌ <strong>NOT NEEDED</strong></td>
</tr>
<tr>
<td><code>test_component_type_routes.py</code></td>
<td>⚠️ <strong>Import only</strong></td>
<td>Pure integration</td>
<td>❌ <strong>NOT NEEDED</strong></td>
</tr>
<tr>
<td><code>test_task_routes.py</code></td>
<td>❌ <strong>No mocks</strong></td>
<td>Pure integration</td>
<td>❌ <strong>NOT NEEDED</strong></td>
</tr>
<tr>
<td><code>test_health_routes.py</code></td>
<td>❌ <strong>No mocks</strong></td>
<td>Pure integration</td>
<td>❌ <strong>NOT NEEDED</strong></td>
</tr>
<tr>
<td><code>test_project_routes.py</code></td>
<td>❌ <strong>No mocks</strong></td>
<td>Pure integration</td>
<td>❌ <strong>NOT NEEDED</strong></td>
</tr>
</tbody>
</table>
<h3 id="key-findings"><strong>Key Findings</strong><a class="headerlink" href="#key-findings" title="Permanent link">&para;</a></h3>
<ol>
<li><strong>Only component routes needed mock refactoring</strong> - All other API tests are pure integration tests</li>
<li><strong>User routes failures are API issues</strong> - Not mock strategy issues</li>
<li><strong>Phase 2.1 scope is complete</strong> - No more mock refactoring needed</li>
<li><strong>Phase 2.2 should address API failures</strong> - HTTP status code standardization</li>
</ol>
<h2 id="issues-identified-for-phase-22">Issues Identified for Phase 2.2<a class="headerlink" href="#issues-identified-for-phase-22" title="Permanent link">&para;</a></h2>
<h3 id="1-http-status-code-contract-violations"><strong>1. HTTP Status Code Contract Violations</strong><a class="headerlink" href="#1-http-status-code-contract-violations" title="Permanent link">&para;</a></h3>
<ul>
<li>Multiple endpoints returning 500 instead of expected status codes</li>
<li>Error responses not following standardized format</li>
<li>Inconsistent error handling across endpoints</li>
</ul>
<h3 id="2-remaining-exceptiongroup-issues"><strong>2. Remaining ExceptionGroup Issues</strong><a class="headerlink" href="#2-remaining-exceptiongroup-issues" title="Permanent link">&para;</a></h3>
<ul>
<li>Some error scenarios still triggering ExceptionGroup errors</li>
<li>Error handling middleware chain needs additional fixes</li>
<li>SecurityMiddleware error processing issues</li>
</ul>
<h3 id="3-test-data-isolation-problems"><strong>3. Test Data Isolation Problems</strong><a class="headerlink" href="#3-test-data-isolation-problems" title="Permanent link">&para;</a></h3>
<ul>
<li>Email conflicts indicating transaction isolation gaps</li>
<li>Test data cleanup not working properly for some scenarios</li>
<li>Need better test data factories for integration tests</li>
</ul>
<h2 id="task-214-conclusion">Task 2.1.4 Conclusion<a class="headerlink" href="#task-214-conclusion" title="Permanent link">&para;</a></h2>
<h3 id="task-status-complete-no-work-required">✅ <strong>Task Status: COMPLETE (No Work Required)</strong><a class="headerlink" href="#task-status-complete-no-work-required" title="Permanent link">&para;</a></h3>
<p><strong>Rationale:</strong>
- User routes tests are integration tests, not unit tests
- No mocks are used, so no mock strategy refactoring is needed
- Test failures are API-level issues for Phase 2.2, not mock issues for Phase 2.1</p>
<h3 id="quality-gate-assessment"><strong>Quality Gate Assessment</strong><a class="headerlink" href="#quality-gate-assessment" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th><strong>Criteria</strong></th>
<th><strong>Status</strong></th>
<th><strong>Evidence</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Mock usage identified</strong></td>
<td>✅ <strong>COMPLETE</strong></td>
<td>No mocks found in user routes tests</td>
</tr>
<tr>
<td><strong>Mock strategy applied</strong></td>
<td>✅ <strong>N/A</strong></td>
<td>No mocks to refactor</td>
</tr>
<tr>
<td><strong>Mock assertion failures fixed</strong></td>
<td>✅ <strong>N/A</strong></td>
<td>No mock assertions exist</td>
</tr>
<tr>
<td><strong>Standardized patterns used</strong></td>
<td>✅ <strong>N/A</strong></td>
<td>Integration test patterns already correct</td>
</tr>
</tbody>
</table>
<h2 id="phase-21-summary">Phase 2.1 Summary<a class="headerlink" href="#phase-21-summary" title="Permanent link">&para;</a></h2>
<h3 id="all-mock-strategy-tasks-complete">✅ <strong>All Mock Strategy Tasks Complete</strong><a class="headerlink" href="#all-mock-strategy-tasks-complete" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th><strong>Task</strong></th>
<th><strong>Status</strong></th>
<th><strong>Outcome</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>2.1.1: Audit Mock Usage</strong></td>
<td>✅ <strong>COMPLETE</strong></td>
<td>Comprehensive audit completed</td>
</tr>
<tr>
<td><strong>2.1.2: Define Mock Strategy</strong></td>
<td>✅ <strong>COMPLETE</strong></td>
<td>Standardized strategy documented</td>
</tr>
<tr>
<td><strong>2.1.3: Refactor Component Routes</strong></td>
<td>✅ <strong>COMPLETE</strong></td>
<td>8/8 tests passing, mock assertions fixed</td>
</tr>
<tr>
<td><strong>2.1.4: Refactor User Routes</strong></td>
<td>✅ <strong>COMPLETE</strong></td>
<td>No refactoring needed (no mocks used)</td>
</tr>
</tbody>
</table>
<h3 id="phase-21-success-metrics"><strong>Phase 2.1 Success Metrics</strong><a class="headerlink" href="#phase-21-success-metrics" title="Permanent link">&para;</a></h3>
<table>
<thead>
<tr>
<th><strong>Metric</strong></th>
<th><strong>Target</strong></th>
<th><strong>Achieved</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Mock Strategy Standardization</strong></td>
<td>100%</td>
<td>✅ <strong>100%</strong></td>
</tr>
<tr>
<td><strong>Mock Assertion Failures</strong></td>
<td>Zero</td>
<td>✅ <strong>Zero</strong></td>
</tr>
<tr>
<td><strong>Component Route Tests</strong></td>
<td>All passing</td>
<td>✅ <strong>8/8 Passing</strong></td>
</tr>
<tr>
<td><strong>User Route Mock Issues</strong></td>
<td>Zero</td>
<td>✅ <strong>Zero (No mocks)</strong></td>
</tr>
</tbody>
</table>
<h2 id="recommendations-for-phase-22">Recommendations for Phase 2.2<a class="headerlink" href="#recommendations-for-phase-22" title="Permanent link">&para;</a></h2>
<h3 id="1-address-http-status-code-issues"><strong>1. Address HTTP Status Code Issues</strong><a class="headerlink" href="#1-address-http-status-code-issues" title="Permanent link">&para;</a></h3>
<ul>
<li>Fix 500 errors in user profile update endpoints</li>
<li>Standardize error response formats</li>
<li>Implement proper HTTP status code contracts</li>
</ul>
<h3 id="2-complete-exceptiongroup-fixes"><strong>2. Complete ExceptionGroup Fixes</strong><a class="headerlink" href="#2-complete-exceptiongroup-fixes" title="Permanent link">&para;</a></h3>
<ul>
<li>Address remaining ExceptionGroup scenarios</li>
<li>Fix SecurityMiddleware error processing</li>
<li>Ensure error handling chain works for all endpoints</li>
</ul>
<h3 id="3-improve-test-data-isolation"><strong>3. Improve Test Data Isolation</strong><a class="headerlink" href="#3-improve-test-data-isolation" title="Permanent link">&para;</a></h3>
<ul>
<li>Fix email conflict issues in integration tests</li>
<li>Enhance transaction isolation for all test scenarios</li>
<li>Implement better test data factories</li>
</ul>
<h3 id="4-api-endpoint-validation"><strong>4. API Endpoint Validation</strong><a class="headerlink" href="#4-api-endpoint-validation" title="Permanent link">&para;</a></h3>
<ul>
<li>Validate all API endpoints return correct status codes</li>
<li>Ensure consistent error response formats</li>
<li>Test error handling scenarios comprehensively</li>
</ul>
<h2 id="next-steps">Next Steps<a class="headerlink" href="#next-steps" title="Permanent link">&para;</a></h2>
<ol>
<li><strong>Mark Phase 2.1 as COMPLETE</strong> - All mock strategy standardization tasks finished</li>
<li><strong>Begin Phase 2.2</strong> - HTTP Status Code Standardization</li>
<li><strong>Address user routes API failures</strong> - Fix 500 errors and ExceptionGroup issues</li>
<li><strong>Validate HTTP status code contracts</strong> - Ensure all endpoints follow standards</li>
</ol>
<hr />
<h2 id="conclusion">Conclusion<a class="headerlink" href="#conclusion" title="Permanent link">&para;</a></h2>
<p><strong>Task 2.1.4 is complete with no work required.</strong> The user routes tests are properly structured integration tests that don't use mocks. The test failures identified are API-level issues that will be addressed in Phase 2.2: HTTP Status Code Standardization.</p>
<p><strong>Phase 2.1: Mock Strategy Standardization is now 100% complete</strong> and ready to proceed to Phase 2.2.</p>
<p><strong>Quality Gate</strong>: ✅ <strong>PASSED</strong> - All mock strategy standardization tasks complete</p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../../js/base.js"></script>
        <script src="../../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
