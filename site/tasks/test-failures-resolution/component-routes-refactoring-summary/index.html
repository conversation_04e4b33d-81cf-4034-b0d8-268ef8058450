<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/tasks/test-failures-resolution/component-routes-refactoring-summary/">
        <link rel="shortcut icon" href="../../../img/favicon.ico">
        <title>Component Routes Unit Tests Refactoring Summary - Ultimate Electrical Designer Docs</title>
        <link href="../../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../../css/brands.min.css" rel="stylesheet">
        <link href="../../../css/solid.min.css" rel="stylesheet">
        <link href="../../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../../../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#component-routes-unit-tests-refactoring-summary" class="nav-link">Component Routes Unit Tests Refactoring Summary</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#ultimate-electrical-designer-task-213-completion-report" class="nav-link">Ultimate Electrical Designer - Task 2.1.3 Completion Report</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#executive-summary" class="nav-link">Executive Summary</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#issues-identified-and-fixed" class="nav-link">Issues Identified and Fixed</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#implementation-details" class="nav-link">Implementation Details</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#test-results" class="nav-link">Test Results</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#key-insights" class="nav-link">Key Insights</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#quality-gates-met" class="nav-link">Quality Gates Met</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#impact-assessment" class="nav-link">Impact Assessment</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#next-steps" class="nav-link">Next Steps</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#success-criteria-validation" class="nav-link">Success Criteria Validation</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#conclusion" class="nav-link">Conclusion</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="component-routes-unit-tests-refactoring-summary">Component Routes Unit Tests Refactoring Summary<a class="headerlink" href="#component-routes-unit-tests-refactoring-summary" title="Permanent link">&para;</a></h1>
<h2 id="ultimate-electrical-designer-task-213-completion-report">Ultimate Electrical Designer - Task 2.1.3 Completion Report<a class="headerlink" href="#ultimate-electrical-designer-task-213-completion-report" title="Permanent link">&para;</a></h2>
<p><strong>Document Version:</strong> 1.0<br />
<strong>Date:</strong> August 7, 2025<br />
<strong>Task:</strong> 2.1.3 - Refactor Component Routes Unit Tests  </p>
<hr />
<h2 id="executive-summary">Executive Summary<a class="headerlink" href="#executive-summary" title="Permanent link">&para;</a></h2>
<p>Successfully refactored component routes unit tests to use standardized mock patterns and fix mock assertion failures. The main issue was ID mismatch between mock expectations and real authenticated user IDs, which has been resolved using ID-agnostic assertion patterns.</p>
<h2 id="issues-identified-and-fixed">Issues Identified and Fixed<a class="headerlink" href="#issues-identified-and-fixed" title="Permanent link">&para;</a></h2>
<h3 id="1-mock-assertion-failure-user-id-mismatch">1. <strong>Mock Assertion Failure - User ID Mismatch</strong><a class="headerlink" href="#1-mock-assertion-failure-user-id-mismatch" title="Permanent link">&para;</a></h3>
<h4 id="problem"><strong>Problem</strong><a class="headerlink" href="#problem" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Test expected mock user ID 1 but got real user ID 35994</span>
<span class="n">mock_component_service</span><span class="o">.</span><span class="n">delete_component</span><span class="o">.</span><span class="n">assert_called_once_with</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">deleted_by_user_id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>
<span class="c1"># AssertionError: Expected: delete_component(1, deleted_by_user_id=1)</span>
<span class="c1">#                 Actual: delete_component(1, deleted_by_user_id=35994)</span>
</code></pre></div>
<h4 id="root-cause"><strong>Root Cause</strong><a class="headerlink" href="#root-cause" title="Permanent link">&para;</a></h4>
<ul>
<li>Test used <code>mock_auth_user</code> fixture with <code>id: 1</code></li>
<li>Real authenticated user had database-generated ID (35994)</li>
<li>Mock assertion expected mock ID but received real ID</li>
</ul>
<h4 id="solution-applied"><strong>Solution Applied</strong><a class="headerlink" href="#solution-applied" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Before (problematic)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_delete_component_success</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">authenticated_client</span><span class="p">,</span>
    <span class="n">mock_component_service</span><span class="p">:</span> <span class="n">MagicMock</span><span class="p">,</span>
    <span class="n">mock_auth_user</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">],</span>  <span class="c1"># Mock user with ID 1</span>
    <span class="n">test_project</span><span class="p">,</span>
<span class="p">):</span>
    <span class="c1"># ...</span>
    <span class="n">mock_component_service</span><span class="o">.</span><span class="n">delete_component</span><span class="o">.</span><span class="n">assert_called_once_with</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">deleted_by_user_id</span><span class="o">=</span><span class="mi">1</span><span class="p">)</span>

<span class="c1"># After (fixed)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_delete_component_success</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">authenticated_client</span><span class="p">,</span>
    <span class="n">mock_component_service</span><span class="p">:</span> <span class="n">MagicMock</span><span class="p">,</span>
    <span class="n">test_project</span><span class="p">,</span>
    <span class="n">test_user</span><span class="p">,</span>  <span class="c1"># Real test user with actual ID</span>
<span class="p">):</span>
    <span class="c1"># ...</span>
    <span class="c1"># Use ID-agnostic assertion with real user ID</span>
    <span class="n">mock_component_service</span><span class="o">.</span><span class="n">delete_component</span><span class="o">.</span><span class="n">assert_called_once_with</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">deleted_by_user_id</span><span class="o">=</span><span class="n">test_user</span><span class="o">.</span><span class="n">id</span><span class="p">)</span>
</code></pre></div>
<h3 id="2-unnecessary-mock-dependencies-removed">2. <strong>Unnecessary Mock Dependencies Removed</strong><a class="headerlink" href="#2-unnecessary-mock-dependencies-removed" title="Permanent link">&para;</a></h3>
<h4 id="problem_1"><strong>Problem</strong><a class="headerlink" href="#problem_1" title="Permanent link">&para;</a></h4>
<ul>
<li>Many tests included <code>mock_auth_user: Dict[str, Any]</code> parameter</li>
<li>Parameter was not used in test logic</li>
<li>Created confusion about test type (unit vs integration)</li>
</ul>
<h4 id="solution-applied_1"><strong>Solution Applied</strong><a class="headerlink" href="#solution-applied_1" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Before</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_create_component_validation_error</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">authenticated_client</span><span class="p">,</span>
    <span class="n">mock_component_service</span><span class="p">:</span> <span class="n">MagicMock</span><span class="p">,</span>
    <span class="n">mock_auth_user</span><span class="p">:</span> <span class="n">Dict</span><span class="p">[</span><span class="nb">str</span><span class="p">,</span> <span class="n">Any</span><span class="p">],</span>  <span class="c1"># Unused parameter</span>
    <span class="n">test_project</span><span class="p">,</span>
<span class="p">):</span>

<span class="c1"># After</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">test_create_component_validation_error</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span>
    <span class="n">authenticated_client</span><span class="p">,</span>
    <span class="n">mock_component_service</span><span class="p">:</span> <span class="n">MagicMock</span><span class="p">,</span>
    <span class="n">test_project</span><span class="p">,</span>  <span class="c1"># Removed unused mock_auth_user</span>
<span class="p">):</span>
</code></pre></div>
<h2 id="implementation-details">Implementation Details<a class="headerlink" href="#implementation-details" title="Permanent link">&para;</a></h2>
<h3 id="files-modified"><strong>Files Modified</strong><a class="headerlink" href="#files-modified" title="Permanent link">&para;</a></h3>
<ul>
<li><code>server/tests/api/v1/test_component_routes.py</code></li>
<li>Fixed <code>test_delete_component_success</code> mock assertion</li>
<li>Removed unused <code>mock_auth_user</code> parameters from 3 tests</li>
<li>Added import for <code>MockAssertionHelpers</code> (for future use)</li>
</ul>
<h3 id="mock-factories-and-assertion-helpers-created"><strong>Mock Factories and Assertion Helpers Created</strong><a class="headerlink" href="#mock-factories-and-assertion-helpers-created" title="Permanent link">&para;</a></h3>
<ul>
<li><code>server/tests/fixtures/mock_factories.py</code> - Standardized mock data creation</li>
<li><code>server/tests/fixtures/integration_factories.py</code> - Real data creation for integration tests</li>
<li><code>server/tests/fixtures/assertion_helpers.py</code> - ID-agnostic assertion utilities</li>
</ul>
<h3 id="test-pattern-standardization"><strong>Test Pattern Standardization</strong><a class="headerlink" href="#test-pattern-standardization" title="Permanent link">&para;</a></h3>
<h4 id="integration-test-pattern-used"><strong>Integration Test Pattern (Used)</strong><a class="headerlink" href="#integration-test-pattern-used" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Component routes tests are integration tests that:</span>
<span class="c1"># 1. Use real database with transaction isolation</span>
<span class="c1"># 2. Use real authentication with real user creation</span>
<span class="c1"># 3. Mock only the service layer for controlled responses</span>
<span class="c1"># 4. Assert on HTTP responses and mock service calls</span>
<span class="c1"># 5. Use real user IDs in mock assertions</span>
</code></pre></div>
<h4 id="unit-test-pattern-not-applicable-here"><strong>Unit Test Pattern (Not applicable here)</strong><a class="headerlink" href="#unit-test-pattern-not-applicable-here" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Pure unit tests would:</span>
<span class="c1"># 1. Mock all external dependencies including database</span>
<span class="c1"># 2. Use predictable mock IDs (1, 2, 3, etc.)</span>
<span class="c1"># 3. Test business logic in isolation</span>
<span class="c1"># 4. Use MockDataFactory for predictable test data</span>
</code></pre></div>
<h2 id="test-results">Test Results<a class="headerlink" href="#test-results" title="Permanent link">&para;</a></h2>
<h3 id="before-refactoring"><strong>Before Refactoring</strong><a class="headerlink" href="#before-refactoring" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>FAILED tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_delete_component_success
AssertionError: expected call not found.
Expected: delete_component(1, deleted_by_user_id=1)
  Actual: delete_component(1, deleted_by_user_id=35994)
</code></pre></div>
<h3 id="after-refactoring"><strong>After Refactoring</strong><a class="headerlink" href="#after-refactoring" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_create_component_success PASSED
tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_create_component_validation_error PASSED
tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_create_component_duplicate_error PASSED
tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_get_component_success PASSED
tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_get_component_not_found PASSED
tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_update_component_success PASSED
tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_delete_component_success PASSED
tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_delete_component_with_dependencies PASSED

================================================================================================
8 passed in 9.21s
================================================================================================
</code></pre></div>
<h2 id="key-insights">Key Insights<a class="headerlink" href="#key-insights" title="Permanent link">&para;</a></h2>
<h3 id="1-test-type-classification">1. <strong>Test Type Classification</strong><a class="headerlink" href="#1-test-type-classification" title="Permanent link">&para;</a></h3>
<ul>
<li>Component routes tests are <strong>integration tests</strong>, not unit tests</li>
<li>They test the full HTTP request/response cycle with real authentication</li>
<li>Service layer is mocked for controlled responses, but authentication is real</li>
</ul>
<h3 id="2-id-agnostic-assertion-pattern">2. <strong>ID-Agnostic Assertion Pattern</strong><a class="headerlink" href="#2-id-agnostic-assertion-pattern" title="Permanent link">&para;</a></h3>
<ul>
<li>Use real user IDs from test fixtures in mock assertions</li>
<li>Avoid hardcoded mock IDs when using real authentication</li>
<li>Pattern: <code>mock_service.method.assert_called_once_with(component_id, deleted_by_user_id=test_user.id)</code></li>
</ul>
<h3 id="3-mock-strategy-consistency">3. <strong>Mock Strategy Consistency</strong><a class="headerlink" href="#3-mock-strategy-consistency" title="Permanent link">&para;</a></h3>
<ul>
<li>Integration tests: Mock services, use real authentication and database</li>
<li>Unit tests: Mock everything, use predictable mock data</li>
<li>No mixing of patterns within the same test</li>
</ul>
<h2 id="quality-gates-met">Quality Gates Met<a class="headerlink" href="#quality-gates-met" title="Permanent link">&para;</a></h2>
<h3 id="all-component-route-unit-tests-pass">✅ <strong>All Component Route Unit Tests Pass</strong><a class="headerlink" href="#all-component-route-unit-tests-pass" title="Permanent link">&para;</a></h3>
<ul>
<li>8/8 component CRUD tests passing</li>
<li>Zero mock assertion failures</li>
<li>Consistent test execution times</li>
</ul>
<h3 id="proper-mock-usage">✅ <strong>Proper Mock Usage</strong><a class="headerlink" href="#proper-mock-usage" title="Permanent link">&para;</a></h3>
<ul>
<li>Service layer appropriately mocked for controlled responses</li>
<li>Real authentication used for integration testing</li>
<li>ID-agnostic assertions prevent future ID mismatch issues</li>
</ul>
<h3 id="standardized-patterns">✅ <strong>Standardized Patterns</strong><a class="headerlink" href="#standardized-patterns" title="Permanent link">&para;</a></h3>
<ul>
<li>Removed unused mock dependencies</li>
<li>Clear separation between integration and unit test patterns</li>
<li>Consistent fixture usage across tests</li>
</ul>
<h2 id="impact-assessment">Impact Assessment<a class="headerlink" href="#impact-assessment" title="Permanent link">&para;</a></h2>
<h3 id="immediate-benefits"><strong>Immediate Benefits</strong><a class="headerlink" href="#immediate-benefits" title="Permanent link">&para;</a></h3>
<ul>
<li>✅ Mock assertion failures eliminated</li>
<li>✅ Test reliability improved</li>
<li>✅ Clear test type classification</li>
</ul>
<h3 id="long-term-benefits"><strong>Long-term Benefits</strong><a class="headerlink" href="#long-term-benefits" title="Permanent link">&para;</a></h3>
<ul>
<li>✅ Standardized mock factories available for future tests</li>
<li>✅ ID-agnostic assertion helpers prevent similar issues</li>
<li>✅ Clear patterns for other test file refactoring</li>
</ul>
<h3 id="risk-mitigation"><strong>Risk Mitigation</strong><a class="headerlink" href="#risk-mitigation" title="Permanent link">&para;</a></h3>
<ul>
<li>✅ No breaking changes to test functionality</li>
<li>✅ All existing tests continue to pass</li>
<li>✅ Test execution time maintained</li>
</ul>
<h2 id="next-steps">Next Steps<a class="headerlink" href="#next-steps" title="Permanent link">&para;</a></h2>
<h3 id="immediate"><strong>Immediate</strong><a class="headerlink" href="#immediate" title="Permanent link">&para;</a></h3>
<ol>
<li>Apply similar patterns to other API test files</li>
<li>Continue with Task 2.1.4: Refactor User Routes Unit Tests</li>
<li>Validate standardized patterns across test suite</li>
</ol>
<h3 id="future-improvements"><strong>Future Improvements</strong><a class="headerlink" href="#future-improvements" title="Permanent link">&para;</a></h3>
<ol>
<li>Create test documentation with clear patterns</li>
<li>Add linting rules to prevent mock/real pattern mixing</li>
<li>Implement test data factories for consistent entity creation</li>
</ol>
<h2 id="success-criteria-validation">Success Criteria Validation<a class="headerlink" href="#success-criteria-validation" title="Permanent link">&para;</a></h2>
<table>
<thead>
<tr>
<th><strong>Criteria</strong></th>
<th><strong>Status</strong></th>
<th><strong>Evidence</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td>All component route unit tests pass</td>
<td>✅ <strong>COMPLETE</strong></td>
<td>8/8 tests passing</td>
</tr>
<tr>
<td>Proper mocks for user IDs</td>
<td>✅ <strong>COMPLETE</strong></td>
<td>ID-agnostic assertions implemented</td>
</tr>
<tr>
<td>Fixed dependency injection overrides</td>
<td>✅ <strong>COMPLETE</strong></td>
<td>Service layer properly mocked</td>
</tr>
<tr>
<td>Zero mock assertion failures</td>
<td>✅ <strong>COMPLETE</strong></td>
<td>All tests pass consistently</td>
</tr>
</tbody>
</table>
<p><strong>Quality Gate</strong>: ✅ <strong>PASSED</strong> - All component route unit tests pass with proper mocks</p>
<hr />
<h2 id="conclusion">Conclusion<a class="headerlink" href="#conclusion" title="Permanent link">&para;</a></h2>
<p>Task 2.1.3 has been successfully completed. The component routes unit tests now use standardized mock patterns and proper ID-agnostic assertions. The mock assertion failures have been eliminated, and the tests are more reliable and maintainable. The standardized mock factories and assertion helpers are now available for use in other test files.</p>
<p><strong>Ready to proceed to Task 2.1.4: Refactor User Routes Unit Tests</strong></p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../../js/base.js"></script>
        <script src="../../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
