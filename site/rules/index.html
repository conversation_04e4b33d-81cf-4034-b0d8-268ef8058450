<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/rules/">
        <link rel="shortcut icon" href="../img/favicon.ico">
        <title>Rules - Ultimate Electrical Designer Docs</title>
        <link href="../css/bootstrap.min.css" rel="stylesheet">
        <link href="../css/fontawesome.min.css" rel="stylesheet">
        <link href="../css/brands.min.css" rel="stylesheet">
        <link href="../css/solid.min.css" rel="stylesheet">
        <link href="../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href=".." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="./" class="nav-link active" aria-current="page">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../tasks/" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                            <li class="nav-item">
                                <a rel="prev" href="../tech/" class="nav-link">
                                    <i class="fa fa-arrow-left"></i> Previous
                                </a>
                            </li>
                            <li class="nav-item">
                                <a rel="next" href="../requirements/" class="nav-link">
                                    Next <i class="fa fa-arrow-right"></i>
                                </a>
                            </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#development-rules-standards" class="nav-link">Development Rules &amp; Standards</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#ultimate-electrical-designer" class="nav-link">Ultimate Electrical Designer</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#overview" class="nav-link">Overview</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#zero-tolerance-policies" class="nav-link">Zero Tolerance Policies</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#architectural-rules" class="nav-link">Architectural Rules</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#code-quality-rules" class="nav-link">Code Quality Rules</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#development-workflow-rules" class="nav-link">Development Workflow Rules</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#engineering-standards-compliance" class="nav-link">Engineering Standards Compliance</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#critical-infrastructure-standards-august-2025" class="nav-link">Critical Infrastructure Standards (August 2025)</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="development-rules-standards">Development Rules &amp; Standards<a class="headerlink" href="#development-rules-standards" title="Permanent link">&para;</a></h1>
<h2 id="ultimate-electrical-designer">Ultimate Electrical Designer<a class="headerlink" href="#ultimate-electrical-designer" title="Permanent link">&para;</a></h2>
<p><strong>Document Version:</strong> 2.0 <strong>Last Updated:</strong> August 2025 <strong>Recent Updates:</strong> Critical Infrastructure Fixes Implemented</p>
<hr />
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>This document establishes comprehensive development rules that ensure engineering-grade quality, maintain architectural
consistency, and enforce professional standards suitable for mission-critical electrical design applications.</p>
<hr />
<h2 id="zero-tolerance-policies">Zero Tolerance Policies<a class="headerlink" href="#zero-tolerance-policies" title="Permanent link">&para;</a></h2>
<h3 id="1-code-quality-standards">1. Code Quality Standards<a class="headerlink" href="#1-code-quality-standards" title="Permanent link">&para;</a></h3>
<p><strong>Policy:</strong> Zero tolerance for code quality violations that compromise system reliability or maintainability.</p>
<h4 id="backend-python-requirements">Backend (Python) Requirements<a class="headerlink" href="#backend-python-requirements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>100% MyPy compliance</strong> for all production code</li>
<li><strong>Zero Ruff linting errors</strong> in committed code</li>
<li><strong>Complete type hints</strong> for all public APIs and critical internal functions</li>
<li><strong>Docstring coverage</strong> for all public methods using Google style format</li>
<li><strong>Email normalization enforcement</strong> for all email fields using lowercase normalization and case-insensitive uniqueness
  validation</li>
<li><strong>Application-level string length validation</strong> for all user input fields with specific error messages and boundary
  condition testing</li>
</ul>
<h4 id="frontend-typescript-requirements">Frontend (TypeScript) Requirements<a class="headerlink" href="#frontend-typescript-requirements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Zero ESLint errors</strong> in committed code</li>
<li><strong>Zero Prettier formatting violations</strong> in committed code</li>
<li><strong>Strict TypeScript mode</strong> with no <code>any</code> types in production code</li>
<li><strong>Complete type annotations</strong> for all component props and API interfaces</li>
<li><strong>Frameworks &amp; Libraries</strong>: Next.js 15 (App Router), React Query 5, Zustand 5, MSW for API mocking</li>
<li><strong>Testing</strong>: Vitest (unit), Testing Library (DOM), Playwright (e2e)</li>
<li><strong>Path Aliases</strong>: Use TypeScript path aliases as configured in tsconfig for clean imports</li>
<li><strong>Component Architecture</strong>: All UI must conform to the Atomic Design System. See
  <a href="../atomic-design-system/ATOMIC_DESIGN_GUIDE/">Atomic Design System Guide</a> for component taxonomy, contracts, and usage
  rules.</li>
</ul>
<h3 id="2-testing-standards">2. Testing Standards<a class="headerlink" href="#2-testing-standards" title="Permanent link">&para;</a></h3>
<p><strong>All testing standards, requirements, and policies are documented in:</strong></p>
<p>📋 <strong><a href="../TESTING/#2-testing-standards-requirements">TESTING.md</a></strong> - Authoritative Testing Standards</p>
<p>This includes comprehensive coverage of:</p>
<ul>
<li><strong>Zero tolerance policies</strong> for test failures and quality violations</li>
<li><strong>Coverage requirements</strong> (95%+ pass rate, 100% critical logic, 85%+ other modules)</li>
<li><strong>Quality gates</strong> and success metrics</li>
<li><strong>Real database testing</strong> requirements and patterns</li>
</ul>
<hr />
<h2 id="architectural-rules">Architectural Rules<a class="headerlink" href="#architectural-rules" title="Permanent link">&para;</a></h2>
<h3 id="1-5-layer-architecture-compliance">1. 5-Layer Architecture Compliance<a class="headerlink" href="#1-5-layer-architecture-compliance" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All backend code must strictly adhere to the 5-layer architecture pattern.</p>
<h4 id="layer-responsibilities">Layer Responsibilities<a class="headerlink" href="#layer-responsibilities" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>API Layer</strong> (<code>src/api/</code>): HTTP request/response handling, input validation, authentication</li>
<li><strong>Service Layer</strong> (<code>src/core/services/</code>): Business logic, workflow orchestration, transaction management</li>
<li><strong>Repository Layer</strong> (<code>src/core/repositories/</code>): Data access abstraction, query optimization</li>
<li><strong>Model Layer</strong> (<code>src/core/models/</code>): Data structure definition, relationships, constraints</li>
<li><strong>Schema Layer</strong> (<code>src/core/schemas/</code>): Request/response validation, data transformation</li>
</ol>
<h4 id="dependency-rules">Dependency Rules<a class="headerlink" href="#dependency-rules" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Downward dependencies only</strong>: Higher layers may depend on lower layers, never upward</li>
<li><strong>No layer skipping</strong>: Each layer must interact only with adjacent layers</li>
<li><strong>Interface segregation</strong>: Use dependency injection for loose coupling between layers</li>
</ul>
<h3 id="2-unified-error-handling">2. Unified Error Handling<a class="headerlink" href="#2-unified-error-handling" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All error handling must use the unified error handling system.</p>
<h4 id="required-decorators">Required Decorators<a class="headerlink" href="#required-decorators" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Service layer methods</span>
<span class="nd">@handle_service_errors</span><span class="p">(</span><span class="s2">&quot;method_name&quot;</span><span class="p">)</span>
<span class="nd">@monitor_service_performance</span><span class="p">(</span><span class="s2">&quot;method_name&quot;</span><span class="p">)</span>

<span class="c1"># API endpoints</span>
<span class="nd">@handle_api_errors</span><span class="p">(</span><span class="s2">&quot;endpoint_name&quot;</span><span class="p">)</span>

<span class="c1"># Database operations</span>
<span class="nd">@handle_database_errors</span><span class="p">(</span><span class="s2">&quot;operation_name&quot;</span><span class="p">)</span>

<span class="c1"># Security operations</span>
<span class="nd">@handle_security_errors</span><span class="p">(</span><span class="s2">&quot;security_operation&quot;</span><span class="p">)</span>
</code></pre></div>
<h3 id="3-frontend-module-organization">3. Frontend Module Organization<a class="headerlink" href="#3-frontend-module-organization" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> Frontend code must follow Domain-Driven Design (DDD) principles with clear module boundaries.</p>
<h4 id="module-structure-requirements">Module Structure Requirements<a class="headerlink" href="#module-structure-requirements" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="n">src</span><span class="o">/</span><span class="n">modules</span><span class="o">/</span><span class="p">{</span><span class="n">domain</span><span class="p">}</span><span class="o">/</span>
<span class="err">├──</span> <span class="n">components</span><span class="o">/</span>     <span class="c1"># Domain-specific UI components</span>
<span class="err">├──</span> <span class="n">hooks</span><span class="o">/</span>          <span class="c1"># Domain-specific React hooks</span>
<span class="err">├──</span> <span class="n">services</span><span class="o">/</span>       <span class="c1"># Domain API services</span>
<span class="err">├──</span> <span class="n">types</span><span class="o">/</span>          <span class="c1"># Domain type definitions</span>
<span class="err">└──</span> <span class="n">__tests__</span><span class="o">/</span>      <span class="c1"># Domain-specific tests</span>
</code></pre></div>
<h3 id="3-schema-design-patterns">3. Schema Design Patterns<a class="headerlink" href="#3-schema-design-patterns" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All Pydantic schemas must follow established patterns for type safety and consistency.</p>
<h4 id="generic-pagination-schema-pattern">Generic Pagination Schema Pattern<a class="headerlink" href="#generic-pagination-schema-pattern" title="Permanent link">&para;</a></h4>
<p><strong>Mandatory Implementation:</strong></p>
<div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">TypeVar</span><span class="p">,</span> <span class="n">Generic</span><span class="p">,</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">pydantic</span><span class="w"> </span><span class="kn">import</span> <span class="n">BaseModel</span><span class="p">,</span> <span class="n">Field</span>

<span class="c1"># Type variable for generic pagination</span>
<span class="n">T</span> <span class="o">=</span> <span class="n">TypeVar</span><span class="p">(</span><span class="s2">&quot;T&quot;</span><span class="p">)</span>

<span class="k">class</span><span class="w"> </span><span class="nc">PaginationSchema</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Schema for pagination parameters.&quot;&quot;&quot;</span>

    <span class="n">page</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Page number (1-based)&quot;</span><span class="p">)</span>
    <span class="n">size</span><span class="p">:</span> <span class="nb">int</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="n">ge</span><span class="o">=</span><span class="mi">1</span><span class="p">,</span> <span class="n">le</span><span class="o">=</span><span class="mi">100</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Items per page&quot;</span><span class="p">)</span>
    <span class="n">total</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Total number of items&quot;</span><span class="p">)</span>
    <span class="n">pages</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="nb">int</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="kc">None</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Total number of pages&quot;</span><span class="p">)</span>

<span class="k">class</span><span class="w"> </span><span class="nc">PaginatedResponseSchema</span><span class="p">(</span><span class="n">BaseModel</span><span class="p">,</span> <span class="n">Generic</span><span class="p">[</span><span class="n">T</span><span class="p">]):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Generic paginated response schema.&quot;&quot;&quot;</span>

    <span class="n">items</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">T</span><span class="p">]</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;List of items&quot;</span><span class="p">)</span>
    <span class="n">pagination</span><span class="p">:</span> <span class="n">PaginationSchema</span> <span class="o">=</span> <span class="n">Field</span><span class="p">(</span><span class="o">...</span><span class="p">,</span> <span class="n">description</span><span class="o">=</span><span class="s2">&quot;Pagination information&quot;</span><span class="p">)</span>

<span class="c1"># Usage - REQUIRED for all paginated endpoints:</span>
<span class="k">class</span><span class="w"> </span><span class="nc">EntityListResponseSchema</span><span class="p">(</span><span class="n">PaginatedResponseSchema</span><span class="p">[</span><span class="n">EntityReadSchema</span><span class="p">]):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Paginated response schema for entities.&quot;&quot;&quot;</span>
    <span class="k">pass</span>
</code></pre></div>
<h4 id="schema-validation-requirements">Schema Validation Requirements<a class="headerlink" href="#schema-validation-requirements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Generic Types</strong>: All paginated responses must use <code>PaginatedResponseSchema[T]</code></li>
<li><strong>Type Parameters</strong>: Must specify exact entity type (e.g., <code>TaskReadSchema</code>, <code>ComponentReadSchema</code>)</li>
<li><strong>MyPy Compliance</strong>: All schemas must pass MyPy type checking without errors</li>
<li><strong>ValidationInfo</strong>: All field validators must include proper type annotations</li>
</ul>
<p><strong>Example Validator Pattern:</strong></p>
<div class="highlight"><pre><span></span><code><span class="kn">from</span><span class="w"> </span><span class="nn">pydantic</span><span class="w"> </span><span class="kn">import</span> <span class="n">field_validator</span><span class="p">,</span> <span class="n">ValidationInfo</span>

<span class="nd">@field_validator</span><span class="p">(</span><span class="s2">&quot;field_name&quot;</span><span class="p">)</span>
<span class="nd">@classmethod</span>
<span class="k">def</span><span class="w"> </span><span class="nf">validate_field</span><span class="p">(</span><span class="bp">cls</span><span class="p">,</span> <span class="n">v</span><span class="p">:</span> <span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">],</span> <span class="n">info</span><span class="p">:</span> <span class="n">ValidationInfo</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">Optional</span><span class="p">[</span><span class="n">datetime</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Validate field with proper type annotations.&quot;&quot;&quot;</span>
    <span class="c1"># Validation logic here</span>
    <span class="k">return</span> <span class="n">v</span>
</code></pre></div>
<h4 id="migration-requirements">Migration Requirements<a class="headerlink" href="#migration-requirements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Legacy Schemas</strong>: All non-generic pagination schemas must be migrated to generic pattern</li>
<li><strong>Type Safety</strong>: Zero tolerance for missing type parameters in generic schemas</li>
<li><strong>Consistency</strong>: All endpoints must use the same pagination structure</li>
</ul>
<hr />
<h2 id="code-quality-rules">Code Quality Rules<a class="headerlink" href="#code-quality-rules" title="Permanent link">&para;</a></h2>
<h3 id="1-type-safety-requirements">1. Type Safety Requirements<a class="headerlink" href="#1-type-safety-requirements" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> Complete type safety across the entire codebase.</p>
<h3 id="2-documentation-standards">2. Documentation Standards<a class="headerlink" href="#2-documentation-standards" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> Complete documentation for all public APIs and complex business logic.</p>
<h4 id="required-documentation">Required Documentation<a class="headerlink" href="#required-documentation" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>API endpoints</strong>: OpenAPI/Swagger documentation with examples</li>
<li><strong>Component props</strong>: TypeScript interfaces with JSDoc comments</li>
<li><strong>Business logic</strong>: Inline comments explaining engineering calculations</li>
<li><strong>Configuration</strong>: Environment variables and settings documentation</li>
</ul>
<h3 id="3-performance-standards">3. Performance Standards<a class="headerlink" href="#3-performance-standards" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All code must meet performance benchmarks for professional electrical design applications.</p>
<h4 id="performance-requirements">Performance Requirements<a class="headerlink" href="#performance-requirements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>API Response Time</strong>: &lt; 200ms for standard operations</li>
<li><strong>Calculation Performance</strong>: &lt; 500ms for complex electrical calculations</li>
<li><strong>Memory Usage</strong>: &lt; 100MB for typical calculation operations</li>
<li><strong>Database Queries</strong>: &lt; 100ms for standard CRUD operations</li>
</ul>
<hr />
<h2 id="development-workflow-rules">Development Workflow Rules<a class="headerlink" href="#development-workflow-rules" title="Permanent link">&para;</a></h2>
<h3 id="1-git-workflow-standards">1. Git Workflow Standards<a class="headerlink" href="#1-git-workflow-standards" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> Structured Git workflow with conventional commits and protected branches.</p>
<h4 id="commit-message-format">Commit Message Format<a class="headerlink" href="#commit-message-format" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code>type(scope): description

feat(auth): implement JWT token refresh mechanism
fix(calc): correct voltage drop calculation for aluminum conductors
docs(api): update component management endpoint documentation
test(e2e): add comprehensive authentication flow tests
</code></pre></div>
<h4 id="branch-protection-rules">Branch Protection Rules<a class="headerlink" href="#branch-protection-rules" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Main branch</strong>: Requires pull request review and passing CI/CD</li>
<li><strong>Feature branches</strong>: Must be up-to-date with main before merging</li>
<li><strong>Hotfix branches</strong>: Emergency fixes with expedited review process</li>
</ul>
<h3 id="2-code-review-standards">2. Code Review Standards<a class="headerlink" href="#2-code-review-standards" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All code changes require peer review with specific quality gates.</p>
<h4 id="review-checklist">Review Checklist<a class="headerlink" href="#review-checklist" title="Permanent link">&para;</a></h4>
<ul>
<li>[ ] <strong>Architecture compliance</strong>: Follows 5-layer pattern and DDD principles</li>
<li>[ ] <strong>Type safety</strong>: Complete type annotations and MyPy/TypeScript compliance</li>
<li>[ ] <strong>Test coverage</strong>: Adequate test coverage with passing tests</li>
<li>[ ] <strong>Documentation</strong>: Updated documentation for API changes</li>
<li>[ ] <strong>Performance</strong>: No performance regressions introduced</li>
<li>[ ] <strong>Security</strong>: No security vulnerabilities or authentication bypasses</li>
</ul>
<h3 id="3-deployment-standards">3. Deployment Standards<a class="headerlink" href="#3-deployment-standards" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> Automated deployment with quality gates and rollback capabilities.</p>
<h4 id="deployment-pipeline">Deployment Pipeline<a class="headerlink" href="#deployment-pipeline" title="Permanent link">&para;</a></h4>
<ol>
<li><strong>Pre-deployment</strong>: All tests pass, type checking passes, security scan passes</li>
<li><strong>Staging deployment</strong>: Automated deployment to staging environment</li>
<li><strong>Integration testing</strong>: Full E2E test suite execution</li>
<li><strong>Production deployment</strong>: Blue-green deployment with health checks</li>
<li><strong>Post-deployment</strong>: Monitoring and alerting validation</li>
</ol>
<hr />
<h2 id="engineering-standards-compliance">Engineering Standards Compliance<a class="headerlink" href="#engineering-standards-compliance" title="Permanent link">&para;</a></h2>
<h3 id="1-iecen-standards-integration">1. IEC/EN Standards Integration<a class="headerlink" href="#1-iecen-standards-integration" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All electrical calculations must comply with relevant international standards.</p>
<h4 id="standards-implementation">Standards Implementation<a class="headerlink" href="#standards-implementation" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>IEC Standards</strong>: International electrotechnical commission standards validation</li>
<li><strong>EN Standards</strong>: European electrical standards verification</li>
<li><strong>Code Compliance</strong>: Automated validation against local electrical codes</li>
</ul>
<h3 id="2-calculation-accuracy-requirements">2. Calculation Accuracy Requirements<a class="headerlink" href="#2-calculation-accuracy-requirements" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> Engineering calculations must meet professional accuracy standards.</p>
<h4 id="accuracy-standards">Accuracy Standards<a class="headerlink" href="#accuracy-standards" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Voltage Drop Calculations</strong>: ±0.1% accuracy compared to manual calculations</li>
<li><strong>Load Calculations</strong>: ±0.5% accuracy for demand factor applications</li>
<li><strong>Short Circuit Analysis</strong>: ±1% accuracy for fault current calculations</li>
<li><strong>Heat Tracing Calculations</strong>: ±2% accuracy for thermal analysis</li>
</ul>
<h3 id="3-professional-documentation-standards">3. Professional Documentation Standards<a class="headerlink" href="#3-professional-documentation-standards" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All generated documentation must meet professional engineering standards.</p>
<h4 id="documentation-requirements">Documentation Requirements<a class="headerlink" href="#documentation-requirements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Calculation Reports</strong>: Include methodology, assumptions, and references</li>
<li><strong>Component Specifications</strong>: Complete technical specifications with manufacturer data</li>
<li><strong>Compliance Certificates</strong>: Official documentation for regulatory submissions</li>
<li><strong>Drawing Integration</strong>: Seamless integration with CAD systems</li>
</ul>
<hr />
<h2 id="critical-infrastructure-standards-august-2025">Critical Infrastructure Standards (August 2025)<a class="headerlink" href="#critical-infrastructure-standards-august-2025" title="Permanent link">&para;</a></h2>
<h3 id="1-test-infrastructure-requirements">1. Test Infrastructure Requirements<a class="headerlink" href="#1-test-infrastructure-requirements" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All new development must follow the established testing patterns implemented during the critical
infrastructure stabilization.</p>
<h4 id="mandatory-testing-patterns">Mandatory Testing Patterns<a class="headerlink" href="#mandatory-testing-patterns" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Transaction Isolation</strong>: All tests must run in isolated transactions with automatic rollback</li>
<li><strong>Unique Data Generation</strong>: All test data must include UUID-based unique identifiers</li>
<li><strong>Async/Await Compliance</strong>: All async operations must be properly awaited</li>
<li><strong>Session Management</strong>: Async fixtures must use async dependencies exclusively</li>
<li><strong>Error Recovery</strong>: All database operations must include transaction recovery mechanisms</li>
</ul>
<h4 id="code-examples">Code Examples<a class="headerlink" href="#code-examples" title="Permanent link">&para;</a></h4>
<p><strong>Required Test Data Pattern:</strong></p>
<div class="highlight"><pre><span></span><code><span class="kn">import</span><span class="w"> </span><span class="nn">uuid</span>
<span class="n">unique_suffix</span> <span class="o">=</span> <span class="nb">str</span><span class="p">(</span><span class="n">uuid</span><span class="o">.</span><span class="n">uuid4</span><span class="p">())[:</span><span class="mi">8</span><span class="p">]</span>
<span class="n">user</span> <span class="o">=</span> <span class="n">User</span><span class="p">(</span>
    <span class="n">name</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;Test User </span><span class="si">{</span><span class="n">unique_suffix</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
    <span class="n">email</span><span class="o">=</span><span class="sa">f</span><span class="s2">&quot;test.</span><span class="si">{</span><span class="n">unique_suffix</span><span class="si">}</span><span class="s2">@example.com&quot;</span>
<span class="p">)</span>
</code></pre></div>
<p><strong>Required Async Pattern:</strong></p>
<div class="highlight"><pre><span></span><code><span class="c1"># CORRECT</span>
<span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="n">async_repository</span><span class="o">.</span><span class="n">get_by_id</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

<span class="c1"># INCORRECT - Will cause test failures</span>
<span class="n">result</span> <span class="o">=</span> <span class="n">async_repository</span><span class="o">.</span><span class="n">get_by_id</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
</code></pre></div>
<h3 id="2-database-schema-compliance">2. Database Schema Compliance<a class="headerlink" href="#2-database-schema-compliance" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All database queries must match the actual database schema exactly.</p>
<h4 id="schema-validation-requirements_1">Schema Validation Requirements<a class="headerlink" href="#schema-validation-requirements_1" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Column Existence</strong>: All referenced columns must exist in the target table</li>
<li><strong>Table Names</strong>: Use exact table names as defined in the database</li>
<li><strong>Data Types</strong>: Ensure query parameters match column data types</li>
<li><strong>Constraints</strong>: Respect all database constraints and relationships</li>
</ul>
<h3 id="3-session-isolation-standards">3. Session Isolation Standards<a class="headerlink" href="#3-session-isolation-standards" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> Maintain strict separation between sync and async database sessions.</p>
<h4 id="session-management-requirements">Session Management Requirements<a class="headerlink" href="#session-management-requirements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Fixture Dependencies</strong>: Async fixtures must only depend on other async fixtures</li>
<li><strong>Session Scope</strong>: Use function-scoped sessions for test isolation</li>
<li><strong>Transaction Management</strong>: Implement automatic rollback for all test transactions</li>
<li><strong>Error Handling</strong>: Include transaction recovery for aborted transactions</li>
</ul>
<h3 id="4-quality-assurance-verification">4. Quality Assurance Verification<a class="headerlink" href="#4-quality-assurance-verification" title="Permanent link">&para;</a></h3>
<p><strong>Rule:</strong> All changes must maintain the established 82.9% test pass rate or higher.</p>
<h4 id="verification-requirements">Verification Requirements<a class="headerlink" href="#verification-requirements" title="Permanent link">&para;</a></h4>
<ul>
<li><strong>Pre-commit Testing</strong>: Run relevant test suites before committing changes</li>
<li><strong>Infrastructure Integrity</strong>: Ensure no regression in core testing infrastructure</li>
<li><strong>Documentation Updates</strong>: Update relevant documentation for any pattern changes</li>
<li><strong>Performance Monitoring</strong>: Monitor test execution times and resource usage</li>
</ul>
<p><strong>Reference Documentation:</strong></p>
<ul>
<li><code>docs/CRITICAL_FIXES_CHANGELOG.md</code> - Complete technical implementation details</li>
<li><code>docs/TESTING.md</code> - Updated testing standards and best practices</li>
</ul></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../js/base.js"></script>
        <script src="../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
