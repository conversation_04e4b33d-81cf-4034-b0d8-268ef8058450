<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/developer-guides/synchronization-developer-guide/">
        <link rel="shortcut icon" href="../../img/favicon.ico">
        <title>Synchronization Guide - Ultimate Electrical Designer Docs</title>
        <link href="../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../css/brands.min.css" rel="stylesheet">
        <link href="../../css/solid.min.css" rel="stylesheet">
        <link href="../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../tasks/" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle active" aria-current="page" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="./" class="dropdown-item active" aria-current="page">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                            <li class="nav-item">
                                <a rel="prev" href="../../TESTING/" class="nav-link">
                                    <i class="fa fa-arrow-left"></i> Previous
                                </a>
                            </li>
                            <li class="nav-item">
                                <a rel="next" href="../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="nav-link">
                                    Next <i class="fa fa-arrow-right"></i>
                                </a>
                            </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#synchronization-offline-development-guide" class="nav-link">Synchronization &amp; Offline Development Guide</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#ultimate-electrical-designer" class="nav-link">Ultimate Electrical Designer</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#table-of-contents" class="nav-link">Table of Contents</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#overview" class="nav-link">Overview</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#adding-new-entities-to-sync" class="nav-link">Adding New Entities to Sync</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#debugging-synchronization-issues" class="nav-link">Debugging Synchronization Issues</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#extending-conflict-resolution" class="nav-link">Extending Conflict Resolution</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#best-practices-for-offline-mutations" class="nav-link">Best Practices for Offline Mutations</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#testing-guidelines" class="nav-link">Testing Guidelines</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#performance-optimization" class="nav-link">Performance Optimization</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#troubleshooting-common-issues" class="nav-link">Troubleshooting Common Issues</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#production-deployment-considerations" class="nav-link">Production Deployment Considerations</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#api-reference" class="nav-link">API Reference</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="synchronization-offline-development-guide">Synchronization &amp; Offline Development Guide<a class="headerlink" href="#synchronization-offline-development-guide" title="Permanent link">&para;</a></h1>
<h2 id="ultimate-electrical-designer">Ultimate Electrical Designer<a class="headerlink" href="#ultimate-electrical-designer" title="Permanent link">&para;</a></h2>
<p><strong>Document Version:</strong> 1.0<br />
<strong>Last Updated:</strong> July 2025<br />
<strong>Target Audience:</strong> Developers, Technical Leads, System Integrators<br />
<strong>Prerequisites:</strong> Understanding of React Query, IndexedDB, PostgreSQL, and FastAPI  </p>
<hr />
<h2 id="table-of-contents">Table of Contents<a class="headerlink" href="#table-of-contents" title="Permanent link">&para;</a></h2>
<ol>
<li><a href="#overview">Overview</a></li>
<li><a href="#adding-new-entities-to-sync">Adding New Entities to Sync</a></li>
<li><a href="#debugging-synchronization-issues">Debugging Synchronization Issues</a></li>
<li><a href="#extending-conflict-resolution">Extending Conflict Resolution</a></li>
<li><a href="#best-practices-for-offline-mutations">Best Practices for Offline Mutations</a></li>
<li><a href="#testing-guidelines">Testing Guidelines</a></li>
<li><a href="#performance-optimization">Performance Optimization</a></li>
<li><a href="#troubleshooting-common-issues">Troubleshooting Common Issues</a></li>
<li><a href="#production-deployment-considerations">Production Deployment Considerations</a></li>
<li><a href="#api-reference">API Reference</a></li>
</ol>
<hr />
<h2 id="overview">Overview<a class="headerlink" href="#overview" title="Permanent link">&para;</a></h2>
<p>The Ultimate Electrical Designer implements a comprehensive <strong>Unified Local Database &amp; Synchronization</strong> system that provides seamless offline capabilities and intelligent data synchronization. This guide provides practical guidance for developers working with this system.</p>
<h3 id="system-components">System Components<a class="headerlink" href="#system-components" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code>graph LR
    subgraph Frontend
        RQ[React Query]
        OM[useOfflineMutation]
        SM[SyncManager]
        IDB[IndexedDB]
    end

    subgraph Backend
        SS[SynchronizationService]
        CDC[Change Data Capture]
        CR[Conflict Resolution]
    end

    subgraph Databases
        LDB[(Local PostgreSQL)]
        CDB[(Central PostgreSQL)]
    end

    OM --&gt; IDB
    SM --&gt; SS
    SS --&gt; CDC
    SS --&gt; CR
    SS --&gt; LDB
    SS --&gt; CDB
</code></pre></div>
<h3 id="key-concepts">Key Concepts<a class="headerlink" href="#key-concepts" title="Permanent link">&para;</a></h3>
<ul>
<li><strong>Outbox Pattern</strong>: Client-side queuing of offline operations</li>
<li><strong>Change Data Capture (CDC)</strong>: Timestamp-based change detection</li>
<li><strong>Last-Write Wins</strong>: Primary conflict resolution strategy</li>
<li><strong>Bi-directional Sync</strong>: Data flows between local and central databases</li>
<li><strong>Optimistic Updates</strong>: Immediate UI updates with rollback capability</li>
</ul>
<hr />
<h2 id="adding-new-entities-to-sync">Adding New Entities to Sync<a class="headerlink" href="#adding-new-entities-to-sync" title="Permanent link">&para;</a></h2>
<h3 id="step-1-backend-model-enhancement">Step 1: Backend Model Enhancement<a class="headerlink" href="#step-1-backend-model-enhancement" title="Permanent link">&para;</a></h3>
<p>Add synchronization metadata to your entity model:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># server/src/core/models/your_entity.py</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sqlalchemy</span><span class="w"> </span><span class="kn">import</span> <span class="n">Column</span><span class="p">,</span> <span class="n">DateTime</span><span class="p">,</span> <span class="n">Integer</span><span class="p">,</span> <span class="n">String</span><span class="p">,</span> <span class="n">Boolean</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">sqlalchemy.sql</span><span class="w"> </span><span class="kn">import</span> <span class="n">func</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">.base</span><span class="w"> </span><span class="kn">import</span> <span class="n">Base</span>

<span class="k">class</span><span class="w"> </span><span class="nc">YourEntity</span><span class="p">(</span><span class="n">Base</span><span class="p">):</span>
    <span class="n">__tablename__</span> <span class="o">=</span> <span class="s2">&quot;your_entity&quot;</span>

    <span class="nb">id</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">primary_key</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
    <span class="n">name</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">100</span><span class="p">),</span> <span class="n">nullable</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>
    <span class="n">description</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">String</span><span class="p">(</span><span class="mi">500</span><span class="p">))</span>

    <span class="c1"># 🔥 REQUIRED: Synchronization metadata</span>
    <span class="n">created_at</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">server_default</span><span class="o">=</span><span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">())</span>
    <span class="n">updated_at</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">DateTime</span><span class="p">(</span><span class="n">timezone</span><span class="o">=</span><span class="kc">True</span><span class="p">),</span> <span class="n">server_default</span><span class="o">=</span><span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">(),</span> <span class="n">onupdate</span><span class="o">=</span><span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">())</span>
    <span class="n">is_deleted</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Boolean</span><span class="p">,</span> <span class="n">default</span><span class="o">=</span><span class="kc">False</span><span class="p">)</span>  <span class="c1"># Soft delete for sync</span>

    <span class="c1"># Optional: Project association for scoped sync</span>
    <span class="n">project_id</span> <span class="o">=</span> <span class="n">Column</span><span class="p">(</span><span class="n">Integer</span><span class="p">,</span> <span class="n">ForeignKey</span><span class="p">(</span><span class="s1">&#39;project.id&#39;</span><span class="p">))</span>
</code></pre></div>
<h3 id="step-2-repository-change-detection">Step 2: Repository Change Detection<a class="headerlink" href="#step-2-repository-change-detection" title="Permanent link">&para;</a></h3>
<p>Add CDC support to your repository:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># server/src/core/repositories/your_entity_repository.py</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">datetime</span><span class="w"> </span><span class="kn">import</span> <span class="n">datetime</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">typing</span><span class="w"> </span><span class="kn">import</span> <span class="n">List</span><span class="p">,</span> <span class="n">Optional</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">.base</span><span class="w"> </span><span class="kn">import</span> <span class="n">BaseRepository</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">..models.your_entity</span><span class="w"> </span><span class="kn">import</span> <span class="n">YourEntity</span>

<span class="k">class</span><span class="w"> </span><span class="nc">YourEntityRepository</span><span class="p">(</span><span class="n">BaseRepository</span><span class="p">[</span><span class="n">YourEntity</span><span class="p">]):</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">get_changes_since</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> 
        <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> 
        <span class="n">since_timestamp</span><span class="p">:</span> <span class="n">datetime</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">ChangeRecord</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Get all changes for entities since the specified timestamp.</span>

<span class="sd">        Returns:</span>
<span class="sd">            List of ChangeRecord objects with operation type and data</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="c1"># Query for all changes since timestamp</span>
        <span class="n">query</span> <span class="o">=</span> <span class="n">select</span><span class="p">(</span><span class="n">YourEntity</span><span class="p">)</span><span class="o">.</span><span class="n">where</span><span class="p">(</span>
            <span class="n">and_</span><span class="p">(</span>
                <span class="n">YourEntity</span><span class="o">.</span><span class="n">project_id</span> <span class="o">==</span> <span class="n">project_id</span><span class="p">,</span>
                <span class="n">YourEntity</span><span class="o">.</span><span class="n">updated_at</span> <span class="o">&gt;</span> <span class="n">since_timestamp</span>
            <span class="p">)</span>
        <span class="p">)</span><span class="o">.</span><span class="n">order_by</span><span class="p">(</span><span class="n">YourEntity</span><span class="o">.</span><span class="n">updated_at</span><span class="p">)</span>

        <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">db_session</span><span class="o">.</span><span class="n">execute</span><span class="p">(</span><span class="n">query</span><span class="p">)</span>
        <span class="n">entities</span> <span class="o">=</span> <span class="n">result</span><span class="o">.</span><span class="n">scalars</span><span class="p">()</span><span class="o">.</span><span class="n">all</span><span class="p">()</span>

        <span class="c1"># Convert to ChangeRecord format</span>
        <span class="n">changes</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="k">for</span> <span class="n">entity</span> <span class="ow">in</span> <span class="n">entities</span><span class="p">:</span>
            <span class="c1"># Determine operation type</span>
            <span class="k">if</span> <span class="n">entity</span><span class="o">.</span><span class="n">created_at</span> <span class="o">==</span> <span class="n">entity</span><span class="o">.</span><span class="n">updated_at</span><span class="p">:</span>
                <span class="n">operation</span> <span class="o">=</span> <span class="s1">&#39;create&#39;</span>
            <span class="k">elif</span> <span class="n">entity</span><span class="o">.</span><span class="n">is_deleted</span><span class="p">:</span>
                <span class="n">operation</span> <span class="o">=</span> <span class="s1">&#39;delete&#39;</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">operation</span> <span class="o">=</span> <span class="s1">&#39;update&#39;</span>

            <span class="n">changes</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">ChangeRecord</span><span class="p">(</span>
                <span class="n">entity_type</span><span class="o">=</span><span class="s1">&#39;your_entity&#39;</span><span class="p">,</span>
                <span class="n">entity_id</span><span class="o">=</span><span class="n">entity</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
                <span class="n">operation</span><span class="o">=</span><span class="n">operation</span><span class="p">,</span>
                <span class="n">data</span><span class="o">=</span><span class="n">entity</span><span class="o">.</span><span class="n">to_dict</span><span class="p">(),</span>
                <span class="n">timestamp</span><span class="o">=</span><span class="n">entity</span><span class="o">.</span><span class="n">updated_at</span>
            <span class="p">))</span>

        <span class="k">return</span> <span class="n">changes</span>

    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">apply_changes</span><span class="p">(</span>
        <span class="bp">self</span><span class="p">,</span> 
        <span class="n">changes</span><span class="p">:</span> <span class="n">List</span><span class="p">[</span><span class="n">ChangeRecord</span><span class="p">]</span>
    <span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">ApplyResult</span><span class="p">]:</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">        Apply a list of changes to the database.</span>

<span class="sd">        Handles:</span>
<span class="sd">        - Create operations (INSERT)</span>
<span class="sd">        - Update operations (UPDATE)</span>
<span class="sd">        - Delete operations (soft delete)</span>
<span class="sd">        - Conflict detection and resolution</span>
<span class="sd">        &quot;&quot;&quot;</span>

        <span class="n">results</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="k">for</span> <span class="n">change</span> <span class="ow">in</span> <span class="n">changes</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">change</span><span class="o">.</span><span class="n">operation</span> <span class="o">==</span> <span class="s1">&#39;create&#39;</span><span class="p">:</span>
                    <span class="c1"># Create new entity</span>
                    <span class="n">entity</span> <span class="o">=</span> <span class="n">YourEntity</span><span class="p">(</span><span class="o">**</span><span class="n">change</span><span class="o">.</span><span class="n">data</span><span class="p">)</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">db_session</span><span class="o">.</span><span class="n">add</span><span class="p">(</span><span class="n">entity</span><span class="p">)</span>
                    <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">ApplyResult</span><span class="p">(</span>
                        <span class="n">success</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                        <span class="n">operation</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">operation</span><span class="p">,</span>
                        <span class="n">entity_id</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">entity_id</span>
                    <span class="p">))</span>

                <span class="k">elif</span> <span class="n">change</span><span class="o">.</span><span class="n">operation</span> <span class="o">==</span> <span class="s1">&#39;update&#39;</span><span class="p">:</span>
                    <span class="c1"># Update existing entity</span>
                    <span class="n">entity</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_by_id</span><span class="p">(</span><span class="n">change</span><span class="o">.</span><span class="n">entity_id</span><span class="p">)</span>
                    <span class="k">if</span> <span class="n">entity</span><span class="p">:</span>
                        <span class="c1"># Check for conflicts</span>
                        <span class="k">if</span> <span class="n">entity</span><span class="o">.</span><span class="n">updated_at</span> <span class="o">&gt;</span> <span class="n">change</span><span class="o">.</span><span class="n">timestamp</span><span class="p">:</span>
                            <span class="c1"># Conflict detected - newer version exists</span>
                            <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">ApplyResult</span><span class="p">(</span>
                                <span class="n">success</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                                <span class="n">operation</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">operation</span><span class="p">,</span>
                                <span class="n">entity_id</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">entity_id</span><span class="p">,</span>
                                <span class="n">conflict</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                <span class="n">conflict_data</span><span class="o">=</span><span class="n">entity</span><span class="o">.</span><span class="n">to_dict</span><span class="p">()</span>
                            <span class="p">))</span>
                        <span class="k">else</span><span class="p">:</span>
                            <span class="c1"># Apply update</span>
                            <span class="k">for</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="ow">in</span> <span class="n">change</span><span class="o">.</span><span class="n">data</span><span class="o">.</span><span class="n">items</span><span class="p">():</span>
                                <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">entity</span><span class="p">,</span> <span class="n">key</span><span class="p">):</span>
                                    <span class="nb">setattr</span><span class="p">(</span><span class="n">entity</span><span class="p">,</span> <span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
                            <span class="n">entity</span><span class="o">.</span><span class="n">updated_at</span> <span class="o">=</span> <span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">()</span>
                            <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">ApplyResult</span><span class="p">(</span>
                                <span class="n">success</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                                <span class="n">operation</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">operation</span><span class="p">,</span>
                                <span class="n">entity_id</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">entity_id</span>
                            <span class="p">))</span>

                <span class="k">elif</span> <span class="n">change</span><span class="o">.</span><span class="n">operation</span> <span class="o">==</span> <span class="s1">&#39;delete&#39;</span><span class="p">:</span>
                    <span class="c1"># Soft delete entity</span>
                    <span class="n">entity</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">get_by_id</span><span class="p">(</span><span class="n">change</span><span class="o">.</span><span class="n">entity_id</span><span class="p">)</span>
                    <span class="k">if</span> <span class="n">entity</span><span class="p">:</span>
                        <span class="n">entity</span><span class="o">.</span><span class="n">is_deleted</span> <span class="o">=</span> <span class="kc">True</span>
                        <span class="n">entity</span><span class="o">.</span><span class="n">updated_at</span> <span class="o">=</span> <span class="n">func</span><span class="o">.</span><span class="n">now</span><span class="p">()</span>
                        <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">ApplyResult</span><span class="p">(</span>
                            <span class="n">success</span><span class="o">=</span><span class="kc">True</span><span class="p">,</span>
                            <span class="n">operation</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">operation</span><span class="p">,</span>
                            <span class="n">entity_id</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">entity_id</span>
                        <span class="p">))</span>

                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">db_session</span><span class="o">.</span><span class="n">commit</span><span class="p">()</span>

            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">db_session</span><span class="o">.</span><span class="n">rollback</span><span class="p">()</span>
                <span class="n">results</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">ApplyResult</span><span class="p">(</span>
                    <span class="n">success</span><span class="o">=</span><span class="kc">False</span><span class="p">,</span>
                    <span class="n">operation</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">operation</span><span class="p">,</span>
                    <span class="n">entity_id</span><span class="o">=</span><span class="n">change</span><span class="o">.</span><span class="n">entity_id</span><span class="p">,</span>
                    <span class="n">error</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span>
                <span class="p">))</span>

        <span class="k">return</span> <span class="n">results</span>
</code></pre></div>
<h3 id="step-3-service-integration">Step 3: Service Integration<a class="headerlink" href="#step-3-service-integration" title="Permanent link">&para;</a></h3>
<p>Integrate with the SynchronizationService:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># server/src/core/services/general/synchronization_service.py</span>
<span class="c1"># Add to the SynchronizationService class</span>

<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_get_entity_changes</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span> 
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> 
    <span class="n">since_timestamp</span><span class="p">:</span> <span class="n">datetime</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">List</span><span class="p">[</span><span class="n">ChangeRecord</span><span class="p">]:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Get changes for all synchronized entities.</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">all_changes</span> <span class="o">=</span> <span class="p">[]</span>

    <span class="c1"># Add your entity to the synchronized entities list</span>
    <span class="n">repositories</span> <span class="o">=</span> <span class="p">[</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">project_repo</span><span class="p">,</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">component_repo</span><span class="p">,</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">your_entity_repo</span><span class="p">,</span>  <span class="c1"># 🔥 Add your repository here</span>
        <span class="c1"># ... other repositories</span>
    <span class="p">]</span>

    <span class="k">for</span> <span class="n">repo</span> <span class="ow">in</span> <span class="n">repositories</span><span class="p">:</span>
        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">repo</span><span class="p">,</span> <span class="s1">&#39;get_changes_since&#39;</span><span class="p">):</span>
            <span class="n">changes</span> <span class="o">=</span> <span class="k">await</span> <span class="n">repo</span><span class="o">.</span><span class="n">get_changes_since</span><span class="p">(</span><span class="n">project_id</span><span class="p">,</span> <span class="n">since_timestamp</span><span class="p">)</span>
            <span class="n">all_changes</span><span class="o">.</span><span class="n">extend</span><span class="p">(</span><span class="n">changes</span><span class="p">)</span>

    <span class="k">return</span> <span class="nb">sorted</span><span class="p">(</span><span class="n">all_changes</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="k">lambda</span> <span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="o">.</span><span class="n">timestamp</span><span class="p">)</span>
</code></pre></div>
<h3 id="step-4-frontend-hook-creation">Step 4: Frontend Hook Creation<a class="headerlink" href="#step-4-frontend-hook-creation" title="Permanent link">&para;</a></h3>
<p>Create a typed hook for your entity:</p>
<div class="highlight"><pre><span></span><code><span class="c1">// client/src/hooks/api/useYourEntity.ts</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">useQuery</span><span class="p">,</span><span class="w"> </span><span class="nx">useMutation</span><span class="p">,</span><span class="w"> </span><span class="nx">useQueryClient</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;@tanstack/react-query&#39;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">useOfflineMutation</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;../cache/useOfflineMutation&#39;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">yourEntityApi</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;../../api/yourEntity&#39;</span>
<span class="k">import</span><span class="w"> </span><span class="kr">type</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">YourEntity</span><span class="p">,</span><span class="w"> </span><span class="nx">CreateYourEntityRequest</span><span class="p">,</span><span class="w"> </span><span class="nx">UpdateYourEntityRequest</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;../../types/yourEntity&#39;</span>

<span class="c1">// Query hooks</span>
<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">useYourEntities</span><span class="p">(</span><span class="nx">projectId</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">useQuery</span><span class="p">({</span>
<span class="w">    </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">projectId</span><span class="p">],</span>
<span class="w">    </span><span class="nx">queryFn</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">yourEntityApi</span><span class="p">.</span><span class="nx">getAll</span><span class="p">(</span><span class="nx">projectId</span><span class="p">),</span>
<span class="w">    </span><span class="nx">staleTime</span><span class="o">:</span><span class="w"> </span><span class="kt">1000</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">60</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">5</span><span class="p">,</span><span class="w"> </span><span class="c1">// 5 minutes</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">useYourEntity</span><span class="p">(</span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">useQuery</span><span class="p">({</span>
<span class="w">    </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">id</span><span class="p">],</span>
<span class="w">    </span><span class="nx">queryFn</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">yourEntityApi</span><span class="p">.</span><span class="nx">getById</span><span class="p">(</span><span class="nx">id</span><span class="p">),</span>
<span class="w">    </span><span class="nx">enabled</span><span class="o">:</span><span class="w"> </span><span class="o">!!</span><span class="nx">id</span><span class="p">,</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">}</span>

<span class="c1">// Mutation hooks with offline support</span>
<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">useCreateYourEntity</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">queryClient</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useQueryClient</span><span class="p">()</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">useOfflineMutation</span><span class="o">&lt;</span><span class="nx">YourEntity</span><span class="p">,</span><span class="w"> </span><span class="ne">Error</span><span class="p">,</span><span class="w"> </span><span class="nx">CreateYourEntityRequest</span><span class="o">&gt;</span><span class="p">({</span>
<span class="w">    </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/api/v1/your-entities&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">mutationFn</span><span class="o">:</span><span class="w"> </span><span class="kt">yourEntityApi.create</span><span class="p">,</span>

<span class="w">    </span><span class="c1">// 🔥 IMPORTANT: Optimistic response for offline usage</span>
<span class="w">    </span><span class="nx">optimisticResponse</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">      </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="sb">`temp_</span><span class="si">${</span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span><span class="w"> </span><span class="c1">// Temporary ID</span>
<span class="w">      </span><span class="p">...</span><span class="nx">variables</span><span class="p">,</span>
<span class="w">      </span><span class="nx">created_at</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">().</span><span class="nx">toISOString</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">updated_at</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">().</span><span class="nx">toISOString</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">_optimistic</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span><span class="w"> </span><span class="c1">// Flag for UI indication</span>
<span class="w">    </span><span class="p">}),</span>

<span class="w">    </span><span class="nx">onSuccess</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">data</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Update queries</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">invalidateQueries</span><span class="p">({</span><span class="w"> </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">project_id</span><span class="p">]</span><span class="w"> </span><span class="p">})</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">setQueryData</span><span class="p">([</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">data</span><span class="p">.</span><span class="nx">id</span><span class="p">],</span><span class="w"> </span><span class="nx">data</span><span class="p">)</span>
<span class="w">    </span><span class="p">},</span>

<span class="w">    </span><span class="nx">onError</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Failed to create your entity:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">)</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">useUpdateYourEntity</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">queryClient</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useQueryClient</span><span class="p">()</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">useOfflineMutation</span><span class="o">&lt;</span><span class="nx">YourEntity</span><span class="p">,</span><span class="w"> </span><span class="ne">Error</span><span class="p">,</span><span class="w"> </span><span class="nx">UpdateYourEntityRequest</span><span class="o">&gt;</span><span class="p">({</span>
<span class="w">    </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="sb">`/api/v1/your-entities/</span><span class="si">${</span><span class="nx">variables</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span>
<span class="w">    </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;PUT&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">mutationFn</span><span class="o">:</span><span class="w"> </span><span class="kt">yourEntityApi.update</span><span class="p">,</span>

<span class="w">    </span><span class="nx">onMutate</span><span class="o">:</span><span class="w"> </span><span class="kt">async</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Cancel any outgoing refetches</span>
<span class="w">      </span><span class="k">await</span><span class="w"> </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">cancelQueries</span><span class="p">({</span><span class="w"> </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">]</span><span class="w"> </span><span class="p">})</span>

<span class="w">      </span><span class="c1">// Snapshot previous value</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">previousEntity</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">getQueryData</span><span class="p">([</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">])</span>

<span class="w">      </span><span class="c1">// Optimistic update</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">setQueryData</span><span class="p">([</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">],</span><span class="w"> </span><span class="p">(</span><span class="nx">old</span><span class="o">:</span><span class="w"> </span><span class="kt">YourEntity</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">        </span><span class="p">...</span><span class="nx">old</span><span class="p">,</span>
<span class="w">        </span><span class="p">...</span><span class="nx">variables</span><span class="p">,</span>
<span class="w">        </span><span class="nx">updated_at</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">().</span><span class="nx">toISOString</span><span class="p">(),</span>
<span class="w">        </span><span class="nx">_optimistic</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">      </span><span class="p">}))</span>

<span class="w">      </span><span class="k">return</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">previousEntity</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>

<span class="w">    </span><span class="nx">onError</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">err</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">,</span><span class="w"> </span><span class="nx">context</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Rollback on error</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">context</span><span class="o">?</span><span class="p">.</span><span class="nx">previousEntity</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">setQueryData</span><span class="p">([</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">],</span><span class="w"> </span><span class="nx">context</span><span class="p">.</span><span class="nx">previousEntity</span><span class="p">)</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">},</span>

<span class="w">    </span><span class="nx">onSettled</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Refresh data after mutation</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">invalidateQueries</span><span class="p">({</span><span class="w"> </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;your-entities&#39;</span><span class="p">]</span><span class="w"> </span><span class="p">})</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">useDeleteYourEntity</span><span class="p">()</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">queryClient</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useQueryClient</span><span class="p">()</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">useOfflineMutation</span><span class="o">&lt;</span><span class="ow">void</span><span class="p">,</span><span class="w"> </span><span class="ne">Error</span><span class="p">,</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">;</span><span class="w"> </span><span class="nx">project_id</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="w"> </span><span class="p">}</span><span class="o">&gt;</span><span class="p">({</span>
<span class="w">    </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="sb">`/api/v1/your-entities/</span><span class="si">${</span><span class="nx">variables</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span>
<span class="w">    </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DELETE&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">mutationFn</span><span class="o">:</span><span class="w"> </span><span class="kt">yourEntityApi.delete</span><span class="p">,</span>

<span class="w">    </span><span class="nx">onMutate</span><span class="o">:</span><span class="w"> </span><span class="kt">async</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Remove from list queries optimistically</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">setQueryData</span><span class="p">([</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">project_id</span><span class="p">],</span><span class="w"> </span><span class="p">(</span><span class="nx">old</span><span class="o">:</span><span class="w"> </span><span class="kt">YourEntity</span><span class="p">[])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span>
<span class="w">        </span><span class="nx">old</span><span class="o">?</span><span class="p">.</span><span class="nx">filter</span><span class="p">(</span><span class="nx">entity</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">entity</span><span class="p">.</span><span class="nx">id</span><span class="w"> </span><span class="o">!==</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">)</span><span class="w"> </span><span class="o">||</span><span class="w"> </span><span class="p">[]</span>
<span class="w">      </span><span class="p">)</span>

<span class="w">      </span><span class="c1">// Remove from individual query</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">removeQueries</span><span class="p">({</span><span class="w"> </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">id</span><span class="p">]</span><span class="w"> </span><span class="p">})</span>
<span class="w">    </span><span class="p">},</span>

<span class="w">    </span><span class="nx">onSuccess</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">data</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">queryClient</span><span class="p">.</span><span class="nx">invalidateQueries</span><span class="p">({</span><span class="w"> </span><span class="nx">queryKey</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="s1">&#39;your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="p">.</span><span class="nx">project_id</span><span class="p">]</span><span class="w"> </span><span class="p">})</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="step-5-api-client-implementation">Step 5: API Client Implementation<a class="headerlink" href="#step-5-api-client-implementation" title="Permanent link">&para;</a></h3>
<p>Create the API client:</p>
<div class="highlight"><pre><span></span><code><span class="c1">// client/src/api/yourEntity.ts</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">apiClient</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;./client&#39;</span>
<span class="k">import</span><span class="w"> </span><span class="kr">type</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">YourEntity</span><span class="p">,</span><span class="w"> </span><span class="nx">CreateYourEntityRequest</span><span class="p">,</span><span class="w"> </span><span class="nx">UpdateYourEntityRequest</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;../types/yourEntity&#39;</span>

<span class="k">export</span><span class="w"> </span><span class="kd">const</span><span class="w"> </span><span class="nx">yourEntityApi</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">getAll</span><span class="p">(</span><span class="nx">projectId</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">YourEntity</span><span class="p">[]</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">apiClient</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="sb">`/api/v1/projects/</span><span class="si">${</span><span class="nx">projectId</span><span class="si">}</span><span class="sb">/your-entities`</span><span class="p">)</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">response</span><span class="p">.</span><span class="nx">data</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">getById</span><span class="p">(</span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">YourEntity</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">apiClient</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="sb">`/api/v1/your-entities/</span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">response</span><span class="p">.</span><span class="nx">data</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">create</span><span class="p">(</span><span class="nx">data</span><span class="o">:</span><span class="w"> </span><span class="kt">CreateYourEntityRequest</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">YourEntity</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">apiClient</span><span class="p">.</span><span class="nx">post</span><span class="p">(</span><span class="s1">&#39;/api/v1/your-entities&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">data</span><span class="p">)</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">response</span><span class="p">.</span><span class="nx">data</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">update</span><span class="p">({</span><span class="w"> </span><span class="nx">id</span><span class="p">,</span><span class="w"> </span><span class="p">...</span><span class="nx">data</span><span class="w"> </span><span class="p">}</span><span class="o">:</span><span class="w"> </span><span class="nx">UpdateYourEntityRequest</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">YourEntity</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">response</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">apiClient</span><span class="p">.</span><span class="nx">put</span><span class="p">(</span><span class="sb">`/api/v1/your-entities/</span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span><span class="w"> </span><span class="nx">data</span><span class="p">)</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">response</span><span class="p">.</span><span class="nx">data</span>
<span class="w">  </span><span class="p">},</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="ow">delete</span><span class="p">(</span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">apiClient</span><span class="p">.</span><span class="ow">delete</span><span class="p">(</span><span class="sb">`/api/v1/your-entities/</span><span class="si">${</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<h2 id="debugging-synchronization-issues">Debugging Synchronization Issues<a class="headerlink" href="#debugging-synchronization-issues" title="Permanent link">&para;</a></h2>
<h3 id="backend-debugging">Backend Debugging<a class="headerlink" href="#backend-debugging" title="Permanent link">&para;</a></h3>
<h4 id="1-enable-detailed-logging">1. Enable Detailed Logging<a class="headerlink" href="#1-enable-detailed-logging" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># server/src/core/services/general/synchronization_service.py</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>

<span class="n">logger</span> <span class="o">=</span> <span class="n">logging</span><span class="o">.</span><span class="n">getLogger</span><span class="p">(</span><span class="vm">__name__</span><span class="p">)</span>

<span class="k">class</span><span class="w"> </span><span class="nc">SynchronizationService</span><span class="p">:</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">synchronize_project</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SynchronizationResult</span><span class="p">:</span>
        <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Starting synchronization for project </span><span class="si">{</span><span class="n">project_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Log timing information</span>
            <span class="n">start_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

            <span class="c1"># Get changes with detailed logging</span>
            <span class="n">local_changes</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_local_changes</span><span class="p">(</span><span class="n">project_id</span><span class="p">,</span> <span class="n">last_sync_timestamp</span><span class="p">)</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Found </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">local_changes</span><span class="p">)</span><span class="si">}</span><span class="s2"> local changes&quot;</span><span class="p">)</span>

            <span class="n">central_changes</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_get_central_changes</span><span class="p">(</span><span class="n">project_id</span><span class="p">,</span> <span class="n">last_sync_timestamp</span><span class="p">)</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Found </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">central_changes</span><span class="p">)</span><span class="si">}</span><span class="s2"> central changes&quot;</span><span class="p">)</span>

            <span class="c1"># Log conflicts</span>
            <span class="n">conflicts</span> <span class="o">=</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_detect_conflicts</span><span class="p">(</span><span class="n">local_changes</span><span class="p">,</span> <span class="n">central_changes</span><span class="p">)</span>
            <span class="k">if</span> <span class="n">conflicts</span><span class="p">:</span>
                <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Detected </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">conflicts</span><span class="p">)</span><span class="si">}</span><span class="s2"> conflicts&quot;</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">conflict</span> <span class="ow">in</span> <span class="n">conflicts</span><span class="p">:</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Conflict: </span><span class="si">{</span><span class="n">conflict</span><span class="o">.</span><span class="n">entity_type</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">conflict</span><span class="o">.</span><span class="n">entity_id</span><span class="si">}</span><span class="s2"> - </span><span class="si">{</span><span class="n">conflict</span><span class="o">.</span><span class="n">conflict_type</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

            <span class="c1"># Log completion</span>
            <span class="n">duration</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">start_time</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">info</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Synchronization completed in </span><span class="si">{</span><span class="n">duration</span><span class="si">:</span><span class="s2">.2f</span><span class="si">}</span><span class="s2">s&quot;</span><span class="p">)</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">logger</span><span class="o">.</span><span class="n">error</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Synchronization failed for project </span><span class="si">{</span><span class="n">project_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">exc_info</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
            <span class="k">raise</span>
</code></pre></div>
<h4 id="2-sync-history-query">2. Sync History Query<a class="headerlink" href="#2-sync-history-query" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">-- Query recent synchronization logs</span>
<span class="k">SELECT</span><span class="w"> </span>
<span class="w">    </span><span class="n">project_id</span><span class="p">,</span>
<span class="w">    </span><span class="n">sync_direction</span><span class="p">,</span>
<span class="w">    </span><span class="n">operation_type</span><span class="p">,</span>
<span class="w">    </span><span class="n">conflicts_detected</span><span class="p">,</span>
<span class="w">    </span><span class="n">conflicts_resolved</span><span class="p">,</span>
<span class="w">    </span><span class="n">status</span><span class="p">,</span>
<span class="w">    </span><span class="n">started_at</span><span class="p">,</span>
<span class="w">    </span><span class="n">completed_at</span><span class="p">,</span>
<span class="w">    </span><span class="n">duration_ms</span><span class="p">,</span>
<span class="w">    </span><span class="n">error_message</span>
<span class="k">FROM</span><span class="w"> </span><span class="n">synchronization_log</span><span class="w"> </span>
<span class="k">WHERE</span><span class="w"> </span><span class="n">project_id</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">?</span><span class="w"> </span>
<span class="k">ORDER</span><span class="w"> </span><span class="k">BY</span><span class="w"> </span><span class="n">started_at</span><span class="w"> </span><span class="k">DESC</span><span class="w"> </span>
<span class="k">LIMIT</span><span class="w"> </span><span class="mi">20</span><span class="p">;</span>
</code></pre></div>
<h4 id="3-check-change-detection">3. Check Change Detection<a class="headerlink" href="#3-check-change-detection" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1"># Debug CDC mechanism</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">debug_change_detection</span><span class="p">(</span><span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span> <span class="n">since</span><span class="p">:</span> <span class="n">datetime</span><span class="p">):</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Debug helper to inspect change detection.&quot;&quot;&quot;</span>

    <span class="c1"># Check what changes are detected</span>
    <span class="n">changes</span> <span class="o">=</span> <span class="k">await</span> <span class="n">sync_service</span><span class="o">.</span><span class="n">_get_local_changes</span><span class="p">(</span><span class="n">project_id</span><span class="p">,</span> <span class="n">since</span><span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Changes detected since </span><span class="si">{</span><span class="n">since</span><span class="si">}</span><span class="s2">:&quot;</span><span class="p">)</span>
    <span class="k">for</span> <span class="n">change</span> <span class="ow">in</span> <span class="n">changes</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  </span><span class="si">{</span><span class="n">change</span><span class="o">.</span><span class="n">entity_type</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">change</span><span class="o">.</span><span class="n">entity_id</span><span class="si">}</span><span class="s2"> - </span><span class="si">{</span><span class="n">change</span><span class="o">.</span><span class="n">operation</span><span class="si">}</span><span class="s2"> at </span><span class="si">{</span><span class="n">change</span><span class="o">.</span><span class="n">timestamp</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

    <span class="c1"># Check timestamps</span>
    <span class="n">latest_timestamp</span> <span class="o">=</span> <span class="k">await</span> <span class="n">sync_service</span><span class="o">.</span><span class="n">_get_last_sync_timestamp</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Last sync timestamp: </span><span class="si">{</span><span class="n">latest_timestamp</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</code></pre></div>
<h3 id="frontend-debugging">Frontend Debugging<a class="headerlink" href="#frontend-debugging" title="Permanent link">&para;</a></h3>
<h4 id="1-outbox-inspection">1. Outbox Inspection<a class="headerlink" href="#1-outbox-inspection" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// Debug utility for inspecting outbox contents</span>
<span class="k">export</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">debugOutbox</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">openDB</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">import</span><span class="p">(</span><span class="s1">&#39;idb&#39;</span><span class="p">)</span>

<span class="w">  </span><span class="k">try</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">db</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">openDB</span><span class="p">(</span><span class="s1">&#39;query-cache&#39;</span><span class="p">,</span><span class="w"> </span><span class="mf">1</span><span class="p">)</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">transaction</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">db</span><span class="p">.</span><span class="nx">transaction</span><span class="p">(</span><span class="s1">&#39;mutation-outbox&#39;</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;readonly&#39;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">store</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">transaction</span><span class="p">.</span><span class="nx">objectStore</span><span class="p">(</span><span class="s1">&#39;mutation-outbox&#39;</span><span class="p">)</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">mutations</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">store</span><span class="p">.</span><span class="nx">getAll</span><span class="p">()</span>

<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">group</span><span class="p">(</span><span class="s1">&#39;🔍 Outbox Inspection&#39;</span><span class="p">)</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Total mutations: </span><span class="si">${</span><span class="nx">mutations</span><span class="p">.</span><span class="nx">length</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>

<span class="w">    </span><span class="nx">mutations</span><span class="p">.</span><span class="nx">forEach</span><span class="p">((</span><span class="nx">mutation</span><span class="p">,</span><span class="w"> </span><span class="nx">index</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">group</span><span class="p">(</span><span class="sb">`Mutation </span><span class="si">${</span><span class="nx">index</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="mf">1</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`ID: </span><span class="si">${</span><span class="nx">mutation</span><span class="p">.</span><span class="nx">id</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Endpoint: </span><span class="si">${</span><span class="nx">mutation</span><span class="p">.</span><span class="nx">endpoint</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Method: </span><span class="si">${</span><span class="nx">mutation</span><span class="p">.</span><span class="nx">method</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Status: </span><span class="si">${</span><span class="nx">mutation</span><span class="p">.</span><span class="nx">status</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Retry Count: </span><span class="si">${</span><span class="nx">mutation</span><span class="p">.</span><span class="nx">retryCount</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Timestamp: </span><span class="si">${</span><span class="nx">mutation</span><span class="p">.</span><span class="nx">timestamp</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Variables:`</span><span class="p">,</span><span class="w"> </span><span class="nx">mutation</span><span class="p">.</span><span class="nx">variables</span><span class="p">)</span>
<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">mutation</span><span class="p">.</span><span class="nx">lastError</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="sb">`Last Error: </span><span class="si">${</span><span class="nx">mutation</span><span class="p">.</span><span class="nx">lastError</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">      </span><span class="nx">console</span><span class="p">.</span><span class="nx">groupEnd</span><span class="p">()</span>
<span class="w">    </span><span class="p">})</span>

<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">groupEnd</span><span class="p">()</span>
<span class="w">  </span><span class="p">}</span><span class="w"> </span><span class="k">catch</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;Failed to inspect outbox:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">)</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>

<span class="c1">// Add to window for debugging</span>
<span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="ow">typeof</span><span class="w"> </span><span class="nb">window</span><span class="w"> </span><span class="o">!==</span><span class="w"> </span><span class="s1">&#39;undefined&#39;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="p">(</span><span class="nb">window</span><span class="w"> </span><span class="kr">as</span><span class="w"> </span><span class="nx">any</span><span class="p">).</span><span class="nx">debugOutbox</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">debugOutbox</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="2-network-quality-monitoring">2. Network Quality Monitoring<a class="headerlink" href="#2-network-quality-monitoring" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// Debug network quality detection</span>
<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">debugNetworkQuality</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">networkMonitor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">NetworkMonitor</span><span class="p">()</span>

<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">group</span><span class="p">(</span><span class="s1">&#39;🌐 Network Quality Debug&#39;</span><span class="p">)</span>

<span class="w">  </span><span class="c1">// Current status</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Online: </span><span class="si">${</span><span class="nx">navigator</span><span class="p">.</span><span class="nx">onLine</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Connection:`</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">navigator</span><span class="w"> </span><span class="kr">as</span><span class="w"> </span><span class="nx">any</span><span class="p">).</span><span class="nx">connection</span><span class="p">)</span>

<span class="w">  </span><span class="c1">// Monitor changes</span>
<span class="w">  </span><span class="nx">networkMonitor</span><span class="p">.</span><span class="nx">addListener</span><span class="p">((</span><span class="nx">status</span><span class="p">,</span><span class="w"> </span><span class="nx">quality</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Network status changed: </span><span class="si">${</span><span class="nx">status</span><span class="si">}</span><span class="sb"> (</span><span class="si">${</span><span class="nx">quality</span><span class="si">}</span><span class="sb">)`</span><span class="p">)</span>
<span class="w">  </span><span class="p">})</span>

<span class="w">  </span><span class="c1">// Test quality detection</span>
<span class="w">  </span><span class="nx">networkMonitor</span><span class="p">.</span><span class="nx">detectConnectionQuality</span><span class="p">().</span><span class="nx">then</span><span class="p">(</span><span class="nx">quality</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Detected quality: </span><span class="si">${</span><span class="nx">quality</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">strategy</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">networkMonitor</span><span class="p">.</span><span class="nx">getAdaptiveSyncStrategy</span><span class="p">()</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Sync strategy:`</span><span class="p">,</span><span class="w"> </span><span class="nx">strategy</span><span class="p">)</span>
<span class="w">  </span><span class="p">})</span>

<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">groupEnd</span><span class="p">()</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="3-sync-manager-status">3. Sync Manager Status<a class="headerlink" href="#3-sync-manager-status" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="c1">// Debug sync manager state</span>
<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">debugSyncManager</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">syncManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">SyncManager</span><span class="p">.</span><span class="nx">getInstance</span><span class="p">()</span>

<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">group</span><span class="p">(</span><span class="s1">&#39;⚙️ Sync Manager Debug&#39;</span><span class="p">)</span>

<span class="w">  </span><span class="c1">// Current state</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Is processing: </span><span class="si">${</span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">isCurrentlyProcessing</span><span class="p">()</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="sb">`Current operation: </span><span class="si">${</span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">getCurrentOperation</span><span class="p">()</span><span class="si">}</span><span class="sb">`</span><span class="p">)</span>

<span class="w">  </span><span class="c1">// Get statistics</span>
<span class="w">  </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">getStats</span><span class="p">().</span><span class="nx">then</span><span class="p">(</span><span class="nx">stats</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Statistics:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">stats</span><span class="p">)</span>
<span class="w">  </span><span class="p">})</span>

<span class="w">  </span><span class="c1">// Monitor sync events</span>
<span class="w">  </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">onSyncStart</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;🟡 Sync started&#39;</span><span class="p">)</span>
<span class="w">  </span><span class="p">})</span>

<span class="w">  </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">onSyncComplete</span><span class="p">((</span><span class="nx">result</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;🟢 Sync completed:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">result</span><span class="p">)</span>
<span class="w">  </span><span class="p">})</span>

<span class="w">  </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">onSyncError</span><span class="p">((</span><span class="nx">error</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">console</span><span class="p">.</span><span class="nx">error</span><span class="p">(</span><span class="s1">&#39;🔴 Sync error:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">error</span><span class="p">)</span>
<span class="w">  </span><span class="p">})</span>

<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">groupEnd</span><span class="p">()</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<h2 id="extending-conflict-resolution">Extending Conflict Resolution<a class="headerlink" href="#extending-conflict-resolution" title="Permanent link">&para;</a></h2>
<p>The system currently supports <strong>Last-Write Wins</strong> strategy. Here's how to add new resolution strategies:</p>
<h3 id="1-add-new-strategy-enum">1. Add New Strategy Enum<a class="headerlink" href="#1-add-new-strategy-enum" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># server/src/core/models/enums.py</span>
<span class="k">class</span><span class="w"> </span><span class="nc">ConflictResolutionStrategy</span><span class="p">(</span><span class="nb">str</span><span class="p">,</span> <span class="n">Enum</span><span class="p">):</span>
    <span class="n">LAST_WRITE_WINS</span> <span class="o">=</span> <span class="s2">&quot;last_write_wins&quot;</span>
    <span class="n">MANUAL_RESOLUTION</span> <span class="o">=</span> <span class="s2">&quot;manual_resolution&quot;</span>
    <span class="n">MERGE_CHANGES</span> <span class="o">=</span> <span class="s2">&quot;merge_changes&quot;</span>  <span class="c1"># 🔥 New strategy</span>
    <span class="n">CLIENT_WINS</span> <span class="o">=</span> <span class="s2">&quot;client_wins&quot;</span>      <span class="c1"># 🔥 New strategy</span>
    <span class="n">SERVER_WINS</span> <span class="o">=</span> <span class="s2">&quot;server_wins&quot;</span>      <span class="c1"># 🔥 New strategy</span>
</code></pre></div>
<h3 id="2-implement-resolution-logic">2. Implement Resolution Logic<a class="headerlink" href="#2-implement-resolution-logic" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># server/src/core/services/general/synchronization_service.py</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_resolve_conflict</span><span class="p">(</span>
    <span class="bp">self</span><span class="p">,</span> 
    <span class="n">conflict</span><span class="p">:</span> <span class="n">ConflictRecord</span><span class="p">,</span>
    <span class="n">strategy</span><span class="p">:</span> <span class="n">ConflictResolutionStrategy</span>
<span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ConflictResolution</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Resolve conflict using specified strategy.&quot;&quot;&quot;</span>

    <span class="k">if</span> <span class="n">strategy</span> <span class="o">==</span> <span class="n">ConflictResolutionStrategy</span><span class="o">.</span><span class="n">LAST_WRITE_WINS</span><span class="p">:</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_apply_last_write_wins</span><span class="p">(</span><span class="n">conflict</span><span class="p">)</span>

    <span class="k">elif</span> <span class="n">strategy</span> <span class="o">==</span> <span class="n">ConflictResolutionStrategy</span><span class="o">.</span><span class="n">MERGE_CHANGES</span><span class="p">:</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_apply_merge_changes</span><span class="p">(</span><span class="n">conflict</span><span class="p">)</span>

    <span class="k">elif</span> <span class="n">strategy</span> <span class="o">==</span> <span class="n">ConflictResolutionStrategy</span><span class="o">.</span><span class="n">CLIENT_WINS</span><span class="p">:</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_apply_client_wins</span><span class="p">(</span><span class="n">conflict</span><span class="p">)</span>

    <span class="k">elif</span> <span class="n">strategy</span> <span class="o">==</span> <span class="n">ConflictResolutionStrategy</span><span class="o">.</span><span class="n">SERVER_WINS</span><span class="p">:</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_apply_server_wins</span><span class="p">(</span><span class="n">conflict</span><span class="p">)</span>

    <span class="k">elif</span> <span class="n">strategy</span> <span class="o">==</span> <span class="n">ConflictResolutionStrategy</span><span class="o">.</span><span class="n">MANUAL_RESOLUTION</span><span class="p">:</span>
        <span class="k">return</span> <span class="k">await</span> <span class="bp">self</span><span class="o">.</span><span class="n">_queue_manual_resolution</span><span class="p">(</span><span class="n">conflict</span><span class="p">)</span>

    <span class="k">else</span><span class="p">:</span>
        <span class="k">raise</span> <span class="ne">ValueError</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Unknown conflict resolution strategy: </span><span class="si">{</span><span class="n">strategy</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">_apply_merge_changes</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">conflict</span><span class="p">:</span> <span class="n">ConflictRecord</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">ConflictResolution</span><span class="p">:</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;</span>
<span class="sd">    Merge changes from both local and central versions.</span>

<span class="sd">    Strategy:</span>
<span class="sd">    - Combine non-conflicting fields</span>
<span class="sd">    - Use timestamps to resolve field-level conflicts</span>
<span class="sd">    - Preserve both versions for audit trail</span>
<span class="sd">    &quot;&quot;&quot;</span>

    <span class="n">local_data</span> <span class="o">=</span> <span class="n">conflict</span><span class="o">.</span><span class="n">local_data</span>
    <span class="n">central_data</span> <span class="o">=</span> <span class="n">conflict</span><span class="o">.</span><span class="n">central_data</span>
    <span class="n">merged_data</span> <span class="o">=</span> <span class="p">{}</span>

    <span class="c1"># Get all unique fields</span>
    <span class="n">all_fields</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">local_data</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span> <span class="o">|</span> <span class="nb">set</span><span class="p">(</span><span class="n">central_data</span><span class="o">.</span><span class="n">keys</span><span class="p">())</span>

    <span class="k">for</span> <span class="n">field</span> <span class="ow">in</span> <span class="n">all_fields</span><span class="p">:</span>
        <span class="n">local_value</span> <span class="o">=</span> <span class="n">local_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">field</span><span class="p">)</span>
        <span class="n">central_value</span> <span class="o">=</span> <span class="n">central_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">field</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">local_value</span> <span class="o">==</span> <span class="n">central_value</span><span class="p">:</span>
            <span class="c1"># No conflict for this field</span>
            <span class="n">merged_data</span><span class="p">[</span><span class="n">field</span><span class="p">]</span> <span class="o">=</span> <span class="n">local_value</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Field-level conflict - use Last-Write Wins</span>
            <span class="n">local_timestamp</span> <span class="o">=</span> <span class="n">local_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;updated_at&#39;</span><span class="p">)</span>
            <span class="n">central_timestamp</span> <span class="o">=</span> <span class="n">central_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">&#39;updated_at&#39;</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">local_timestamp</span> <span class="ow">and</span> <span class="n">central_timestamp</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">local_timestamp</span> <span class="o">&gt;</span> <span class="n">central_timestamp</span><span class="p">:</span>
                    <span class="n">merged_data</span><span class="p">[</span><span class="n">field</span><span class="p">]</span> <span class="o">=</span> <span class="n">local_value</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">merged_data</span><span class="p">[</span><span class="n">field</span><span class="p">]</span> <span class="o">=</span> <span class="n">central_value</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># Fallback to central value</span>
                <span class="n">merged_data</span><span class="p">[</span><span class="n">field</span><span class="p">]</span> <span class="o">=</span> <span class="n">central_value</span>

    <span class="k">return</span> <span class="n">ConflictResolution</span><span class="p">(</span>
        <span class="n">conflict_id</span><span class="o">=</span><span class="n">conflict</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
        <span class="n">resolution_strategy</span><span class="o">=</span><span class="s1">&#39;merge_changes&#39;</span><span class="p">,</span>
        <span class="n">winner</span><span class="o">=</span><span class="s1">&#39;merged&#39;</span><span class="p">,</span>
        <span class="n">resolved_data</span><span class="o">=</span><span class="n">merged_data</span><span class="p">,</span>
        <span class="n">metadata</span><span class="o">=</span><span class="p">{</span>
            <span class="s1">&#39;merged_fields&#39;</span><span class="p">:</span> <span class="nb">len</span><span class="p">(</span><span class="n">all_fields</span><span class="p">),</span>
            <span class="s1">&#39;conflicting_fields&#39;</span><span class="p">:</span> <span class="nb">len</span><span class="p">([</span><span class="n">f</span> <span class="k">for</span> <span class="n">f</span> <span class="ow">in</span> <span class="n">all_fields</span> <span class="k">if</span> <span class="n">local_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">f</span><span class="p">)</span> <span class="o">!=</span> <span class="n">central_data</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="n">f</span><span class="p">)])</span>
        <span class="p">}</span>
    <span class="p">)</span>
</code></pre></div>
<h3 id="3-frontend-strategy-selection">3. Frontend Strategy Selection<a class="headerlink" href="#3-frontend-strategy-selection" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// client/src/lib/sync/conflict-resolver.ts</span>
<span class="k">export</span><span class="w"> </span><span class="kd">interface</span><span class="w"> </span><span class="nx">ConflictResolutionOptions</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">strategy</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;last_write_wins&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;merge_changes&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;client_wins&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;server_wins&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;manual&#39;</span>
<span class="w">  </span><span class="nx">entityType</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span>
<span class="w">  </span><span class="nx">priority</span><span class="o">?:</span><span class="w"> </span><span class="s1">&#39;high&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;medium&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;low&#39;</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">ConflictResolver</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">resolveConflict</span><span class="p">(</span>
<span class="w">    </span><span class="nx">conflict</span><span class="o">:</span><span class="w"> </span><span class="kt">ConflictRecord</span><span class="p">,</span>
<span class="w">    </span><span class="nx">options</span><span class="o">:</span><span class="w"> </span><span class="kt">ConflictResolutionOptions</span>
<span class="w">  </span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">ConflictResolution</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="nx">options</span><span class="p">.</span><span class="nx">strategy</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;merge_changes&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">mergeChanges</span><span class="p">(</span><span class="nx">conflict</span><span class="p">)</span>

<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;client_wins&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">clientWins</span><span class="p">(</span><span class="nx">conflict</span><span class="p">)</span>

<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;server_wins&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">serverWins</span><span class="p">(</span><span class="nx">conflict</span><span class="p">)</span>

<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;manual&#39;</span><span class="o">:</span>
<span class="w">        </span><span class="k">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">requestManualResolution</span><span class="p">(</span><span class="nx">conflict</span><span class="p">)</span>

<span class="w">      </span><span class="nx">default</span><span class="o">:</span>
<span class="w">        </span><span class="kt">return</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">lastWriteWins</span><span class="p">(</span><span class="nx">conflict</span><span class="p">)</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="nx">mergeChanges</span><span class="p">(</span><span class="nx">conflict</span><span class="o">:</span><span class="w"> </span><span class="kt">ConflictRecord</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">ConflictResolution</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Implement field-level merging logic</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">localData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">conflict</span><span class="p">.</span><span class="nx">local_data</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">serverData</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">conflict</span><span class="p">.</span><span class="nx">server_data</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">merged</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">...</span><span class="nx">serverData</span><span class="w"> </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Custom merging logic for specific fields</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">conflict</span><span class="p">.</span><span class="nx">entity_type</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s1">&#39;project&#39;</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// Example: Merge project-specific fields intelligently</span>
<span class="w">      </span><span class="nx">merged</span><span class="p">.</span><span class="nx">name</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">resolveStringConflict</span><span class="p">(</span><span class="nx">localData</span><span class="p">.</span><span class="nx">name</span><span class="p">,</span><span class="w"> </span><span class="nx">serverData</span><span class="p">.</span><span class="nx">name</span><span class="p">)</span>
<span class="w">      </span><span class="nx">merged</span><span class="p">.</span><span class="nx">description</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">mergeDescriptions</span><span class="p">(</span><span class="nx">localData</span><span class="p">.</span><span class="nx">description</span><span class="p">,</span><span class="w"> </span><span class="nx">serverData</span><span class="p">.</span><span class="nx">description</span><span class="p">)</span>
<span class="w">      </span><span class="nx">merged</span><span class="p">.</span><span class="nx">settings</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="p">...</span><span class="nx">serverData</span><span class="p">.</span><span class="nx">settings</span><span class="p">,</span><span class="w"> </span><span class="p">...</span><span class="nx">localData</span><span class="p">.</span><span class="nx">settings</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">success</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span><span class="p">,</span>
<span class="w">      </span><span class="nx">strategy</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;merge_changes&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">resolvedData</span><span class="o">:</span><span class="w"> </span><span class="kt">merged</span><span class="p">,</span>
<span class="w">      </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">mergedFields</span><span class="o">:</span><span class="w"> </span><span class="kt">Object.keys</span><span class="p">(</span><span class="nx">merged</span><span class="p">).</span><span class="nx">length</span><span class="p">,</span>
<span class="w">        </span><span class="nx">strategy</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;intelligent_merge&#39;</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<h2 id="best-practices-for-offline-mutations">Best Practices for Offline Mutations<a class="headerlink" href="#best-practices-for-offline-mutations" title="Permanent link">&para;</a></h2>
<h3 id="1-design-for-offline-first">1. Design for Offline-First<a class="headerlink" href="#1-design-for-offline-first" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// ✅ Good: Design mutations to work offline</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">useCreateProject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">useOfflineMutation</span><span class="p">({</span>
<span class="w">    </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/api/v1/projects&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="p">,</span>
<span class="w">    </span><span class="nx">mutationFn</span><span class="o">:</span><span class="w"> </span><span class="kt">createProject</span><span class="p">,</span>

<span class="w">    </span><span class="c1">// Always provide optimistic response</span>
<span class="w">    </span><span class="nx">optimisticResponse</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">      </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="sb">`temp_</span><span class="si">${</span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span><span class="si">}</span><span class="sb">`</span><span class="p">,</span>
<span class="w">      </span><span class="p">...</span><span class="nx">variables</span><span class="p">,</span>
<span class="w">      </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;DRAFT&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">created_at</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">().</span><span class="nx">toISOString</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">_optimistic</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span>
<span class="w">    </span><span class="p">}),</span>

<span class="w">    </span><span class="c1">// Handle dependency chains</span>
<span class="w">    </span><span class="nx">metadata</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">entityType</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;project&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">operation</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;create&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">priority</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;high&#39;</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">}</span>

<span class="c1">// ❌ Bad: No offline consideration</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">useCreateProject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="nx">useMutation</span><span class="p">({</span>
<span class="w">    </span><span class="nx">mutationFn</span><span class="o">:</span><span class="w"> </span><span class="kt">createProject</span><span class="p">,</span><span class="w"> </span><span class="c1">// Will fail offline</span>
<span class="w">    </span><span class="nx">onSuccess</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="c1">// No optimistic updates</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="2-handle-temporary-ids">2. Handle Temporary IDs<a class="headerlink" href="#2-handle-temporary-ids" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Utility for managing temporary IDs</span>
<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">generateTempId</span><span class="p">(</span><span class="nx">prefix</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;temp&#39;</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="sb">`</span><span class="si">${</span><span class="nx">prefix</span><span class="si">}</span><span class="sb">_</span><span class="si">${</span><span class="nb">Date</span><span class="p">.</span><span class="nx">now</span><span class="p">()</span><span class="si">}</span><span class="sb">_</span><span class="si">${</span><span class="nb">Math</span><span class="p">.</span><span class="nx">random</span><span class="p">().</span><span class="nx">toString</span><span class="p">(</span><span class="mf">36</span><span class="p">).</span><span class="nx">substring</span><span class="p">(</span><span class="mf">2</span><span class="p">)</span><span class="si">}</span><span class="sb">`</span>
<span class="p">}</span>

<span class="k">export</span><span class="w"> </span><span class="kd">function</span><span class="w"> </span><span class="nx">isTempId</span><span class="p">(</span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kt">number</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="ow">typeof</span><span class="w"> </span><span class="nx">id</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="s1">&#39;string&#39;</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="nx">id</span><span class="p">.</span><span class="nx">startsWith</span><span class="p">(</span><span class="s1">&#39;temp_&#39;</span><span class="p">)</span>
<span class="p">}</span>

<span class="c1">// Usage in components</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">CreateProjectForm</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">createProject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useCreateProject</span><span class="p">()</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">handleSubmit</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">(</span><span class="nx">data</span><span class="o">:</span><span class="w"> </span><span class="kt">CreateProjectRequest</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Create with temporary ID for immediate UI feedback</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">tempProject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">generateTempId</span><span class="p">(</span><span class="s1">&#39;project&#39;</span><span class="p">),</span>
<span class="w">      </span><span class="p">...</span><span class="nx">data</span><span class="p">,</span>
<span class="w">      </span><span class="nx">_optimistic</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Mutation will handle offline queueing</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">createProject</span><span class="p">.</span><span class="nx">mutateAsync</span><span class="p">(</span><span class="nx">data</span><span class="p">)</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="3-implement-dependency-management">3. Implement Dependency Management<a class="headerlink" href="#3-implement-dependency-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Handle dependencies between mutations</span>
<span class="kd">interface</span><span class="w"> </span><span class="nx">MutationDependency</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">dependsOn</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">[]</span><span class="w">  </span><span class="c1">// IDs of other mutations</span>
<span class="w">  </span><span class="nx">entityType</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span>
<span class="w">  </span><span class="nx">priority</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span>
<span class="p">}</span>

<span class="kd">const</span><span class="w"> </span><span class="nx">useCreateProjectWithComponents</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">createProject</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useCreateProject</span><span class="p">()</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">createComponent</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">useCreateComponent</span><span class="p">()</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">(</span><span class="nx">projectData</span><span class="o">:</span><span class="w"> </span><span class="kt">ProjectData</span><span class="p">,</span><span class="w"> </span><span class="nx">components</span><span class="o">:</span><span class="w"> </span><span class="kt">ComponentData</span><span class="p">[])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Create project first</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">project</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">createProject</span><span class="p">.</span><span class="nx">mutateAsync</span><span class="p">(</span><span class="nx">projectData</span><span class="p">)</span>

<span class="w">    </span><span class="c1">// Create components with dependency on project</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">componentPromises</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">components</span><span class="p">.</span><span class="nx">map</span><span class="p">(</span><span class="nx">componentData</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">      </span><span class="nx">createComponent</span><span class="p">.</span><span class="nx">mutateAsync</span><span class="p">({</span>
<span class="w">        </span><span class="p">...</span><span class="nx">componentData</span><span class="p">,</span>
<span class="w">        </span><span class="nx">project_id</span><span class="o">:</span><span class="w"> </span><span class="kt">project.id</span><span class="p">,</span>
<span class="w">        </span><span class="c1">// Metadata for dependency tracking</span>
<span class="w">        </span><span class="nx">_metadata</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">          </span><span class="nx">dependsOn</span><span class="o">:</span><span class="w"> </span><span class="p">[</span><span class="nx">project</span><span class="p">.</span><span class="nx">id</span><span class="p">],</span>
<span class="w">          </span><span class="nx">priority</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;medium&#39;</span>
<span class="w">        </span><span class="p">}</span>
<span class="w">      </span><span class="p">})</span>
<span class="w">    </span><span class="p">)</span>

<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nb">Promise</span><span class="p">.</span><span class="nx">all</span><span class="p">(</span><span class="nx">componentPromises</span><span class="p">)</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="4-provide-user-feedback">4. Provide User Feedback<a class="headerlink" href="#4-provide-user-feedback" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Component with offline status indication</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">ProjectCard</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">({</span><span class="w"> </span><span class="nx">project</span><span class="w"> </span><span class="p">}</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">project</span><span class="o">:</span><span class="w"> </span><span class="kt">Project</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">isOptimistic</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">project</span><span class="p">.</span><span class="nx">_optimistic</span>
<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">isOffline</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="o">!</span><span class="nx">navigator</span><span class="p">.</span><span class="nx">onLine</span>

<span class="w">  </span><span class="k">return</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="o">&lt;</span><span class="nx">Card</span><span class="w"> </span><span class="nx">className</span><span class="o">=</span><span class="p">{</span><span class="nx">cn</span><span class="p">(</span><span class="s2">&quot;project-card&quot;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="s2">&quot;opacity-75&quot;</span><span class="o">:</span><span class="w"> </span><span class="nx">isOptimistic</span><span class="p">,</span>
<span class="w">      </span><span class="s2">&quot;border-orange-500&quot;</span><span class="o">:</span><span class="w"> </span><span class="nx">isOffline</span>
<span class="w">    </span><span class="p">})}</span><span class="o">&gt;</span>
<span class="w">      </span><span class="o">&lt;</span><span class="nx">CardHeader</span><span class="o">&gt;</span>
<span class="w">        </span><span class="o">&lt;</span><span class="nx">div</span><span class="w"> </span><span class="nx">className</span><span class="o">=</span><span class="s2">&quot;flex items-center justify-between&quot;</span><span class="o">&gt;</span>
<span class="w">          </span><span class="o">&lt;</span><span class="nx">CardTitle</span><span class="o">&gt;</span><span class="p">{</span><span class="nx">project</span><span class="p">.</span><span class="nx">name</span><span class="p">}</span><span class="o">&lt;</span><span class="err">/CardTitle&gt;</span>
<span class="w">          </span><span class="p">{</span><span class="nx">isOptimistic</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="p">(</span>
<span class="w">            </span><span class="o">&lt;</span><span class="nx">Badge</span><span class="w"> </span><span class="nx">variant</span><span class="o">=</span><span class="s2">&quot;outline&quot;</span><span class="w"> </span><span class="nx">className</span><span class="o">=</span><span class="s2">&quot;text-orange-600&quot;</span><span class="o">&gt;</span>
<span class="w">              </span><span class="o">&lt;</span><span class="nx">Clock</span><span class="w"> </span><span class="nx">className</span><span class="o">=</span><span class="s2">&quot;w-3 h-3 mr-1&quot;</span><span class="w"> </span><span class="o">/&gt;</span>
<span class="w">              </span><span class="nx">Pending</span><span class="w"> </span><span class="nx">Sync</span>
<span class="w">            </span><span class="o">&lt;</span><span class="err">/Badge&gt;</span>
<span class="w">          </span><span class="p">)}</span>
<span class="w">          </span><span class="p">{</span><span class="nx">isOffline</span><span class="w"> </span><span class="o">&amp;&amp;</span><span class="w"> </span><span class="p">(</span>
<span class="w">            </span><span class="o">&lt;</span><span class="nx">Badge</span><span class="w"> </span><span class="nx">variant</span><span class="o">=</span><span class="s2">&quot;secondary&quot;</span><span class="o">&gt;</span>
<span class="w">              </span><span class="o">&lt;</span><span class="nx">WifiOff</span><span class="w"> </span><span class="nx">className</span><span class="o">=</span><span class="s2">&quot;w-3 h-3 mr-1&quot;</span><span class="w"> </span><span class="o">/&gt;</span>
<span class="w">              </span><span class="nx">Offline</span>
<span class="w">            </span><span class="o">&lt;</span><span class="err">/Badge&gt;</span>
<span class="w">          </span><span class="p">)}</span>
<span class="w">        </span><span class="o">&lt;</span><span class="err">/div&gt;</span>
<span class="w">      </span><span class="o">&lt;</span><span class="err">/CardHeader&gt;</span>
<span class="w">      </span><span class="o">&lt;</span><span class="nx">CardContent</span><span class="o">&gt;</span>
<span class="w">        </span><span class="o">&lt;</span><span class="nx">p</span><span class="o">&gt;</span><span class="p">{</span><span class="nx">project</span><span class="p">.</span><span class="nx">description</span><span class="p">}</span><span class="o">&lt;</span><span class="err">/p&gt;</span>
<span class="w">      </span><span class="o">&lt;</span><span class="err">/CardContent&gt;</span>
<span class="w">    </span><span class="o">&lt;</span><span class="err">/Card&gt;</span>
<span class="w">  </span><span class="p">)</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<h2 id="testing-guidelines">Testing Guidelines<a class="headerlink" href="#testing-guidelines" title="Permanent link">&para;</a></h2>
<h3 id="1-unit-tests-for-offline-hooks">1. Unit Tests for Offline Hooks<a class="headerlink" href="#1-unit-tests-for-offline-hooks" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// tests/hooks/useOfflineMutation.test.ts</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">renderHook</span><span class="p">,</span><span class="w"> </span><span class="nx">waitFor</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;@testing-library/react&#39;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">QueryClient</span><span class="p">,</span><span class="w"> </span><span class="nx">QueryClientProvider</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;@tanstack/react-query&#39;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">useOfflineMutation</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;../../src/hooks/cache/useOfflineMutation&#39;</span>

<span class="c1">// Mock network status</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">mockNavigator</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">onLine</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span>
<span class="p">}</span>
<span class="nb">Object</span><span class="p">.</span><span class="nx">defineProperty</span><span class="p">(</span><span class="nb">window</span><span class="p">,</span><span class="w"> </span><span class="s1">&#39;navigator&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">value</span><span class="o">:</span><span class="w"> </span><span class="kt">mockNavigator</span><span class="p">,</span>
<span class="w">  </span><span class="nx">writable</span><span class="o">:</span><span class="w"> </span><span class="kt">true</span>
<span class="p">})</span>

<span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;useOfflineMutation&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="kd">let</span><span class="w"> </span><span class="nx">queryClient</span><span class="o">:</span><span class="w"> </span><span class="kt">QueryClient</span>

<span class="w">  </span><span class="nx">beforeEach</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">queryClient</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">QueryClient</span><span class="p">({</span>
<span class="w">      </span><span class="nx">defaultOptions</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">queries</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">retry</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="w"> </span><span class="p">},</span>
<span class="w">        </span><span class="nx">mutations</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">retry</span><span class="o">:</span><span class="w"> </span><span class="kt">false</span><span class="w"> </span><span class="p">}</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">})</span>
<span class="w">  </span><span class="p">})</span>

<span class="w">  </span><span class="kd">const</span><span class="w"> </span><span class="nx">wrapper</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">({</span><span class="w"> </span><span class="nx">children</span><span class="w"> </span><span class="p">})</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">(</span>
<span class="w">    </span><span class="o">&lt;</span><span class="nx">QueryClientProvider</span><span class="w"> </span><span class="nx">client</span><span class="o">=</span><span class="p">{</span><span class="nx">queryClient</span><span class="p">}</span><span class="o">&gt;</span>
<span class="w">      </span><span class="p">{</span><span class="nx">children</span><span class="p">}</span>
<span class="w">    </span><span class="o">&lt;</span><span class="err">/QueryClientProvider&gt;</span>
<span class="w">  </span><span class="p">)</span>

<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;executes mutation when online&#39;</span><span class="p">,</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">mutationFn</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">jest</span><span class="p">.</span><span class="nx">fn</span><span class="p">().</span><span class="nx">mockResolvedValue</span><span class="p">({</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">1</span><span class="p">,</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test&#39;</span><span class="w"> </span><span class="p">})</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">renderHook</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">      </span><span class="nx">useOfflineMutation</span><span class="p">({</span>
<span class="w">        </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/api/test&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">mutationFn</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">      </span><span class="p">{</span><span class="w"> </span><span class="nx">wrapper</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="p">)</span>

<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">result</span><span class="p">.</span><span class="nx">current</span><span class="p">.</span><span class="nx">mutateAsync</span><span class="p">({</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test&#39;</span><span class="w"> </span><span class="p">})</span>

<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">mutationFn</span><span class="p">).</span><span class="nx">toHaveBeenCalledWith</span><span class="p">({</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test&#39;</span><span class="w"> </span><span class="p">})</span>
<span class="w">  </span><span class="p">})</span>

<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;queues mutation when offline&#39;</span><span class="p">,</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Set offline</span>
<span class="w">    </span><span class="nx">mockNavigator</span><span class="p">.</span><span class="nx">onLine</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">false</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">mutationFn</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">jest</span><span class="p">.</span><span class="nx">fn</span><span class="p">().</span><span class="nx">mockResolvedValue</span><span class="p">({</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">1</span><span class="p">,</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test&#39;</span><span class="w"> </span><span class="p">})</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">addToOutbox</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">jest</span><span class="p">.</span><span class="nx">fn</span><span class="p">()</span>

<span class="w">    </span><span class="c1">// Mock SyncManager</span>
<span class="w">    </span><span class="nx">jest</span><span class="p">.</span><span class="nx">doMock</span><span class="p">(</span><span class="s1">&#39;../../src/lib/sync/sync-manager&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">      </span><span class="nx">SyncManager</span><span class="o">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">        </span><span class="nx">getInstance</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span>
<span class="w">          </span><span class="nx">addToOutbox</span>
<span class="w">        </span><span class="p">})</span>
<span class="w">      </span><span class="p">}</span>
<span class="w">    </span><span class="p">}))</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">renderHook</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span>
<span class="w">      </span><span class="nx">useOfflineMutation</span><span class="p">({</span>
<span class="w">        </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/api/test&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="p">,</span>
<span class="w">        </span><span class="nx">mutationFn</span><span class="p">,</span>
<span class="w">        </span><span class="nx">optimisticResponse</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">({</span><span class="w"> </span><span class="p">...</span><span class="nx">variables</span><span class="p">,</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;temp_123&#39;</span><span class="w"> </span><span class="p">})</span>
<span class="w">      </span><span class="p">}),</span>
<span class="w">      </span><span class="p">{</span><span class="w"> </span><span class="nx">wrapper</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="p">)</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">result</span><span class="p">.</span><span class="nx">current</span><span class="p">.</span><span class="nx">mutateAsync</span><span class="p">({</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test&#39;</span><span class="w"> </span><span class="p">})</span>

<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">mutationFn</span><span class="p">).</span><span class="nx">not</span><span class="p">.</span><span class="nx">toHaveBeenCalled</span><span class="p">()</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">addToOutbox</span><span class="p">).</span><span class="nx">toHaveBeenCalledWith</span><span class="p">(</span><span class="nx">expect</span><span class="p">.</span><span class="nx">objectContaining</span><span class="p">({</span>
<span class="w">      </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/api/test&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">variables</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test&#39;</span><span class="w"> </span><span class="p">}</span>
<span class="w">    </span><span class="p">}))</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result</span><span class="p">).</span><span class="nx">toEqual</span><span class="p">({</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;temp_123&#39;</span><span class="w"> </span><span class="p">})</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">})</span>
</code></pre></div>
<h3 id="2-integration-tests-for-sync-flow">2. Integration Tests for Sync Flow<a class="headerlink" href="#2-integration-tests-for-sync-flow" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// tests/integration/sync-flow.test.ts</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">setupServer</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;msw/node&#39;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">rest</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;msw&#39;</span>
<span class="k">import</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">SyncManager</span><span class="w"> </span><span class="p">}</span><span class="w"> </span><span class="kr">from</span><span class="w"> </span><span class="s1">&#39;../../src/lib/sync/sync-manager&#39;</span>

<span class="kd">const</span><span class="w"> </span><span class="nx">server</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">setupServer</span><span class="p">(</span>
<span class="w">  </span><span class="nx">rest</span><span class="p">.</span><span class="nx">post</span><span class="p">(</span><span class="s1">&#39;/api/v1/projects&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">(</span><span class="nx">req</span><span class="p">,</span><span class="w"> </span><span class="nx">res</span><span class="p">,</span><span class="w"> </span><span class="nx">ctx</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">return</span><span class="w"> </span><span class="nx">res</span><span class="p">(</span><span class="nx">ctx</span><span class="p">.</span><span class="nx">json</span><span class="p">({</span><span class="w"> </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="kt">1</span><span class="p">,</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test Project&#39;</span><span class="w"> </span><span class="p">}))</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">)</span>

<span class="nx">describe</span><span class="p">(</span><span class="s1">&#39;Sync Flow Integration&#39;</span><span class="p">,</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">beforeAll</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">server</span><span class="p">.</span><span class="nx">listen</span><span class="p">())</span>
<span class="w">  </span><span class="nx">afterEach</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">server</span><span class="p">.</span><span class="nx">resetHandlers</span><span class="p">())</span>
<span class="w">  </span><span class="nx">afterAll</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">server</span><span class="p">.</span><span class="nx">close</span><span class="p">())</span>

<span class="w">  </span><span class="nx">test</span><span class="p">(</span><span class="s1">&#39;processes outbox when coming online&#39;</span><span class="p">,</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">syncManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">SyncManager</span><span class="p">.</span><span class="nx">getInstance</span><span class="p">()</span>

<span class="w">    </span><span class="c1">// Add mutation to outbox</span>
<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">addToOutbox</span><span class="p">({</span>
<span class="w">      </span><span class="nx">id</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;test-mutation&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;/api/v1/projects&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="p">,</span>
<span class="w">      </span><span class="nx">variables</span><span class="o">:</span><span class="w"> </span><span class="p">{</span><span class="w"> </span><span class="nx">name</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;Test Project&#39;</span><span class="w"> </span><span class="p">},</span>
<span class="w">      </span><span class="nx">timestamp</span><span class="o">:</span><span class="w"> </span><span class="kt">new</span><span class="w"> </span><span class="nb">Date</span><span class="p">().</span><span class="nx">toISOString</span><span class="p">(),</span>
<span class="w">      </span><span class="nx">retryCount</span><span class="o">:</span><span class="w"> </span><span class="kt">0</span><span class="p">,</span>
<span class="w">      </span><span class="nx">status</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;pending&#39;</span>
<span class="w">    </span><span class="p">})</span>

<span class="w">    </span><span class="c1">// Process outbox</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">processOutbox</span><span class="p">()</span>

<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">status</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="s1">&#39;completed&#39;</span><span class="p">)</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">result</span><span class="p">.</span><span class="nx">completed</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="mf">1</span><span class="p">)</span>

<span class="w">    </span><span class="c1">// Verify outbox is empty</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">outboxCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">getOutboxCount</span><span class="p">()</span>
<span class="w">    </span><span class="nx">expect</span><span class="p">(</span><span class="nx">outboxCount</span><span class="p">).</span><span class="nx">toBe</span><span class="p">(</span><span class="mf">0</span><span class="p">)</span>
<span class="w">  </span><span class="p">})</span>
<span class="p">})</span>
</code></pre></div>
<h3 id="3-e2e-tests-for-offline-scenarios">3. E2E Tests for Offline Scenarios<a class="headerlink" href="#3-e2e-tests-for-offline-scenarios" title="Permanent link">&para;</a></h3>
<p>See the comprehensive E2E tests created in Work Batches 8.1-8.3 for complete offline scenario testing.</p>
<hr />
<h2 id="performance-optimization">Performance Optimization<a class="headerlink" href="#performance-optimization" title="Permanent link">&para;</a></h2>
<h3 id="1-optimize-indexeddb-operations">1. Optimize IndexedDB Operations<a class="headerlink" href="#1-optimize-indexeddb-operations" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Batch IndexedDB operations for better performance</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">OptimizedIndexedDBPersister</span><span class="w"> </span><span class="k">implements</span><span class="w"> </span><span class="nx">Persister</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">batchSize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">100</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">pendingOperations</span><span class="o">:</span><span class="w"> </span><span class="kt">Array</span><span class="o">&lt;</span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;&gt;</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="p">[]</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">flushTimeout</span><span class="o">:</span><span class="w"> </span><span class="kt">NodeJS.Timeout</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span>

<span class="w">  </span><span class="k">async</span><span class="w"> </span><span class="nx">persistClient</span><span class="p">(</span><span class="nx">client</span><span class="o">:</span><span class="w"> </span><span class="kt">PersistedClient</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Add to batch</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">pendingOperations</span><span class="p">.</span><span class="nx">push</span><span class="p">(</span><span class="k">async</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">doPersistClient</span><span class="p">(</span><span class="nx">client</span><span class="p">)</span>
<span class="w">    </span><span class="p">})</span>

<span class="w">    </span><span class="c1">// Flush batch if needed</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">pendingOperations</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">batchSize</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">flushBatch</span><span class="p">()</span>
<span class="w">    </span><span class="p">}</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">scheduleFlush</span><span class="p">()</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">scheduleFlush</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">flushTimeout</span><span class="p">)</span><span class="w"> </span><span class="k">return</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">flushTimeout</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">setTimeout</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">flushBatch</span><span class="p">()</span>
<span class="w">    </span><span class="p">},</span><span class="w"> </span><span class="mf">1000</span><span class="p">)</span><span class="w"> </span><span class="c1">// Flush after 1 second</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">async</span><span class="w"> </span><span class="nx">flushBatch</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">flushTimeout</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">clearTimeout</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">flushTimeout</span><span class="p">)</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">flushTimeout</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="kc">null</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">operations</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">pendingOperations</span><span class="p">.</span><span class="nx">splice</span><span class="p">(</span><span class="mf">0</span><span class="p">,</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">batchSize</span><span class="p">)</span>

<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">operations</span><span class="p">.</span><span class="nx">length</span><span class="w"> </span><span class="o">===</span><span class="w"> </span><span class="mf">0</span><span class="p">)</span><span class="w"> </span><span class="k">return</span>

<span class="w">    </span><span class="c1">// Execute all operations in a single transaction</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">db</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getDatabase</span><span class="p">()</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">transaction</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">db</span><span class="p">.</span><span class="nx">transaction</span><span class="p">([</span><span class="s1">&#39;query-cache&#39;</span><span class="p">],</span><span class="w"> </span><span class="s1">&#39;readwrite&#39;</span><span class="p">)</span>

<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nb">Promise</span><span class="p">.</span><span class="nx">all</span><span class="p">(</span><span class="nx">operations</span><span class="p">.</span><span class="nx">map</span><span class="p">(</span><span class="nx">op</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">op</span><span class="p">()))</span>

<span class="w">    </span><span class="k">await</span><span class="w"> </span><span class="nx">transaction</span><span class="p">.</span><span class="nx">complete</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="2-intelligent-sync-scheduling">2. Intelligent Sync Scheduling<a class="headerlink" href="#2-intelligent-sync-scheduling" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Smart sync scheduling based on network conditions</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">SmartSyncScheduler</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">syncIntervals</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Map</span><span class="o">&lt;</span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">NodeJS</span><span class="p">.</span><span class="nx">Timeout</span><span class="o">&gt;</span><span class="p">()</span>

<span class="w">  </span><span class="nx">scheduleSync</span><span class="p">(</span>
<span class="w">    </span><span class="nx">projectId</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span>
<span class="w">    </span><span class="nx">networkQuality</span><span class="o">:</span><span class="w"> </span><span class="kt">ConnectionQuality</span><span class="p">,</span>
<span class="w">    </span><span class="nx">priority</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;high&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;medium&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;low&#39;</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="s1">&#39;medium&#39;</span>
<span class="w">  </span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>

<span class="w">    </span><span class="c1">// Clear existing schedule</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">existingInterval</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">syncIntervals</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="nx">projectId</span><span class="p">)</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">existingInterval</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">clearTimeout</span><span class="p">(</span><span class="nx">existingInterval</span><span class="p">)</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Determine sync interval based on quality and priority</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">baseInterval</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getBaseInterval</span><span class="p">(</span><span class="nx">networkQuality</span><span class="p">)</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">priorityMultiplier</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">getPriorityMultiplier</span><span class="p">(</span><span class="nx">priority</span><span class="p">)</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">interval</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">baseInterval</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="nx">priorityMultiplier</span>

<span class="w">    </span><span class="c1">// Schedule sync</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">timeout</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">setTimeout</span><span class="p">(()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">triggerSync</span><span class="p">(</span><span class="nx">projectId</span><span class="p">)</span>
<span class="w">    </span><span class="p">},</span><span class="w"> </span><span class="nx">interval</span><span class="p">)</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">syncIntervals</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="nx">projectId</span><span class="p">,</span><span class="w"> </span><span class="nx">timeout</span><span class="p">)</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">getBaseInterval</span><span class="p">(</span><span class="nx">quality</span><span class="o">:</span><span class="w"> </span><span class="kt">ConnectionQuality</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="nx">quality</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;fast&#39;</span><span class="o">:</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="mf">5000</span><span class="w">    </span><span class="c1">// 5 seconds</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;slow&#39;</span><span class="o">:</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="mf">30000</span><span class="w">   </span><span class="c1">// 30 seconds</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;unstable&#39;</span><span class="o">:</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="mf">60000</span><span class="w"> </span><span class="c1">// 1 minute</span>
<span class="w">      </span><span class="nx">default</span><span class="o">:</span><span class="w"> </span><span class="kt">return</span><span class="w"> </span><span class="mf">15000</span><span class="w">       </span><span class="c1">// 15 seconds</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">getPriorityMultiplier</span><span class="p">(</span><span class="nx">priority</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="k">switch</span><span class="w"> </span><span class="p">(</span><span class="nx">priority</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;high&#39;</span><span class="o">:</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="mf">0.5</span><span class="w">    </span><span class="c1">// Sync more frequently</span>
<span class="w">      </span><span class="k">case</span><span class="w"> </span><span class="s1">&#39;low&#39;</span><span class="o">:</span><span class="w"> </span><span class="k">return</span><span class="w"> </span><span class="mf">2.0</span><span class="w">     </span><span class="c1">// Sync less frequently</span>
<span class="w">      </span><span class="nx">default</span><span class="o">:</span><span class="w"> </span><span class="kt">return</span><span class="w"> </span><span class="mf">1.0</span><span class="w">        </span><span class="c1">// Normal frequency</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h3 id="3-memory-management">3. Memory Management<a class="headerlink" href="#3-memory-management" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">// Implement memory-conscious caching</span>
<span class="k">export</span><span class="w"> </span><span class="kd">class</span><span class="w"> </span><span class="nx">MemoryOptimizedCache</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="k">readonly</span><span class="w"> </span><span class="nx">maxCacheSize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">50</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">1024</span><span class="w"> </span><span class="o">*</span><span class="w"> </span><span class="mf">1024</span><span class="w"> </span><span class="c1">// 50MB</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">cacheSize</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span>
<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">cache</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nb">Map</span><span class="o">&lt;</span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">CacheEntry</span><span class="o">&gt;</span><span class="p">()</span>

<span class="w">  </span><span class="nx">set</span><span class="p">(</span><span class="nx">key</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="p">,</span><span class="w"> </span><span class="nx">value</span><span class="o">:</span><span class="w"> </span><span class="kt">any</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">serialized</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">JSON</span><span class="p">.</span><span class="nx">stringify</span><span class="p">(</span><span class="nx">value</span><span class="p">)</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">size</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">Blob</span><span class="p">([</span><span class="nx">serialized</span><span class="p">]).</span><span class="nx">size</span>

<span class="w">    </span><span class="c1">// Check if addition would exceed limit</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">cacheSize</span><span class="w"> </span><span class="o">+</span><span class="w"> </span><span class="nx">size</span><span class="w"> </span><span class="o">&gt;</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">maxCacheSize</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">evictOldEntries</span><span class="p">(</span><span class="nx">size</span><span class="p">)</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Remove existing entry if updating</span>
<span class="w">    </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="nx">has</span><span class="p">(</span><span class="nx">key</span><span class="p">))</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="kd">const</span><span class="w"> </span><span class="nx">existing</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="nx">get</span><span class="p">(</span><span class="nx">key</span><span class="p">)</span><span class="o">!</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">cacheSize</span><span class="w"> </span><span class="o">-=</span><span class="w"> </span><span class="nx">existing</span><span class="p">.</span><span class="nx">size</span>
<span class="w">    </span><span class="p">}</span>

<span class="w">    </span><span class="c1">// Add new entry</span>
<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="nx">set</span><span class="p">(</span><span class="nx">key</span><span class="p">,</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="nx">value</span><span class="o">:</span><span class="w"> </span><span class="kt">serialized</span><span class="p">,</span>
<span class="w">      </span><span class="nx">size</span><span class="p">,</span>
<span class="w">      </span><span class="nx">lastAccessed</span><span class="o">:</span><span class="w"> </span><span class="kt">Date.now</span><span class="p">()</span>
<span class="w">    </span><span class="p">})</span>

<span class="w">    </span><span class="k">this</span><span class="p">.</span><span class="nx">cacheSize</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="nx">size</span>
<span class="w">  </span><span class="p">}</span>

<span class="w">  </span><span class="k">private</span><span class="w"> </span><span class="nx">evictOldEntries</span><span class="p">(</span><span class="nx">requiredSpace</span><span class="o">:</span><span class="w"> </span><span class="kt">number</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="c1">// Sort by last accessed time</span>
<span class="w">    </span><span class="kd">const</span><span class="w"> </span><span class="nx">entries</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nb">Array</span><span class="p">.</span><span class="kr">from</span><span class="p">(</span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="nx">entries</span><span class="p">())</span>
<span class="w">      </span><span class="p">.</span><span class="nx">sort</span><span class="p">(([,</span><span class="nx">a</span><span class="p">],</span><span class="w"> </span><span class="p">[,</span><span class="nx">b</span><span class="p">])</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">a</span><span class="p">.</span><span class="nx">lastAccessed</span><span class="w"> </span><span class="o">-</span><span class="w"> </span><span class="nx">b</span><span class="p">.</span><span class="nx">lastAccessed</span><span class="p">)</span>

<span class="w">    </span><span class="kd">let</span><span class="w"> </span><span class="nx">freedSpace</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="mf">0</span>

<span class="w">    </span><span class="k">for</span><span class="w"> </span><span class="p">(</span><span class="kd">const</span><span class="w"> </span><span class="p">[</span><span class="nx">key</span><span class="p">,</span><span class="w"> </span><span class="nx">entry</span><span class="p">]</span><span class="w"> </span><span class="k">of</span><span class="w"> </span><span class="nx">entries</span><span class="p">)</span><span class="w"> </span><span class="p">{</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">cache</span><span class="p">.</span><span class="ow">delete</span><span class="p">(</span><span class="nx">key</span><span class="p">)</span>
<span class="w">      </span><span class="k">this</span><span class="p">.</span><span class="nx">cacheSize</span><span class="w"> </span><span class="o">-=</span><span class="w"> </span><span class="nx">entry</span><span class="p">.</span><span class="nx">size</span>
<span class="w">      </span><span class="nx">freedSpace</span><span class="w"> </span><span class="o">+=</span><span class="w"> </span><span class="nx">entry</span><span class="p">.</span><span class="nx">size</span>

<span class="w">      </span><span class="k">if</span><span class="w"> </span><span class="p">(</span><span class="nx">freedSpace</span><span class="w"> </span><span class="o">&gt;=</span><span class="w"> </span><span class="nx">requiredSpace</span><span class="p">)</span><span class="w"> </span><span class="k">break</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<h2 id="troubleshooting-common-issues">Troubleshooting Common Issues<a class="headerlink" href="#troubleshooting-common-issues" title="Permanent link">&para;</a></h2>
<h3 id="issue-1-sync-not-triggering">Issue 1: Sync Not Triggering<a class="headerlink" href="#issue-1-sync-not-triggering" title="Permanent link">&para;</a></h3>
<p><strong>Symptoms:</strong>
- Outbox has pending mutations
- Network is online
- No sync activity</p>
<p><strong>Diagnosis:</strong>
<div class="highlight"><pre><span></span><code><span class="c1">// Check sync manager status</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">syncManager</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="nx">SyncManager</span><span class="p">.</span><span class="nx">getInstance</span><span class="p">()</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Is processing:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">isCurrentlyProcessing</span><span class="p">())</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Network status:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">navigator</span><span class="p">.</span><span class="nx">onLine</span><span class="p">)</span>

<span class="c1">// Check network monitor</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">networkMonitor</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="ow">new</span><span class="w"> </span><span class="nx">NetworkMonitor</span><span class="p">()</span>
<span class="nx">networkMonitor</span><span class="p">.</span><span class="nx">detectConnectionQuality</span><span class="p">().</span><span class="nx">then</span><span class="p">(</span><span class="nx">quality</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Connection quality:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">quality</span><span class="p">)</span>
<span class="p">})</span>

<span class="c1">// Manually trigger sync</span>
<span class="nx">syncManager</span><span class="p">.</span><span class="nx">processOutbox</span><span class="p">().</span><span class="nx">then</span><span class="p">(</span><span class="nx">result</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Manual sync result:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">result</span><span class="p">)</span>
<span class="p">})</span>
</code></pre></div></p>
<p><strong>Solutions:</strong>
1. Verify network event listeners are attached
2. Check for JavaScript errors blocking sync
3. Ensure sync manager is properly initialized
4. Verify backend endpoints are accessible</p>
<h3 id="issue-2-conflicts-not-resolving">Issue 2: Conflicts Not Resolving<a class="headerlink" href="#issue-2-conflicts-not-resolving" title="Permanent link">&para;</a></h3>
<p><strong>Symptoms:</strong>
- Mutations appear to succeed but data doesn't match expected state
- Conflict errors in logs
- Data inconsistency between clients</p>
<p><strong>Diagnosis:</strong>
<div class="highlight"><pre><span></span><code><span class="c1"># Check conflict detection</span>
<span class="n">conflicts</span> <span class="o">=</span> <span class="k">await</span> <span class="n">sync_service</span><span class="o">.</span><span class="n">_detect_conflicts</span><span class="p">(</span><span class="n">local_changes</span><span class="p">,</span> <span class="n">central_changes</span><span class="p">)</span>
<span class="k">for</span> <span class="n">conflict</span> <span class="ow">in</span> <span class="n">conflicts</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Conflict: </span><span class="si">{</span><span class="n">conflict</span><span class="o">.</span><span class="n">entity_type</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">conflict</span><span class="o">.</span><span class="n">entity_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  Local: </span><span class="si">{</span><span class="n">conflict</span><span class="o">.</span><span class="n">local_data</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  Central: </span><span class="si">{</span><span class="n">conflict</span><span class="o">.</span><span class="n">central_data</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;  Type: </span><span class="si">{</span><span class="n">conflict</span><span class="o">.</span><span class="n">conflict_type</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="c1"># Check resolution strategy</span>
<span class="n">strategy</span> <span class="o">=</span> <span class="n">ConflictResolutionStrategy</span><span class="o">.</span><span class="n">LAST_WRITE_WINS</span>
<span class="n">resolution</span> <span class="o">=</span> <span class="k">await</span> <span class="n">sync_service</span><span class="o">.</span><span class="n">_resolve_conflict</span><span class="p">(</span><span class="n">conflict</span><span class="p">,</span> <span class="n">strategy</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Resolution: </span><span class="si">{</span><span class="n">resolution</span><span class="o">.</span><span class="n">winner</span><span class="si">}</span><span class="s2"> - </span><span class="si">{</span><span class="n">resolution</span><span class="o">.</span><span class="n">resolved_data</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</code></pre></div></p>
<p><strong>Solutions:</strong>
1. Verify timestamp consistency across systems
2. Check conflict resolution strategy configuration
3. Ensure proper conflict logging
4. Validate resolution logic for specific entity types</p>
<h3 id="issue-3-performance-degradation">Issue 3: Performance Degradation<a class="headerlink" href="#issue-3-performance-degradation" title="Permanent link">&para;</a></h3>
<p><strong>Symptoms:</strong>
- Slow sync operations
- High memory usage
- Browser freezing during sync</p>
<p><strong>Diagnosis:</strong>
<div class="highlight"><pre><span></span><code><span class="c1">// Performance monitoring</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">time</span><span class="p">(</span><span class="s1">&#39;sync-operation&#39;</span><span class="p">)</span>

<span class="kd">const</span><span class="w"> </span><span class="nx">result</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">processOutbox</span><span class="p">()</span>

<span class="nx">console</span><span class="p">.</span><span class="nx">timeEnd</span><span class="p">(</span><span class="s1">&#39;sync-operation&#39;</span><span class="p">)</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Processed:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">result</span><span class="p">.</span><span class="nx">processed</span><span class="p">)</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Memory usage:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">performance</span><span class="p">.</span><span class="nx">memory</span><span class="p">)</span>

<span class="c1">// Check outbox size</span>
<span class="kd">const</span><span class="w"> </span><span class="nx">outboxCount</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">await</span><span class="w"> </span><span class="nx">syncManager</span><span class="p">.</span><span class="nx">getOutboxCount</span><span class="p">()</span>
<span class="nx">console</span><span class="p">.</span><span class="nx">log</span><span class="p">(</span><span class="s1">&#39;Outbox size:&#39;</span><span class="p">,</span><span class="w"> </span><span class="nx">outboxCount</span><span class="p">)</span>
</code></pre></div></p>
<p><strong>Solutions:</strong>
1. Implement batched processing for large outboxes
2. Add memory management for large datasets
3. Optimize IndexedDB queries
4. Implement progressive sync for large projects</p>
<h3 id="issue-4-data-loss-during-sync">Issue 4: Data Loss During Sync<a class="headerlink" href="#issue-4-data-loss-during-sync" title="Permanent link">&para;</a></h3>
<p><strong>Symptoms:</strong>
- Missing data after sync
- Inconsistent state between local and central databases
- User reports of lost changes</p>
<p><strong>Diagnosis:</strong>
<div class="highlight"><pre><span></span><code><span class="c1"># Audit data integrity</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">audit_sync_integrity</span><span class="p">(</span><span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">):</span>
    <span class="c1"># Compare record counts</span>
    <span class="n">local_count</span> <span class="o">=</span> <span class="k">await</span> <span class="n">local_repo</span><span class="o">.</span><span class="n">count_by_project</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>
    <span class="n">central_count</span> <span class="o">=</span> <span class="k">await</span> <span class="n">central_repo</span><span class="o">.</span><span class="n">count_by_project</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>

    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Local records: </span><span class="si">{</span><span class="n">local_count</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Central records: </span><span class="si">{</span><span class="n">central_count</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

    <span class="c1"># Check for missing records</span>
    <span class="n">local_ids</span> <span class="o">=</span> <span class="k">await</span> <span class="n">local_repo</span><span class="o">.</span><span class="n">get_all_ids</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>
    <span class="n">central_ids</span> <span class="o">=</span> <span class="k">await</span> <span class="n">central_repo</span><span class="o">.</span><span class="n">get_all_ids</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>

    <span class="n">missing_in_central</span> <span class="o">=</span> <span class="n">local_ids</span> <span class="o">-</span> <span class="n">central_ids</span>
    <span class="n">missing_in_local</span> <span class="o">=</span> <span class="n">central_ids</span> <span class="o">-</span> <span class="n">local_ids</span>

    <span class="k">if</span> <span class="n">missing_in_central</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Missing in central: </span><span class="si">{</span><span class="n">missing_in_central</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
    <span class="k">if</span> <span class="n">missing_in_local</span><span class="p">:</span>
        <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Missing in local: </span><span class="si">{</span><span class="n">missing_in_local</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</code></pre></div></p>
<p><strong>Solutions:</strong>
1. Implement comprehensive sync validation
2. Add integrity checks before and after sync
3. Implement rollback mechanism for failed syncs
4. Add detailed audit logging</p>
<hr />
<h2 id="production-deployment-considerations">Production Deployment Considerations<a class="headerlink" href="#production-deployment-considerations" title="Permanent link">&para;</a></h2>
<h3 id="1-environment-configuration">1. Environment Configuration<a class="headerlink" href="#1-environment-configuration" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Environment variables for production</span>
<span class="nv">SYNC_BATCH_SIZE</span><span class="o">=</span><span class="m">50</span><span class="w">                    </span><span class="c1"># Batch size for sync operations</span>
<span class="nv">SYNC_RETRY_ATTEMPTS</span><span class="o">=</span><span class="m">5</span><span class="w">                 </span><span class="c1"># Max retry attempts</span>
<span class="nv">SYNC_TIMEOUT_SECONDS</span><span class="o">=</span><span class="m">300</span><span class="w">              </span><span class="c1"># Sync operation timeout</span>
<span class="nv">SYNC_LOG_RETENTION_DAYS</span><span class="o">=</span><span class="m">90</span><span class="w">            </span><span class="c1"># How long to keep sync logs</span>
<span class="nv">CONFLICT_RESOLUTION_STRATEGY</span><span class="o">=</span>last_write_wins<span class="w">  </span><span class="c1"># Default strategy</span>
<span class="nv">ENABLE_SYNC_METRICS</span><span class="o">=</span><span class="nb">true</span><span class="w">              </span><span class="c1"># Enable monitoring</span>
</code></pre></div>
<h3 id="2-monitoring-and-alerting">2. Monitoring and Alerting<a class="headerlink" href="#2-monitoring-and-alerting" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Production monitoring setup</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">prometheus_client</span><span class="w"> </span><span class="kn">import</span> <span class="n">Counter</span><span class="p">,</span> <span class="n">Histogram</span><span class="p">,</span> <span class="n">Gauge</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">logging</span>

<span class="c1"># Metrics</span>
<span class="n">sync_operations_total</span> <span class="o">=</span> <span class="n">Counter</span><span class="p">(</span><span class="s1">&#39;sync_operations_total&#39;</span><span class="p">,</span> <span class="s1">&#39;Total sync operations&#39;</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;status&#39;</span><span class="p">,</span> <span class="s1">&#39;project_id&#39;</span><span class="p">])</span>
<span class="n">sync_duration_seconds</span> <span class="o">=</span> <span class="n">Histogram</span><span class="p">(</span><span class="s1">&#39;sync_duration_seconds&#39;</span><span class="p">,</span> <span class="s1">&#39;Sync operation duration&#39;</span><span class="p">)</span>
<span class="n">sync_conflicts_total</span> <span class="o">=</span> <span class="n">Counter</span><span class="p">(</span><span class="s1">&#39;sync_conflicts_total&#39;</span><span class="p">,</span> <span class="s1">&#39;Total conflicts detected&#39;</span><span class="p">,</span> <span class="p">[</span><span class="s1">&#39;entity_type&#39;</span><span class="p">,</span> <span class="s1">&#39;resolution&#39;</span><span class="p">])</span>
<span class="n">sync_queue_size</span> <span class="o">=</span> <span class="n">Gauge</span><span class="p">(</span><span class="s1">&#39;sync_queue_size&#39;</span><span class="p">,</span> <span class="s1">&#39;Current sync queue size&#39;</span><span class="p">)</span>

<span class="k">class</span><span class="w"> </span><span class="nc">ProductionSynchronizationService</span><span class="p">(</span><span class="n">SynchronizationService</span><span class="p">):</span>
    <span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">synchronize_project</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">)</span> <span class="o">-&gt;</span> <span class="n">SynchronizationResult</span><span class="p">:</span>
        <span class="k">with</span> <span class="n">sync_duration_seconds</span><span class="o">.</span><span class="n">time</span><span class="p">():</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="nb">super</span><span class="p">()</span><span class="o">.</span><span class="n">synchronize_project</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>

                <span class="c1"># Record metrics</span>
                <span class="n">sync_operations_total</span><span class="o">.</span><span class="n">labels</span><span class="p">(</span><span class="n">status</span><span class="o">=</span><span class="s1">&#39;success&#39;</span><span class="p">,</span> <span class="n">project_id</span><span class="o">=</span><span class="n">project_id</span><span class="p">)</span><span class="o">.</span><span class="n">inc</span><span class="p">()</span>
                <span class="n">sync_conflicts_total</span><span class="o">.</span><span class="n">labels</span><span class="p">(</span>
                    <span class="n">entity_type</span><span class="o">=</span><span class="s1">&#39;all&#39;</span><span class="p">,</span>
                    <span class="n">resolution</span><span class="o">=</span><span class="n">result</span><span class="o">.</span><span class="n">conflict_resolution_strategy</span>
                <span class="p">)</span><span class="o">.</span><span class="n">inc</span><span class="p">(</span><span class="n">result</span><span class="o">.</span><span class="n">conflicts_resolved</span><span class="p">)</span>

                <span class="c1"># Alert on high conflict rates</span>
                <span class="k">if</span> <span class="n">result</span><span class="o">.</span><span class="n">conflicts_detected</span> <span class="o">&gt;</span> <span class="mi">10</span><span class="p">:</span>
                    <span class="n">logger</span><span class="o">.</span><span class="n">warning</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;High conflict rate for project </span><span class="si">{</span><span class="n">project_id</span><span class="si">}</span><span class="s2">: </span><span class="si">{</span><span class="n">result</span><span class="o">.</span><span class="n">conflicts_detected</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

                <span class="k">return</span> <span class="n">result</span>

            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                <span class="n">sync_operations_total</span><span class="o">.</span><span class="n">labels</span><span class="p">(</span><span class="n">status</span><span class="o">=</span><span class="s1">&#39;error&#39;</span><span class="p">,</span> <span class="n">project_id</span><span class="o">=</span><span class="n">project_id</span><span class="p">)</span><span class="o">.</span><span class="n">inc</span><span class="p">()</span>
                <span class="k">raise</span>
</code></pre></div>
<h3 id="3-database-optimization">3. Database Optimization<a class="headerlink" href="#3-database-optimization" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1">-- Production database indexes for sync performance</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">CONCURRENTLY</span><span class="w"> </span><span class="n">idx_project_updated_at</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">project</span><span class="p">(</span><span class="n">updated_at</span><span class="p">)</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">false</span><span class="p">;</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">CONCURRENTLY</span><span class="w"> </span><span class="n">idx_sync_log_project_id_started_at</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">synchronization_log</span><span class="p">(</span><span class="n">project_id</span><span class="p">,</span><span class="w"> </span><span class="n">started_at</span><span class="w"> </span><span class="k">DESC</span><span class="p">);</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">INDEX</span><span class="w"> </span><span class="n">CONCURRENTLY</span><span class="w"> </span><span class="n">idx_component_project_updated</span><span class="w"> </span><span class="k">ON</span><span class="w"> </span><span class="n">component</span><span class="p">(</span><span class="n">project_id</span><span class="p">,</span><span class="w"> </span><span class="n">updated_at</span><span class="p">)</span><span class="w"> </span><span class="k">WHERE</span><span class="w"> </span><span class="n">is_deleted</span><span class="w"> </span><span class="o">=</span><span class="w"> </span><span class="k">false</span><span class="p">;</span>

<span class="c1">-- Partitioning for large sync logs</span>
<span class="k">CREATE</span><span class="w"> </span><span class="k">TABLE</span><span class="w"> </span><span class="n">synchronization_log_2024</span><span class="w"> </span><span class="n">PARTITION</span><span class="w"> </span><span class="k">OF</span><span class="w"> </span><span class="n">synchronization_log</span><span class="w"> </span>
<span class="k">FOR</span><span class="w"> </span><span class="k">VALUES</span><span class="w"> </span><span class="k">FROM</span><span class="w"> </span><span class="p">(</span><span class="s1">&#39;2024-01-01&#39;</span><span class="p">)</span><span class="w"> </span><span class="k">TO</span><span class="w"> </span><span class="p">(</span><span class="s1">&#39;2025-01-01&#39;</span><span class="p">);</span>
</code></pre></div>
<h3 id="4-security-considerations">4. Security Considerations<a class="headerlink" href="#4-security-considerations" title="Permanent link">&para;</a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Secure sync endpoint with comprehensive validation</span>
<span class="nd">@router</span><span class="o">.</span><span class="n">post</span><span class="p">(</span><span class="s2">&quot;/projects/</span><span class="si">{project_id}</span><span class="s2">/sync&quot;</span><span class="p">)</span>
<span class="k">async</span> <span class="k">def</span><span class="w"> </span><span class="nf">synchronize_project</span><span class="p">(</span>
    <span class="n">project_id</span><span class="p">:</span> <span class="nb">int</span><span class="p">,</span>
    <span class="n">request</span><span class="p">:</span> <span class="n">SyncRequest</span><span class="p">,</span>
    <span class="n">current_user</span><span class="p">:</span> <span class="n">User</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_current_user</span><span class="p">),</span>
    <span class="n">rate_limiter</span><span class="p">:</span> <span class="n">RateLimiter</span> <span class="o">=</span> <span class="n">Depends</span><span class="p">(</span><span class="n">get_rate_limiter</span><span class="p">)</span>
<span class="p">):</span>
    <span class="c1"># Rate limiting</span>
    <span class="k">await</span> <span class="n">rate_limiter</span><span class="o">.</span><span class="n">check_rate_limit</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;sync:</span><span class="si">{</span><span class="n">current_user</span><span class="o">.</span><span class="n">id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="n">limit</span><span class="o">=</span><span class="mi">10</span><span class="p">,</span> <span class="n">window</span><span class="o">=</span><span class="mi">300</span><span class="p">)</span>

    <span class="c1"># Permission validation</span>
    <span class="n">project</span> <span class="o">=</span> <span class="k">await</span> <span class="n">project_repo</span><span class="o">.</span><span class="n">get_by_id</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>
    <span class="k">if</span> <span class="ow">not</span> <span class="n">project</span><span class="p">:</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="mi">404</span><span class="p">,</span> <span class="s2">&quot;Project not found&quot;</span><span class="p">)</span>

    <span class="k">await</span> <span class="n">check_project_access</span><span class="p">(</span><span class="n">current_user</span><span class="p">,</span> <span class="n">project</span><span class="p">,</span> <span class="s1">&#39;sync&#39;</span><span class="p">)</span>

    <span class="c1"># Input validation</span>
    <span class="k">if</span> <span class="n">request</span><span class="o">.</span><span class="n">last_sync_timestamp</span> <span class="ow">and</span> <span class="n">request</span><span class="o">.</span><span class="n">last_sync_timestamp</span> <span class="o">&gt;</span> <span class="n">datetime</span><span class="o">.</span><span class="n">utcnow</span><span class="p">():</span>
        <span class="k">raise</span> <span class="n">HTTPException</span><span class="p">(</span><span class="mi">400</span><span class="p">,</span> <span class="s2">&quot;Invalid sync timestamp from future&quot;</span><span class="p">)</span>

    <span class="c1"># Audit logging</span>
    <span class="k">await</span> <span class="n">audit_service</span><span class="o">.</span><span class="n">log_sync_attempt</span><span class="p">(</span>
        <span class="n">user_id</span><span class="o">=</span><span class="n">current_user</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
        <span class="n">project_id</span><span class="o">=</span><span class="n">project_id</span><span class="p">,</span>
        <span class="n">client_ip</span><span class="o">=</span><span class="n">request</span><span class="o">.</span><span class="n">client</span><span class="o">.</span><span class="n">host</span>
    <span class="p">)</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="n">result</span> <span class="o">=</span> <span class="k">await</span> <span class="n">sync_service</span><span class="o">.</span><span class="n">synchronize_project</span><span class="p">(</span><span class="n">project_id</span><span class="p">)</span>

        <span class="c1"># Success audit</span>
        <span class="k">await</span> <span class="n">audit_service</span><span class="o">.</span><span class="n">log_sync_success</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">current_user</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
            <span class="n">project_id</span><span class="o">=</span><span class="n">project_id</span><span class="p">,</span>
            <span class="n">sync_result</span><span class="o">=</span><span class="n">result</span>
        <span class="p">)</span>

        <span class="k">return</span> <span class="n">result</span>

    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="c1"># Error audit</span>
        <span class="k">await</span> <span class="n">audit_service</span><span class="o">.</span><span class="n">log_sync_error</span><span class="p">(</span>
            <span class="n">user_id</span><span class="o">=</span><span class="n">current_user</span><span class="o">.</span><span class="n">id</span><span class="p">,</span>
            <span class="n">project_id</span><span class="o">=</span><span class="n">project_id</span><span class="p">,</span>
            <span class="n">error</span><span class="o">=</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)</span>
        <span class="p">)</span>
        <span class="k">raise</span>
</code></pre></div>
<hr />
<h2 id="api-reference">API Reference<a class="headerlink" href="#api-reference" title="Permanent link">&para;</a></h2>
<h3 id="backend-api-endpoints">Backend API Endpoints<a class="headerlink" href="#backend-api-endpoints" title="Permanent link">&para;</a></h3>
<h4 id="post-apiv1projectsproject_idsync">POST /api/v1/projects/{project_id}/sync<a class="headerlink" href="#post-apiv1projectsproject_idsync" title="Permanent link">&para;</a></h4>
<p>Trigger synchronization for a specific project.</p>
<p><strong>Request:</strong>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;last_sync_timestamp&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-07-22T10:30:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;force_full_sync&quot;</span><span class="p">:</span><span class="w"> </span><span class="kc">false</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;conflict_resolution_strategy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;last_write_wins&quot;</span>
<span class="p">}</span>
</code></pre></div></p>
<p><strong>Response:</strong>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;project_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;completed&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;local_to_central&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;created&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;updated&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;deleted&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;central_to_local&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nt">&quot;created&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;updated&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;deleted&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span>
<span class="w">    </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span>
<span class="w">  </span><span class="p">},</span>
<span class="w">  </span><span class="nt">&quot;conflicts_detected&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;conflicts_resolved&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;conflict_resolution_strategy&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;last_write_wins&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;sync_direction&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bidirectional&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;started_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-07-22T10:31:00Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;completed_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-07-22T10:31:15Z&quot;</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;duration_ms&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;message&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;Synchronization completed successfully&quot;</span>
<span class="p">}</span>
</code></pre></div></p>
<h4 id="get-apiv1projectsproject_idsynchistory">GET /api/v1/projects/{project_id}/sync/history<a class="headerlink" href="#get-apiv1projectsproject_idsynchistory" title="Permanent link">&para;</a></h4>
<p>Get synchronization history for a project.</p>
<p><strong>Response:</strong>
<div class="highlight"><pre><span></span><code><span class="p">{</span>
<span class="w">  </span><span class="nt">&quot;items&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">[</span>
<span class="w">    </span><span class="p">{</span>
<span class="w">      </span><span class="nt">&quot;id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">456</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;project_id&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">123</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;sync_direction&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;bidirectional&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;operation_type&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;manual&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;started_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-07-22T10:31:00Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;completed_at&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;2024-07-22T10:31:15Z&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;duration_ms&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">15000</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;status&quot;</span><span class="p">:</span><span class="w"> </span><span class="s2">&quot;completed&quot;</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;conflicts_detected&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;conflicts_resolved&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span>
<span class="w">      </span><span class="nt">&quot;local_to_central&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nt">&quot;created&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">5</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;updated&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;deleted&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">},</span>
<span class="w">      </span><span class="nt">&quot;central_to_local&quot;</span><span class="p">:</span><span class="w"> </span><span class="p">{</span><span class="nt">&quot;created&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">2</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;updated&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">8</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;deleted&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">,</span><span class="w"> </span><span class="nt">&quot;errors&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">0</span><span class="p">}</span>
<span class="w">    </span><span class="p">}</span>
<span class="w">  </span><span class="p">],</span>
<span class="w">  </span><span class="nt">&quot;total&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">25</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;page&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">1</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;size&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">10</span><span class="p">,</span>
<span class="w">  </span><span class="nt">&quot;pages&quot;</span><span class="p">:</span><span class="w"> </span><span class="mi">3</span>
<span class="p">}</span>
</code></pre></div></p>
<h3 id="frontend-hooks-api">Frontend Hooks API<a class="headerlink" href="#frontend-hooks-api" title="Permanent link">&para;</a></h3>
<h4 id="useofflinemutation">useOfflineMutation<a class="headerlink" href="#useofflinemutation" title="Permanent link">&para;</a></h4>
<p>Hook for offline-capable mutations.</p>
<div class="highlight"><pre><span></span><code><span class="kd">interface</span><span class="w"> </span><span class="nx">OfflineMutationOptions</span><span class="o">&lt;</span><span class="nx">TData</span><span class="p">,</span><span class="w"> </span><span class="nx">TError</span><span class="p">,</span><span class="w"> </span><span class="nx">TVariables</span><span class="o">&gt;</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="nx">endpoint</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="p">((</span><span class="nx">variables</span><span class="o">:</span><span class="w"> </span><span class="kt">TVariables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="kt">string</span><span class="p">)</span>
<span class="w">  </span><span class="nx">method</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;GET&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;POST&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;PUT&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;DELETE&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;PATCH&#39;</span>
<span class="w">  </span><span class="nx">mutationFn</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="o">:</span><span class="w"> </span><span class="kt">TVariables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">TData</span><span class="o">&gt;</span>
<span class="w">  </span><span class="nx">optimisticResponse</span><span class="o">?:</span><span class="w"> </span><span class="p">(</span><span class="nx">variables</span><span class="o">:</span><span class="w"> </span><span class="kt">TVariables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="nx">TData</span>
<span class="w">  </span><span class="nx">onSuccess</span><span class="o">?:</span><span class="w"> </span><span class="p">(</span><span class="nx">data</span><span class="o">:</span><span class="w"> </span><span class="kt">TData</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="o">:</span><span class="w"> </span><span class="kt">TVariables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">void</span>
<span class="w">  </span><span class="nx">onError</span><span class="o">?:</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">TError</span><span class="p">,</span><span class="w"> </span><span class="nx">variables</span><span class="o">:</span><span class="w"> </span><span class="kt">TVariables</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">void</span>
<span class="w">  </span><span class="nx">metadata</span><span class="o">?:</span><span class="w"> </span><span class="p">{</span>
<span class="w">    </span><span class="nx">entityType</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span>
<span class="w">    </span><span class="nx">operation</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;create&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;update&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;delete&#39;</span>
<span class="w">    </span><span class="nx">priority</span><span class="o">:</span><span class="w"> </span><span class="s1">&#39;high&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;medium&#39;</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="s1">&#39;low&#39;</span>
<span class="w">  </span><span class="p">}</span>
<span class="p">}</span>
</code></pre></div>
<h4 id="syncmanager-api">SyncManager API<a class="headerlink" href="#syncmanager-api" title="Permanent link">&para;</a></h4>
<p>Central sync management interface.</p>
<div class="highlight"><pre><span></span><code><span class="kd">interface</span><span class="w"> </span><span class="nx">SyncManager</span><span class="w"> </span><span class="p">{</span>
<span class="w">  </span><span class="c1">// Core methods</span>
<span class="w">  </span><span class="nx">processOutbox</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">SyncResult</span><span class="o">&gt;</span>
<span class="w">  </span><span class="nx">addToOutbox</span><span class="p">(</span><span class="nx">entry</span><span class="o">:</span><span class="w"> </span><span class="kt">OutboxEntry</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span>
<span class="w">  </span><span class="nx">getOutboxCount</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="kt">number</span><span class="o">&gt;</span>

<span class="w">  </span><span class="c1">// Status methods</span>
<span class="w">  </span><span class="nx">isCurrentlyProcessing</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="kt">boolean</span>
<span class="w">  </span><span class="nx">getCurrentOperation</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="kt">string</span><span class="w"> </span><span class="o">|</span><span class="w"> </span><span class="kc">null</span>
<span class="w">  </span><span class="nx">getStats</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">SyncStats</span><span class="o">&gt;</span>

<span class="w">  </span><span class="c1">// Event handlers</span>
<span class="w">  </span><span class="nx">onSyncStart</span><span class="p">(</span><span class="nx">callback</span><span class="o">:</span><span class="w"> </span><span class="p">()</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">void</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span>
<span class="w">  </span><span class="nx">onSyncComplete</span><span class="p">(</span><span class="nx">callback</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">result</span><span class="o">:</span><span class="w"> </span><span class="kt">SyncResult</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">void</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span>
<span class="w">  </span><span class="nx">onSyncError</span><span class="p">(</span><span class="nx">callback</span><span class="o">:</span><span class="w"> </span><span class="p">(</span><span class="nx">error</span><span class="o">:</span><span class="w"> </span><span class="kt">Error</span><span class="p">)</span><span class="w"> </span><span class="p">=&gt;</span><span class="w"> </span><span class="ow">void</span><span class="p">)</span><span class="o">:</span><span class="w"> </span><span class="ow">void</span>

<span class="w">  </span><span class="c1">// Management</span>
<span class="w">  </span><span class="nx">clearOutbox</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="ow">void</span><span class="o">&gt;</span>
<span class="w">  </span><span class="nx">retryFailedOperations</span><span class="p">()</span><span class="o">:</span><span class="w"> </span><span class="nb">Promise</span><span class="o">&lt;</span><span class="nx">SyncResult</span><span class="o">&gt;</span>
<span class="p">}</span>
</code></pre></div>
<hr />
<p>This comprehensive developer guide provides all the practical information needed to work with the Ultimate Electrical Designer's synchronization and offline systems. For additional support, refer to the design documentation and consult the development team.</p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../js/base.js"></script>
        <script src="../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
