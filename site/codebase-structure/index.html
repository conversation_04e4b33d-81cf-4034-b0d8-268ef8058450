<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/codebase-structure/">
        <link rel="shortcut icon" href="../img/favicon.ico">
        <title>Codebase structure - Ultimate Electrical Designer Docs</title>
        <link href="../css/bootstrap.min.css" rel="stylesheet">
        <link href="../css/fontawesome.min.css" rel="stylesheet">
        <link href="../css/brands.min.css" rel="stylesheet">
        <link href="../css/solid.min.css" rel="stylesheet">
        <link href="../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href=".." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../tasks/" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<p>ued/
├── .env
├── .roomodes
├── README.md
├── .roo/
│   ├── rules/
│   │   └── rules.md
│   ├── rules-code/
│   │   ├── 1_workflow.xml
│   │   ├── 2_best_practices.xml
│   │   ├── 3_common_patterns.xml
│   │   ├── 4_tool_usage.xml
│   │   ├── 5_examples.xml
│   │   ├── 6_error_handling.xml
│   │   └── 7_communication.xml
│   ├── rules-orchestrator/
│   │   ├── 1_workflow.xml
│   │   ├── 2_best_practices.xml
│   │   ├── 3_tool_usage.xml
│   │   ├── 4_examples.xml
│   │   └── 5_error_handling.xml
│   ├── rules-quality/
│   │   ├── 1_workflow.xml
│   │   ├── 2_quality_standards.xml
│   │   ├── 3_tool_usage.xml
│   │   ├── 4_violation_reporting.xml
│   │   ├── 5_examples.xml
│   │   ├── 6_error_handling.xml
│   │   └── 7_communication.xml
│   ├── rules-task-planner/
│   │   ├── 1_task_breakdown_methodology.xml
│   │   ├── 2_task_templates.xml
│   │   ├── 3_planning_examples.xml
│   │   └── 4_quality_validation.xml
│   └── rules-technical-design/
│       ├── 1_discovery_analysis.xml
│       ├── 2_architectural_patterns.xml
│       ├── 3_design_examples.xml
│       └── 4_validation_checklist.xml
├── cad-integrator-service/
│   ├── Dockerfile
│   ├── README.md
│   └── src/
│       ├── ultimate_electrical_designer.CadIntegrator.csproj
│       ├── Controllers/
│       │   └── CadController.cs
│       └── Services/
│           └── AutoCADService.cs
├── client/
│   ├── DOMAIN_MIGRATION_ROADMAP.md
│   ├── playwright.config.ts
│   ├── tailwind.config.ts
│   ├── vitest.config.ts
│   ├── vitest.setup.ts
│   ├── docs/
│   │   ├── atomic-design-system/
│   │   │   └── ATOMIC_DESIGN_GUIDE.md
│   │   └── caching-design-system/
│   │       └── CACHE_DESIGN.md
│   ├── src/
│   │   ├── app/
│   │   │   ├── layout.tsx
│   │   │   ├── page.tsx
│   │   │   └── providers.tsx
│   │   ├── assets/
│   │   ├── components/
│   │   │   ├── atoms/
│   │   │   │   ├── badge-alt.tsx
│   │   │   │   ├── badge.tsx
│   │   │   │   ├── button.tsx
│   │   │   │   ├── checkbox.tsx
│   │   │   │   ├── form.tsx
│   │   │   │   ├── icon.tsx
│   │   │   │   ├── input.tsx
│   │   │   │   ├── label.tsx
│   │   │   │   ├── progress.tsx
│   │   │   │   ├── radio-group.tsx
│   │   │   │   ├── separator.tsx
│   │   │   │   ├── skeleton.tsx
│   │   │   │   ├── slider.tsx
│   │   │   │   ├── sonner.tsx
│   │   │   │   ├── state.tsx
│   │   │   │   ├── switch.tsx
│   │   │   │   ├── textarea.tsx
│   │   │   │   ├── timeline.tsx
│   │   │   │   └── toggle.tsx
│   │   │   ├── modules/
│   │   │   │   └── navigation/
│   │   │   │       ├── app-sidebar.tsx
│   │   │   │       ├── nav-items.ts
│   │   │   │       ├── nav-list-item.tsx
│   │   │   │       ├── nav-user.tsx
│   │   │   │       └── <strong>tests</strong>/
│   │   │   │           └── app-sidebar.test.tsx
│   │   │   ├── molecules/
│   │   │   │   ├── accordion.tsx
│   │   │   │   ├── alert.tsx
│   │   │   │   ├── avatar.tsx
│   │   │   │   ├── breadcrumb.tsx
│   │   │   │   ├── card.tsx
│   │   │   │   ├── collapsible.tsx
│   │   │   │   ├── form.tsx
│   │   │   │   ├── hover-card.tsx
│   │   │   │   ├── popover.tsx
│   │   │   │   ├── select-native.tsx
│   │   │   │   ├── select.tsx
│   │   │   │   ├── table.tsx
│   │   │   │   ├── toast.tsx
│   │   │   │   ├── toaster.tsx
│   │   │   │   ├── toggle-group.tsx
│   │   │   │   └── tooltip.tsx
│   │   │   ├── organisms/
│   │   │   │   ├── alert-dialog.tsx
│   │   │   │   ├── calendar-rac.tsx
│   │   │   │   ├── calendar.tsx
│   │   │   │   ├── command.tsx
│   │   │   │   ├── datefield-rac.tsx
│   │   │   │   ├── dialog.tsx
│   │   │   │   ├── dropdown-menu.tsx
│   │   │   │   ├── multiselect.tsx
│   │   │   │   ├── navigation-menu.tsx
│   │   │   │   ├── pagination.tsx
│   │   │   │   ├── sheet.tsx
│   │   │   │   ├── sidebar.tsx
│   │   │   │   ├── stepper.tsx
│   │   │   │   └── tree.tsx
│   │   │   └── templates/
│   │   │       ├── checkbox-tree.tsx
│   │   │       ├── cropper.tsx
│   │   │       ├── resizable.tsx
│   │   │       ├── scroll-area.tsx
│   │   │       └── tabs.tsx
│   │   ├── core/
│   │   │   └── caching/
│   │   │       ├── cache_provider.tsx
│   │   │       ├── index.ts
│   │   │       ├── indexed_db_persister.ts
│   │   │       ├── sync_manager.ts
│   │   │       └── <strong>tests</strong>/
│   │   │           ├── cache_provider.integration.test.tsx
│   │   │           ├── cache_provider.test.tsx
│   │   │           ├── cache_provider.validation.test.tsx
│   │   │           ├── indexed_db_persister.integration.test.ts
│   │   │           ├── indexed_db_persister.test.ts
│   │   │           ├── indexed_db_persister.validation.test.ts
│   │   │           ├── sync_manager.test.ts
│   │   │           └── sync_manager.validation.test.tsx
│   │   ├── hooks/
│   │   │   ├── useCharacterLimit.ts
│   │   │   ├── useCopyToClipboard.ts
│   │   │   ├── useFileUpload.ts
│   │   │   ├── useLayout.tsx
│   │   │   ├── useOfflineMutation.ts
│   │   │   ├── usePagination.ts
│   │   │   ├── useSliderWithInput.ts
│   │   │   ├── useToast.ts
│   │   │   ├── <strong>tests</strong>/
│   │   │   │   ├── useAuth.test.tsx
│   │   │   │   └── useOfflineMutation.test.tsx
│   │   │   └── api/
│   │   │       ├── useAudit.ts
│   │   │       ├── useAuth.ts
│   │   │       ├── useRbac.ts
│   │   │       ├── useUsers.ts
│   │   │       └── <strong>tests</strong>/
│   │   │           ├── useAudit.test.tsx
│   │   │           └── useRbac.test.tsx
│   │   ├── lib/
│   │   │   ├── config.ts
│   │   │   ├── fonts.ts
│   │   │   ├── utils.ts
│   │   │   ├── api/
│   │   │   ├── auth/
│   │   │   ├── store/
│   │   │   │   └── index.ts
│   │   │   └── websocket/
│   │   ├── providers/
│   │   │   ├── query-provider.tsx
│   │   │   └── theme-provider.tsx
│   │   ├── stores/
│   │   │   ├── authStore.ts
│   │   │   ├── networkStatusStore.ts
│   │   │   └── <strong>tests</strong>/
│   │   │       └── networkStatusStore.test.ts
│   │   ├── styles/
│   │   ├── test/
│   │   │   ├── setup.ts
│   │   │   ├── utils.tsx
│   │   │   ├── factories/
│   │   │   ├── integration/
│   │   │   │   ├── auth-integration.test.tsx
│   │   │   │   ├── offline-mode-integration.test.tsx
│   │   │   │   └── rbac-workflow.test.tsx
│   │   │   └── reporters/
│   │   │       └── domain-reporter.ts
│   │   ├── types/
│   │   │   └── nav.ts
│   │   └── utils/
│   │       ├── active-theme.tsx
│   │       ├── tailwind-indicator.tsx
│   │       └── textUtils.ts
│   └── tests/
│       ├── e2e/
│       │   ├── cache-persistence.spec.ts
│       │   ├── component-management.spec.ts
│       │   ├── global.setup.ts
│       │   ├── offline-mode.spec.ts
│       │   └── rbac-audit-workflow.spec.ts
│       └── mocks/
│           ├── server.ts
│           ├── fixtures/
│           │   ├── auth.ts
│           │   ├── componentCategories.ts
│           │   ├── componentTypes.ts
│           │   └── components.ts
│           └── handlers/
│               ├── auth.ts
│               ├── componentCategories.ts
│               ├── componentTypes.ts
│               ├── components.ts
│               ├── health.ts
│               ├── projects.ts
│               └── users.ts
├── computation-engine-service/
│   ├── Dockerfile
│   ├── README.md
│   └── src/
│       ├── ultimate_electrical_designer.ComputationEngine.csproj
│       ├── Controllers/
│       │   └── ComputationController.cs
│       └── Services/
│           └── PowerFlowSolver.cs
├── docs/
│   ├── README.md
│   ├── TESTING.md
│   ├── codebase-structure.md
│   ├── design.md
│   ├── product.md
│   ├── requirements.md
│   ├── rules.md
│   ├── tasks.md
│   ├── tech.md
│   ├── workflows.md
│   ├── deployment/
│   │   └── POSTGRESQL_INSTALLER_PLAN.md
│   ├── developer-guides/
│   │   └── synchronization-developer-guide.md
│   ├── personal/
│   │   ├── agents/
│   │   │   ├── code.md
│   │   │   ├── framework.md
│   │   │   ├── orchestrator.md
│   │   │   ├── quality.md
│   │   │   ├── task-planner.md
│   │   │   ├── technical-design.md
│   │   │   └── template.md
│   │   └── templates/
│   │       └── Fix Type Errors Template.md
│   └── tasks/
│       └── test-failures-resolution/
│           ├── code-quality-verification-report.md
│           ├── component-routes-refactoring-summary.md
│           ├── http-status-code-audit-report.md
│           ├── implementation-plan-test-failures-resolution.md
│           ├── middleware-safe-error-handling-design.md
│           ├── mock-usage-audit-report.md
│           ├── standardized-mock-strategy.md
│           ├── technical-design-test-failures-resolution.md
│           ├── transaction-based-test-isolation-design.md
│           └── user-routes-refactoring-summary.md
├── scripts/
├── server/
│   ├── .env
│   ├── Dockerfile
│   ├── README.md
│   ├── conftest.py
│   ├── pyproject.toml
│   ├── data/
│   │   └── seed_general.py
│   ├── docs/
│   │   ├── 2025-07-19_test-database-integrity/
│   │   │   ├── 01-test_database_integrity_report.md
│   │   │   ├── 02-enhanced_verification_report.md
│   │   │   ├── 03-test_coverage_immediate_actions.md
│   │   │   ├── 04-performance_testing_enhancement_summary.md
│   │   │   ├── 05-database_operations_testing_comprehensive_summary.md
│   │   │   └── 06-advanced_validation_compatibility.md
│   │   ├── 2025-07-20_dual-database-setup-DEPRECATED/
│   │   │   ├── discovery-analysis.md
│   │   │   ├── dual-database-guide.md
│   │   │   ├── implementation-report.md
│   │   │   └── plan.md
│   │   ├── 2025-07-21_offline-mode/
│   │   │   ├── discovery_analysis.md
│   │   │   └── plan.md
│   │   ├── 2025-07-22_unified_local_database/
│   │   │   ├── phase1_discovery_analysis.md
│   │   │   ├── phase2_implementation_plan.md
│   │   │   └── phase2_implementation_report.md
│   │   └── 2025-07-29_comprehensive-test-verification/
│   │       ├── phase4_comprehensive_verification_report.md
│   │       └── systematic_issues_resolution_summary.md
│   ├── src/
│   │   ├── app.py
│   │   ├── main.py
│   │   ├── alembic/
│   │   ├── api/
│   │   │   ├── main_router.py
│   │   │   └── v1/
│   │   │       ├── auth_routes.py
│   │   │       ├── component_category_routes.py
│   │   │       ├── component_routes.py
│   │   │       ├── component_type_routes.py
│   │   │       ├── cross_validation_routes.py
│   │   │       ├── health_routes.py
│   │   │       ├── parallel_validation_routes.py
│   │   │       ├── project_phase_routes.py
│   │   │       ├── project_routes.py
│   │   │       ├── router.py
│   │   │       ├── system_configuration_routes.py
│   │   │       ├── task_routes.py
│   │   │       ├── user_preferences_routes.py
│   │   │       ├── user_routes.py
│   │   │       └── validation_routes.py
│   │   ├── config/
│   │   │   ├── logging_config.py
│   │   │   └── settings.py
│   │   ├── core/
│   │   │   ├── auth/
│   │   │   │   └── dependencies.py
│   │   │   ├── calculations/
│   │   │   ├── database/
│   │   │   │   ├── connection_manager.py
│   │   │   │   ├── dependencies.py
│   │   │   │   ├── engine.py
│   │   │   │   ├── initialization.py
│   │   │   │   └── session.py
│   │   │   ├── enums/
│   │   │   │   ├── calculation_enums.py
│   │   │   │   ├── common_enums.py
│   │   │   │   ├── data_io_enums.py
│   │   │   │   ├── electrical_enums.py
│   │   │   │   ├── heat_tracing_enums.py
│   │   │   │   ├── mechanical_enums.py
│   │   │   │   ├── project_management_enums.py
│   │   │   │   ├── standards_enums.py
│   │   │   │   └── system_enums.py
│   │   │   ├── errors/
│   │   │   │   ├── exceptions.py
│   │   │   │   └── unified_error_handler.py
│   │   │   ├── integrations/
│   │   │   │   └── README.md
│   │   │   ├── models/
│   │   │   │   ├── base.py
│   │   │   │   └── general/
│   │   │   │       ├── activity_log.py
│   │   │   │       ├── component.py
│   │   │   │       ├── component_category.py
│   │   │   │       ├── component_type.py
│   │   │   │       ├── project.py
│   │   │   │       ├── project_phase.py
│   │   │   │       ├── synchronization_log.py
│   │   │   │       ├── system_configuration.py
│   │   │   │       ├── task.py
│   │   │   │       ├── user.py
│   │   │   │       └── user_role.py
│   │   │   ├── monitoring/
│   │   │   │   ├── performance_monitor.py
│   │   │   │   └── unified_performance_monitor.py
│   │   │   ├── repositories/
│   │   │   │   ├── base_repository.py
│   │   │   │   ├── repository_dependencies.py
│   │   │   │   └── general/
│   │   │   │       ├── component_category_repository.py
│   │   │   │       ├── component_repository.py
│   │   │   │       ├── component_type_repository.py
│   │   │   │       ├── project_member_repository.py
│   │   │   │       ├── project_repository.py
│   │   │   │       ├── task_repository.py
│   │   │   │       ├── user_preference_repository.py
│   │   │   │       └── user_repository.py
│   │   │   ├── schemas/
│   │   │   │   ├── base_schemas.py
│   │   │   │   ├── error.py
│   │   │   │   ├── health.py
│   │   │   │   └── general/
│   │   │   │       ├── audit_trail_schemas.py
│   │   │   │       ├── component_category_schemas.py
│   │   │   │       ├── component_schemas.py
│   │   │   │       ├── component_type_schemas.py
│   │   │   │       ├── project_member_schemas.py
│   │   │   │       ├── project_phase_schemas.py
│   │   │   │       ├── project_schemas.py
│   │   │   │       ├── system_configuration_schemas.py
│   │   │   │       ├── task_schemas.py
│   │   │   │       ├── user_role_schemas.py
│   │   │   │       └── user_schemas.py
│   │   │   ├── security/
│   │   │   │   ├── enhanced_dependencies.py
│   │   │   │   ├── input_validators.py
│   │   │   │   ├── password_handler.py
│   │   │   │   └── unified_security_validator.py
│   │   │   ├── services/
│   │   │   │   ├── dependencies.py
│   │   │   │   └── general/
│   │   │   │       ├── audit_trail_service.py
│   │   │   │       ├── component_category_service.py
│   │   │   │       ├── component_service.py
│   │   │   │       ├── component_type_service.py
│   │   │   │       ├── health_service.py
│   │   │   │       ├── project_member_service.py
│   │   │   │       ├── project_phase_service.py
│   │   │   │       ├── project_service.py
│   │   │   │       ├── synchronization_service.py
│   │   │   │       ├── task_manager_service.py
│   │   │   │       └── user_service.py
│   │   │   ├── standards/
│   │   │   ├── utils/
│   │   │   │   ├── advanced_cache_manager.py
│   │   │   │   ├── crud_endpoint_factory.py
│   │   │   │   ├── datetime_utils.py
│   │   │   │   ├── file_io_utils.py
│   │   │   │   ├── json_validation.py
│   │   │   │   ├── logger.py
│   │   │   │   ├── memory_manager.py
│   │   │   │   ├── pagination_utils.py
│   │   │   │   ├── performance_optimizer.py
│   │   │   │   ├── performance_utils.py
│   │   │   │   ├── query_optimizer.py
│   │   │   │   ├── query_utils.py
│   │   │   │   ├── search_query_builder.py
│   │   │   │   ├── security.py
│   │   │   │   ├── string_utils.py
│   │   │   │   └── uuid_utils.py
│   │   │   └── validation/
│   │   │       ├── advanced_validators.py
│   │   │       ├── compatibility_matrix.py
│   │   │       ├── constraint_validator.py
│   │   │       ├── cross_entity_validator.py
│   │   │       ├── data_format_validator.py
│   │   │       ├── intelligent_caching.py
│   │   │       ├── json_schema_validator.py
│   │   │       ├── legacy_migration_validator.py
│   │   │       ├── parallel_processor.py
│   │   │       └── standards_validator.py
│   │   ├── middleware/
│   │   │   ├── caching_middleware.py
│   │   │   ├── context_middleware.py
│   │   │   ├── logging_middleware.py
│   │   │   ├── rate_limiting_middleware.py
│   │   │   └── security_middleware.py
│   │   └── ~/
│   ├── tests/
│   │   ├── conftest.py
│   │   ├── test_cleanup_utilities.py
│   │   ├── test_transaction_isolation.py
│   │   ├── api/
│   │   │   ├── conftest.py
│   │   │   └── v1/
│   │   │       ├── test_auth_routes.py
│   │   │       ├── test_component_category_routes.py
│   │   │       ├── test_component_routes.py
│   │   │       ├── test_component_type_routes.py
│   │   │       ├── test_health_routes.py
│   │   │       ├── test_project_routes.py
│   │   │       ├── test_task_routes.py
│   │   │       └── test_user_routes.py
│   │   ├── core/
│   │   │   ├── calculations/
│   │   │   │   └── conftest.py
│   │   │   ├── config/
│   │   │   │   └── test_settings.py
│   │   │   ├── database/
│   │   │   │   ├── test_alembic_migration_automation.py
│   │   │   │   ├── test_connection_manager.py
│   │   │   │   ├── test_connection_manager_integration.py
│   │   │   │   ├── test_migration_rollback_scenarios.py
│   │   │   │   ├── test_project_database_routing.py
│   │   │   │   └── test_task_migration.py
│   │   │   ├── errors/
│   │   │   │   ├── test_error_context.py
│   │   │   │   ├── test_error_handler_decorators.py
│   │   │   │   ├── test_error_handling_result.py
│   │   │   │   ├── test_middleware_safe_error_handlers.py
│   │   │   │   └── test_unified_error_handler.py
│   │   │   ├── models/
│   │   │   │   ├── test_component.py
│   │   │   │   ├── test_component_category.py
│   │   │   │   ├── test_component_relational.py
│   │   │   │   ├── test_component_type.py
│   │   │   │   ├── test_project_model_database_url.py
│   │   │   │   ├── test_synchronization_log_model.py
│   │   │   │   └── test_task.py
│   │   │   ├── repositories/
│   │   │   │   ├── conftest.py
│   │   │   │   ├── test_component_category_repository.py
│   │   │   │   ├── test_component_repository.py
│   │   │   │   ├── test_component_type_repository.py
│   │   │   │   └── test_task_repository.py
│   │   │   ├── security/
│   │   │   │   ├── test_input_validators.py
│   │   │   │   └── test_password_handler.py
│   │   │   ├── services/
│   │   │   │   ├── conftest.py
│   │   │   │   ├── test_component_category_service.py
│   │   │   │   ├── test_component_service.py
│   │   │   │   ├── test_component_type_service.py
│   │   │   │   ├── test_project_member_service.py
│   │   │   │   ├── test_project_service.py
│   │   │   │   ├── test_project_service_database_url.py
│   │   │   │   ├── test_synchronization_service_conflict_resolution.py
│   │   │   │   ├── test_synchronization_service_main_orchestration.py
│   │   │   │   ├── test_synchronization_service_utilities.py
│   │   │   │   ├── test_task_manager_service.py
│   │   │   │   └── test_user_service.py
│   │   │   └── utils/
│   │   │       └── test_advanced_search.py
│   │   ├── fixtures/
│   │   │   ├── assertion_helpers.py
│   │   │   ├── integration_factories.py
│   │   │   ├── mock_factories.py
│   │   │   └── test_mock_factories.py
│   │   ├── integration/
│   │   │   ├── test_component_management_workflow.py
│   │   │   ├── test_comprehensive_data_integrity.py
│   │   │   ├── test_constraint_violations.py
│   │   │   ├── test_data_integrity.py
│   │   │   ├── test_middleware_integration.py
│   │   │   ├── test_middleware_safe_error_handling.py
│   │   │   ├── test_synchronization_service_cdc.py
│   │   │   ├── test_synchronization_service_conflict_integration.py
│   │   │   ├── test_synchronization_service_log_integration.py
│   │   │   ├── test_synchronization_service_transaction_management.py
│   │   │   └── test_validation_integration.py
│   │   ├── middleware/
│   │   │   ├── conftest.py
│   │   │   ├── test_caching_middleware.py
│   │   │   ├── test_context_middleware.py
│   │   │   ├── test_logging_middleware.py
│   │   │   ├── test_rate_limiting_middleware.py
│   │   │   └── test_security_middleware.py
│   │   ├── performance/
│   │   │   ├── conftest.py
│   │   │   ├── locust_validation_load_tests.py
│   │   │   ├── sync_repository_adapter.py
│   │   │   ├── test_component_performance.py
│   │   │   ├── test_concurrent_validation_stress.py
│   │   │   ├── test_database_performance.py
│   │   │   ├── test_email_lookup_benchmarks.py
│   │   │   ├── test_email_lookup_scale_performance.py
│   │   │   ├── test_memory_usage_concurrency.py
│   │   │   ├── test_performance_optimization.py
│   │   │   └── test_validation_pipeline_performance.py
│   │   └── validation/
│   │       ├── test_advanced_validators.py
│   │       ├── test_compatibility_matrix.py
│   │       ├── test_data_format_validator.py
│   │       ├── test_json_schema_validator.py
│   │       ├── test_legacy_migration_validator.py
│   │       ├── test_parallel_processor.py
│   │       └── test_standards_validator.py
│   └── ~/
└── ~/</p></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../js/base.js"></script>
        <script src="../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
