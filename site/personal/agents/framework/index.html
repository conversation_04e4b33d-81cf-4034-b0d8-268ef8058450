<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        
        
        <link rel="canonical" href="https://example.com/personal/agents/framework/">
        <link rel="shortcut icon" href="../../../img/favicon.ico">
        <title>Agentic Framework - Ultimate Electrical Designer Docs</title>
        <link href="../../../css/bootstrap.min.css" rel="stylesheet">
        <link href="../../../css/fontawesome.min.css" rel="stylesheet">
        <link href="../../../css/brands.min.css" rel="stylesheet">
        <link href="../../../css/solid.min.css" rel="stylesheet">
        <link href="../../../css/v4-font-face.min.css" rel="stylesheet">
        <link href="../../../css/base.css" rel="stylesheet">
        <link id="hljs-light" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github.min.css" >
        <link id="hljs-dark" rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/github-dark.min.css" disabled>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
        <script>hljs.highlightAll();</script> 
    </head>

    <body>
        <div class="navbar fixed-top navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="../../..">Ultimate Electrical Designer Docs</a>
                <!-- Expander button -->
                <button type="button" class="navbar-toggler" data-bs-toggle="collapse" data-bs-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Expanded navigation -->
                <div id="navbar-collapse" class="navbar-collapse collapse">
                        <!-- Main navigation -->
                        <ul class="nav navbar-nav">
                            <li class="nav-item">
                                <a href="../../.." class="nav-link">Overview</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../product/" class="nav-link">Product</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../structure/" class="nav-link">Structure</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../tech/" class="nav-link">Technology</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../rules/" class="nav-link">Rules</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../requirements/" class="nav-link">Requirements</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../design/" class="nav-link">Design</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../tasks/" class="nav-link">Tasks</a>
                            </li>
                            <li class="nav-item">
                                <a href="../../../TESTING/" class="nav-link">Testing</a>
                            </li>
                            <li class="nav-item dropdown">
                                <a href="#" class="nav-link dropdown-toggle" role="button" data-bs-toggle="dropdown"  aria-expanded="false">Developer Guides</a>
                                <ul class="dropdown-menu">
                                    
<li>
    <a href="../../../developer-guides/synchronization-developer-guide/" class="dropdown-item">Synchronization Guide</a>
</li>
                                    
<li>
    <a href="../../../atomic-design-system/ATOMIC_DESIGN_GUIDE/" class="dropdown-item">Atomic Design System</a>
</li>
                                </ul>
                            </li>
                        </ul>

                    <ul class="nav navbar-nav ms-md-auto">
                        <li class="nav-item">
                            <a href="#" class="nav-link" data-bs-toggle="modal" data-bs-target="#mkdocs_search_modal">
                                <i class="fa fa-search"></i> Search
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="container">
            <div class="row">
                    <div class="col-md-3"><div class="navbar-expand-md bs-sidebar hidden-print affix" role="complementary">
    <div class="navbar-header">
        <button type="button" class="navbar-toggler collapsed" data-bs-toggle="collapse" data-bs-target="#toc-collapse" title="Table of Contents">
            <span class="fa fa-angle-down"></span>
        </button>
    </div>

    
    <div id="toc-collapse" class="navbar-collapse collapse card bg-body-tertiary">
        <ul class="nav flex-column">
            
            <li class="nav-item" data-bs-level="1"><a href="#agentic-framework" class="nav-link">Agentic Framework</a>
              <ul class="nav flex-column">
            <li class="nav-item" data-bs-level="2"><a href="#agents" class="nav-link">Agents</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#pipeline" class="nav-link">Pipeline</a>
              <ul class="nav flex-column">
              </ul>
            </li>
            <li class="nav-item" data-bs-level="2"><a href="#documentation" class="nav-link">Documentation</a>
              <ul class="nav flex-column">
              </ul>
            </li>
              </ul>
            </li>
        </ul>
    </div>
</div></div>
                    <div class="col-md-9" role="main">

<h1 id="agentic-framework">Agentic Framework<a class="headerlink" href="#agentic-framework" title="Permanent link">&para;</a></h1>
<p>This document outlines the agentic framework for the Ultimate Electrical Designer project. The framework consists of
five specialized agents, each with a distinct role and responsibility. The agents work together in a predefined pipeline
to ensure that the project's standards and methodologies are consistently applied throughout the feature development
lifecycle.</p>
<h2 id="agents">Agents<a class="headerlink" href="#agents" title="Permanent link">&para;</a></h2>
<ol>
<li><strong>Orchestrator Agent</strong>: Oversees the entire feature development process, ensuring adherence to the 5-Phase
   Implementation Methodology and coordinating between specialized agents.</li>
<li><strong>Technical Design Agent</strong>: Defines the technical architecture and design for new features, ensuring alignment with
   project standards and the 5-layer architecture.</li>
<li><strong>Task Planner Agent</strong>: Breaks down the design into actionable implementation tasks, creating a structured plan for
   the development team.</li>
<li><strong>Backend/Frontend Agent</strong>: Implements the feature by executing the task plan, adhering to the project's
   architectural patterns and development standards.</li>
<li><strong>Code Quality Agent</strong>: Enforces all quality standards and rules, ensuring that the implemented code meets the
   project's engineering-grade quality requirements.</li>
</ol>
<h2 id="pipeline">Pipeline<a class="headerlink" href="#pipeline" title="Permanent link">&para;</a></h2>
<p>The agentic pipeline follows a sequential workflow, with each agent's output serving as the input for the next agent in
the pipeline. The pipeline consists of the following stages:</p>
<ol>
<li><strong>Discovery &amp; Analysis</strong>: The Orchestrator Agent delegates the initial request to the Technical Design Agent for
   defining the feature's purpose, scope, and technical requirements.</li>
<li><strong>Task Planning</strong>: The Technical Design Agent's output is passed to the Task Planner Agent, which breaks down the
   design into actionable implementation tasks.</li>
<li><strong>Implementation</strong>: The Task Planner Agent's output is passed to the Backend/Frontend Agent, which implements the
   feature by executing the task plan.</li>
<li><strong>Verification</strong>: The Backend/Frontend Agent's output is passed to the Code Quality Agent, which enforces all quality
   standards and rules.</li>
<li><strong>Documentation &amp; Handover</strong>: The Code Quality Agent's output is passed back to the Orchestrator Agent, which
   oversees the final documentation and handover process.</li>
</ol>
<h2 id="documentation">Documentation<a class="headerlink" href="#documentation" title="Permanent link">&para;</a></h2>
<p>All agents MUST reference the project's documentation for guidance on technical standards, architectural patterns, and
quality requirements. The documentation is stored in the <code>docs/</code> directory and includes the following files:</p>
<ul>
<li><code>README.md</code>: Project overview and high-level guidelines.</li>
<li><code>requirements.md</code>: Detailed requirements and acceptance criteria.</li>
<li><code>design.md</code>: Technical design and architecture documentation.</li>
<li><code>tech.md</code>: Technology stack and tooling documentation.</li>
<li><code>rules.md</code>: Development standards and quality gates.</li>
<li><code>workflows.md</code>: Common development workflows and methodologies.</li>
<li><code>TESTING.md</code>: Authoritative testing documentation and strategy.</li>
</ul></div>
            </div>
        </div>

        <footer class="col-md-12">
            <hr>
            <p>Documentation built with <a href="https://www.mkdocs.org/">MkDocs</a>.</p>
        </footer>
        <script src="../../../js/bootstrap.bundle.min.js"></script>
        <script>
            var base_url = "../../..",
                shortcuts = {"help": 191, "next": 78, "previous": 80, "search": 83};
        </script>
        <script src="../../../js/base.js"></script>
        <script src="../../../search/main.js"></script>

        <div class="modal" id="mkdocs_search_modal" tabindex="-1" role="dialog" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="searchModalLabel">Search</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>From here you can search these documents. Enter your search terms below.</p>
                <form>
                    <div class="form-group">
                        <input type="search" class="form-control" placeholder="Search..." id="mkdocs-search-query" title="Type search term here">
                    </div>
                </form>
                <div id="mkdocs-search-results" data-no-results-text="No results found"></div>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div><div class="modal" id="mkdocs_keyboard_modal" tabindex="-1" role="dialog" aria-labelledby="keyboardModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="keyboardModalLabel">Keyboard Shortcuts</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <table class="table">
                <thead>
                  <tr>
                    <th style="width: 20%;">Keys</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="help shortcut"><kbd>?</kbd></td>
                    <td>Open this help</td>
                  </tr>
                  <tr>
                    <td class="next shortcut"><kbd>n</kbd></td>
                    <td>Next page</td>
                  </tr>
                  <tr>
                    <td class="prev shortcut"><kbd>p</kbd></td>
                    <td>Previous page</td>
                  </tr>
                  <tr>
                    <td class="search shortcut"><kbd>s</kbd></td>
                    <td>Search</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div class="modal-footer">
            </div>
        </div>
    </div>
</div>

    </body>
</html>
