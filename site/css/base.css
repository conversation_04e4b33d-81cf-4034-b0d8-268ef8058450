html {
    /* The nav header is 3.5rem high, plus 20px for the margin-top of the
       main container. */
    scroll-padding-top: calc(3.5rem + 20px);
}

/* Replacement for `body { background-attachment: fixed; }`, which has
   performance issues when scrolling on large displays. See #1394. */
body::before {
    content: ' ';
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: var(--bs-body-bg);
    background: url(../img/grid.png) repeat-x;
    will-change: transform;
    z-index: -1;
}

body > .container {
    margin-top: 20px;
    min-height: 400px;
}

.navbar.fixed-top {
    position: -webkit-sticky;
    position: sticky;
}

.source-links {
    float: right;
}

.col-md-9 img {
    max-width: 100%;
    display: inline-block;
    padding: 4px;
    line-height: 1.428571429;
    background-color: var(--bs-secondary-bg-subtle);
    border: 1px solid var(--bs-secondary-border-subtle);
    border-radius: 4px;
    margin: 20px auto 30px auto;
}

h1 {
    color: inherit;
    font-weight: 400;
    font-size: 42px;
}

h2, h3, h4, h5, h6 {
    color: inherit;
    font-weight: 300;
}

hr {
    border-top: 1px solid #aaa;
    opacity: 1;
}

pre, .rst-content tt {
    max-width: 100%;
    background-color: var(--bs-body-bg);
    border: solid 1px var(--bs-border-color);
    color: var(--bs-body-color);
    overflow-x: auto;
}

code.code-large, .rst-content tt.code-large {
    font-size: 90%;
}

code {
    padding: 2px 5px;
    background-color: rgba(var(--bs-body-bg-rgb), 0.75);
    border: solid 1px var(--bs-border-color);
    color: var(--bs-body-color);
    white-space: pre-wrap;
    word-wrap: break-word;
}

pre code {
    display: block;
    border: none;
    white-space: pre;
    word-wrap: normal;
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 12px;
}

kbd {
    padding: 2px 4px;
    font-size: 90%;
    color: var(--bs-secondary-text-emphasis);
    background-color: var(--bs-secondary-bg-subtle);
    border-radius: 3px;
    -webkit-box-shadow: inset 0 -1px 0 rgba(0,0,0,.25);
    box-shadow: inset 0 -1px 0 rgba(0,0,0,.25);
}

a code {
    color: inherit;
}

a:hover code, a:focus code {
    color: inherit;
}

footer {
    margin-top: 30px;
    margin-bottom: 10px;
    text-align: center;
    font-weight: 200;
}

.modal-dialog {
    margin-top: 60px;
}

/*
 * Side navigation
 *
 * Scrollspy and affixed enhanced navigation to highlight sections and secondary
 * sections of docs content.
 */

.bs-sidebar.affix {
    position: -webkit-sticky;
    position: sticky;
    /* The nav header is 3.5rem high, plus 20px for the margin-top of the
       main container. */
    top: calc(3.5rem + 20px);
}

.bs-sidebar.card {
    padding: 0;
    max-height: 90%;
    overflow-y: auto;
}

/* Toggle (vertically flip) sidebar collapse icon */
.bs-sidebar .navbar-toggler span {
    -moz-transform: scale(1, -1);
    -webkit-transform: scale(1, -1);
    -o-transform: scale(1, -1);
    -ms-transform: scale(1, -1);
    transform: scale(1, -1);
}

.bs-sidebar .navbar-toggler.collapsed span {
    -moz-transform: scale(1, 1);
    -webkit-transform: scale(1, 1);
    -o-transform: scale(1, 1);
    -ms-transform: scale(1, 1);
    transform: scale(1, 1);
}

/* First level of nav */
.bs-sidebar > .navbar-collapse > .nav {
    padding-top:    10px;
    padding-bottom: 10px;
    border-radius: 5px;
    width: 100%;
}

/* All levels of nav */
.bs-sidebar .nav > li > a {
    display: block;
    padding: 5px 20px;
    z-index: 1;
}
.bs-sidebar .nav > li > a:hover,
.bs-sidebar .nav > li > a:focus {
    text-decoration: none;
    border-right: 1px solid;
}
.bs-sidebar .nav > li > a.active,
.bs-sidebar .nav > li > a.active:hover,
.bs-sidebar .nav > li > a.active:focus {
    font-weight: bold;
    background-color: transparent;
    border-right: 1px solid;
}

.bs-sidebar .nav .nav .nav {
    margin-left: 1em;
}

.bs-sidebar .nav > li > a {
    font-weight: bold;
}

.bs-sidebar .nav .nav > li > a {
    font-weight: normal;
}

.headerlink {
    font-family: FontAwesome;
    font-size: 14px;
    display: none;
    padding-left: .5em;
    text-decoration: none;
    vertical-align: middle;
}

h1:hover .headerlink, h2:hover .headerlink, h3:hover .headerlink, h4:hover .headerlink, h5:hover .headerlink, h6:hover .headerlink {
    display:inline-block;
}

blockquote {
    padding-left: 10px;
    border-left: 4px solid #e6e6e6;
}

.admonition, details {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    text-align: left;
}

.admonition.note, details.note {
    color: var(--bs-primary-text-emphasis);
    background-color: var(--bs-primary-bg-subtle);
    border-color: var(--bs-primary-border-subtle);
}

.admonition.note h1, .admonition.note h2, .admonition.note h3,
.admonition.note h4, .admonition.note h5, .admonition.note h6,
details.note h1, details.note h2, details.note h3,
details.note h4, details.note h5, details.note h6 {
    color: var(--bs-primary-text-emphasis);
}

.admonition.info, details.info {
    color: var(--bs-info-text-emphasis);
    background-color: var(--bs-info-bg-subtle);
    border-color: var(--bs-info-border-subtle);
}

.admonition.info h1, .admonition.info h2, .admonition.info h3,
.admonition.info h4, .admonition.info h5, .admonition.info h6,
details.info h1, details.info h2, details.info h3,
details.info h4, details.info h5, details.info h6 {
    color: var(--bs-info-text-emphasis);
}

.admonition.warning, details.warning {
    color: var(--bs-warning-text-emphasis);
    background-color: var(--bs-warning-bg-subtle);
    border-color: var(--bs-warning-border-subtle);
}

.admonition.warning h1, .admonition.warning h2, .admonition.warning h3,
.admonition.warning h4, .admonition.warning h5, .admonition.warning h6,
details.warning h1, details.warning h2, details.warning h3,
details.warning h4, details.warning h5, details.warning h6 {
    color: var(--bs-warning-text-emphasis);
}

.admonition.danger, details.danger {
    color: var(--bs-danger-text-emphasis);
    background-color: var(--bs-danger-bg-subtle);
    border-color: var(--bs-danger-border-subtle);
}

.admonition.danger h1, .admonition.danger h2, .admonition.danger h3,
.admonition.danger h4, .admonition.danger h5, .admonition.danger h6,
details.danger h1, details.danger h2, details.danger h3,
details.danger h4, details.danger h5, details.danger h6 {
    color: var(--bs-danger-text-emphasis);
}

.admonition, details {
    color: var(--bs-light-text-emphasis);
    background-color: var(--bs-light-bg-subtle);
    border-color: var(--bs-light-border-subtle);
}

.admonition h1, .admonition h2, .admonition h3,
.admonition h4, .admonition h5, .admonition h6,
details h1, details h2, details h3,
details h4, details h5, details h6 {
    color: var(--bs-light-text-emphasis);
}

.admonition-title, summary {
    font-weight: bold;
    text-align: left;
}

.admonition>p:last-child, details>p:last-child {
    margin-bottom: 0;
}

@media (max-width: 991.98px) {
    .navbar-collapse.show {
        overflow-y: auto;
        max-height: calc(100vh - 3.5rem);
    }
}

.dropdown-item.open {
    color: var(--bs-dropdown-link-active-color);
    background-color: var(--bs-dropdown-link-active-bg);
}

.dropdown-submenu > .dropdown-menu {
    margin: 0 0 0 1.5rem;
    padding: 0;
    border-width: 0;
}

.dropdown-submenu > a::after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: var(--bs-dropdown-link-active-color);
    margin-top: 5px;
    margin-right: -10px;
}

.dropdown-submenu:hover > a::after {
    border-left-color: var(--bs-dropdown-link-active-color);
}

@media (min-width: 992px) {
    .dropdown-menu {
        overflow-y: auto;
        max-height: calc(100vh - 3.5rem);
    }

    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu > .dropdown-menu {
        position: fixed !important;
        margin-top: -9px;
        margin-left: -2px;
        border-width: 1px;
        padding: 0.5rem 0;
    }

    .dropdown-submenu.pull-left {
        float: none;
    }

    .dropdown-submenu.pull-left > .dropdown-menu {
        left: -100%;
        margin-left: 10px;
    }
}

@media print {
    /* Remove sidebar when print */
    .col-md-3 { display: none; }
}
